TV Backtester Version Comparison
================================================================================
Started at: 2025-05-30 21:41:53.625621
Checking test data...
Created test TV file: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tv_test.xlsx

================================================================================
Testing CPU-based TV Backtester (Aggregated V4)
================================================================================
Command: /bin/python3 backtester_stable/BTRUN/BT_TV_GPU_aggregated_v4.py --start-date 240119 --end-date 240120 --output-dir /srv/samba/shared/Trades/tv_cpu_v4_test --slippage 0.1
Working directory: /srv/samba/shared/bt
Return code: 1
Time taken: 2.70 seconds

Output (last 1000 chars):
CONFIG.PY: Attempting to load config.py...
CONFIG.PY: Finished loading config.py.
Warning: Could not import required modules: No module named 'bt.backtester_stable.models'


Errors (last 1000 chars):
2025-05-30 21:41:54,056 - backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
2025-05-30 21:41:55,422 - numba.cuda.cudadrv.driver - INFO - init
2025-05-30 21:41:55,746 - backtester_stable.BTRUN.core.gpu_helpers - INFO - cuDF is available - GPU acceleration enabled
2025-05-30 21:41:56,013 - root - ERROR - Failed to import BTRUN config module
2025-05-30 21:41:56,021 - __main__ - INFO - ============================================================
2025-05-30 21:41:56,021 - __main__ - INFO - TV AGGREGATED BACKTEST V4
2025-05-30 21:41:56,022 - __main__ - INFO - ============================================================
2025-05-30 21:41:56,022 - backtester_stable.BTRUN.core.gpu_helpers - INFO - Switched to CPU-only mode programmatically
2025-05-30 21:41:56,022 - __main__ - ERROR - TV file not found: bt/backtester_stable/BTRUN/input_sheets/input_tv_universal.xlsx
2025-05-30 21:41:56,022 - __main__ - ERROR - No TV settings found


❌ No output files generated

================================================================================
Testing GPU-enhanced TV Backtester
================================================================================
Command: /bin/python3 backtester_stable/BTRUN/BT_TV_GPU_enhanced.py --tv-file input_tv.xlsx --output-dir /srv/samba/shared/Trades/tv_gpu_enhanced_test --debug
Working directory: /srv/samba/shared/bt
Return code: 0
Time taken: 2.69 seconds

Output (last 1000 chars):
CONFIG.PY: Attempting to load config.py...
CONFIG.PY: Finished loading config.py.


Errors (last 1000 chars):
ESTORED DEBUG CONFIG.PY - get_table_name active!
2025-05-30 21:41:56,750 - bt.backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
2025-05-30 21:41:56,750 - bt.backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
2025-05-30 21:41:58,111 - numba.cuda.cudadrv.driver - INFO - init
2025-05-30 21:41:58,414 - bt.backtester_stable.BTRUN.core.gpu_helpers - INFO - cuDF is available - GPU acceleration enabled
2025-05-30 21:41:58,675 - bt.backtester_stable.BTRUN.strategies.tv_processor - INFO - Using DirectDAL for database access
2025-05-30 21:41:58,695 - root - ERROR - Failed to import BTRUN config module
2025-05-30 21:41:58,705 - __main__ - INFO - Starting Enhanced TV GPU Backtester
2025-05-30 21:41:58,705 - __main__ - ERROR - TV file not found: bt/backtester_stable/BTRUN/input_sheets/input_tv.xlsx
2025-05-30 21:41:58,705 - __main__ - ERROR - No enabled TV settings found


❌ No output files generated

================================================================================
COMPARISON RESULTS
================================================================================

⏱️  Performance:
CPU V4 time: 2.70 seconds
GPU Enhanced time: 2.69 seconds
Speedup: 1.01x

❌ Cannot compare - missing output files

================================================================================
TEST COMPLETE
================================================================================
