2025-05-30 21:58:02,572 - backtester_stable.BTRUN.core.config - WARNING - Partially restored config.py is being imported - Phase 10.
2025-05-30 21:58:02,573 - backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - get_table_name active!
2025-05-30 21:58:02,573 - backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
2025-05-30 21:58:02,573 - backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
2025-05-30 21:58:03,966 - numba.cuda.cudadrv.driver - INFO - init
2025-05-30 21:58:04,276 - backtester_stable.BTRUN.core.gpu_helpers - INFO - cuDF is available - GPU acceleration enabled
2025-05-30 21:58:04,547 - backtester_stable.BTRUN.strategies.tv_processor - INFO - Using DirectDAL for database access
2025-05-30 21:58:04,568 - root - ERROR - Failed to import BTRUN config module
2025-05-30 21:58:04,579 - __main__ - INFO - ================================================================================
2025-05-30 21:58:04,579 - __main__ - INFO - TV GPU ENHANCED BACKTESTER (FIXED VERSION)
2025-05-30 21:58:04,579 - __main__ - INFO - ================================================================================
2025-05-30 21:58:04,579 - __main__ - INFO - Starting Enhanced TV GPU Backtester (FIXED)
2025-05-30 21:58:04,579 - __main__ - INFO - Using 1 workers
2025-05-30 21:58:04,579 - __main__ - INFO - Loading TV settings from: bt/backtester_stable/BTRUN/input_sheets/input_tv.xlsx
2025-05-30 21:58:04,579 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - INFO - Parsing TV settings from: bt/backtester_stable/BTRUN/input_sheets/input_tv.xlsx
2025-05-30 21:58:04,721 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Found 332 rows in Setting sheet
2025-05-30 21:58:04,723 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - INFO - Parsed TV setting: NFNDSTR
2025-05-30 21:58:04,723 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 1
2025-05-30 21:58:04,724 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 2
2025-05-30 21:58:04,724 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 3
2025-05-30 21:58:04,724 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 4
2025-05-30 21:58:04,725 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 5
2025-05-30 21:58:04,725 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,725 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 6
2025-05-30 21:58:04,726 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 7
2025-05-30 21:58:04,726 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 8
2025-05-30 21:58:04,726 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 9
2025-05-30 21:58:04,727 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 10
2025-05-30 21:58:04,727 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 11
2025-05-30 21:58:04,727 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 12
2025-05-30 21:58:04,728 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 13
2025-05-30 21:58:04,728 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 14
2025-05-30 21:58:04,729 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 15
2025-05-30 21:58:04,729 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 16
2025-05-30 21:58:04,729 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 17
2025-05-30 21:58:04,730 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,730 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 18
2025-05-30 21:58:04,730 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,730 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 19
2025-05-30 21:58:04,730 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 20
2025-05-30 21:58:04,731 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 21
2025-05-30 21:58:04,731 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 22
2025-05-30 21:58:04,732 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 23
2025-05-30 21:58:04,732 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 24
2025-05-30 21:58:04,732 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 25
2025-05-30 21:58:04,733 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 26
2025-05-30 21:58:04,733 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 27
2025-05-30 21:58:04,733 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 28
2025-05-30 21:58:04,734 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 29
2025-05-30 21:58:04,734 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 30
2025-05-30 21:58:04,735 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 31
2025-05-30 21:58:04,735 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 32
2025-05-30 21:58:04,735 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 33
2025-05-30 21:58:04,736 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 34
2025-05-30 21:58:04,736 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 35
2025-05-30 21:58:04,737 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 36
2025-05-30 21:58:04,737 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 37
2025-05-30 21:58:04,737 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 38
2025-05-30 21:58:04,738 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 39
2025-05-30 21:58:04,738 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 40
2025-05-30 21:58:04,738 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 41
2025-05-30 21:58:04,739 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 42
2025-05-30 21:58:04,739 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 43
2025-05-30 21:58:04,740 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,740 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 44
2025-05-30 21:58:04,740 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 45
2025-05-30 21:58:04,740 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 46
2025-05-30 21:58:04,741 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 47
2025-05-30 21:58:04,741 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 48
2025-05-30 21:58:04,742 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 49
2025-05-30 21:58:04,742 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 50
2025-05-30 21:58:04,743 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 51
2025-05-30 21:58:04,743 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 52
2025-05-30 21:58:04,743 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 53
2025-05-30 21:58:04,744 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 54
2025-05-30 21:58:04,744 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 55
2025-05-30 21:58:04,744 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 56
2025-05-30 21:58:04,745 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,745 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 57
2025-05-30 21:58:04,745 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,745 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 58
2025-05-30 21:58:04,745 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 59
2025-05-30 21:58:04,746 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 60
2025-05-30 21:58:04,746 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 61
2025-05-30 21:58:04,747 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 62
2025-05-30 21:58:04,747 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 63
2025-05-30 21:58:04,747 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 64
2025-05-30 21:58:04,748 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 65
2025-05-30 21:58:04,748 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 66
2025-05-30 21:58:04,748 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 67
2025-05-30 21:58:04,749 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 68
2025-05-30 21:58:04,749 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 69
2025-05-30 21:58:04,749 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 70
2025-05-30 21:58:04,750 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 71
2025-05-30 21:58:04,750 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 72
2025-05-30 21:58:04,750 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 73
2025-05-30 21:58:04,751 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 74
2025-05-30 21:58:04,751 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 75
2025-05-30 21:58:04,752 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 76
2025-05-30 21:58:04,752 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 77
2025-05-30 21:58:04,752 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 78
2025-05-30 21:58:04,753 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 79
2025-05-30 21:58:04,753 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 80
2025-05-30 21:58:04,753 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 81
2025-05-30 21:58:04,754 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 82
2025-05-30 21:58:04,754 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,754 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 83
2025-05-30 21:58:04,754 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,754 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 84
2025-05-30 21:58:04,755 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,755 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 85
2025-05-30 21:58:04,755 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 86
2025-05-30 21:58:04,755 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 87
2025-05-30 21:58:04,756 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 88
2025-05-30 21:58:04,756 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 89
2025-05-30 21:58:04,757 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 90
2025-05-30 21:58:04,757 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 91
2025-05-30 21:58:04,757 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 92
2025-05-30 21:58:04,758 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 93
2025-05-30 21:58:04,758 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 94
2025-05-30 21:58:04,759 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 95
2025-05-30 21:58:04,759 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 96
2025-05-30 21:58:04,759 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 97
2025-05-30 21:58:04,760 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 98
2025-05-30 21:58:04,760 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 99
2025-05-30 21:58:04,760 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 100
2025-05-30 21:58:04,761 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 101
2025-05-30 21:58:04,761 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 102
2025-05-30 21:58:04,761 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 103
2025-05-30 21:58:04,762 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 104
2025-05-30 21:58:04,762 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 105
2025-05-30 21:58:04,763 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 106
2025-05-30 21:58:04,763 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 107
2025-05-30 21:58:04,763 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 108
2025-05-30 21:58:04,764 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 109
2025-05-30 21:58:04,764 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,764 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 110
2025-05-30 21:58:04,764 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,764 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 111
2025-05-30 21:58:04,765 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,765 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 112
2025-05-30 21:58:04,765 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 113
2025-05-30 21:58:04,765 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 114
2025-05-30 21:58:04,766 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 115
2025-05-30 21:58:04,766 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 116
2025-05-30 21:58:04,766 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 117
2025-05-30 21:58:04,767 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 118
2025-05-30 21:58:04,767 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 119
2025-05-30 21:58:04,768 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 120
2025-05-30 21:58:04,768 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 121
2025-05-30 21:58:04,768 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,768 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 122
2025-05-30 21:58:04,769 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,769 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 123
2025-05-30 21:58:04,769 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,769 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 124
2025-05-30 21:58:04,769 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 125
2025-05-30 21:58:04,770 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 126
2025-05-30 21:58:04,770 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 127
2025-05-30 21:58:04,771 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 128
2025-05-30 21:58:04,771 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 129
2025-05-30 21:58:04,771 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 130
2025-05-30 21:58:04,772 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 131
2025-05-30 21:58:04,772 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 132
2025-05-30 21:58:04,772 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 133
2025-05-30 21:58:04,773 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 134
2025-05-30 21:58:04,773 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 135
2025-05-30 21:58:04,773 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 136
2025-05-30 21:58:04,774 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 137
2025-05-30 21:58:04,774 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 138
2025-05-30 21:58:04,775 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 139
2025-05-30 21:58:04,775 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 140
2025-05-30 21:58:04,775 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 141
2025-05-30 21:58:04,776 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 142
2025-05-30 21:58:04,776 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 143
2025-05-30 21:58:04,777 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 144
2025-05-30 21:58:04,777 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 145
2025-05-30 21:58:04,777 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,778 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 146
2025-05-30 21:58:04,778 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 147
2025-05-30 21:58:04,778 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,778 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 148
2025-05-30 21:58:04,779 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 149
2025-05-30 21:58:04,779 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 150
2025-05-30 21:58:04,779 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 151
2025-05-30 21:58:04,780 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 152
2025-05-30 21:58:04,780 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 153
2025-05-30 21:58:04,780 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 154
2025-05-30 21:58:04,781 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,781 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 155
2025-05-30 21:58:04,781 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,781 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 156
2025-05-30 21:58:04,781 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,781 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 157
2025-05-30 21:58:04,782 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 158
2025-05-30 21:58:04,782 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 159
2025-05-30 21:58:04,782 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,783 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 160
2025-05-30 21:58:04,783 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 161
2025-05-30 21:58:04,783 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 162
2025-05-30 21:58:04,784 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 163
2025-05-30 21:58:04,784 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 164
2025-05-30 21:58:04,784 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 165
2025-05-30 21:58:04,785 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,785 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 166
2025-05-30 21:58:04,785 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 167
2025-05-30 21:58:04,786 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 168
2025-05-30 21:58:04,786 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,786 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 169
2025-05-30 21:58:04,786 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 170
2025-05-30 21:58:04,787 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 171
2025-05-30 21:58:04,787 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 172
2025-05-30 21:58:04,788 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 173
2025-05-30 21:58:04,788 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 174
2025-05-30 21:58:04,788 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,789 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 175
2025-05-30 21:58:04,789 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 176
2025-05-30 21:58:04,789 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 177
2025-05-30 21:58:04,790 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,790 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 178
2025-05-30 21:58:04,790 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 179
2025-05-30 21:58:04,790 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 180
2025-05-30 21:58:04,791 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 181
2025-05-30 21:58:04,791 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 182
2025-05-30 21:58:04,792 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 183
2025-05-30 21:58:04,792 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,792 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 184
2025-05-30 21:58:04,792 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 185
2025-05-30 21:58:04,793 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 186
2025-05-30 21:58:04,793 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,793 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 187
2025-05-30 21:58:04,794 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 188
2025-05-30 21:58:04,794 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 189
2025-05-30 21:58:04,794 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 190
2025-05-30 21:58:04,795 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 191
2025-05-30 21:58:04,795 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 192
2025-05-30 21:58:04,795 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,795 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 193
2025-05-30 21:58:04,796 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 194
2025-05-30 21:58:04,796 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 195
2025-05-30 21:58:04,797 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,797 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 196
2025-05-30 21:58:04,797 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 197
2025-05-30 21:58:04,797 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 198
2025-05-30 21:58:04,798 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 199
2025-05-30 21:58:04,798 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 200
2025-05-30 21:58:04,799 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 201
2025-05-30 21:58:04,799 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,799 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 202
2025-05-30 21:58:04,799 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,799 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 203
2025-05-30 21:58:04,800 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,800 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 204
2025-05-30 21:58:04,800 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,800 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 205
2025-05-30 21:58:04,800 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,800 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 206
2025-05-30 21:58:04,801 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 207
2025-05-30 21:58:04,801 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 208
2025-05-30 21:58:04,801 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 209
2025-05-30 21:58:04,802 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 210
2025-05-30 21:58:04,802 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 211
2025-05-30 21:58:04,802 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 212
2025-05-30 21:58:04,803 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 213
2025-05-30 21:58:04,803 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 214
2025-05-30 21:58:04,804 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 215
2025-05-30 21:58:04,804 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 216
2025-05-30 21:58:04,804 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 217
2025-05-30 21:58:04,805 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 218
2025-05-30 21:58:04,805 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 219
2025-05-30 21:58:04,806 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 220
2025-05-30 21:58:04,806 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 221
2025-05-30 21:58:04,806 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 222
2025-05-30 21:58:04,807 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 223
2025-05-30 21:58:04,807 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 224
2025-05-30 21:58:04,807 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 225
2025-05-30 21:58:04,808 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 226
2025-05-30 21:58:04,808 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 227
2025-05-30 21:58:04,809 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 228
2025-05-30 21:58:04,809 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 229
2025-05-30 21:58:04,809 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 230
2025-05-30 21:58:04,810 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 231
2025-05-30 21:58:04,810 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 232
2025-05-30 21:58:04,810 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 233
2025-05-30 21:58:04,811 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 234
2025-05-30 21:58:04,811 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 235
2025-05-30 21:58:04,811 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 236
2025-05-30 21:58:04,812 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 237
2025-05-30 21:58:04,812 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 238
2025-05-30 21:58:04,812 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 239
2025-05-30 21:58:04,813 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 240
2025-05-30 21:58:04,813 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 241
2025-05-30 21:58:04,813 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 242
2025-05-30 21:58:04,814 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 243
2025-05-30 21:58:04,814 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 244
2025-05-30 21:58:04,814 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 245
2025-05-30 21:58:04,815 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 246
2025-05-30 21:58:04,815 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 247
2025-05-30 21:58:04,815 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 248
2025-05-30 21:58:04,816 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,816 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 249
2025-05-30 21:58:04,816 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,816 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 250
2025-05-30 21:58:04,817 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 251
2025-05-30 21:58:04,817 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 252
2025-05-30 21:58:04,817 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 253
2025-05-30 21:58:04,818 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 254
2025-05-30 21:58:04,818 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 255
2025-05-30 21:58:04,818 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 256
2025-05-30 21:58:04,819 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 257
2025-05-30 21:58:04,819 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 258
2025-05-30 21:58:04,819 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 259
2025-05-30 21:58:04,820 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 260
2025-05-30 21:58:04,820 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 261
2025-05-30 21:58:04,821 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 262
2025-05-30 21:58:04,821 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 263
2025-05-30 21:58:04,821 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 264
2025-05-30 21:58:04,822 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 265
2025-05-30 21:58:04,822 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 266
2025-05-30 21:58:04,822 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 267
2025-05-30 21:58:04,823 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 268
2025-05-30 21:58:04,823 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 269
2025-05-30 21:58:04,824 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 270
2025-05-30 21:58:04,824 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 271
2025-05-30 21:58:04,824 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 272
2025-05-30 21:58:04,825 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 273
2025-05-30 21:58:04,825 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 274
2025-05-30 21:58:04,825 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 275
2025-05-30 21:58:04,826 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 276
2025-05-30 21:58:04,826 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 277
2025-05-30 21:58:04,826 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 278
2025-05-30 21:58:04,827 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 279
2025-05-30 21:58:04,827 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 280
2025-05-30 21:58:04,827 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 281
2025-05-30 21:58:04,828 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 282
2025-05-30 21:58:04,828 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 283
2025-05-30 21:58:04,829 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 284
2025-05-30 21:58:04,829 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 285
2025-05-30 21:58:04,829 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 286
2025-05-30 21:58:04,830 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 287
2025-05-30 21:58:04,830 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 288
2025-05-30 21:58:04,830 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 289
2025-05-30 21:58:04,831 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 290
2025-05-30 21:58:04,831 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 291
2025-05-30 21:58:04,831 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 292
2025-05-30 21:58:04,832 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,832 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 293
2025-05-30 21:58:04,832 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,832 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 294
2025-05-30 21:58:04,832 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 295
2025-05-30 21:58:04,833 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 296
2025-05-30 21:58:04,833 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 297
2025-05-30 21:58:04,833 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 298
2025-05-30 21:58:04,834 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 299
2025-05-30 21:58:04,834 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 300
2025-05-30 21:58:04,834 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 301
2025-05-30 21:58:04,835 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 302
2025-05-30 21:58:04,835 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 303
2025-05-30 21:58:04,835 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 304
2025-05-30 21:58:04,836 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,836 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 305
2025-05-30 21:58:04,836 - backtester_stable.BTRUN.excel_parser.tv_column_mapper - WARNING - TV Column Validation: IntradaySqOffApplicable must be YES if TvExitApplicable is NO
2025-05-30 21:58:04,836 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 306
2025-05-30 21:58:04,836 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 307
2025-05-30 21:58:04,837 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 308
2025-05-30 21:58:04,837 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 309
2025-05-30 21:58:04,837 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 310
2025-05-30 21:58:04,838 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 311
2025-05-30 21:58:04,838 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 312
2025-05-30 21:58:04,839 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 313
2025-05-30 21:58:04,839 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 314
2025-05-30 21:58:04,839 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 315
2025-05-30 21:58:04,840 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 316
2025-05-30 21:58:04,840 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 317
2025-05-30 21:58:04,840 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 318
2025-05-30 21:58:04,841 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 319
2025-05-30 21:58:04,841 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 320
2025-05-30 21:58:04,841 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 321
2025-05-30 21:58:04,842 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 322
2025-05-30 21:58:04,842 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 323
2025-05-30 21:58:04,842 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 324
2025-05-30 21:58:04,843 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 325
2025-05-30 21:58:04,843 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 326
2025-05-30 21:58:04,843 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 327
2025-05-30 21:58:04,844 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 328
2025-05-30 21:58:04,844 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 329
2025-05-30 21:58:04,845 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 330
2025-05-30 21:58:04,845 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Skipping disabled setting at row 331
2025-05-30 21:58:04,845 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - INFO - Successfully parsed 1 TV settings
2025-05-30 21:58:04,845 - __main__ - INFO - Found 1 enabled TV settings
2025-05-30 21:58:04,845 - __main__ - INFO - 
Processing TV setting: NFNDSTR
2025-05-30 21:58:04,845 - __main__ - INFO -   Signal file: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/NF-NDSTR-D-24-25.xlsx
2025-05-30 21:58:04,845 - __main__ - INFO -   Loading signals from: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/NF-NDSTR-D-24-25.xlsx
2025-05-30 21:58:04,845 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - INFO - Parsing TV signals from: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/NF-NDSTR-D-24-25.xlsx
2025-05-30 21:58:05,022 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - DEBUG - Found 1764 signals
2025-05-30 21:58:05,215 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - INFO - Successfully parsed 1764 signals
2025-05-30 21:58:05,216 - __main__ - INFO -   Loaded 1764 raw signals
2025-05-30 21:58:05,216 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - INFO - Processing 1764 signals for NFNDSTR
2025-05-30 21:58:05,223 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - INFO - Added 1048 synthetic manual signals
2025-05-30 21:58:05,309 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - INFO - LONG: 507 processed signals
2025-05-30 21:58:05,310 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - INFO - SHORT: 375 processed signals
2025-05-30 21:58:05,310 - backtester_stable.BTRUN.excel_parser.tv_parser_enhanced - INFO - MANUAL: 524 processed signals
2025-05-30 21:58:05,310 - __main__ - INFO -   Processed 1406 signals
2025-05-30 21:58:05,310 - __main__ - INFO - 
  Processing LONG signals: 507
2025-05-30 21:58:05,311 - __main__ - INFO - 
  Processing SHORT signals: 375
2025-05-30 21:58:05,312 - __main__ - INFO - 
  Processing MANUAL signals: 524
2025-05-30 21:58:05,314 - __main__ - INFO - Generating output for 1406 results
2025-05-30 21:58:05,602 - __main__ - INFO - ✅ Output saved to: /srv/samba/shared/Trades/gpu_worker_test_1_20250530_215802/tv_enhanced_output_20250530_215805.xlsx
2025-05-30 21:58:05,602 - __main__ - INFO - Total trades: 1406
2025-05-30 21:58:05,602 - __main__ - INFO - Total P&L: -20259.99
2025-05-30 21:58:05,602 - __main__ - INFO - TV backtesting completed
