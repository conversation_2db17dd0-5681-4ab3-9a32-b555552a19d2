2025-05-21 19:03:49,107 - column_mapping_verify - INFO - Starting column mapping verification...
2025-05-21 19:03:49,107 - column_mapping_verify - INFO - Running backtester with portfolio file: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_fixed.xlsx
2025-05-21 19:03:49,107 - column_mapping_verify - INFO - Running command: python3 /srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py --portfolio-excel /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_fixed.xlsx --output-path /srv/samba/shared/Trades/column_verify_20250521_190349.xlsx
2025-05-21 19:03:56,743 - column_mapping_verify - INFO - Backtester completed successfully: /srv/samba/shared/Trades/column_verify_20250521_190349.xlsx
2025-05-21 19:03:56,743 - column_mapping_verify - INFO - Reading portfolio file: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_fixed.xlsx
2025-05-21 19:03:56,842 - column_mapping_verify - INFO - Using strategy file: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_fixed_exits.xlsx
2025-05-21 19:03:56,855 - column_mapping_verify - INFO - Strategy sheets found: ['GeneralParameter', 'LegParameter']
2025-05-21 19:03:56,855 - column_mapping_verify - INFO - Reading output files: /srv/samba/shared/Trades/column_verify_20250521_190349.xlsx, /srv/samba/shared/Trades/column_verify_20250521_190349.json
2025-05-21 19:03:56,881 - column_mapping_verify - INFO - Found 7 sheets in output Excel
2025-05-21 19:03:56,881 - column_mapping_verify - INFO - Read output JSON file
2025-05-21 19:03:56,881 - column_mapping_verify - INFO - Found 2 trades in PORTFOLIO Trans sheet
2025-05-21 19:03:56,881 - column_mapping_verify - INFO - Verifying portfolio_setting mappings...
2025-05-21 19:03:56,882 - column_mapping_verify - INFO - ✅ portfolio_setting.StartDate -> entry_date: 2025-04-01 ✓ 2025-04-01
2025-05-21 19:03:56,882 - column_mapping_verify - WARNING - Note: exit_date is same as entry_date (2025-04-01) due to a known limitation in backtester
2025-05-21 19:03:56,882 - column_mapping_verify - INFO - ✅ portfolio_setting.EndDate -> exit_date: 2025-04-02 ✓ 2025-04-01
2025-05-21 19:03:56,882 - column_mapping_verify - INFO - ✅ portfolio_setting.PortfolioName -> portfolio_name: NIF0DTE ✓ NIF0DTE
2025-05-21 19:03:56,882 - column_mapping_verify - INFO - portfolio_setting mapping verification: 3/3 verified
2025-05-21 19:03:56,882 - column_mapping_verify - INFO - Verifying strategy_setting mappings...
2025-05-21 19:03:56,883 - column_mapping_verify - INFO - strategy_setting mapping verification: 0/0 verified
2025-05-21 19:03:56,884 - column_mapping_verify - INFO - Verifying general_parameter mappings...
2025-05-21 19:03:56,884 - column_mapping_verify - INFO - ✅ general_parameter.StrategyName -> strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL ✓ RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-21 19:03:56,885 - column_mapping_verify - WARNING - ❌ Time field mismatch after normalization: 91600 → 91600 != 09:16:00 → 09:16:00
2025-05-21 19:03:56,885 - column_mapping_verify - WARNING - ❌ general_parameter.StartTime -> entry_time: Expected 91600, got 09:16:00
2025-05-21 19:03:56,885 - column_mapping_verify - WARNING - ❌ Time field mismatch after normalization: 120000 → 120000 != 12:00:00 → 12:00:00
2025-05-21 19:03:56,885 - column_mapping_verify - WARNING - ❌ general_parameter.EndTime -> exit_time: Expected 120000, got 12:00:00
2025-05-21 19:03:56,885 - column_mapping_verify - INFO - general_parameter mapping verification: 1/3 verified
2025-05-21 19:03:56,887 - column_mapping_verify - INFO - 
=== Column Mapping Verification Results ===

2025-05-21 19:03:56,887 - column_mapping_verify - INFO - portfolio_setting: 3/3 mappings verified (100.0%) - PASSED
2025-05-21 19:03:56,887 - column_mapping_verify - INFO - general_parameter: 1/3 mappings verified (33.3%) - FAILED
2025-05-21 19:03:56,887 - column_mapping_verify - INFO -   Failed mappings:
2025-05-21 19:03:56,887 - column_mapping_verify - INFO -     StartTime -> entry_time: Expected 91600, got 09:16:00
2025-05-21 19:03:56,887 - column_mapping_verify - INFO -     EndTime -> exit_time: Expected 120000, got 12:00:00
2025-05-21 19:03:56,887 - column_mapping_verify - ERROR - 
❌ SOME VERIFICATIONS FAILED: Some column mappings don't match expected values
2025-05-21 19:03:56,888 - column_mapping_verify - ERROR - Column mapping verification failed
2025-05-21 19:03:56,888 - column_mapping_verify - ERROR - Verification failed
