2025-06-02 20:20:25,105 - [MainProcess] - INFO - === Starting Robust Multiprocess Bulk Loader ===
2025-06-02 20:20:25,105 - [MainProcess] - INFO - Workers: 8
2025-06-02 20:20:25,105 - [MainProcess] - INFO - Batch size: 5000
2025-06-02 20:20:25,106 - [MainProcess] - INFO - Found 28 CSV files to process
2025-06-02 20:20:25,106 - [MainProcess] - INFO - Attempting to connect to HeavyDB via heavyai (1/3)...
2025-06-02 20:20:25,106 - [MainProcess] - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-02 20:20:25,335 - [MainProcess] - INFO - Successfully connected to HeavyDB via heavyai.
2025-06-02 20:20:25,359 - [MainProcess] - INFO - Starting with 54,000 rows in table
2025-06-02 20:20:25,404 - [ForkProcess-1] - INFO - Worker 0: Starting processing of IV_2023_apr_nifty_futures.csv
2025-06-02 20:20:25,404 - [ForkProcess-2] - INFO - Worker 1: Starting processing of IV_2023_aug_nifty_futures.csv
2025-06-02 20:20:25,404 - [ForkProcess-3] - INFO - Worker 2: Starting processing of IV_2023_dec_nifty_futures.csv
2025-06-02 20:20:25,404 - [ForkProcess-4] - INFO - Worker 3: Starting processing of IV_2023_feb_nifty_futures.csv
2025-06-02 20:20:25,404 - [ForkProcess-5] - INFO - Worker 4: Starting processing of IV_2023_jan_nifty_futures.csv
2025-06-02 20:20:25,404 - [ForkProcess-6] - INFO - Worker 5: Starting processing of IV_2023_july_nifty_futures.csv
2025-06-02 20:20:25,404 - [ForkProcess-7] - INFO - Worker 6: Starting processing of IV_2023_june_nifty_futures.csv
2025-06-02 20:20:25,404 - [ForkProcess-8] - INFO - Worker 7: Starting processing of IV_2023_mar_nifty_futures.csv
2025-06-02 20:20:25,405 - [ForkProcess-3] - INFO - Attempting to connect to HeavyDB via heavyai (1/3)...
2025-06-02 20:20:25,405 - [ForkProcess-2] - INFO - Attempting to connect to HeavyDB via heavyai (1/3)...
2025-06-02 20:20:25,405 - [ForkProcess-4] - INFO - Attempting to connect to HeavyDB via heavyai (1/3)...
2025-06-02 20:20:25,405 - [ForkProcess-1] - INFO - Attempting to connect to HeavyDB via heavyai (1/3)...
2025-06-02 20:20:25,405 - [ForkProcess-5] - INFO - Attempting to connect to HeavyDB via heavyai (1/3)...
2025-06-02 20:20:25,405 - [ForkProcess-6] - INFO - Attempting to connect to HeavyDB via heavyai (1/3)...
2025-06-02 20:20:25,405 - [ForkProcess-7] - INFO - Attempting to connect to HeavyDB via heavyai (1/3)...
2025-06-02 20:20:25,405 - [ForkProcess-8] - INFO - Attempting to connect to HeavyDB via heavyai (1/3)...
2025-06-02 20:20:25,405 - [ForkProcess-2] - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-02 20:20:25,405 - [ForkProcess-3] - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-02 20:20:25,405 - [ForkProcess-4] - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-02 20:20:25,405 - [ForkProcess-1] - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-02 20:20:25,406 - [ForkProcess-5] - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-02 20:20:25,406 - [ForkProcess-6] - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-02 20:20:25,406 - [ForkProcess-7] - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-02 20:20:25,406 - [ForkProcess-8] - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-02 20:20:25,676 - [ForkProcess-3] - INFO - Successfully connected to HeavyDB via heavyai.
2025-06-02 20:20:25,676 - [ForkProcess-2] - INFO - Successfully connected to HeavyDB via heavyai.
2025-06-02 20:20:25,676 - [ForkProcess-3] - INFO - Worker 2: Reading IV_2023_dec_nifty_futures.csv
2025-06-02 20:20:25,676 - [ForkProcess-2] - INFO - Worker 1: Reading IV_2023_aug_nifty_futures.csv
2025-06-02 20:20:25,676 - [ForkProcess-4] - INFO - Successfully connected to HeavyDB via heavyai.
2025-06-02 20:20:25,676 - [ForkProcess-1] - INFO - Successfully connected to HeavyDB via heavyai.
2025-06-02 20:20:25,676 - [ForkProcess-4] - INFO - Worker 3: Reading IV_2023_feb_nifty_futures.csv
2025-06-02 20:20:25,676 - [ForkProcess-1] - INFO - Worker 0: Reading IV_2023_apr_nifty_futures.csv
2025-06-02 20:20:25,676 - [ForkProcess-5] - INFO - Successfully connected to HeavyDB via heavyai.
2025-06-02 20:20:25,676 - [ForkProcess-5] - INFO - Worker 4: Reading IV_2023_jan_nifty_futures.csv
2025-06-02 20:20:25,676 - [ForkProcess-8] - INFO - Successfully connected to HeavyDB via heavyai.
2025-06-02 20:20:25,676 - [ForkProcess-8] - INFO - Worker 7: Reading IV_2023_mar_nifty_futures.csv
2025-06-02 20:20:25,676 - [ForkProcess-6] - INFO - Successfully connected to HeavyDB via heavyai.
2025-06-02 20:20:25,677 - [ForkProcess-6] - INFO - Worker 5: Reading IV_2023_july_nifty_futures.csv
2025-06-02 20:20:25,741 - [ForkProcess-7] - INFO - Successfully connected to HeavyDB via heavyai.
2025-06-02 20:20:25,742 - [ForkProcess-7] - INFO - Worker 6: Reading IV_2023_june_nifty_futures.csv
2025-06-02 20:20:27,715 - [ForkProcess-7] - INFO - Worker 6: File IV_2023_june_nifty_futures.csv has 291,324 rows
2025-06-02 20:20:28,146 - [ForkProcess-8] - INFO - Worker 7: File IV_2023_mar_nifty_futures.csv has 378,002 rows
2025-06-02 20:20:28,188 - [ForkProcess-4] - INFO - Worker 3: File IV_2023_feb_nifty_futures.csv has 357,510 rows
2025-06-02 20:20:28,219 - [ForkProcess-1] - INFO - Worker 0: File IV_2023_apr_nifty_futures.csv has 357,478 rows
2025-06-02 20:20:28,441 - [ForkProcess-5] - INFO - Worker 4: File IV_2023_jan_nifty_futures.csv has 370,861 rows
2025-06-02 20:20:28,677 - [ForkProcess-2] - INFO - Worker 1: File IV_2023_aug_nifty_futures.csv has 447,168 rows
2025-06-02 20:20:28,820 - [ForkProcess-6] - INFO - Worker 5: File IV_2023_july_nifty_futures.csv has 460,477 rows
2025-06-02 20:20:28,927 - [ForkProcess-3] - INFO - Worker 2: File IV_2023_dec_nifty_futures.csv has 518,939 rows
