2025-05-30 19:20:29,332 - bt.backtester_stable.BTRUN.core.config - WARNING - Partially restored config.py is being imported - Phase 10.
2025-05-30 19:20:29,332 - bt.backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - get_table_name active!
2025-05-30 19:20:29,332 - bt.backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
2025-05-30 19:20:29,332 - bt.backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
2025-05-30 19:20:30,735 - numba.cuda.cudadrv.driver - INFO - init
2025-05-30 19:20:31,039 - bt.backtester_stable.BTRUN.core.gpu_helpers - INFO - cuDF is available - GPU acceleration enabled
2025-05-30 19:20:31,315 - bt.backtester_stable.BTRUN.strategies.tv_processor - INFO - Using DirectDAL for database access
2025-05-30 19:20:31,338 - root - ERROR - Failed to import BTRUN config module
2025-05-30 19:20:31,340 - __main__ - ERROR - Import error: No module named 'backtester_stable'
