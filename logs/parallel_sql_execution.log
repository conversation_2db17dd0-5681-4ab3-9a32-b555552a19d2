2025-06-02 20:47:26,839 - [MainThread] - INFO - === Parallel SQL Batch Executor ===
2025-06-02 20:47:26,839 - [MainThread] - INFO - Workers: 8
2025-06-02 20:47:26,840 - [MainThread] - INFO - Starting at: 2025-06-02 20:47:26.840024
2025-06-02 20:47:27,132 - [MainThread] - INFO - Initial row count: 189,000
2025-06-02 20:47:27,134 - [MainThread] - INFO - Found 496 SQL files to execute
2025-06-02 20:47:27,763 - [ThreadPoolExecutor-0_2] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0003.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:27,769 - [ThreadPoolExecutor-0_3] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0004.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:27,777 - [ThreadPoolExecutor-0_6] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0007.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:27,784 - [ThreadPoolExecutor-0_5] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0006.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:27,789 - [ThreadPoolExecutor-0_4] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0005.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:27,791 - [ThreadPoolExecutor-0_7] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0008.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:27,814 - [ThreadPoolExecutor-0_1] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0002.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:27,820 - [ThreadPoolExecutor-0_0] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0001.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:28,391 - [ThreadPoolExecutor-0_3] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0010.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:28,433 - [ThreadPoolExecutor-0_2] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0009.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:28,446 - [ThreadPoolExecutor-0_7] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0014.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:28,466 - [ThreadPoolExecutor-0_5] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0012.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:28,495 - [ThreadPoolExecutor-0_6] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0011.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:28,504 - [ThreadPoolExecutor-0_4] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0013.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:28,559 - [ThreadPoolExecutor-0_1] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0015.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:28,592 - [ThreadPoolExecutor-0_0] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0016.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:28,740 - [MainThread] - INFO - Progress: 10/496 (2.0%) | Success: 0 | Failed: 16 | Rows: 189,000 (+0)
2025-06-02 20:47:29,036 - [ThreadPoolExecutor-0_3] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0017.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:29,086 - [ThreadPoolExecutor-0_2] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0018.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:29,107 - [ThreadPoolExecutor-0_7] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0019.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:29,120 - [ThreadPoolExecutor-0_5] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0020.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:29,190 - [ThreadPoolExecutor-0_1] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0023.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:29,191 - [ThreadPoolExecutor-0_4] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0022.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:29,212 - [ThreadPoolExecutor-0_6] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0021.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:29,217 - [ThreadPoolExecutor-0_0] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0024.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:29,437 - [MainThread] - INFO - Progress: 20/496 (4.0%) | Success: 0 | Failed: 24 | Rows: 189,000 (+0)
2025-06-02 20:47:29,674 - [ThreadPoolExecutor-0_3] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0025.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:29,742 - [ThreadPoolExecutor-0_2] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0026.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:29,782 - [ThreadPoolExecutor-0_5] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0028.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:29,798 - [ThreadPoolExecutor-0_7] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0027.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:29,841 - [ThreadPoolExecutor-0_6] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0031.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:29,869 - [ThreadPoolExecutor-0_4] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0030.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:29,899 - [ThreadPoolExecutor-0_1] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0029.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:29,920 - [ThreadPoolExecutor-0_0] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0032.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:30,166 - [MainThread] - INFO - Progress: 30/496 (6.0%) | Success: 0 | Failed: 32 | Rows: 189,000 (+0)
2025-06-02 20:47:30,286 - [ThreadPoolExecutor-0_3] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0033.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:30,396 - [ThreadPoolExecutor-0_2] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0034.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:30,443 - [ThreadPoolExecutor-0_7] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0036.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:30,475 - [ThreadPoolExecutor-0_5] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0035.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:30,482 - [ThreadPoolExecutor-0_4] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0038.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:30,534 - [ThreadPoolExecutor-0_6] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0037.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:30,584 - [ThreadPoolExecutor-0_0] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0040.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:30,605 - [ThreadPoolExecutor-0_1] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0039.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:30,912 - [MainThread] - INFO - Progress: 40/496 (8.1%) | Success: 0 | Failed: 40 | Rows: 189,000 (+0)
2025-06-02 20:47:31,006 - [ThreadPoolExecutor-0_3] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0041.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:31,038 - [ThreadPoolExecutor-0_2] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0042.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:31,109 - [ThreadPoolExecutor-0_5] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0044.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:31,153 - [ThreadPoolExecutor-0_7] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0043.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:31,179 - [ThreadPoolExecutor-0_4] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0045.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:31,190 - [ThreadPoolExecutor-0_6] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0046.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:31,273 - [ThreadPoolExecutor-0_0] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0047.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:31,289 - [ThreadPoolExecutor-0_1] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0048.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:31,630 - [ThreadPoolExecutor-0_3] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0049.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:31,692 - [ThreadPoolExecutor-0_2] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0050.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:31,755 - [ThreadPoolExecutor-0_5] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0051.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:31,781 - [ThreadPoolExecutor-0_7] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0052.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:31,820 - [ThreadPoolExecutor-0_6] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0054.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:31,827 - [ThreadPoolExecutor-0_4] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0053.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:31,934 - [ThreadPoolExecutor-0_1] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0056.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:31,949 - [ThreadPoolExecutor-0_0] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0055.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:32,046 - [MainThread] - INFO - Progress: 50/496 (10.1%) | Success: 0 | Failed: 56 | Rows: 189,000 (+0)
2025-06-02 20:47:32,313 - [ThreadPoolExecutor-0_7] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0060.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:32,319 - [ThreadPoolExecutor-0_3] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0057.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:32,370 - [ThreadPoolExecutor-0_2] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0058.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:32,385 - [ThreadPoolExecutor-0_5] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0059.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:32,454 - [ThreadPoolExecutor-0_6] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0061.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:32,470 - [ThreadPoolExecutor-0_4] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0062.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:32,573 - [ThreadPoolExecutor-0_1] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0063.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:32,581 - [ThreadPoolExecutor-0_0] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0064.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:32,672 - [MainThread] - INFO - Progress: 60/496 (12.1%) | Success: 0 | Failed: 64 | Rows: 189,000 (+0)
2025-06-02 20:47:32,973 - [ThreadPoolExecutor-0_3] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0066.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:32,984 - [ThreadPoolExecutor-0_2] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0067.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:33,016 - [ThreadPoolExecutor-0_7] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0065.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:33,080 - [ThreadPoolExecutor-0_6] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0069.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:33,089 - [ThreadPoolExecutor-0_5] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0068.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:33,097 - [ThreadPoolExecutor-0_4] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0070.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:33,198 - [ThreadPoolExecutor-0_1] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0071.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:33,273 - [ThreadPoolExecutor-0_0] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0072.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:33,392 - [MainThread] - INFO - Progress: 70/496 (14.1%) | Success: 0 | Failed: 72 | Rows: 189,000 (+0)
2025-06-02 20:47:33,583 - [ThreadPoolExecutor-0_2] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0074.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:33,605 - [ThreadPoolExecutor-0_3] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0073.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:33,655 - [ThreadPoolExecutor-0_7] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0075.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:33,703 - [ThreadPoolExecutor-0_5] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0077.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:33,751 - [ThreadPoolExecutor-0_6] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0076.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:33,790 - [ThreadPoolExecutor-0_4] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0078.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:33,810 - [ThreadPoolExecutor-0_1] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0079.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:33,912 - [ThreadPoolExecutor-0_0] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0080.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:34,200 - [MainThread] - INFO - Progress: 80/496 (16.1%) | Success: 0 | Failed: 80 | Rows: 189,000 (+0)
2025-06-02 20:47:34,216 - [ThreadPoolExecutor-0_2] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0081.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:34,238 - [ThreadPoolExecutor-0_3] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0082.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:34,273 - [ThreadPoolExecutor-0_7] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0083.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:34,326 - [ThreadPoolExecutor-0_5] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0084.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:34,383 - [ThreadPoolExecutor-0_6] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0085.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:34,410 - [ThreadPoolExecutor-0_4] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0086.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:34,421 - [ThreadPoolExecutor-0_1] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0087.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:34,591 - [ThreadPoolExecutor-0_0] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0088.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:34,871 - [ThreadPoolExecutor-0_3] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0090.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:34,893 - [ThreadPoolExecutor-0_2] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0089.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:34,904 - [ThreadPoolExecutor-0_7] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0091.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:34,994 - [ThreadPoolExecutor-0_5] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0092.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:35,019 - [ThreadPoolExecutor-0_6] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0093.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:35,033 - [ThreadPoolExecutor-0_4] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0094.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:35,063 - [ThreadPoolExecutor-0_1] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0095.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:35,181 - [MainThread] - INFO - Progress: 90/496 (18.1%) | Success: 0 | Failed: 95 | Rows: 189,000 (+0)
2025-06-02 20:47:35,254 - [ThreadPoolExecutor-0_0] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0096.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:35,485 - [ThreadPoolExecutor-0_3] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0097.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:35,542 - [ThreadPoolExecutor-0_7] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0099.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:35,553 - [ThreadPoolExecutor-0_2] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0098.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:35,609 - [ThreadPoolExecutor-0_5] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0100.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:35,656 - [ThreadPoolExecutor-0_4] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0102.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:35,679 - [ThreadPoolExecutor-0_1] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0103.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:35,693 - [ThreadPoolExecutor-0_6] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0101.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:35,877 - [ThreadPoolExecutor-0_0] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0104.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:35,898 - [MainThread] - INFO - Progress: 100/496 (20.2%) | Success: 0 | Failed: 104 | Rows: 189,000 (+0)
2025-06-02 20:47:36,104 - [ThreadPoolExecutor-0_3] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0105.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:36,159 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0106.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:36,168 - [ThreadPoolExecutor-0_2] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0107.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:36,276 - [ThreadPoolExecutor-0_5] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0108.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:36,328 - [ThreadPoolExecutor-0_4] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0109.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:36,329 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0110.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:36,336 - [ThreadPoolExecutor-0_6] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0111.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:36,599 - [ThreadPoolExecutor-0_0] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0112.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:36,622 - [MainThread] - INFO - Progress: 110/496 (22.2%) | Success: 0 | Failed: 112 | Rows: 189,000 (+0)
2025-06-02 20:47:36,791 - [ThreadPoolExecutor-0_3] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0113.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:36,813 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0114.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:36,832 - [ThreadPoolExecutor-0_2] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0115.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:36,897 - [ThreadPoolExecutor-0_5] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0116.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:36,942 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0118.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:36,962 - [ThreadPoolExecutor-0_4] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0117.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:36,968 - [ThreadPoolExecutor-0_6] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0119.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:37,325 - [ThreadPoolExecutor-0_0] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0120.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:37,408 - [ThreadPoolExecutor-0_3] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0121.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:37,433 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0122.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:37,474 - [ThreadPoolExecutor-0_2] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0123.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:37,526 - [ThreadPoolExecutor-0_5] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0124.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:37,592 - [ThreadPoolExecutor-0_1] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0125.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:37,611 - [ThreadPoolExecutor-0_4] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0126.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:37,617 - [ThreadPoolExecutor-0_6] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0127.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:37,618 - [MainThread] - INFO - Progress: 120/496 (24.2%) | Success: 0 | Failed: 127 | Rows: 189,000 (+0)
2025-06-02 20:47:37,945 - [ThreadPoolExecutor-0_0] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0128.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:38,025 - [ThreadPoolExecutor-0_3] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0129.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:38,050 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0130.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:38,143 - [ThreadPoolExecutor-0_2] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0131.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:38,145 - [ThreadPoolExecutor-0_5] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0132.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:38,183 - [ThreadPoolExecutor-0_6] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0135.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:38,201 - [ThreadPoolExecutor-0_1] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0133.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:38,210 - [ThreadPoolExecutor-0_4] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0134.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:38,345 - [MainThread] - INFO - Progress: 130/496 (26.2%) | Success: 0 | Failed: 135 | Rows: 189,000 (+0)
2025-06-02 20:47:38,549 - [ThreadPoolExecutor-0_0] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0136.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:38,619 - [ThreadPoolExecutor-0_3] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0137.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:38,651 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0138.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:38,727 - [ThreadPoolExecutor-0_2] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0139.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:38,735 - [ThreadPoolExecutor-0_5] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0140.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:38,799 - [ThreadPoolExecutor-0_6] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0141.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:38,818 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0142.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:38,842 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0143.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:39,015 - [MainThread] - INFO - Progress: 140/496 (28.2%) | Success: 0 | Failed: 143 | Rows: 189,000 (+0)
2025-06-02 20:47:39,165 - [ThreadPoolExecutor-0_0] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0144.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:39,244 - [ThreadPoolExecutor-0_3] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0145.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:39,271 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0146.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:39,369 - [ThreadPoolExecutor-0_2] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0147.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:39,388 - [ThreadPoolExecutor-0_6] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0149.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:39,419 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0150.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:39,428 - [ThreadPoolExecutor-0_5] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0148.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:39,494 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0151.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:39,737 - [MainThread] - INFO - Progress: 150/496 (30.2%) | Success: 0 | Failed: 151 | Rows: 189,000 (+0)
2025-06-02 20:47:39,795 - [ThreadPoolExecutor-0_0] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0152.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:39,857 - [ThreadPoolExecutor-0_3] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0153.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:39,899 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0154.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:39,980 - [ThreadPoolExecutor-0_2] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0155.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:40,003 - [ThreadPoolExecutor-0_6] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0156.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:40,038 - [ThreadPoolExecutor-0_1] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0157.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:40,044 - [ThreadPoolExecutor-0_5] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0158.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:40,103 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0159.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:40,402 - [ThreadPoolExecutor-0_0] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0160.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:40,475 - [ThreadPoolExecutor-0_3] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0161.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:40,521 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0162.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:40,608 - [ThreadPoolExecutor-0_2] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0163.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:40,610 - [ThreadPoolExecutor-0_6] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0164.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:40,653 - [ThreadPoolExecutor-0_5] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0166.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:40,658 - [ThreadPoolExecutor-0_1] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0165.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:40,693 - [MainThread] - INFO - Progress: 160/496 (32.3%) | Success: 0 | Failed: 166 | Rows: 189,000 (+0)
2025-06-02 20:47:40,715 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0167.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:41,024 - [ThreadPoolExecutor-0_0] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0168.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:41,080 - [ThreadPoolExecutor-0_3] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0169.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:41,131 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0170.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:41,216 - [ThreadPoolExecutor-0_2] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0171.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:41,222 - [ThreadPoolExecutor-0_6] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0172.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:41,270 - [ThreadPoolExecutor-0_5] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0173.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:41,330 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0175.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:41,382 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0174.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:41,416 - [MainThread] - INFO - Progress: 170/496 (34.3%) | Success: 0 | Failed: 175 | Rows: 189,000 (+0)
2025-06-02 20:47:41,631 - [ThreadPoolExecutor-0_0] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0176.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:41,660 - [ThreadPoolExecutor-0_3] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0177.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:41,700 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0178.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:41,821 - [ThreadPoolExecutor-0_2] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0179.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:41,840 - [ThreadPoolExecutor-0_6] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0180.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:41,882 - [ThreadPoolExecutor-0_5] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0181.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:41,955 - [ThreadPoolExecutor-0_4] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0182.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:41,998 - [ThreadPoolExecutor-0_1] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0183.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:42,131 - [MainThread] - INFO - Progress: 180/496 (36.3%) | Success: 0 | Failed: 183 | Rows: 189,000 (+0)
2025-06-02 20:47:42,289 - [ThreadPoolExecutor-0_3] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0185.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:42,322 - [ThreadPoolExecutor-0_0] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0184.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:42,329 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0186.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:42,487 - [ThreadPoolExecutor-0_2] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0187.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:42,488 - [ThreadPoolExecutor-0_6] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0188.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:42,575 - [ThreadPoolExecutor-0_5] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0189.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:42,585 - [ThreadPoolExecutor-0_4] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0190.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:42,628 - [ThreadPoolExecutor-0_1] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0191.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:42,878 - [MainThread] - INFO - Progress: 190/496 (38.3%) | Success: 0 | Failed: 191 | Rows: 189,000 (+0)
2025-06-02 20:47:42,929 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0192.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:42,969 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0194.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:43,054 - [ThreadPoolExecutor-0_0] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0193.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:43,084 - [ThreadPoolExecutor-0_6] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0196.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:43,178 - [ThreadPoolExecutor-0_2] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0195.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:43,252 - [ThreadPoolExecutor-0_4] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0198.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:43,270 - [ThreadPoolExecutor-0_1] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0199.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:43,277 - [ThreadPoolExecutor-0_5] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0197.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:43,557 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0200.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:43,673 - [ThreadPoolExecutor-0_0] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0202.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:43,676 - [ThreadPoolExecutor-0_7] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0201.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:43,711 - [ThreadPoolExecutor-0_6] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0203.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:43,791 - [ThreadPoolExecutor-0_2] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0204.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:43,848 - [MainThread] - INFO - Progress: 200/496 (40.3%) | Success: 0 | Failed: 204 | Rows: 189,000 (+0)
2025-06-02 20:47:43,879 - [ThreadPoolExecutor-0_4] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0205.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:43,916 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0206.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:43,940 - [ThreadPoolExecutor-0_5] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0207.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:44,239 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0208.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:44,302 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0210.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:44,303 - [ThreadPoolExecutor-0_0] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0209.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:44,336 - [ThreadPoolExecutor-0_6] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0211.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:44,416 - [ThreadPoolExecutor-0_2] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0212.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:44,586 - [ThreadPoolExecutor-0_5] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0215.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:44,586 - [ThreadPoolExecutor-0_4] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0213.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:44,598 - [MainThread] - INFO - Progress: 210/496 (42.3%) | Success: 0 | Failed: 214 | Rows: 189,000 (+0)
2025-06-02 20:47:44,613 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0214.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:44,823 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0216.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:44,912 - [ThreadPoolExecutor-0_0] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0218.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:44,961 - [ThreadPoolExecutor-0_7] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0217.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:44,980 - [ThreadPoolExecutor-0_6] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0219.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:45,041 - [ThreadPoolExecutor-0_2] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0220.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:45,217 - [ThreadPoolExecutor-0_5] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0221.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:45,221 - [ThreadPoolExecutor-0_4] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0222.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:45,242 - [ThreadPoolExecutor-0_1] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0223.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:45,332 - [MainThread] - INFO - Progress: 220/496 (44.4%) | Success: 0 | Failed: 223 | Rows: 189,000 (+0)
2025-06-02 20:47:45,446 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0224.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:45,525 - [ThreadPoolExecutor-0_0] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0225.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:45,599 - [ThreadPoolExecutor-0_6] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0227.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:45,646 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0226.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:45,739 - [ThreadPoolExecutor-0_2] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0228.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:45,843 - [ThreadPoolExecutor-0_5] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0229.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:45,856 - [ThreadPoolExecutor-0_4] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0230.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:45,878 - [ThreadPoolExecutor-0_1] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0231.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:46,071 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0232.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:46,146 - [MainThread] - INFO - Progress: 230/496 (46.4%) | Success: 0 | Failed: 232 | Rows: 189,000 (+0)
2025-06-02 20:47:46,168 - [ThreadPoolExecutor-0_0] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0233.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:46,223 - [ThreadPoolExecutor-0_6] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0234.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:46,277 - [ThreadPoolExecutor-0_7] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0235.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:46,365 - [ThreadPoolExecutor-0_2] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0236.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:46,452 - [ThreadPoolExecutor-0_5] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0237.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:46,471 - [ThreadPoolExecutor-0_4] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0238.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:46,492 - [ThreadPoolExecutor-0_1] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0239.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:46,702 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0240.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:46,794 - [ThreadPoolExecutor-0_0] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0241.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:46,837 - [ThreadPoolExecutor-0_6] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0242.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:46,900 - [ThreadPoolExecutor-0_7] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0243.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:47,000 - [MainThread] - INFO - Progress: 240/496 (48.4%) | Success: 0 | Failed: 243 | Rows: 189,000 (+0)
2025-06-02 20:47:47,007 - [ThreadPoolExecutor-0_2] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0244.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:47,089 - [ThreadPoolExecutor-0_5] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0245.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:47,110 - [ThreadPoolExecutor-0_1] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0247.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:47,115 - [ThreadPoolExecutor-0_4] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0246.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:47,394 - [ThreadPoolExecutor-0_0] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0249.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:47,445 - [ThreadPoolExecutor-0_6] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0250.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:47,460 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0248.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:47,508 - [ThreadPoolExecutor-0_7] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0251.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:47,666 - [ThreadPoolExecutor-0_2] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0252.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:47,725 - [ThreadPoolExecutor-0_5] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0253.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:47,754 - [MainThread] - INFO - Progress: 250/496 (50.4%) | Success: 0 | Failed: 253 | Rows: 189,000 (+0)
2025-06-02 20:47:47,759 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0255.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:47,760 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0254.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:47,980 - [ThreadPoolExecutor-0_0] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0256.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:48,040 - [ThreadPoolExecutor-0_3] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0258.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:48,176 - [ThreadPoolExecutor-0_7] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0259.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:48,204 - [ThreadPoolExecutor-0_6] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0257.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:48,289 - [ThreadPoolExecutor-0_2] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0260.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:48,343 - [ThreadPoolExecutor-0_5] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0261.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:48,381 - [ThreadPoolExecutor-0_1] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0263.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:48,396 - [ThreadPoolExecutor-0_4] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0262.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:48,593 - [MainThread] - INFO - Progress: 260/496 (52.4%) | Success: 0 | Failed: 263 | Rows: 189,000 (+0)
2025-06-02 20:47:48,686 - [ThreadPoolExecutor-0_3] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0265.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:48,703 - [ThreadPoolExecutor-0_0] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0264.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:48,794 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0266.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:48,834 - [ThreadPoolExecutor-0_6] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0267.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:48,906 - [ThreadPoolExecutor-0_2] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0268.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:48,968 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0270.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:48,982 - [ThreadPoolExecutor-0_5] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0269.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:49,070 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0271.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:49,271 - [MainThread] - INFO - Progress: 270/496 (54.4%) | Success: 0 | Failed: 271 | Rows: 189,000 (+0)
2025-06-02 20:47:49,352 - [ThreadPoolExecutor-0_0] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0273.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:49,363 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0272.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:49,434 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0274.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:49,451 - [ThreadPoolExecutor-0_6] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0275.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:49,534 - [ThreadPoolExecutor-0_2] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0276.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:49,583 - [ThreadPoolExecutor-0_1] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0277.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:49,616 - [ThreadPoolExecutor-0_5] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0278.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:49,706 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0279.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:50,057 - [ThreadPoolExecutor-0_3] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0281.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:50,078 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0282.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:50,096 - [ThreadPoolExecutor-0_6] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0283.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:50,120 - [ThreadPoolExecutor-0_0] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0280.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:50,157 - [ThreadPoolExecutor-0_2] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0284.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:50,249 - [ThreadPoolExecutor-0_5] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0286.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:50,271 - [ThreadPoolExecutor-0_1] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0285.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:50,326 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0287.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:50,352 - [MainThread] - INFO - Progress: 280/496 (56.5%) | Success: 0 | Failed: 287 | Rows: 189,000 (+0)
2025-06-02 20:47:50,683 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0288.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:50,706 - [ThreadPoolExecutor-0_7] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0289.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:50,744 - [ThreadPoolExecutor-0_6] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0290.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:50,775 - [ThreadPoolExecutor-0_0] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0291.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:50,798 - [ThreadPoolExecutor-0_2] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0292.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:50,917 - [ThreadPoolExecutor-0_5] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0293.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:50,936 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0294.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:50,986 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0295.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:51,035 - [MainThread] - INFO - Progress: 290/496 (58.5%) | Success: 0 | Failed: 295 | Rows: 189,000 (+0)
2025-06-02 20:47:51,298 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0296.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:51,369 - [ThreadPoolExecutor-0_6] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0298.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:51,421 - [ThreadPoolExecutor-0_0] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0299.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:51,433 - [ThreadPoolExecutor-0_7] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0297.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:51,445 - [ThreadPoolExecutor-0_2] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0300.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:51,568 - [ThreadPoolExecutor-0_5] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0301.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:51,576 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0302.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:51,613 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0303.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:51,741 - [MainThread] - INFO - Progress: 300/496 (60.5%) | Success: 0 | Failed: 303 | Rows: 189,000 (+0)
2025-06-02 20:47:51,894 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0304.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:51,988 - [ThreadPoolExecutor-0_6] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0305.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:52,066 - [ThreadPoolExecutor-0_7] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0307.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:52,068 - [ThreadPoolExecutor-0_0] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0306.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:52,078 - [ThreadPoolExecutor-0_2] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0308.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:52,184 - [ThreadPoolExecutor-0_5] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0309.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:52,217 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0310.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:52,239 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0311.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:52,512 - [MainThread] - INFO - Progress: 310/496 (62.5%) | Success: 0 | Failed: 311 | Rows: 189,000 (+0)
2025-06-02 20:47:52,514 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0312.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:52,616 - [ThreadPoolExecutor-0_6] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0313.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:52,688 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0314.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:52,694 - [ThreadPoolExecutor-0_0] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0315.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:52,810 - [ThreadPoolExecutor-0_2] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0316.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:52,836 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0318.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:52,887 - [ThreadPoolExecutor-0_5] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0317.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:52,924 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0319.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:53,220 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0320.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:53,246 - [ThreadPoolExecutor-0_6] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0321.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:53,312 - [ThreadPoolExecutor-0_0] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0323.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:53,331 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0322.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:53,448 - [ThreadPoolExecutor-0_2] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0324.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:53,471 - [ThreadPoolExecutor-0_1] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0325.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:53,512 - [MainThread] - INFO - Progress: 320/496 (64.5%) | Success: 0 | Failed: 325 | Rows: 189,000 (+0)
2025-06-02 20:47:53,540 - [ThreadPoolExecutor-0_5] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0326.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:53,566 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0327.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:53,827 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0328.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:53,847 - [ThreadPoolExecutor-0_6] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0329.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:53,933 - [ThreadPoolExecutor-0_7] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0331.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:53,990 - [ThreadPoolExecutor-0_0] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0330.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:54,068 - [ThreadPoolExecutor-0_2] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0332.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:54,142 - [ThreadPoolExecutor-0_5] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0334.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:54,149 - [ThreadPoolExecutor-0_1] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0333.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:54,203 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0335.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:54,232 - [MainThread] - INFO - Progress: 330/496 (66.5%) | Success: 0 | Failed: 335 | Rows: 189,000 (+0)
2025-06-02 20:47:54,458 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0336.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:54,466 - [ThreadPoolExecutor-0_6] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0337.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:54,543 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0338.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:54,622 - [ThreadPoolExecutor-0_0] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0339.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:54,676 - [ThreadPoolExecutor-0_2] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0340.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:54,739 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0342.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:54,760 - [ThreadPoolExecutor-0_5] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0341.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:54,809 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0343.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:54,950 - [MainThread] - INFO - Progress: 340/496 (68.5%) | Success: 0 | Failed: 343 | Rows: 189,000 (+0)
2025-06-02 20:47:55,062 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0344.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:55,104 - [ThreadPoolExecutor-0_6] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0345.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:55,162 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0346.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:55,209 - [ThreadPoolExecutor-0_0] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0347.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:55,268 - [ThreadPoolExecutor-0_2] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0348.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:55,334 - [ThreadPoolExecutor-0_1] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0349.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:55,357 - [ThreadPoolExecutor-0_5] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0350.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:55,430 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_apr_nifty_futures_batch_0351.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:55,652 - [MainThread] - INFO - Progress: 350/496 (70.6%) | Success: 0 | Failed: 351 | Rows: 189,000 (+0)
2025-06-02 20:47:55,686 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_apr_nifty_futures_batch_0352.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:55,716 - [ThreadPoolExecutor-0_6] - ERROR - Worker 0: ✗ IV_2023_apr_nifty_futures_batch_0353.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:55,790 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_apr_nifty_futures_batch_0354.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:55,807 - [ThreadPoolExecutor-0_5] - ERROR - Worker 5: ✗ IV_2023_apr_nifty_futures_batch_0358.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:55,837 - [ThreadPoolExecutor-0_0] - ERROR - Worker 2: ✗ IV_2023_apr_nifty_futures_batch_0355.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:55,898 - [ThreadPoolExecutor-0_2] - ERROR - Worker 3: ✗ IV_2023_apr_nifty_futures_batch_0356.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:55,908 - [ThreadPoolExecutor-0_1] - ERROR - Worker 4: ✗ IV_2023_apr_nifty_futures_batch_0357.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:56,120 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_aug_nifty_futures_batch_0001.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:56,342 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_aug_nifty_futures_batch_0002.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:56,379 - [ThreadPoolExecutor-0_6] - ERROR - Worker 0: ✗ IV_2023_aug_nifty_futures_batch_0003.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:56,490 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_aug_nifty_futures_batch_0004.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:56,504 - [ThreadPoolExecutor-0_5] - ERROR - Worker 2: ✗ IV_2023_aug_nifty_futures_batch_0005.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:56,544 - [ThreadPoolExecutor-0_2] - ERROR - Worker 4: ✗ IV_2023_aug_nifty_futures_batch_0007.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:56,548 - [ThreadPoolExecutor-0_0] - ERROR - Worker 3: ✗ IV_2023_aug_nifty_futures_batch_0006.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:56,594 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_aug_nifty_futures_batch_0008.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:56,639 - [MainThread] - INFO - Progress: 360/496 (72.6%) | Success: 0 | Failed: 366 | Rows: 189,000 (+0)
2025-06-02 20:47:56,753 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_aug_nifty_futures_batch_0009.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:56,926 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_aug_nifty_futures_batch_0010.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:56,984 - [ThreadPoolExecutor-0_6] - ERROR - Worker 0: ✗ IV_2023_aug_nifty_futures_batch_0011.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:57,119 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_aug_nifty_futures_batch_0012.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:57,145 - [ThreadPoolExecutor-0_5] - ERROR - Worker 2: ✗ IV_2023_aug_nifty_futures_batch_0013.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:57,161 - [ThreadPoolExecutor-0_2] - ERROR - Worker 3: ✗ IV_2023_aug_nifty_futures_batch_0014.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:57,174 - [ThreadPoolExecutor-0_0] - ERROR - Worker 4: ✗ IV_2023_aug_nifty_futures_batch_0015.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:57,258 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_aug_nifty_futures_batch_0016.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:57,366 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_aug_nifty_futures_batch_0017.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:57,413 - [MainThread] - INFO - Progress: 370/496 (74.6%) | Success: 0 | Failed: 375 | Rows: 189,000 (+0)
2025-06-02 20:47:57,581 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_aug_nifty_futures_batch_0018.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:57,680 - [ThreadPoolExecutor-0_6] - ERROR - Worker 0: ✗ IV_2023_aug_nifty_futures_batch_0019.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:57,729 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_aug_nifty_futures_batch_0020.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:57,789 - [ThreadPoolExecutor-0_5] - ERROR - Worker 2: ✗ IV_2023_aug_nifty_futures_batch_0021.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:57,828 - [ThreadPoolExecutor-0_0] - ERROR - Worker 4: ✗ IV_2023_aug_nifty_futures_batch_0023.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:57,834 - [ThreadPoolExecutor-0_2] - ERROR - Worker 3: ✗ IV_2023_aug_nifty_futures_batch_0022.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:57,887 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_aug_nifty_futures_batch_0024.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:58,005 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_aug_nifty_futures_batch_0025.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:58,133 - [MainThread] - INFO - Progress: 380/496 (76.6%) | Success: 0 | Failed: 383 | Rows: 189,000 (+0)
2025-06-02 20:47:58,241 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_aug_nifty_futures_batch_0026.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:58,358 - [ThreadPoolExecutor-0_7] - ERROR - Worker 1: ✗ IV_2023_aug_nifty_futures_batch_0028.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:58,385 - [ThreadPoolExecutor-0_6] - ERROR - Worker 0: ✗ IV_2023_aug_nifty_futures_batch_0027.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:58,494 - [ThreadPoolExecutor-0_5] - ERROR - Worker 2: ✗ IV_2023_aug_nifty_futures_batch_0029.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:58,500 - [ThreadPoolExecutor-0_2] - ERROR - Worker 4: ✗ IV_2023_aug_nifty_futures_batch_0031.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:58,517 - [ThreadPoolExecutor-0_0] - ERROR - Worker 3: ✗ IV_2023_aug_nifty_futures_batch_0030.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:58,586 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_aug_nifty_futures_batch_0032.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:58,612 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_aug_nifty_futures_batch_0033.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:58,870 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_aug_nifty_futures_batch_0034.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:58,883 - [MainThread] - INFO - Progress: 390/496 (78.6%) | Success: 0 | Failed: 392 | Rows: 189,000 (+0)
2025-06-02 20:47:59,028 - [ThreadPoolExecutor-0_7] - ERROR - Worker 0: ✗ IV_2023_aug_nifty_futures_batch_0035.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:59,032 - [ThreadPoolExecutor-0_6] - ERROR - Worker 1: ✗ IV_2023_aug_nifty_futures_batch_0036.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:59,100 - [ThreadPoolExecutor-0_5] - ERROR - Worker 2: ✗ IV_2023_aug_nifty_futures_batch_0037.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:59,165 - [ThreadPoolExecutor-0_0] - ERROR - Worker 4: ✗ IV_2023_aug_nifty_futures_batch_0039.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:59,173 - [ThreadPoolExecutor-0_2] - ERROR - Worker 3: ✗ IV_2023_aug_nifty_futures_batch_0038.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:59,227 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_aug_nifty_futures_batch_0040.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:59,241 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_aug_nifty_futures_batch_0041.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:59,515 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_aug_nifty_futures_batch_0042.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:59,657 - [ThreadPoolExecutor-0_7] - ERROR - Worker 0: ✗ IV_2023_aug_nifty_futures_batch_0043.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:59,681 - [ThreadPoolExecutor-0_6] - ERROR - Worker 1: ✗ IV_2023_aug_nifty_futures_batch_0044.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:59,724 - [ThreadPoolExecutor-0_5] - ERROR - Worker 2: ✗ IV_2023_aug_nifty_futures_batch_0045.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:59,787 - [ThreadPoolExecutor-0_0] - ERROR - Worker 3: ✗ IV_2023_aug_nifty_futures_batch_0046.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:59,810 - [MainThread] - INFO - Progress: 400/496 (80.6%) | Success: 0 | Failed: 404 | Rows: 189,000 (+0)
2025-06-02 20:47:59,851 - [ThreadPoolExecutor-0_2] - ERROR - Worker 4: ✗ IV_2023_aug_nifty_futures_batch_0047.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:59,880 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_aug_nifty_futures_batch_0048.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:47:59,894 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_aug_nifty_futures_batch_0049.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:00,105 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_aug_nifty_futures_batch_0050.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:00,298 - [ThreadPoolExecutor-0_7] - ERROR - Worker 0: ✗ IV_2023_aug_nifty_futures_batch_0051.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:00,331 - [ThreadPoolExecutor-0_6] - ERROR - Worker 1: ✗ IV_2023_aug_nifty_futures_batch_0052.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:00,342 - [ThreadPoolExecutor-0_5] - ERROR - Worker 2: ✗ IV_2023_aug_nifty_futures_batch_0053.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:00,409 - [ThreadPoolExecutor-0_0] - ERROR - Worker 3: ✗ IV_2023_aug_nifty_futures_batch_0054.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:00,494 - [ThreadPoolExecutor-0_2] - ERROR - Worker 4: ✗ IV_2023_aug_nifty_futures_batch_0055.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:00,525 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_aug_nifty_futures_batch_0056.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:00,568 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_aug_nifty_futures_batch_0057.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:00,625 - [MainThread] - INFO - Progress: 410/496 (82.7%) | Success: 0 | Failed: 415 | Rows: 189,000 (+0)
2025-06-02 20:48:00,687 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_aug_nifty_futures_batch_0058.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:00,918 - [ThreadPoolExecutor-0_7] - ERROR - Worker 0: ✗ IV_2023_aug_nifty_futures_batch_0059.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:00,956 - [ThreadPoolExecutor-0_6] - ERROR - Worker 1: ✗ IV_2023_aug_nifty_futures_batch_0060.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:00,965 - [ThreadPoolExecutor-0_5] - ERROR - Worker 2: ✗ IV_2023_aug_nifty_futures_batch_0061.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:01,032 - [ThreadPoolExecutor-0_0] - ERROR - Worker 3: ✗ IV_2023_aug_nifty_futures_batch_0062.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:01,126 - [ThreadPoolExecutor-0_2] - ERROR - Worker 4: ✗ IV_2023_aug_nifty_futures_batch_0063.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:01,177 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_aug_nifty_futures_batch_0064.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:01,194 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_aug_nifty_futures_batch_0065.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:01,315 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_aug_nifty_futures_batch_0066.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:01,323 - [MainThread] - INFO - Progress: 420/496 (84.7%) | Success: 0 | Failed: 424 | Rows: 189,000 (+0)
2025-06-02 20:48:01,549 - [ThreadPoolExecutor-0_7] - ERROR - Worker 0: ✗ IV_2023_aug_nifty_futures_batch_0067.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:01,584 - [ThreadPoolExecutor-0_6] - ERROR - Worker 1: ✗ IV_2023_aug_nifty_futures_batch_0068.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:01,652 - [ThreadPoolExecutor-0_5] - ERROR - Worker 2: ✗ IV_2023_aug_nifty_futures_batch_0069.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:01,714 - [ThreadPoolExecutor-0_0] - ERROR - Worker 3: ✗ IV_2023_aug_nifty_futures_batch_0070.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:01,755 - [ThreadPoolExecutor-0_2] - ERROR - Worker 4: ✗ IV_2023_aug_nifty_futures_batch_0071.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:01,806 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_aug_nifty_futures_batch_0072.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:01,851 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_aug_nifty_futures_batch_0073.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:01,940 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_aug_nifty_futures_batch_0074.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:02,098 - [MainThread] - INFO - Progress: 430/496 (86.7%) | Success: 0 | Failed: 432 | Rows: 189,000 (+0)
2025-06-02 20:48:02,164 - [ThreadPoolExecutor-0_7] - ERROR - Worker 0: ✗ IV_2023_aug_nifty_futures_batch_0075.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:02,243 - [ThreadPoolExecutor-0_6] - ERROR - Worker 1: ✗ IV_2023_aug_nifty_futures_batch_0076.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:02,295 - [ThreadPoolExecutor-0_5] - ERROR - Worker 2: ✗ IV_2023_aug_nifty_futures_batch_0077.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:02,344 - [ThreadPoolExecutor-0_0] - ERROR - Worker 3: ✗ IV_2023_aug_nifty_futures_batch_0078.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:02,411 - [ThreadPoolExecutor-0_2] - ERROR - Worker 4: ✗ IV_2023_aug_nifty_futures_batch_0079.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:02,430 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_aug_nifty_futures_batch_0080.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:02,446 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_aug_nifty_futures_batch_0081.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:02,565 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_aug_nifty_futures_batch_0082.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:02,790 - [ThreadPoolExecutor-0_7] - ERROR - Worker 0: ✗ IV_2023_aug_nifty_futures_batch_0083.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:02,858 - [MainThread] - INFO - Progress: 440/496 (88.7%) | Success: 0 | Failed: 441 | Rows: 189,000 (+0)
2025-06-02 20:48:02,864 - [ThreadPoolExecutor-0_6] - ERROR - Worker 1: ✗ IV_2023_aug_nifty_futures_batch_0084.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:02,918 - [ThreadPoolExecutor-0_5] - ERROR - Worker 2: ✗ IV_2023_aug_nifty_futures_batch_0085.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:02,987 - [ThreadPoolExecutor-0_0] - ERROR - Worker 3: ✗ IV_2023_aug_nifty_futures_batch_0086.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:03,049 - [ThreadPoolExecutor-0_2] - ERROR - Worker 4: ✗ IV_2023_aug_nifty_futures_batch_0087.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:03,084 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_aug_nifty_futures_batch_0088.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:03,116 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_aug_nifty_futures_batch_0089.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:03,214 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_aug_nifty_futures_batch_0090.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:03,412 - [ThreadPoolExecutor-0_7] - ERROR - Worker 0: ✗ IV_2023_aug_nifty_futures_batch_0091.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:03,482 - [ThreadPoolExecutor-0_6] - ERROR - Worker 1: ✗ IV_2023_aug_nifty_futures_batch_0092.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:03,523 - [ThreadPoolExecutor-0_5] - ERROR - Worker 2: ✗ IV_2023_aug_nifty_futures_batch_0093.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:03,614 - [ThreadPoolExecutor-0_0] - ERROR - Worker 3: ✗ IV_2023_aug_nifty_futures_batch_0094.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:03,680 - [ThreadPoolExecutor-0_2] - ERROR - Worker 4: ✗ IV_2023_aug_nifty_futures_batch_0095.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:03,706 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_aug_nifty_futures_batch_0096.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:03,760 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_aug_nifty_futures_batch_0097.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:03,797 - [MainThread] - INFO - Progress: 450/496 (90.7%) | Success: 0 | Failed: 455 | Rows: 189,000 (+0)
2025-06-02 20:48:03,833 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_aug_nifty_futures_batch_0098.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:04,057 - [ThreadPoolExecutor-0_7] - ERROR - Worker 0: ✗ IV_2023_aug_nifty_futures_batch_0099.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:04,135 - [ThreadPoolExecutor-0_5] - ERROR - Worker 2: ✗ IV_2023_aug_nifty_futures_batch_0101.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:04,193 - [ThreadPoolExecutor-0_6] - ERROR - Worker 1: ✗ IV_2023_aug_nifty_futures_batch_0100.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:04,255 - [ThreadPoolExecutor-0_0] - ERROR - Worker 3: ✗ IV_2023_aug_nifty_futures_batch_0102.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:04,342 - [ThreadPoolExecutor-0_2] - ERROR - Worker 4: ✗ IV_2023_aug_nifty_futures_batch_0103.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:04,367 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_aug_nifty_futures_batch_0105.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:04,408 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_aug_nifty_futures_batch_0104.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:04,442 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_aug_nifty_futures_batch_0106.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:04,586 - [MainThread] - INFO - Progress: 460/496 (92.7%) | Success: 0 | Failed: 464 | Rows: 189,000 (+0)
2025-06-02 20:48:04,685 - [ThreadPoolExecutor-0_7] - ERROR - Worker 0: ✗ IV_2023_aug_nifty_futures_batch_0107.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:04,778 - [ThreadPoolExecutor-0_5] - ERROR - Worker 1: ✗ IV_2023_aug_nifty_futures_batch_0108.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:04,969 - [ThreadPoolExecutor-0_2] - ERROR - Worker 4: ✗ IV_2023_aug_nifty_futures_batch_0111.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:04,983 - [ThreadPoolExecutor-0_6] - ERROR - Worker 2: ✗ IV_2023_aug_nifty_futures_batch_0109.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:04,996 - [ThreadPoolExecutor-0_4] - ERROR - Worker 5: ✗ IV_2023_aug_nifty_futures_batch_0112.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:05,035 - [ThreadPoolExecutor-0_1] - ERROR - Worker 6: ✗ IV_2023_aug_nifty_futures_batch_0113.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:05,081 - [ThreadPoolExecutor-0_0] - ERROR - Worker 3: ✗ IV_2023_aug_nifty_futures_batch_0110.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:05,140 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_aug_nifty_futures_batch_0114.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:05,316 - [ThreadPoolExecutor-0_7] - ERROR - Worker 0: ✗ IV_2023_aug_nifty_futures_batch_0115.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:05,327 - [MainThread] - INFO - Progress: 470/496 (94.8%) | Success: 0 | Failed: 473 | Rows: 189,000 (+0)
2025-06-02 20:48:05,409 - [ThreadPoolExecutor-0_5] - ERROR - Worker 1: ✗ IV_2023_aug_nifty_futures_batch_0116.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:05,620 - [ThreadPoolExecutor-0_2] - ERROR - Worker 2: ✗ IV_2023_aug_nifty_futures_batch_0117.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:05,626 - [ThreadPoolExecutor-0_6] - ERROR - Worker 3: ✗ IV_2023_aug_nifty_futures_batch_0118.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:05,710 - [ThreadPoolExecutor-0_0] - ERROR - Worker 6: ✗ IV_2023_aug_nifty_futures_batch_0121.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:05,711 - [ThreadPoolExecutor-0_4] - ERROR - Worker 4: ✗ IV_2023_aug_nifty_futures_batch_0119.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:05,728 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_aug_nifty_futures_batch_0120.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:05,755 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_aug_nifty_futures_batch_0122.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:05,941 - [ThreadPoolExecutor-0_7] - ERROR - Worker 0: ✗ IV_2023_aug_nifty_futures_batch_0123.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:06,034 - [ThreadPoolExecutor-0_5] - ERROR - Worker 1: ✗ IV_2023_aug_nifty_futures_batch_0124.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:06,048 - [MainThread] - INFO - Progress: 480/496 (96.8%) | Success: 0 | Failed: 482 | Rows: 189,000 (+0)
2025-06-02 20:48:06,238 - [ThreadPoolExecutor-0_2] - ERROR - Worker 2: ✗ IV_2023_aug_nifty_futures_batch_0125.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:06,247 - [ThreadPoolExecutor-0_6] - ERROR - Worker 3: ✗ IV_2023_aug_nifty_futures_batch_0126.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:06,329 - [ThreadPoolExecutor-0_0] - ERROR - Worker 4: ✗ IV_2023_aug_nifty_futures_batch_0127.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:06,348 - [ThreadPoolExecutor-0_1] - ERROR - Worker 6: ✗ IV_2023_aug_nifty_futures_batch_0129.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:06,353 - [ThreadPoolExecutor-0_4] - ERROR - Worker 5: ✗ IV_2023_aug_nifty_futures_batch_0128.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:06,381 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_aug_nifty_futures_batch_0130.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:06,584 - [ThreadPoolExecutor-0_7] - ERROR - Worker 0: ✗ IV_2023_aug_nifty_futures_batch_0131.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:06,667 - [ThreadPoolExecutor-0_5] - ERROR - Worker 1: ✗ IV_2023_aug_nifty_futures_batch_0132.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:06,863 - [ThreadPoolExecutor-0_2] - ERROR - Worker 2: ✗ IV_2023_aug_nifty_futures_batch_0133.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:06,878 - [ThreadPoolExecutor-0_6] - ERROR - Worker 3: ✗ IV_2023_aug_nifty_futures_batch_0134.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:06,962 - [MainThread] - INFO - Progress: 490/496 (98.8%) | Success: 0 | Failed: 492 | Rows: 189,000 (+0)
2025-06-02 20:48:06,980 - [ThreadPoolExecutor-0_1] - ERROR - Worker 5: ✗ IV_2023_aug_nifty_futures_batch_0136.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:06,991 - [ThreadPoolExecutor-0_0] - ERROR - Worker 4: ✗ IV_2023_aug_nifty_futures_batch_0135.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:07,002 - [ThreadPoolExecutor-0_3] - ERROR - Worker 7: ✗ IV_2023_aug_nifty_futures_batch_0138.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:07,007 - [ThreadPoolExecutor-0_4] - ERROR - Worker 6: ✗ IV_2023_aug_nifty_futures_batch_0137.sql - Error: SQL statements starting with comments are currently not allowed.

2025-06-02 20:48:07,253 - [MainThread] - INFO - 
=== Execution Complete ===
2025-06-02 20:48:07,253 - [MainThread] - INFO - Total files: 496
2025-06-02 20:48:07,253 - [MainThread] - INFO - Successful: 0
2025-06-02 20:48:07,253 - [MainThread] - INFO - Failed: 496
2025-06-02 20:48:07,253 - [MainThread] - INFO - Initial count: 189,000
2025-06-02 20:48:07,253 - [MainThread] - INFO - Final count: 189,000
2025-06-02 20:48:07,253 - [MainThread] - INFO - Rows added: 0
2025-06-02 20:48:07,254 - [MainThread] - INFO - Total time: 39.9 seconds
2025-06-02 20:48:07,254 - [MainThread] - INFO - Average time per file: 0.08 seconds
2025-06-02 20:48:07,254 - [MainThread] - INFO - Effective rate: 0 rows/second
2025-06-02 20:48:07,254 - [MainThread] - INFO - Finished at: 2025-06-02 20:48:07.254239
