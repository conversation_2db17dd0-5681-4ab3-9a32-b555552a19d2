================================================================================
COMPREHENSIVE BACKTEST COMPARISON
Test Date: 2024-04-01
Strategies: TBS, TV, ORB, OI
================================================================================
✓ Archive system configured to use synthetic future ATM

============================================================
Testing TBS Strategy
============================================================
✓ Input files found

1. Running Archive System for TBS...
  Executing archive backtest...
  ✓ Archive output: /srv/samba/shared/comparison_results/archive_TBS_2024-04-01.xlsx
  Duration: 39.51 seconds

2. Running New System for TBS...
  Executing new system backtest...
  Response: Response: 200
{
  "status": "success",
  "job_id": "v1_job_20250607_171628_tbs",
  "message": "Backtest completed successfully",
  "result": null
}

  ✓ New system output: /srv/samba/shared/comparison_results/new_TBS_2024-04-01.xlsx
  Duration: 1.43 seconds

3. Comparing TBS Results...
  Archive sheets: 9
  New sheets: 8
  Common sheets: 7

============================================================
Testing TV Strategy
============================================================
✓ Input files found

1. Running Archive System for TV...
  Executing archive backtest...
  ✓ Archive output: /srv/samba/shared/comparison_results/archive_TV_2024-04-01.xlsx
  Duration: 39.30 seconds

2. Running New System for TV...
  Executing new system backtest...
  Response: Response: 400
{
  "detail": "1 validation error for BacktestRequest\nfiles\n  Value error, Missing required files for tv: ['settings', 'signals'] [type=value_error, input_value={'portfolio': '/tmp/tmps9...s97ukxin/strategy.xlsx'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error"
}

  ✗ No new system output generated

============================================================
Testing ORB Strategy
============================================================
✓ Input files found

1. Running Archive System for ORB...
  Executing archive backtest...
  ✓ Archive output: /srv/samba/shared/comparison_results/archive_ORB_2024-04-01.xlsx
  Duration: 39.64 seconds

2. Running New System for ORB...
  Executing new system backtest...
  Response: Response: 200
{
  "status": "success",
  "job_id": "v1_job_20250607_171749_orb",
  "message": "Backtest completed successfully",
  "result": null
}

  ✗ No new system output generated

============================================================
Testing OI Strategy
============================================================
✓ Input files found

1. Running Archive System for OI...
  Executing archive backtest...
  ✓ Archive output: /srv/samba/shared/comparison_results/archive_OI_2024-04-01.xlsx
  Duration: 39.35 seconds

2. Running New System for OI...
  Executing new system backtest...
  Response: Response: 200
{
  "status": "success",
  "job_id": "v1_job_20250607_171829_oi",
  "message": "Backtest completed successfully",
  "result": null
}

  ✗ No new system output generated

================================================================================
SUMMARY REPORT
================================================================================

Results saved to: /srv/samba/shared/comparison_results
Summary: /srv/samba/shared/comparison_results/comparison_summary_20250607_171830.json

Execution Summary:
Strategy   Archive         New System      Match     
--------------------------------------------------
TBS        ✓               ✓               ✓         
TV         ✓               ✗               ✗         
ORB        ✓               ✗               ✗         
OI         ✓               ✗               ✗         

Note: Both systems are using synthetic future ATM calculation
