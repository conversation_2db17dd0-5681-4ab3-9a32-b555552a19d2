2025-05-21 18:57:44,079 - column_mapping_test - INFO - Starting column mapping test...
2025-05-21 18:57:44,080 - column_mapping_test - INFO - Running backtester with portfolio file: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_fixed.xlsx
2025-05-21 18:57:44,080 - column_mapping_test - INFO - Running command: python3 /srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py --portfolio-excel /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_fixed.xlsx --output-path /srv/samba/shared/Trades/column_test_20250521_185744.xlsx
2025-05-21 18:57:51,422 - column_mapping_test - INFO - Backtester completed successfully: /srv/samba/shared/Trades/column_test_20250521_185744.xlsx
2025-05-21 18:57:51,422 - column_mapping_test - INFO - Analyzing output file: /srv/samba/shared/Trades/column_test_20250521_185744.xlsx
2025-05-21 18:57:51,540 - column_mapping_test - INFO - Sheets in output file: ['Metrics', 'Max Profit and Loss', 'PORTFOLIO Trans', 'RS,916-1200,ATM-Sell,OTM2_Trans', 'RS,916-1200,ATM-Sell,OTM2_DayWise', 'RS,916-1200,ATM-Sell,OTM2_MonthWise', 'RS,916-1200,ATM-Sell,OTM2_MarginPct']
2025-05-21 18:57:51,540 - column_mapping_test - INFO - Found 2 trades in output file
2025-05-21 18:57:51,540 - column_mapping_test - INFO - All required sheets and columns present
2025-05-21 18:57:51,540 - column_mapping_test - INFO - Checking trade values for correctness...
2025-05-21 18:57:51,541 - column_mapping_test - INFO - Unique entry times: ['09:16:00']
2025-05-21 18:57:51,541 - column_mapping_test - INFO - Unique exit times: ['12:00:00']
2025-05-21 18:57:51,541 - column_mapping_test - INFO - Unique exit reasons: ['Exit Time Hit']
2025-05-21 18:57:51,541 - column_mapping_test - INFO - Reading portfolio file: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_fixed.xlsx
2025-05-21 18:57:51,552 - column_mapping_test - INFO - Reading strategy file: /srv/samba/shared/bt/backtester_stable/BTRUN/D:\Ajay\BT PORTFOLIO\INPUT SHEETS\INPUT ORB.xlsx
2025-05-21 18:57:51,552 - column_mapping_test - ERROR - Strategy file not found: /srv/samba/shared/bt/backtester_stable/BTRUN/D:\Ajay\BT PORTFOLIO\INPUT SHEETS\INPUT ORB.xlsx
2025-05-21 18:57:51,552 - column_mapping_test - INFO - Portfolio name from input: NIF0DTE
2025-05-21 18:57:51,552 - column_mapping_test - INFO - Strategy type from input: ORB
2025-05-21 18:57:51,579 - column_mapping_test - INFO - Portfolio name mapping: ✅ OK
2025-05-21 18:57:51,579 - column_mapping_test - INFO - 
=== Column Mapping Test Results ===
2025-05-21 18:57:51,579 - column_mapping_test - INFO - Portfolio name: ✅ PASSED
2025-05-21 18:57:51,579 - column_mapping_test - INFO - 
✅ ALL CHECKS PASSED: Excel columns correctly mapped to output
2025-05-21 18:57:51,579 - column_mapping_test - INFO - Column mapping test completed successfully
