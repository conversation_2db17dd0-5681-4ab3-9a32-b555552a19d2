COMPREHENSIVE TBS COLUMN TEST
Testing all column functionalities with GPU optimization


================================================================================
COMPREHENSIVE TBS COLUMN TEST - GPU OPTIMIZED
================================================================================
Start time: 2025-05-30 09:00:02.079256
================================================================================
SYSTEM READINESS CHECK
================================================================================
✅ GPU detected
   | N/A   35C    P0    44W / 400W |   4560MiB / 40960MiB |      0%      Default |
   |    0   N/A  N/A   3726629      C   /opt/heavyai/bin/heavydb         4540MiB |

✅ CPU cores available: 72

GPU Modules:
  ✅ cudf
  ✅ cupy
  ✅ numba

✅ HeavyDB is running

✅ GPU optimization enabled

Command:
python3 /srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py --portfolio-excel /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/comprehensive_tests/MASTER_COMPREHENSIVE_TEST_PORTFOLIO.xlsx --output-path /srv/samba/shared/comprehensive_test_results_20250530_090004.xlsx --workers auto --cpu-workers 72 --batch-days 7 --merge-output --debug --use-gpu-optimization --gpu-workers 4 --gpu-threshold 1000

--------------------------------------------------------------------------------
Running comprehensive backtest...
--------------------------------------------------------------------------------
CONFIG.PY: Attempting to load config.py...
Partially restored config.py is being imported - Phase 10.
USING PARTIALLY RESTORED DEBUG CONFIG.PY - get_table_name active!
USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
CONFIG.PY: Finished loading config.py.
Warning: Could not import required modules: No module named 'bt.backtester_stable.models'
2025-05-30 09:00:06,640 - root - ERROR - Failed to import BTRUN config module
Failed to auto-detect optimal workers, defaulting to 1
2025-05-30 14:30:06,645 - __main__ - INFO - Running in sequential (single-worker) mode.
2025-05-30 14:30:06,645 - __main__ - INFO - GPU optimization is not available (missing dependencies or import error). Running sequentially.
2025-05-30 14:30:06,645 - __main__ - INFO - GPU optimization disabled or unavailable. Running sequentially.
2025-05-30 14:30:06,646 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-30 14:30:06,646 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-30 14:30:06,646 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-30 14:30:06,651 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-30 14:30:06,651 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-30 14:30:06,652 - backtester_stable.BTRUN.core.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-30 14:30:06,652 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-30 14:30:06,652 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-30 14:30:06,652 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-30 14:30:06,652 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-30 14:30:06,652 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-30 14:30:06,652 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-30 14:30:06,652 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/comprehensive_tests/MASTER_COMPREHENSIVE_TEST_PORTFOLIO.xlsx
2025-05-30 14:30:06,652 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/comprehensive_tests
2025-05-30 14:30:06,652 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/comprehensive_tests/MASTER_COMPREHENSIVE_TEST_PORTFOLIO.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/comprehensive_tests
2025-05-30 14:30:06,752 - __main__ - ERROR - Error in main (sequential path): 'PortfolioName'
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/indexes/base.py", line 3802, in get_loc
    return self._engine.get_loc(casted_key)
  File "pandas/_libs/index.pyx", line 138, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/index.pyx", line 165, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 5745, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 5753, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'PortfolioName'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py", line 757, in main
    parsed_portfolio_models = modern_portfolio_parser.parse_portfolio_excel(main_portfolio_excel_path)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/excel_parser/portfolio_parser.py", line 95, in parse_portfolio_excel
    stgy_rows = strategy_df[strategy_df["PortfolioName"].str.upper() == name]
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/frame.py", line 3807, in __getitem__
    indexer = self.columns.get_loc(key)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/indexes/base.py", line 3804, in get_loc
    raise KeyError(key) from err
KeyError: 'PortfolioName'
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/indexes/base.py", line 3802, in get_loc
    return self._engine.get_loc(casted_key)
  File "pandas/_libs/index.pyx", line 138, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/index.pyx", line 165, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 5745, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 5753, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'PortfolioName'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py", line 792, in <module>
    main()
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py", line 757, in main
    parsed_portfolio_models = modern_portfolio_parser.parse_portfolio_excel(main_portfolio_excel_path)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/excel_parser/portfolio_parser.py", line 95, in parse_portfolio_excel
    stgy_rows = strategy_df[strategy_df["PortfolioName"].str.upper() == name]
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/frame.py", line 3807, in __getitem__
    indexer = self.columns.get_loc(key)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/indexes/base.py", line 3804, in get_loc
    raise KeyError(key) from err
KeyError: 'PortfolioName'

================================================================================
BACKTEST COMPLETED
================================================================================
Return code: 1
Duration: 2.72 seconds (0.05 minutes)
Output file: /srv/samba/shared/comprehensive_test_results_20250530_090004.xlsx
Log file: /srv/samba/shared/comprehensive_test_20250530_090004.log

❌ Backtest failed!
Check log file: /srv/samba/shared/comprehensive_test_20250530_090004.log
2025-06-09 11:10:25,441 - INFO - Starting comprehensive test execution
2025-06-09 11:10:25,441 - INFO - 
============================================================
2025-06-09 11:10:25,442 - INFO - Testing OI Strategy
2025-06-09 11:10:25,442 - INFO - ============================================================
2025-06-09 11:10:25,442 - INFO - 
Running test: OI_Comprehensive_All_Columns
2025-06-09 11:10:25,442 - INFO - Description: Test all OI columns including MAXOI, MAXCOI, thresholds
2025-06-09 11:10:25,442 - INFO - Running archive system test: OI_Comprehensive_All_Columns
2025-06-09 11:10:25,446 - ERROR - Error running archive test OI_Comprehensive_All_Columns: [Errno 2] No such file or directory: 'python'
2025-06-09 11:10:25,446 - INFO - Running new system test: OI_Comprehensive_All_Columns
2025-06-09 11:10:25,447 - ERROR - Error running new system test OI_Comprehensive_All_Columns: [Errno 2] No such file or directory: 'python'
2025-06-09 11:10:25,447 - INFO - 
Running test: OI_Archive_Format
2025-06-09 11:10:25,447 - INFO - Description: Test archive format compatibility
2025-06-09 11:10:25,447 - INFO - Running archive system test: OI_Archive_Format
2025-06-09 11:10:25,450 - ERROR - Error running archive test OI_Archive_Format: [Errno 2] No such file or directory: 'python'
2025-06-09 11:10:25,450 - INFO - Running new system test: OI_Archive_Format
2025-06-09 11:10:25,451 - ERROR - Error running new system test OI_Archive_Format: [Errno 2] No such file or directory: 'python'
2025-06-09 11:10:25,451 - INFO - 
Running test: OI_Standard
2025-06-09 11:10:25,451 - INFO - Description: Standard OI test file
2025-06-09 11:10:25,451 - INFO - Running archive system test: OI_Standard
2025-06-09 11:10:25,453 - ERROR - Error running archive test OI_Standard: [Errno 2] No such file or directory: 'python'
2025-06-09 11:10:25,453 - INFO - Running new system test: OI_Standard
2025-06-09 11:10:25,454 - ERROR - Error running new system test OI_Standard: [Errno 2] No such file or directory: 'python'
2025-06-09 11:10:25,454 - INFO - 
============================================================
2025-06-09 11:10:25,454 - INFO - Testing TBS Strategy
2025-06-09 11:10:25,454 - INFO - ============================================================
2025-06-09 11:10:25,454 - INFO - 
Running test: TBS_Comprehensive_Portfolio
2025-06-09 11:10:25,454 - INFO - Description: Comprehensive TBS portfolio test
2025-06-09 11:10:25,454 - INFO - Running archive system test: TBS_Comprehensive_Portfolio
2025-06-09 11:10:25,456 - ERROR - Error running archive test TBS_Comprehensive_Portfolio: [Errno 2] No such file or directory: 'python'
2025-06-09 11:10:25,456 - INFO - Running new system test: TBS_Comprehensive_Portfolio
2025-06-09 11:10:25,457 - ERROR - Error running new system test TBS_Comprehensive_Portfolio: [Errno 2] No such file or directory: 'python'
2025-06-09 11:10:25,457 - INFO - 
Running test: TBS_Simple
2025-06-09 11:10:25,457 - INFO - Description: Simple TBS test
2025-06-09 11:10:25,457 - INFO - Running archive system test: TBS_Simple
2025-06-09 11:10:25,462 - ERROR - Error running archive test TBS_Simple: [Errno 2] No such file or directory: 'python'
2025-06-09 11:10:25,462 - INFO - Running new system test: TBS_Simple
2025-06-09 11:10:25,462 - ERROR - Error running new system test TBS_Simple: [Errno 2] No such file or directory: 'python'
2025-06-09 11:10:25,462 - INFO - 
Running test: TBS_Risk_Management
2025-06-09 11:10:25,462 - INFO - Description: All SL types test
2025-06-09 11:10:25,462 - INFO - Running archive system test: TBS_Risk_Management
2025-06-09 11:10:25,464 - ERROR - Error running archive test TBS_Risk_Management: [Errno 2] No such file or directory: 'python'
2025-06-09 11:10:25,465 - INFO - Running new system test: TBS_Risk_Management
2025-06-09 11:10:25,465 - ERROR - Error running new system test TBS_Risk_Management: [Errno 2] No such file or directory: 'python'
2025-06-09 11:10:25,465 - INFO - 
============================================================
2025-06-09 11:10:25,465 - INFO - Testing TV Strategy
2025-06-09 11:10:25,465 - INFO - ============================================================
2025-06-09 11:10:25,465 - INFO - 
Running test: TV_Comprehensive
2025-06-09 11:10:25,465 - INFO - Description: Comprehensive TV test with signals
2025-06-09 11:10:25,466 - INFO - Running archive system test: TV_Comprehensive
2025-06-09 11:10:25,469 - ERROR - Error running archive test TV_Comprehensive: [Errno 2] No such file or directory: 'python'
2025-06-09 11:10:25,469 - INFO - Running new system test: TV_Comprehensive
2025-06-09 11:10:25,470 - ERROR - Error running new system test TV_Comprehensive: [Errno 2] No such file or directory: 'python'
2025-06-09 11:10:25,470 - INFO - 
Running test: TV_Standard
2025-06-09 11:10:25,470 - INFO - Description: Standard TV 6-file hierarchy test
2025-06-09 11:10:25,470 - INFO - Running archive system test: TV_Standard
2025-06-09 11:10:25,481 - ERROR - Error running archive test TV_Standard: [Errno 2] No such file or directory: 'python'
2025-06-09 11:10:25,481 - INFO - Running new system test: TV_Standard
2025-06-09 11:10:25,482 - ERROR - Error running new system test TV_Standard: [Errno 2] No such file or directory: 'python'
2025-06-09 11:10:25,482 - INFO - 
============================================================
2025-06-09 11:10:25,482 - INFO - Testing ORB Strategy
2025-06-09 11:10:25,482 - INFO - ============================================================
2025-06-09 11:10:25,482 - INFO - 
Running test: ORB_Comprehensive
2025-06-09 11:10:25,482 - INFO - Description: Comprehensive ORB test with all features
2025-06-09 11:10:25,482 - INFO - Running archive system test: ORB_Comprehensive
2025-06-09 11:10:25,484 - ERROR - Error running archive test ORB_Comprehensive: [Errno 2] No such file or directory: 'python'
2025-06-09 11:10:25,484 - INFO - Running new system test: ORB_Comprehensive
2025-06-09 11:10:25,485 - ERROR - Error running new system test ORB_Comprehensive: [Errno 2] No such file or directory: 'python'
2025-06-09 11:10:25,485 - INFO - 
Running test: ORB_Standard
2025-06-09 11:10:25,485 - INFO - Description: Standard ORB test
2025-06-09 11:10:25,485 - INFO - Running archive system test: ORB_Standard
2025-06-09 11:10:25,487 - ERROR - Error running archive test ORB_Standard: [Errno 2] No such file or directory: 'python'
2025-06-09 11:10:25,488 - INFO - Running new system test: ORB_Standard
2025-06-09 11:10:25,488 - ERROR - Error running new system test ORB_Standard: [Errno 2] No such file or directory: 'python'
2025-06-09 11:10:25,488 - INFO - 
============================================================
2025-06-09 11:10:25,488 - INFO - Testing POS Strategy
2025-06-09 11:10:25,488 - INFO - ============================================================
2025-06-09 11:10:25,488 - INFO - 
Running test: POS_Comprehensive
2025-06-09 11:10:25,488 - INFO - Description: Comprehensive POS test (new system only)
2025-06-09 11:10:25,488 - INFO - POS is new system only - skipping archive
2025-06-09 11:10:25,489 - INFO - Running new system test: POS_Comprehensive
2025-06-09 11:10:25,489 - ERROR - Error running new system test POS_Comprehensive: [Errno 2] No such file or directory: 'python'
2025-06-09 11:10:25,489 - INFO - 
Running test: POS_Iron_Fly
2025-06-09 11:10:25,489 - INFO - Description: Iron Fly strategy test
2025-06-09 11:10:25,489 - INFO - POS is new system only - skipping archive
2025-06-09 11:10:25,490 - INFO - Running new system test: POS_Iron_Fly
2025-06-09 11:10:25,490 - ERROR - Error running new system test POS_Iron_Fly: [Errno 2] No such file or directory: 'python'
2025-06-09 11:10:25,490 - INFO - 
============================================================
2025-06-09 11:10:25,490 - INFO - Testing ML_INDICATOR Strategy
2025-06-09 11:10:25,490 - INFO - ============================================================
2025-06-09 11:10:25,490 - INFO - 
Running test: ML_Comprehensive
2025-06-09 11:10:25,490 - INFO - Description: Comprehensive ML Indicator test (new system only)
2025-06-09 11:10:25,490 - INFO - ML_INDICATOR is new system only - skipping archive
2025-06-09 11:10:25,491 - INFO - Running new system test: ML_Comprehensive
2025-06-09 11:10:25,491 - ERROR - Error running new system test ML_Comprehensive: [Errno 2] No such file or directory: 'python'
2025-06-09 11:10:25,491 - INFO - 
============================================================
2025-06-09 11:10:25,491 - INFO - COMPREHENSIVE TEST RESULTS SUMMARY
2025-06-09 11:10:25,491 - INFO - ============================================================
2025-06-09 11:10:25,491 - INFO - 
OI:
2025-06-09 11:10:25,491 - INFO -   Total Tests: 3
2025-06-09 11:10:25,492 - INFO -   Passed: 0
2025-06-09 11:10:25,492 - INFO -   Failed: 0
2025-06-09 11:10:25,492 - INFO -   Errors: 3
2025-06-09 11:10:25,492 - INFO - 
TBS:
2025-06-09 11:10:25,492 - INFO -   Total Tests: 3
2025-06-09 11:10:25,492 - INFO -   Passed: 0
2025-06-09 11:10:25,492 - INFO -   Failed: 0
2025-06-09 11:10:25,492 - INFO -   Errors: 3
2025-06-09 11:10:25,492 - INFO - 
TV:
2025-06-09 11:10:25,492 - INFO -   Total Tests: 2
2025-06-09 11:10:25,492 - INFO -   Passed: 0
2025-06-09 11:10:25,492 - INFO -   Failed: 0
2025-06-09 11:10:25,492 - INFO -   Errors: 2
2025-06-09 11:10:25,492 - INFO - 
ORB:
2025-06-09 11:10:25,492 - INFO -   Total Tests: 2
2025-06-09 11:10:25,492 - INFO -   Passed: 0
2025-06-09 11:10:25,492 - INFO -   Failed: 0
2025-06-09 11:10:25,492 - INFO -   Errors: 2
2025-06-09 11:10:25,492 - INFO - 
POS:
2025-06-09 11:10:25,492 - INFO -   Total Tests: 2
2025-06-09 11:10:25,492 - INFO -   Passed: 0
2025-06-09 11:10:25,492 - INFO -   Failed: 2
2025-06-09 11:10:25,493 - INFO -   Errors: 0
2025-06-09 11:10:25,493 - INFO - 
ML_INDICATOR:
2025-06-09 11:10:25,493 - INFO -   Total Tests: 1
2025-06-09 11:10:25,493 - INFO -   Passed: 0
2025-06-09 11:10:25,493 - INFO -   Failed: 1
2025-06-09 11:10:25,493 - INFO -   Errors: 0
2025-06-09 11:10:25,493 - INFO - 
OVERALL:
2025-06-09 11:10:25,493 - INFO -   Total Tests: 13
2025-06-09 11:10:25,493 - INFO -   Passed: 0
2025-06-09 11:10:25,493 - INFO -   Success Rate: 0.00%
2025-06-09 11:10:25,493 - INFO - 
Detailed report saved to: /srv/samba/shared/test_comparisons/comprehensive_test_report_20250609_111025.json
2025-06-09 11:10:25,493 - INFO - HTML report saved to: /srv/samba/shared/test_comparisons/comprehensive_test_report_20250609_111025.html
2025-06-09 11:10:25,494 - INFO - 
============================================================
2025-06-09 11:10:25,494 - INFO - COMPREHENSIVE TESTING COMPLETE
2025-06-09 11:10:25,494 - INFO - ============================================================
2025-06-09 11:10:25,494 - INFO - 
Next Steps:
2025-06-09 11:10:25,494 - INFO - 1. Review the comparison reports in: /srv/samba/shared/test_comparisons
2025-06-09 11:10:25,494 - INFO - 2. Investigate any failures or mismatches
2025-06-09 11:10:25,494 - INFO - 3. Update the code to fix any issues
2025-06-09 11:10:25,494 - INFO - 4. Re-run failed tests
2025-06-09 11:10:25,494 - INFO - 5. Document any acceptable differences (e.g., ATM calculations)
