2025-05-27 15:31:44,013 - backtester_stable.BTRUN.core.config - WARNING - Partially restored config.py is being imported - Phase 10.
2025-05-27 15:31:44,013 - backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - get_table_name active!
2025-05-27 15:31:44,013 - backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
2025-05-27 15:31:44,013 - backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
2025-05-27 15:31:45,303 - numba.cuda.cudadrv.driver - INFO - init
2025-05-27 15:31:45,593 - backtester_stable.BTRUN.core.gpu_helpers - INFO - cuDF is available - GPU acceleration enabled
2025-05-27 15:31:45,858 - root - ERROR - Failed to import BTRUN config module
2025-05-27 15:31:45,867 - __main__ - INFO - ============================================================
2025-05-27 15:31:45,867 - __main__ - INFO - TV AGGREGATED BACKTEST V4
2025-05-27 15:31:45,867 - __main__ - INFO - ============================================================
2025-05-27 15:31:45,867 - backtester_stable.BTRUN.core.gpu_helpers - INFO - Switched to CPU-only mode programmatically
2025-05-27 15:31:46,070 - __main__ - INFO - Processing TV setting: NFNDSTR
2025-05-27 15:31:46,244 - __main__ - INFO - Loaded 1764 signals
2025-05-27 15:31:46,322 - __main__ - INFO - Processed 0 signals
2025-05-27 15:31:46,322 - __main__ - INFO - Aggregating 0 results...
2025-05-27 15:31:46,322 - __main__ - ERROR - No results to aggregate
2025-05-27 15:31:46,322 - __main__ - INFO - ✅ TV aggregated backtest completed
