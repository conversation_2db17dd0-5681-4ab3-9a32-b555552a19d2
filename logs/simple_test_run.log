CONFIG.PY: Attempting to load config.py...
Partially restored config.py is being imported - Phase 10.
USING PARTIALLY RESTORED DEBUG CONFIG.PY - get_table_name active!
USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
CONFIG.PY: Finished loading config.py.
2025-05-30 09:09:01,397 - root - ERROR - Failed to import BTRUN config module
Warning: Could not import required modules: No module named 'bt.backtester_stable.models'
Failed to auto-detect optimal workers, defaulting to 1
2025-05-30 14:39:01,402 - __main__ - INFO - Running in sequential (single-worker) mode.
2025-05-30 14:39:01,403 - __main__ - INFO - GPU optimization is not available (missing dependencies or import error). Running sequentially.
2025-05-30 14:39:01,403 - __main__ - INFO - GPU optimization disabled or unavailable. Running sequentially.
2025-05-30 14:39:01,403 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-30 14:39:01,403 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-30 14:39:01,403 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-30 14:39:01,408 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-30 14:39:01,408 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-30 14:39:01,408 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-30 14:39:01,409 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-30 14:39:01,409 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-30 14:39:01,409 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-30 14:39:01,409 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-30 14:39:01,409 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-30 14:39:01,409 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/comprehensive_tests/SIMPLE_TEST_PORTFOLIO.xlsx
2025-05-30 14:39:01,409 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/comprehensive_tests
2025-05-30 14:39:01,409 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/comprehensive_tests/SIMPLE_TEST_PORTFOLIO.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/comprehensive_tests
_parse_leg_row Warning: Unknown Instrument (option type): OPTIONS. Skipping leg.
Row: StrategyName                   LEG_REENTRY_ALL_TYPES
LegID                                              1
Instrument                                   OPTIONS
Transaction                                     SELL
StrikeMethod                                     ATM
StrikeValue                                        0
SLType                                       PERCENT
SLValue                                         25.0
ReEntryType                              ASPERSTRIKE
ReEnteriesCount                                    3
Lots                                               2
IsIdle                                            NO
Expiry                                            CW
W&Type                                          NONE
W&TValue                                           0
TrailW&T                                           0
TGTType                                         NONE
TGTValue                                           0
TrailSLType                                     NONE
SL_TrailAt                                         0
SL_TrailBy                                         0
OnEntry_OpenTradeOn                               NO
OnEntry_SqOffTradeOff                             NO
OnEntry_SqOffAllLegs                              NO
OnEntry_OpenTradeDelay                             0
OnEntry_SqOffDelay                                 0
OnExit_OpenTradeOn                                NO
OnExit_SqOffTradeOff                              NO
OnExit_SqOffAllLegs                               NO
OnExit_OpenAllLegs                                NO
OnExit_OpenTradeDelay                              0
OnExit_SqOffDelay                                  0
OpenHedge                                         NO
HedgeStrikeMethod                               NONE
HedgeStrikeValue                                   0
HedgeStrikePremiumCondition                     NONE
Name: 0, dtype: object
_parse_leg_row Warning: Unknown Instrument (option type): OPTIONS. Skipping leg.
Row: StrategyName                   LEG_REENTRY_ALL_TYPES
LegID                                              2
Instrument                                   OPTIONS
Transaction                                     SELL
StrikeMethod                                     OTM
StrikeValue                                        2
SLType                                       PERCENT
SLValue                                         30.0
ReEntryType                                   STRIKE
ReEnteriesCount                                    2
Lots                                               2
IsIdle                                            NO
Expiry                                            CW
W&Type                                          NONE
W&TValue                                           0
TrailW&T                                           0
TGTType                                         NONE
TGTValue                                           0
TrailSLType                                     NONE
SL_TrailAt                                         0
SL_TrailBy                                         0
OnEntry_OpenTradeOn                               NO
OnEntry_SqOffTradeOff                             NO
OnEntry_SqOffAllLegs                              NO
OnEntry_OpenTradeDelay                             0
OnEntry_SqOffDelay                                 0
OnExit_OpenTradeOn                                NO
OnExit_SqOffTradeOff                              NO
OnExit_SqOffAllLegs                               NO
OnExit_OpenAllLegs                                NO
OnExit_OpenTradeDelay                              0
OnExit_SqOffDelay                                  0
OpenHedge                                         NO
HedgeStrikeMethod                               NONE
HedgeStrikeValue                                   0
HedgeStrikePremiumCondition                     NONE
Name: 1, dtype: object
_parse_leg_row Warning: Unknown Instrument (option type): OPTIONS. Skipping leg.
Row: StrategyName                   LEG_REENTRY_ALL_TYPES
LegID                                              3
Instrument                                   OPTIONS
Transaction                                      BUY
StrikeMethod                                     OTM
StrikeValue                                        5
SLType                                           NaN
SLValue                                          NaN
ReEntryType                                     NONE
ReEnteriesCount                                    0
Lots                                               2
IsIdle                                            NO
Expiry                                            CW
W&Type                                          NONE
W&TValue                                           0
TrailW&T                                           0
TGTType                                         NONE
TGTValue                                           0
TrailSLType                                     NONE
SL_TrailAt                                         0
SL_TrailBy                                         0
OnEntry_OpenTradeOn                               NO
OnEntry_SqOffTradeOff                             NO
OnEntry_SqOffAllLegs                              NO
OnEntry_OpenTradeDelay                             0
OnEntry_SqOffDelay                                 0
OnExit_OpenTradeOn                                NO
OnExit_SqOffTradeOff                              NO
OnExit_SqOffAllLegs                               NO
OnExit_OpenAllLegs                                NO
OnExit_OpenTradeDelay                              0
OnExit_SqOffDelay                                  0
OpenHedge                                         NO
HedgeStrikeMethod                               NONE
HedgeStrikeValue                                   0
HedgeStrikePremiumCondition                     NONE
Name: 2, dtype: object
_parse_leg_row Warning: Unknown Instrument (option type): OPTIONS. Skipping leg.
Row: StrategyName                   COMPLEX_REENTRY_CONDITIONS
LegID                                                   1
Instrument                                        OPTIONS
Transaction                                          SELL
StrikeMethod                                          ATM
StrikeValue                                             0
SLType                                            PERCENT
SLValue                                              20.0
ReEntryType                                   ASPERSTRIKE
ReEnteriesCount                                       2.0
OnExit_OpenTradeOn                                    YES
OnExit_OpenTradeDelay                                60.0
OnExit_SqOffAllLegs                                   YES
Lots                                                    2
OnEntry_SqOffTradeOff                                 NaN
OnEntry_SqOffDelay                                    NaN
IsIdle                                                 NO
Expiry                                                 CW
W&Type                                               NONE
W&TValue                                                0
TrailW&T                                                0
TGTType                                              NONE
TGTValue                                                0
TrailSLType                                          NONE
SL_TrailAt                                              0
SL_TrailBy                                              0
OnEntry_OpenTradeOn                                    NO
OnEntry_SqOffAllLegs                                   NO
OnEntry_OpenTradeDelay                                  0
OnExit_SqOffTradeOff                                   NO
OnExit_OpenAllLegs                                     NO
OnExit_SqOffDelay                                       0
OpenHedge                                              NO
HedgeStrikeMethod                                    NONE
HedgeStrikeValue                                        0
HedgeStrikePremiumCondition                          NONE
Name: 0, dtype: object
_parse_leg_row Warning: Unknown Instrument (option type): OPTIONS. Skipping leg.
Row: StrategyName                   COMPLEX_REENTRY_CONDITIONS
LegID                                                   2
Instrument                                        OPTIONS
Transaction                                           BUY
StrikeMethod                                          OTM
StrikeValue                                             5
SLType                                                NaN
SLValue                                               NaN
ReEntryType                                           NaN
ReEnteriesCount                                       NaN
OnExit_OpenTradeOn                                    NaN
OnExit_OpenTradeDelay                                 NaN
OnExit_SqOffAllLegs                                   NaN
Lots                                                    2
OnEntry_SqOffTradeOff                                 YES
OnEntry_SqOffDelay                                   30.0
IsIdle                                                 NO
Expiry                                                 CW
W&Type                                               NONE
W&TValue                                                0
TrailW&T                                                0
TGTType                                              NONE
TGTValue                                                0
TrailSLType                                          NONE
SL_TrailAt                                              0
SL_TrailBy                                              0
OnEntry_OpenTradeOn                                    NO
OnEntry_SqOffAllLegs                                   NO
OnEntry_OpenTradeDelay                                  0
OnExit_SqOffTradeOff                                   NO
OnExit_OpenAllLegs                                     NO
OnExit_SqOffDelay                                       0
OpenHedge                                              NO
HedgeStrikeMethod                                    NONE
HedgeStrikeValue                                        0
HedgeStrikePremiumCondition                          NONE
Name: 1, dtype: object
_parse_leg_row Warning: Unknown Instrument (option type): OPTIONS. Skipping leg.
Row: StrategyName                   COMPLEX_REENTRY_CONDITIONS
LegID                                                   3
Instrument                                        OPTIONS
Transaction                                          SELL
StrikeMethod                                          OTM
StrikeValue                                            -2
SLType                                            PERCENT
SLValue                                              30.0
ReEntryType                                        STRIKE
ReEnteriesCount                                       1.0
OnExit_OpenTradeOn                                    NaN
OnExit_OpenTradeDelay                                 NaN
OnExit_SqOffAllLegs                                   NaN
Lots                                                    2
OnEntry_SqOffTradeOff                                 NaN
OnEntry_SqOffDelay                                    NaN
IsIdle                                                 NO
Expiry                                                 CW
W&Type                                               NONE
W&TValue                                                0
TrailW&T                                                0
TGTType                                              NONE
TGTValue                                                0
TrailSLType                                          NONE
SL_TrailAt                                              0
SL_TrailBy                                              0
OnEntry_OpenTradeOn                                    NO
OnEntry_SqOffAllLegs                                   NO
OnEntry_OpenTradeDelay                                  0
OnExit_SqOffTradeOff                                   NO
OnExit_OpenAllLegs                                     NO
OnExit_SqOffDelay                                       0
OpenHedge                                              NO
HedgeStrikeMethod                                    NONE
HedgeStrikeValue                                        0
HedgeStrikePremiumCondition                          NONE
Name: 2, dtype: object
_parse_leg_row Warning: Unknown Instrument (option type): OPTIONS. Skipping leg.
Row: StrategyName                   STRATEGY_REEXECUTE
LegID                                           1
Instrument                                OPTIONS
Transaction                                  SELL
StrikeMethod                                  ATM
StrikeValue                                     0
SLType                                    PERCENT
SLValue                                      40.0
Lots                                            1
IsIdle                                         NO
Expiry                                         CW
W&Type                                       NONE
W&TValue                                        0
TrailW&T                                        0
TGTType                                      NONE
TGTValue                                        0
TrailSLType                                  NONE
SL_TrailAt                                      0
SL_TrailBy                                      0
ReEntryType                                  NONE
ReEnteriesCount                                 0
OnEntry_OpenTradeOn                            NO
OnEntry_SqOffTradeOff                          NO
OnEntry_SqOffAllLegs                           NO
OnEntry_OpenTradeDelay                          0
OnEntry_SqOffDelay                              0
OnExit_OpenTradeOn                             NO
OnExit_SqOffTradeOff                           NO
OnExit_SqOffAllLegs                            NO
OnExit_OpenAllLegs                             NO
OnExit_OpenTradeDelay                           0
OnExit_SqOffDelay                               0
OpenHedge                                      NO
HedgeStrikeMethod                            NONE
HedgeStrikeValue                                0
HedgeStrikePremiumCondition                  NONE
Name: 0, dtype: object
_parse_leg_row Warning: Unknown Instrument (option type): OPTIONS. Skipping leg.
Row: StrategyName                   STRATEGY_REEXECUTE
LegID                                           2
Instrument                                OPTIONS
Transaction                                   BUY
StrikeMethod                                  OTM
StrikeValue                                     2
SLType                                        NaN
SLValue                                       NaN
Lots                                            1
IsIdle                                         NO
Expiry                                         CW
W&Type                                       NONE
W&TValue                                        0
TrailW&T                                        0
TGTType                                      NONE
TGTValue                                        0
TrailSLType                                  NONE
SL_TrailAt                                      0
SL_TrailBy                                      0
ReEntryType                                  NONE
ReEnteriesCount                                 0
OnEntry_OpenTradeOn                            NO
OnEntry_SqOffTradeOff                          NO
OnEntry_SqOffAllLegs                           NO
OnEntry_OpenTradeDelay                          0
OnEntry_SqOffDelay                              0
OnExit_OpenTradeOn                             NO
OnExit_SqOffTradeOff                           NO
OnExit_SqOffAllLegs                            NO
OnExit_OpenAllLegs                             NO
OnExit_OpenTradeDelay                           0
OnExit_SqOffDelay                               0
OpenHedge                                      NO
HedgeStrikeMethod                            NONE
HedgeStrikeValue                                0
HedgeStrikePremiumCondition                  NONE
Name: 1, dtype: object
_parse_leg_row Warning: Unknown Instrument (option type): OPTIONS. Skipping leg.
Row: StrategyName                   ATM_ALL_VARIATIONS
LegID                                           1
Instrument                                OPTIONS
Transaction                                  SELL
StrikeMethod                                  ATM
StrikeValue                                     0
Expiry                                         CW
Lots                                            1
IsIdle                                         NO
W&Type                                       NONE
W&TValue                                        0
TrailW&T                                        0
SLType                                       NONE
SLValue                                         0
TGTType                                      NONE
TGTValue                                        0
TrailSLType                                  NONE
SL_TrailAt                                      0
SL_TrailBy                                      0
ReEntryType                                  NONE
ReEnteriesCount                                 0
OnEntry_OpenTradeOn                            NO
OnEntry_SqOffTradeOff                          NO
OnEntry_SqOffAllLegs                           NO
OnEntry_OpenTradeDelay                          0
OnEntry_SqOffDelay                              0
OnExit_OpenTradeOn                             NO
OnExit_SqOffTradeOff                           NO
OnExit_SqOffAllLegs                            NO
OnExit_OpenAllLegs                             NO
OnExit_OpenTradeDelay                           0
OnExit_SqOffDelay                               0
OpenHedge                                      NO
HedgeStrikeMethod                            NONE
HedgeStrikeValue                                0
HedgeStrikePremiumCondition                  NONE
Name: 0, dtype: object
_parse_leg_row Warning: Unknown Instrument (option type): OPTIONS. Skipping leg.
Row: StrategyName                   ATM_ALL_VARIATIONS
LegID                                           2
Instrument                                OPTIONS
Transaction                                  SELL
StrikeMethod                                  ATM
StrikeValue                                     2
Expiry                                         CW
Lots                                            1
IsIdle                                         NO
W&Type                                       NONE
W&TValue                                        0
TrailW&T                                        0
SLType                                       NONE
SLValue                                         0
TGTType                                      NONE
TGTValue                                        0
TrailSLType                                  NONE
SL_TrailAt                                      0
SL_TrailBy                                      0
ReEntryType                                  NONE
ReEnteriesCount                                 0
OnEntry_OpenTradeOn                            NO
OnEntry_SqOffTradeOff                          NO
OnEntry_SqOffAllLegs                           NO
OnEntry_OpenTradeDelay                          0
OnEntry_SqOffDelay                              0
OnExit_OpenTradeOn                             NO
OnExit_SqOffTradeOff                           NO
OnExit_SqOffAllLegs                            NO
OnExit_OpenAllLegs                             NO
OnExit_OpenTradeDelay                           0
OnExit_SqOffDelay                               0
OpenHedge                                      NO
HedgeStrikeMethod                            NONE
HedgeStrikeValue                                0
HedgeStrikePremiumCondition                  NONE
Name: 1, dtype: object
_parse_leg_row Warning: Unknown Instrument (option type): OPTIONS. Skipping leg.
Row: StrategyName                   ATM_ALL_VARIATIONS
LegID                                           3
Instrument                                OPTIONS
Transaction                                  SELL
StrikeMethod                                  ATM
StrikeValue                                    -2
Expiry                                         CW
Lots                                            1
IsIdle                                         NO
W&Type                                       NONE
W&TValue                                        0
TrailW&T                                        0
SLType                                       NONE
SLValue                                         0
TGTType                                      NONE
TGTValue                                        0
TrailSLType                                  NONE
SL_TrailAt                                      0
SL_TrailBy                                      0
ReEntryType                                  NONE
ReEnteriesCount                                 0
OnEntry_OpenTradeOn                            NO
OnEntry_SqOffTradeOff                          NO
OnEntry_SqOffAllLegs                           NO
OnEntry_OpenTradeDelay                          0
OnEntry_SqOffDelay                              0
OnExit_OpenTradeOn                             NO
OnExit_SqOffTradeOff                           NO
OnExit_SqOffAllLegs                            NO
OnExit_OpenAllLegs                             NO
OnExit_OpenTradeDelay                           0
OnExit_SqOffDelay                               0
OpenHedge                                      NO
HedgeStrikeMethod                            NONE
HedgeStrikeValue                                0
HedgeStrikePremiumCondition                  NONE
Name: 2, dtype: object
_parse_leg_row Warning: Unknown Instrument (option type): OPTIONS. Skipping leg.
Row: StrategyName                   ATM_ALL_VARIATIONS
LegID                                           4
Instrument                                OPTIONS
Transaction                                   BUY
StrikeMethod                                  ATM
StrikeValue                                     5
Expiry                                         CW
Lots                                            1
IsIdle                                         NO
W&Type                                       NONE
W&TValue                                        0
TrailW&T                                        0
SLType                                       NONE
SLValue                                         0
TGTType                                      NONE
TGTValue                                        0
TrailSLType                                  NONE
SL_TrailAt                                      0
SL_TrailBy                                      0
ReEntryType                                  NONE
ReEnteriesCount                                 0
OnEntry_OpenTradeOn                            NO
OnEntry_SqOffTradeOff                          NO
OnEntry_SqOffAllLegs                           NO
OnEntry_OpenTradeDelay                          0
OnEntry_SqOffDelay                              0
OnExit_OpenTradeOn                             NO
OnExit_SqOffTradeOff                           NO
OnExit_SqOffAllLegs                            NO
OnExit_OpenAllLegs                             NO
OnExit_OpenTradeDelay                           0
OnExit_SqOffDelay                               0
OpenHedge                                      NO
HedgeStrikeMethod                            NONE
HedgeStrikeValue                                0
HedgeStrikePremiumCondition                  NONE
Name: 3, dtype: object
_parse_leg_row Warning: Unknown Instrument (option type): OPTIONS. Skipping leg.
Row: StrategyName                   ITM_OTM_METHODS
LegID                                        1
Instrument                             OPTIONS
Transaction                               SELL
StrikeMethod                              ITM1
StrikeValue                                  0
Expiry                                      CW
Lots                                         2
IsIdle                                      NO
W&Type                                    NONE
W&TValue                                     0
TrailW&T                                     0
SLType                                    NONE
SLValue                                      0
TGTType                                   NONE
TGTValue                                     0
TrailSLType                               NONE
SL_TrailAt                                   0
SL_TrailBy                                   0
ReEntryType                               NONE
ReEnteriesCount                              0
OnEntry_OpenTradeOn                         NO
OnEntry_SqOffTradeOff                       NO
OnEntry_SqOffAllLegs                        NO
OnEntry_OpenTradeDelay                       0
OnEntry_SqOffDelay                           0
OnExit_OpenTradeOn                          NO
OnExit_SqOffTradeOff                        NO
OnExit_SqOffAllLegs                         NO
OnExit_OpenAllLegs                          NO
OnExit_OpenTradeDelay                        0
OnExit_SqOffDelay                            0
OpenHedge                                   NO
HedgeStrikeMethod                         NONE
HedgeStrikeValue                             0
HedgeStrikePremiumCondition               NONE
Name: 0, dtype: object
_parse_leg_row Warning: Unknown Instrument (option type): OPTIONS. Skipping leg.
Row: StrategyName                   ITM_OTM_METHODS
LegID                                        2
Instrument                             OPTIONS
Transaction                               SELL
StrikeMethod                              OTM1
StrikeValue                                  0
Expiry                                      CW
Lots                                         2
IsIdle                                      NO
W&Type                                    NONE
W&TValue                                     0
TrailW&T                                     0
SLType                                    NONE
SLValue                                      0
TGTType                                   NONE
TGTValue                                     0
TrailSLType                               NONE
SL_TrailAt                                   0
SL_TrailBy                                   0
ReEntryType                               NONE
ReEnteriesCount                              0
OnEntry_OpenTradeOn                         NO
OnEntry_SqOffTradeOff                       NO
OnEntry_SqOffAllLegs                        NO
OnEntry_OpenTradeDelay                       0
OnEntry_SqOffDelay                           0
OnExit_OpenTradeOn                          NO
OnExit_SqOffTradeOff                        NO
OnExit_SqOffAllLegs                         NO
OnExit_OpenAllLegs                          NO
OnExit_OpenTradeDelay                        0
OnExit_SqOffDelay                            0
OpenHedge                                   NO
HedgeStrikeMethod                         NONE
HedgeStrikeValue                             0
HedgeStrikePremiumCondition               NONE
Name: 1, dtype: object
_parse_leg_row Warning: Unknown Instrument (option type): OPTIONS. Skipping leg.
Row: StrategyName                   ITM_OTM_METHODS
LegID                                        3
Instrument                             OPTIONS
Transaction                                BUY
StrikeMethod                              ITM3
StrikeValue                                  0
Expiry                                      CW
Lots                                         2
IsIdle                                      NO
W&Type                                    NONE
W&TValue                                     0
TrailW&T                                     0
SLType                                    NONE
SLValue                                      0
TGTType                                   NONE
TGTValue                                     0
TrailSLType                               NONE
SL_TrailAt                                   0
SL_TrailBy                                   0
ReEntryType                               NONE
ReEnteriesCount                              0
OnEntry_OpenTradeOn                         NO
OnEntry_SqOffTradeOff                       NO
OnEntry_SqOffAllLegs                        NO
OnEntry_OpenTradeDelay                       0
OnEntry_SqOffDelay                           0
OnExit_OpenTradeOn                          NO
OnExit_SqOffTradeOff                        NO
OnExit_SqOffAllLegs                         NO
OnExit_OpenAllLegs                          NO
OnExit_OpenTradeDelay                        0
OnExit_SqOffDelay                            0
OpenHedge                                   NO
HedgeStrikeMethod                         NONE
HedgeStrikeValue                             0
HedgeStrikePremiumCondition               NONE
Name: 2, dtype: object
_parse_leg_row Warning: Unknown Instrument (option type): OPTIONS. Skipping leg.
Row: StrategyName                   ITM_OTM_METHODS
LegID                                        4
Instrument                             OPTIONS
Transaction                                BUY
StrikeMethod                              OTM3
StrikeValue                                  0
Expiry                                      CW
Lots                                         2
IsIdle                                      NO
W&Type                                    NONE
W&TValue                                     0
TrailW&T                                     0
SLType                                    NONE
SLValue                                      0
TGTType                                   NONE
TGTValue                                     0
TrailSLType                               NONE
SL_TrailAt                                   0
SL_TrailBy                                   0
ReEntryType                               NONE
ReEnteriesCount                              0
OnEntry_OpenTradeOn                         NO
OnEntry_SqOffTradeOff                       NO
OnEntry_SqOffAllLegs                        NO
OnEntry_OpenTradeDelay                       0
OnEntry_SqOffDelay                           0
OnExit_OpenTradeOn                          NO
OnExit_SqOffTradeOff                        NO
OnExit_SqOffAllLegs                         NO
OnExit_OpenAllLegs                          NO
OnExit_OpenTradeDelay                        0
OnExit_SqOffDelay                            0
OpenHedge                                   NO
HedgeStrikeMethod                         NONE
HedgeStrikeValue                             0
HedgeStrikePremiumCondition               NONE
Name: 3, dtype: object
2025-05-30 14:39:01,603 - __main__ - INFO - Processing portfolio 'COMPREHENSIVE_TEST' sequentially.
2025-05-30 14:39:01,604 - __main__ - INFO - Processing batch: COMPREHENSIVE_TEST_fulldaterange (2024-04-01 to 2024-04-30) for original portfolio 'COMPREHENSIVE_TEST'
[DEBUG] run_full_backtest bt_params keys: ['portfolio_model', 'portfolio_name', 'start_date', 'end_date']
2025-05-30 14:39:01,604 - backtester_stable.BTRUN.core.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'COMPREHENSIVE_TEST'
2025-05-30 14:39:01,826 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 14:39:01,875 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.048s, returned 563 rows. Query: SELECT SUBSTRING('SELECT DISTINCT trade_date FROM nifty_option_chain', 1, 200) ...
2025-05-30 14:39:01,916 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - Cached 563 distinct trade dates
2025-05-30 14:39:01,917 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DTE filter found in strategy LEG_REENTRY_ALL_TYPES: 0
2025-05-30 14:39:01,958 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.041s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-01'' 
                    AND expiry_date = ''2024-04-01''
                    LIMIT 1', 1, 200) ...
2025-05-30 14:39:01,958 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-01 - Not an expiry day (DTE=0 filter active)
2025-05-30 14:39:01,997 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.039s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-02'' 
                    AND expiry_date = ''2024-04-02''
                    LIMIT 1', 1, 200) ...
2025-05-30 14:39:01,998 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-02 - Not an expiry day (DTE=0 filter active)
2025-05-30 14:39:02,038 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.040s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-03'' 
                    AND expiry_date = ''2024-04-03''
                    LIMIT 1', 1, 200) ...
2025-05-30 14:39:02,038 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-03 - Not an expiry day (DTE=0 filter active)
2025-05-30 14:39:02,077 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.039s, returned 1 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-04'' 
                    AND expiry_date = ''2024-04-04''
                    LIMIT 1', 1, 200) ...
2025-05-30 14:39:02,077 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Processing date 2024-04-04 - Is an expiry day (DTE=0 filter active)
2025-05-30 14:39:02,115 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.038s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-05'' 
                    AND expiry_date = ''2024-04-05''
                    LIMIT 1', 1, 200) ...
2025-05-30 14:39:02,115 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-05 - Not an expiry day (DTE=0 filter active)
2025-05-30 14:39:02,156 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.040s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-08'' 
                    AND expiry_date = ''2024-04-08''
                    LIMIT 1', 1, 200) ...
2025-05-30 14:39:02,156 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-08 - Not an expiry day (DTE=0 filter active)
2025-05-30 14:39:02,194 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.038s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-09'' 
                    AND expiry_date = ''2024-04-09''
                    LIMIT 1', 1, 200) ...
2025-05-30 14:39:02,194 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-09 - Not an expiry day (DTE=0 filter active)
2025-05-30 14:39:02,234 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.039s, returned 1 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-10'' 
                    AND expiry_date = ''2024-04-10''
                    LIMIT 1', 1, 200) ...
2025-05-30 14:39:02,234 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Processing date 2024-04-10 - Is an expiry day (DTE=0 filter active)
2025-05-30 14:39:02,272 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.038s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-12'' 
                    AND expiry_date = ''2024-04-12''
                    LIMIT 1', 1, 200) ...
2025-05-30 14:39:02,272 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-12 - Not an expiry day (DTE=0 filter active)
2025-05-30 14:39:02,317 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.045s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-15'' 
                    AND expiry_date = ''2024-04-15''
                    LIMIT 1', 1, 200) ...
2025-05-30 14:39:02,317 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-15 - Not an expiry day (DTE=0 filter active)
2025-05-30 14:39:02,356 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.038s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-16'' 
                    AND expiry_date = ''2024-04-16''
                    LIMIT 1', 1, 200) ...
2025-05-30 14:39:02,356 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-16 - Not an expiry day (DTE=0 filter active)
2025-05-30 14:39:02,388 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.031s, returned 1 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-18'' 
                    AND expiry_date = ''2024-04-18''
                    LIMIT 1', 1, 200) ...
2025-05-30 14:39:02,388 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Processing date 2024-04-18 - Is an expiry day (DTE=0 filter active)
2025-05-30 14:39:02,429 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.041s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-19'' 
                    AND expiry_date = ''2024-04-19''
                    LIMIT 1', 1, 200) ...
2025-05-30 14:39:02,429 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-19 - Not an expiry day (DTE=0 filter active)
2025-05-30 14:39:02,468 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.038s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-22'' 
                    AND expiry_date = ''2024-04-22''
                    LIMIT 1', 1, 200) ...
2025-05-30 14:39:02,468 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-22 - Not an expiry day (DTE=0 filter active)
2025-05-30 14:39:02,508 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.040s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-23'' 
                    AND expiry_date = ''2024-04-23''
                    LIMIT 1', 1, 200) ...
2025-05-30 14:39:02,508 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-23 - Not an expiry day (DTE=0 filter active)
2025-05-30 14:39:02,550 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.041s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-24'' 
                    AND expiry_date = ''2024-04-24''
                    LIMIT 1', 1, 200) ...
2025-05-30 14:39:02,550 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-24 - Not an expiry day (DTE=0 filter active)
2025-05-30 14:39:02,587 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.037s, returned 1 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-25'' 
                    AND expiry_date = ''2024-04-25''
                    LIMIT 1', 1, 200) ...
2025-05-30 14:39:02,587 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Processing date 2024-04-25 - Is an expiry day (DTE=0 filter active)
2025-05-30 14:39:02,627 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.040s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-26'' 
                    AND expiry_date = ''2024-04-26''
                    LIMIT 1', 1, 200) ...
2025-05-30 14:39:02,628 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-26 - Not an expiry day (DTE=0 filter active)
2025-05-30 14:39:02,668 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.040s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-29'' 
                    AND expiry_date = ''2024-04-29''
                    LIMIT 1', 1, 200) ...
2025-05-30 14:39:02,669 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-29 - Not an expiry day (DTE=0 filter active)
2025-05-30 14:39:02,702 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.034s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-30'' 
                    AND expiry_date = ''2024-04-30''
                    LIMIT 1', 1, 200) ...
2025-05-30 14:39:02,703 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-30 - Not an expiry day (DTE=0 filter active)
2025-05-30 14:39:02,703 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Generated 0 trade records via model-driven path
2025-05-30 14:39:02,703 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - WARNING - No tick P&L data calculated, Max Profit/Loss sheet will have no data
2025-05-30 14:39:02,704 - debug_detail - WARNING - BUILDERS: No 'trades' data in bt_response['data'] or invalid format.
2025-05-30 14:39:02,704 - backtester_stable.BTRUN.core.runtime - WARNING - No trades found in backtest response after parsing in runtime.
2025-05-30 14:39:02,707 - __main__ - INFO - Worker completed batch: COMPREHENSIVE_TEST_fulldaterange for portfolio 'COMPREHENSIVE_TEST' in 1.10s
2025-05-30 14:39:02,707 - __main__ - INFO - Combining 1 batch(es) for portfolio: COMPREHENSIVE_TEST
2025-05-30 14:39:02,710 - __main__ - INFO - Attempting to save combined backtest results for 1 portfolio(s) to /srv/samba/shared/simple_test_results_20250530_090858.xlsx
2025-05-30 14:39:02,710 - backtester_stable.BTRUN.core.io - INFO - Writing results to /srv/samba/shared/simple_test_results_20250530_090858.xlsx
2025-05-30 14:39:02,711 - backtester_stable.BTRUN.core.io - INFO - 2025-05-30 09:09:02.711793, Started writing stats to excel file: /srv/samba/shared/simple_test_results_20250530_090858.xlsx
2025-05-30 14:39:02,748 - backtester_stable.BTRUN.core.io - INFO - Generated PortfolioParameter sheet from PortfolioSetting
2025-05-30 14:39:02,749 - backtester_stable.BTRUN.core.io - INFO - Added empty PORTFOLIO Trans sheet
2025-05-30 14:39:02,750 - backtester_stable.BTRUN.core.io - INFO - Added PORTFOLIO Results sheet
2025-05-30 14:39:02,750 - backtester_stable.BTRUN.core.io - INFO - 2025-05-30 09:09:02.750940, Excel file prepared successfully, Time taken: 0.04s
2025-05-30 14:39:02,764 - backtester_stable.BTRUN.core.io - INFO - Successfully wrote results to /srv/samba/shared/simple_test_results_20250530_090858.xlsx
2025-05-30 14:39:02,764 - __main__ - INFO - Successfully saved backtest results to /srv/samba/shared/simple_test_results_20250530_090858.xlsx
2025-05-30 14:39:02,764 - __main__ - INFO - Backtest results saved to /srv/samba/shared/simple_test_results_20250530_090858.xlsx
================================================================================
SIMPLE TEST - 5 STRATEGIES
================================================================================
Command:
python3 /srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py --portfolio-excel /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/comprehensive_tests/SIMPLE_TEST_PORTFOLIO.xlsx --output-path /srv/samba/shared/simple_test_results_20250530_090858.xlsx --workers auto --batch-days 7 --merge-output --use-gpu-optimization --gpu-workers 4

Running simple test...
--------------------------------------------------------------------------------

✅ Test completed successfully!
Output: /srv/samba/shared/simple_test_results_20250530_090858.xlsx

Sheets in output: ['PortfolioParameter', 'PORTFOLIO Trans', 'PORTFOLIO Results']

Total trades across all strategies: 0
