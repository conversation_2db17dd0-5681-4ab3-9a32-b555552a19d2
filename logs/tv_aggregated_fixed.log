CONFIG.PY: Attempting to load config.py...
2025-05-27 09:32:27,311 - core.config - WARNING - Partially restored config.py is being imported - Phase 10.
2025-05-27 09:32:27,311 - core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - get_table_name active!
2025-05-27 09:32:27,311 - core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
2025-05-27 09:32:27,311 - core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
CONFIG.PY: Finished loading config.py.
2025-05-27 09:32:28,612 - numba.cuda.cudadrv.driver - INFO - init
2025-05-27 09:32:28,916 - core.gpu_helpers - INFO - cuDF is available - GPU acceleration enabled
Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/BT_TV_GPU_aggregated.py", line 33, in <module>
    from bt.backtester_stable.BTRUN.core import config, gpu_helpers, runtime, io
ModuleNotFoundError: No module named 'bt.backtester_stable.BTRUN.core'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/BT_TV_GPU_aggregated.py", line 41, in <module>
    from core import config
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/core/__init__.py", line 3, in <module>
    from . import runtime
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/core/runtime.py", line 21, in <module>
    from .. import builders
ImportError: attempted relative import beyond top-level package
