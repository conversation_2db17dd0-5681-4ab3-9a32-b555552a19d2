2025-05-21 14:59:50,997 - __main__ - INFO - === Starting Detailed Test ===
2025-05-21 14:59:50,997 - __main__ - INFO - Ensuring risk.py has proper fixes: /srv/samba/shared/bt/backtester_stable/BTRUN/models/risk.py
2025-05-21 14:59:50,997 - __main__ - INFO - Created backup of risk.py at /srv/samba/shared/bt/backtester_stable/BTRUN/models/risk.py.bak
2025-05-21 14:59:50,998 - __main__ - INFO - Applied enhanced fixes to risk.py
2025-05-21 14:59:50,998 - __main__ - INFO - Ensuring trade_builder.py has proper fixes: /srv/samba/shared/bt/backtester_stable/BTRUN/trade_builder.py
2025-05-21 14:59:50,998 - __main__ - INFO - Created backup of trade_builder.py at /srv/samba/shared/bt/backtester_stable/BTRUN/trade_builder.py.bak
2025-05-21 14:59:50,998 - __main__ - INFO - Applied enhanced fixes to trade_builder.py
2025-05-21 14:59:50,998 - __main__ - INFO - Running detailed test with command: python3 /srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py --portfolio-excel /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_fixed.xlsx --output-path /srv/samba/shared/Trades/detailed_test_20250521_145950.xlsx --debug
2025-05-21 14:59:51,420 - __main__ - INFO - BTRUN: CONFIG.PY: Attempting to load config.py...
2025-05-21 14:59:51,420 - __main__ - INFO - BTRUN: CONFIG.PY: Finished loading config.py.
2025-05-21 14:59:53,523 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,523 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-21 14:59:53,525 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,523 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-21 14:59:53,525 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,524 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages']
2025-05-21 14:59:53,525 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,524 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-21 14:59:53,525 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,524 - __main__ - DEBUG - GPU enabled: False
2025-05-21 14:59:53,525 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,525 - __main__ - INFO - No config provided – defaulting to Excel input format
2025-05-21 14:59:53,526 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,525 - __main__ - INFO - GPU acceleration enabled: False
2025-05-21 14:59:53,526 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,526 - __main__ - INFO - HeavyDB integration available (Host: 127.0.0.1)
2025-05-21 14:59:53,745 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,745 - backtester_stable.BTRUN.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-21 14:59:53,745 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,745 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-21 14:59:53,745 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,745 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-21 14:59:53,745 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,745 - backtester_stable.BTRUN.utils - INFO - Running necessary functions before starting BT...
2025-05-21 14:59:53,745 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,745 - backtester_stable.BTRUN.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/bt/backtester_stable/BTRUN/input/fixtures/LOTSIZE.csv
2025-05-21 14:59:53,745 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,745 - backtester_stable.BTRUN.utils - INFO - Loading lot sizes from: /srv/samba/shared/bt/backtester_stable/BTRUN/input/fixtures/LOTSIZE.csv
2025-05-21 14:59:53,751 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,751 - backtester_stable.BTRUN.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-21 14:59:53,751 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,751 - backtester_stable.BTRUN.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-21 14:59:53,752 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,751 - backtester_stable.BTRUN.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-21 14:59:53,752 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,752 - backtester_stable.BTRUN.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-21 14:59:53,752 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,752 - backtester_stable.BTRUN.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-21 14:59:53,752 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,752 - backtester_stable.BTRUN.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-21 14:59:53,752 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,752 - backtester_stable.BTRUN.utils - INFO - Pre-BT functions complete.
2025-05-21 14:59:53,752 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,752 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-21 14:59:53,752 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,752 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-21 14:59:53,752 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,752 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_fixed.xlsx
2025-05-21 14:59:53,752 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,752 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-21 14:59:53,753 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,752 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_fixed.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-21 14:59:53,877 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,877 - __main__ - INFO - Successfully parsed 1 portfolio(s) using modern parser.
2025-05-21 14:59:53,877 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,877 - __main__ - INFO - Queueing portfolio: NIF0DTE (from modern_portfolio_parser)
2025-05-21 14:59:53,877 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,877 - __main__ - INFO - Executing 1 portfolios sequentially (multiprocessing temporarily disabled for debugging)
2025-05-21 14:59:53,877 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,877 - __main__ - INFO - Processing payload 1/1: NIF0DTE
2025-05-21 14:59:53,877 - __main__ - INFO - BTRUN: [DEBUG] run_full_backtest bt_params keys: ['portfolio_model', 'portfolio_name', 'start_date', 'end_date']
2025-05-21 14:59:53,878 - __main__ - INFO - BTRUN: 2025-05-21 14:59:53,877 - backtester_stable.BTRUN.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'NIF0DTE'
2025-05-21 14:59:54,094 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,094 - backtester_stable.BTRUN.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-21 14:59:54,130 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,130 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.035s, returned 563 rows. Query: SELECT SUBSTRING('SELECT DISTINCT trade_date FROM nifty_option_chain', 1, 200) ...
2025-05-21 14:59:54,173 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,173 - backtester_stable.BTRUN.heavydb_data_access - INFO - Cached 563 distinct trade dates
2025-05-21 14:59:54,174 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,173 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] portfolio_model_dict keys: ['portfolio_name', 'start_date', 'end_date', 'slippage_percent', 'margin_multiplier', 'is_tick_bt', 'strategies', 'extra_params']
2025-05-21 14:59:54,174 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,174 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
2025-05-21 14:59:54,175 - __main__ - INFO - BTRUN: SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:54,175 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
2025-05-21 14:59:54,175 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time ASC
2025-05-21 14:59:54,175 - __main__ - INFO - BTRUN: LIMIT 1
2025-05-21 14:59:54,175 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,174 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
2025-05-21 14:59:54,175 - __main__ - INFO - BTRUN: SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:54,175 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '12:00:00'
2025-05-21 14:59:54,175 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time DESC
2025-05-21 14:59:54,175 - __main__ - INFO - BTRUN: LIMIT 1
2025-05-21 14:59:54,200 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,200 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.025s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:54,200 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
2025-05-21 14:59:54,200 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 14:59:54,223 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,223 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.023s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:54,223 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''12:00:00''
2025-05-21 14:59:54,223 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 14:59:54,224 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,224 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
2025-05-21 14:59:54,224 - __main__ - INFO - BTRUN: trade_date                   2025-04-01
2025-05-21 14:59:54,225 - __main__ - INFO - BTRUN: trade_time                     09:16:00
2025-05-21 14:59:54,225 - __main__ - INFO - BTRUN: expiry_date                  2025-04-03
2025-05-21 14:59:54,225 - __main__ - INFO - BTRUN: index_name                        NIFTY
2025-05-21 14:59:54,225 - __main__ - INFO - BTRUN: spot                           23420.45
2025-05-21 14:59:54,225 - __main__ - INFO - BTRUN: atm_strike                      23450.0
2025-05-21 14:59:54,225 - __main__ - INFO - BTRUN: strike                          23450.0
2025-05-21 14:59:54,225 - __main__ - INFO - BTRUN: dte                                   2
2025-05-21 14:59:54,225 - __main__ - INFO - BTRUN: expiry_bucket                        CW
2025-05-21 14:59:54,225 - __main__ - INFO - BTRUN: zone_id                               1
2025-05-21 14:59:54,225 - __main__ - INFO - BTRUN: zone_name                          OPEN
2025-05-21 14:59:54,226 - __main__ - INFO - BTRUN: call_strike_type                    ATM
2025-05-21 14:59:54,226 - __main__ - INFO - BTRUN: put_strike_type                     ATM
2025-05-21 14:59:54,226 - __main__ - INFO - BTRUN: ce_symbol           NIFTY03APR2523450CE
2025-05-21 14:59:54,226 - __main__ - INFO - BTRUN: ce_open                          102.85
2025-05-21 14:59:54,226 - __main__ - INFO - BTRUN: ce_high                          106.65
2025-05-21 14:59:54,226 - __main__ - INFO - BTRUN: ce_low                            97.85
2025-05-21 14:59:54,226 - __main__ - INFO - BTRUN: ce_close                         100.75
2025-05-21 14:59:54,226 - __main__ - INFO - BTRUN: ce_volume                       1015050
2025-05-21 14:59:54,226 - __main__ - INFO - BTRUN: ce_oi                            978075
2025-05-21 14:59:54,227 - __main__ - INFO - BTRUN: ce_coi                                0
2025-05-21 14:59:54,227 - __main__ - INFO - BTRUN: ce_iv                             13.44
2025-05-21 14:59:54,227 - __main__ - INFO - BTRUN: ce_delta                           0.48
2025-05-21 14:59:54,227 - __main__ - INFO - BTRUN: ce_gamma                         0.0015
2025-05-21 14:59:54,227 - __main__ - INFO - BTRUN: ce_theta                         -23.27
2025-05-21 14:59:54,227 - __main__ - INFO - BTRUN: ce_vega                            7.95
2025-05-21 14:59:54,227 - __main__ - INFO - BTRUN: ce_rho                          81.3115
2025-05-21 14:59:54,227 - __main__ - INFO - BTRUN: pe_symbol           NIFTY03APR2523450PE
2025-05-21 14:59:54,227 - __main__ - INFO - BTRUN: pe_open                           118.2
2025-05-21 14:59:54,227 - __main__ - INFO - BTRUN: pe_high                           122.3
2025-05-21 14:59:54,228 - __main__ - INFO - BTRUN: pe_low                            112.2
2025-05-21 14:59:54,228 - __main__ - INFO - BTRUN: pe_close                          115.7
2025-05-21 14:59:54,228 - __main__ - INFO - BTRUN: pe_volume                        877125
2025-05-21 14:59:54,228 - __main__ - INFO - BTRUN: pe_oi                           1618950
2025-05-21 14:59:54,228 - __main__ - INFO - BTRUN: pe_coi                                0
2025-05-21 14:59:54,228 - __main__ - INFO - BTRUN: pe_iv                             13.74
2025-05-21 14:59:54,228 - __main__ - INFO - BTRUN: pe_delta                          -0.52
2025-05-21 14:59:54,228 - __main__ - INFO - BTRUN: pe_gamma                         0.0015
2025-05-21 14:59:54,228 - __main__ - INFO - BTRUN: pe_theta                          -17.3
2025-05-21 14:59:54,228 - __main__ - INFO - BTRUN: pe_vega                            7.95
2025-05-21 14:59:54,229 - __main__ - INFO - BTRUN: pe_rho                         -88.3744
2025-05-21 14:59:54,229 - __main__ - INFO - BTRUN: future_open                     23534.5
2025-05-21 14:59:54,229 - __main__ - INFO - BTRUN: future_high                    23543.95
2025-05-21 14:59:54,229 - __main__ - INFO - BTRUN: future_low                      23527.0
2025-05-21 14:59:54,229 - __main__ - INFO - BTRUN: future_close                   23536.75
2025-05-21 14:59:54,230 - __main__ - INFO - BTRUN: future_volume                     86475
2025-05-21 14:59:54,230 - __main__ - INFO - BTRUN: future_oi                      12605100
2025-05-21 14:59:54,230 - __main__ - INFO - BTRUN: future_coi                            0
2025-05-21 14:59:54,230 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,225 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
2025-05-21 14:59:54,230 - __main__ - INFO - BTRUN: trade_date                   2025-04-01
2025-05-21 14:59:54,230 - __main__ - INFO - BTRUN: trade_time                     12:00:00
2025-05-21 14:59:54,231 - __main__ - INFO - BTRUN: expiry_date                  2025-04-03
2025-05-21 14:59:54,231 - __main__ - INFO - BTRUN: index_name                        NIFTY
2025-05-21 14:59:54,231 - __main__ - INFO - BTRUN: spot                            23195.3
2025-05-21 14:59:54,231 - __main__ - INFO - BTRUN: atm_strike                      23200.0
2025-05-21 14:59:54,231 - __main__ - INFO - BTRUN: strike                          23200.0
2025-05-21 14:59:54,232 - __main__ - INFO - BTRUN: dte                                   2
2025-05-21 14:59:54,232 - __main__ - INFO - BTRUN: expiry_bucket                        CW
2025-05-21 14:59:54,232 - __main__ - INFO - BTRUN: zone_id                               2
2025-05-21 14:59:54,232 - __main__ - INFO - BTRUN: zone_name                      MID_MORN
2025-05-21 14:59:54,232 - __main__ - INFO - BTRUN: call_strike_type                    ATM
2025-05-21 14:59:54,232 - __main__ - INFO - BTRUN: put_strike_type                     ATM
2025-05-21 14:59:54,233 - __main__ - INFO - BTRUN: ce_symbol           NIFTY03APR2523200CE
2025-05-21 14:59:54,233 - __main__ - INFO - BTRUN: ce_open                           123.6
2025-05-21 14:59:54,233 - __main__ - INFO - BTRUN: ce_high                           124.3
2025-05-21 14:59:54,233 - __main__ - INFO - BTRUN: ce_low                           119.25
2025-05-21 14:59:54,233 - __main__ - INFO - BTRUN: ce_close                          124.2
2025-05-21 14:59:54,234 - __main__ - INFO - BTRUN: ce_volume                        326775
2025-05-21 14:59:54,234 - __main__ - INFO - BTRUN: ce_oi                           4374450
2025-05-21 14:59:54,234 - __main__ - INFO - BTRUN: ce_coi                           193725
2025-05-21 14:59:54,234 - __main__ - INFO - BTRUN: ce_iv                             14.99
2025-05-21 14:59:54,234 - __main__ - INFO - BTRUN: ce_delta                           0.52
2025-05-21 14:59:54,234 - __main__ - INFO - BTRUN: ce_gamma                         0.0013
2025-05-21 14:59:54,235 - __main__ - INFO - BTRUN: ce_theta                         -25.57
2025-05-21 14:59:54,235 - __main__ - INFO - BTRUN: ce_vega                            7.87
2025-05-21 14:59:54,235 - __main__ - INFO - BTRUN: ce_rho                          86.2799
2025-05-21 14:59:54,235 - __main__ - INFO - BTRUN: pe_symbol           NIFTY03APR2523200PE
2025-05-21 14:59:54,235 - __main__ - INFO - BTRUN: pe_open                           100.2
2025-05-21 14:59:54,236 - __main__ - INFO - BTRUN: pe_high                           105.2
2025-05-21 14:59:54,236 - __main__ - INFO - BTRUN: pe_low                            99.55
2025-05-21 14:59:54,236 - __main__ - INFO - BTRUN: pe_close                         100.75
2025-05-21 14:59:54,236 - __main__ - INFO - BTRUN: pe_volume                        526875
2025-05-21 14:59:54,236 - __main__ - INFO - BTRUN: pe_oi                           5030025
2025-05-21 14:59:54,236 - __main__ - INFO - BTRUN: pe_coi                           101175
2025-05-21 14:59:54,237 - __main__ - INFO - BTRUN: pe_iv                             13.55
2025-05-21 14:59:54,237 - __main__ - INFO - BTRUN: pe_delta                          -0.48
2025-05-21 14:59:54,237 - __main__ - INFO - BTRUN: pe_gamma                         0.0015
2025-05-21 14:59:54,237 - __main__ - INFO - BTRUN: pe_theta                         -17.08
2025-05-21 14:59:54,237 - __main__ - INFO - BTRUN: pe_vega                            7.87
2025-05-21 14:59:54,237 - __main__ - INFO - BTRUN: pe_rho                         -81.3242
2025-05-21 14:59:54,238 - __main__ - INFO - BTRUN: future_open                     23347.0
2025-05-21 14:59:54,238 - __main__ - INFO - BTRUN: future_high                    23348.85
2025-05-21 14:59:54,238 - __main__ - INFO - BTRUN: future_low                      23339.0
2025-05-21 14:59:54,238 - __main__ - INFO - BTRUN: future_close                    23348.1
2025-05-21 14:59:54,238 - __main__ - INFO - BTRUN: future_volume                     19050
2025-05-21 14:59:54,238 - __main__ - INFO - BTRUN: future_oi                      13928550
2025-05-21 14:59:54,238 - __main__ - INFO - BTRUN: future_coi                       -28275
2025-05-21 14:59:54,239 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,225 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 14:59:54,239 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,225 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-21 14:59:54,239 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,225 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 14:59:54,239 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,225 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140374028661168
2025-05-21 14:59:54,239 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,225 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140374028661168, OptionType.PUT ID: 140374028661280
2025-05-21 14:59:54,239 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,226 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 14:59:54,239 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,226 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-21 14:59:54,239 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,226 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-21 14:59:54,239 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,227 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
2025-05-21 14:59:54,240 - __main__ - INFO - BTRUN: WHERE index_name = 'NIFTY'
2025-05-21 14:59:54,240 - __main__ - INFO - BTRUN: AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
2025-05-21 14:59:54,240 - __main__ - INFO - BTRUN: ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 14:59:54,495 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,495 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.268s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 14:59:54,497 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,497 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-21 14:59:54,498 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,498 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-21 14:59:54,556 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,555 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 14:59:54,564 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,564 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
2025-05-21 14:59:54,564 - __main__ - INFO - BTRUN: SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:54,564 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
2025-05-21 14:59:54,564 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time ASC
2025-05-21 14:59:54,564 - __main__ - INFO - BTRUN: LIMIT 1
2025-05-21 14:59:54,565 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,564 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
2025-05-21 14:59:54,565 - __main__ - INFO - BTRUN: SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:54,565 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '12:00:00'
2025-05-21 14:59:54,565 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time DESC
2025-05-21 14:59:54,565 - __main__ - INFO - BTRUN: LIMIT 1
2025-05-21 14:59:54,604 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,604 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.040s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:54,605 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
2025-05-21 14:59:54,605 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 14:59:54,645 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,645 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.041s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:54,645 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''12:00:00''
2025-05-21 14:59:54,646 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 14:59:54,647 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,646 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
2025-05-21 14:59:54,647 - __main__ - INFO - BTRUN: trade_date                   2025-04-01
2025-05-21 14:59:54,647 - __main__ - INFO - BTRUN: trade_time                     09:16:00
2025-05-21 14:59:54,647 - __main__ - INFO - BTRUN: expiry_date                  2025-04-03
2025-05-21 14:59:54,647 - __main__ - INFO - BTRUN: index_name                        NIFTY
2025-05-21 14:59:54,647 - __main__ - INFO - BTRUN: spot                           23420.45
2025-05-21 14:59:54,647 - __main__ - INFO - BTRUN: atm_strike                      23450.0
2025-05-21 14:59:54,647 - __main__ - INFO - BTRUN: strike                          23450.0
2025-05-21 14:59:54,647 - __main__ - INFO - BTRUN: dte                                   2
2025-05-21 14:59:54,648 - __main__ - INFO - BTRUN: expiry_bucket                        CW
2025-05-21 14:59:54,648 - __main__ - INFO - BTRUN: zone_id                               1
2025-05-21 14:59:54,648 - __main__ - INFO - BTRUN: zone_name                          OPEN
2025-05-21 14:59:54,648 - __main__ - INFO - BTRUN: call_strike_type                    ATM
2025-05-21 14:59:54,648 - __main__ - INFO - BTRUN: put_strike_type                     ATM
2025-05-21 14:59:54,648 - __main__ - INFO - BTRUN: ce_symbol           NIFTY03APR2523450CE
2025-05-21 14:59:54,648 - __main__ - INFO - BTRUN: ce_open                          102.85
2025-05-21 14:59:54,648 - __main__ - INFO - BTRUN: ce_high                          106.65
2025-05-21 14:59:54,648 - __main__ - INFO - BTRUN: ce_low                            97.85
2025-05-21 14:59:54,649 - __main__ - INFO - BTRUN: ce_close                         100.75
2025-05-21 14:59:54,649 - __main__ - INFO - BTRUN: ce_volume                       1015050
2025-05-21 14:59:54,649 - __main__ - INFO - BTRUN: ce_oi                            978075
2025-05-21 14:59:54,649 - __main__ - INFO - BTRUN: ce_coi                                0
2025-05-21 14:59:54,649 - __main__ - INFO - BTRUN: ce_iv                             13.44
2025-05-21 14:59:54,649 - __main__ - INFO - BTRUN: ce_delta                           0.48
2025-05-21 14:59:54,649 - __main__ - INFO - BTRUN: ce_gamma                         0.0015
2025-05-21 14:59:54,649 - __main__ - INFO - BTRUN: ce_theta                         -23.27
2025-05-21 14:59:54,649 - __main__ - INFO - BTRUN: ce_vega                            7.95
2025-05-21 14:59:54,649 - __main__ - INFO - BTRUN: ce_rho                          81.3115
2025-05-21 14:59:54,650 - __main__ - INFO - BTRUN: pe_symbol           NIFTY03APR2523450PE
2025-05-21 14:59:54,650 - __main__ - INFO - BTRUN: pe_open                           118.2
2025-05-21 14:59:54,650 - __main__ - INFO - BTRUN: pe_high                           122.3
2025-05-21 14:59:54,650 - __main__ - INFO - BTRUN: pe_low                            112.2
2025-05-21 14:59:54,650 - __main__ - INFO - BTRUN: pe_close                          115.7
2025-05-21 14:59:54,650 - __main__ - INFO - BTRUN: pe_volume                        877125
2025-05-21 14:59:54,650 - __main__ - INFO - BTRUN: pe_oi                           1618950
2025-05-21 14:59:54,650 - __main__ - INFO - BTRUN: pe_coi                                0
2025-05-21 14:59:54,650 - __main__ - INFO - BTRUN: pe_iv                             13.74
2025-05-21 14:59:54,650 - __main__ - INFO - BTRUN: pe_delta                          -0.52
2025-05-21 14:59:54,651 - __main__ - INFO - BTRUN: pe_gamma                         0.0015
2025-05-21 14:59:54,651 - __main__ - INFO - BTRUN: pe_theta                          -17.3
2025-05-21 14:59:54,651 - __main__ - INFO - BTRUN: pe_vega                            7.95
2025-05-21 14:59:54,651 - __main__ - INFO - BTRUN: pe_rho                         -88.3744
2025-05-21 14:59:54,651 - __main__ - INFO - BTRUN: future_open                     23534.5
2025-05-21 14:59:54,651 - __main__ - INFO - BTRUN: future_high                    23543.95
2025-05-21 14:59:54,651 - __main__ - INFO - BTRUN: future_low                      23527.0
2025-05-21 14:59:54,651 - __main__ - INFO - BTRUN: future_close                   23536.75
2025-05-21 14:59:54,651 - __main__ - INFO - BTRUN: future_volume                     86475
2025-05-21 14:59:54,651 - __main__ - INFO - BTRUN: future_oi                      12605100
2025-05-21 14:59:54,652 - __main__ - INFO - BTRUN: future_coi                            0
2025-05-21 14:59:54,652 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,647 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
2025-05-21 14:59:54,652 - __main__ - INFO - BTRUN: trade_date                   2025-04-01
2025-05-21 14:59:54,652 - __main__ - INFO - BTRUN: trade_time                     12:00:00
2025-05-21 14:59:54,652 - __main__ - INFO - BTRUN: expiry_date                  2025-04-03
2025-05-21 14:59:54,652 - __main__ - INFO - BTRUN: index_name                        NIFTY
2025-05-21 14:59:54,652 - __main__ - INFO - BTRUN: spot                            23195.3
2025-05-21 14:59:54,652 - __main__ - INFO - BTRUN: atm_strike                      23200.0
2025-05-21 14:59:54,652 - __main__ - INFO - BTRUN: strike                          23200.0
2025-05-21 14:59:54,653 - __main__ - INFO - BTRUN: dte                                   2
2025-05-21 14:59:54,653 - __main__ - INFO - BTRUN: expiry_bucket                        CW
2025-05-21 14:59:54,653 - __main__ - INFO - BTRUN: zone_id                               2
2025-05-21 14:59:54,653 - __main__ - INFO - BTRUN: zone_name                      MID_MORN
2025-05-21 14:59:54,653 - __main__ - INFO - BTRUN: call_strike_type                    ATM
2025-05-21 14:59:54,653 - __main__ - INFO - BTRUN: put_strike_type                     ATM
2025-05-21 14:59:54,653 - __main__ - INFO - BTRUN: ce_symbol           NIFTY03APR2523200CE
2025-05-21 14:59:54,653 - __main__ - INFO - BTRUN: ce_open                           123.6
2025-05-21 14:59:54,653 - __main__ - INFO - BTRUN: ce_high                           124.3
2025-05-21 14:59:54,653 - __main__ - INFO - BTRUN: ce_low                           119.25
2025-05-21 14:59:54,654 - __main__ - INFO - BTRUN: ce_close                          124.2
2025-05-21 14:59:54,654 - __main__ - INFO - BTRUN: ce_volume                        326775
2025-05-21 14:59:54,654 - __main__ - INFO - BTRUN: ce_oi                           4374450
2025-05-21 14:59:54,654 - __main__ - INFO - BTRUN: ce_coi                           193725
2025-05-21 14:59:54,654 - __main__ - INFO - BTRUN: ce_iv                             14.99
2025-05-21 14:59:54,654 - __main__ - INFO - BTRUN: ce_delta                           0.52
2025-05-21 14:59:54,654 - __main__ - INFO - BTRUN: ce_gamma                         0.0013
2025-05-21 14:59:54,655 - __main__ - INFO - BTRUN: ce_theta                         -25.57
2025-05-21 14:59:54,655 - __main__ - INFO - BTRUN: ce_vega                            7.87
2025-05-21 14:59:54,655 - __main__ - INFO - BTRUN: ce_rho                          86.2799
2025-05-21 14:59:54,655 - __main__ - INFO - BTRUN: pe_symbol           NIFTY03APR2523200PE
2025-05-21 14:59:54,655 - __main__ - INFO - BTRUN: pe_open                           100.2
2025-05-21 14:59:54,655 - __main__ - INFO - BTRUN: pe_high                           105.2
2025-05-21 14:59:54,656 - __main__ - INFO - BTRUN: pe_low                            99.55
2025-05-21 14:59:54,656 - __main__ - INFO - BTRUN: pe_close                         100.75
2025-05-21 14:59:54,656 - __main__ - INFO - BTRUN: pe_volume                        526875
2025-05-21 14:59:54,656 - __main__ - INFO - BTRUN: pe_oi                           5030025
2025-05-21 14:59:54,656 - __main__ - INFO - BTRUN: pe_coi                           101175
2025-05-21 14:59:54,657 - __main__ - INFO - BTRUN: pe_iv                             13.55
2025-05-21 14:59:54,657 - __main__ - INFO - BTRUN: pe_delta                          -0.48
2025-05-21 14:59:54,657 - __main__ - INFO - BTRUN: pe_gamma                         0.0015
2025-05-21 14:59:54,657 - __main__ - INFO - BTRUN: pe_theta                         -17.08
2025-05-21 14:59:54,657 - __main__ - INFO - BTRUN: pe_vega                            7.87
2025-05-21 14:59:54,658 - __main__ - INFO - BTRUN: pe_rho                         -81.3242
2025-05-21 14:59:54,658 - __main__ - INFO - BTRUN: future_open                     23347.0
2025-05-21 14:59:54,658 - __main__ - INFO - BTRUN: future_high                    23348.85
2025-05-21 14:59:54,658 - __main__ - INFO - BTRUN: future_low                      23339.0
2025-05-21 14:59:54,658 - __main__ - INFO - BTRUN: future_close                    23348.1
2025-05-21 14:59:54,658 - __main__ - INFO - BTRUN: future_volume                     19050
2025-05-21 14:59:54,659 - __main__ - INFO - BTRUN: future_oi                      13928550
2025-05-21 14:59:54,659 - __main__ - INFO - BTRUN: future_coi                       -28275
2025-05-21 14:59:54,659 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,647 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 14:59:54,659 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,647 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-21 14:59:54,659 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,649 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 14:59:54,660 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,649 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140374028661280
2025-05-21 14:59:54,660 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,649 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140374028661168, OptionType.PUT ID: 140374028661280
2025-05-21 14:59:54,660 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,649 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 14:59:54,660 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,649 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-21 14:59:54,660 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,649 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-21 14:59:54,661 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,650 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
2025-05-21 14:59:54,661 - __main__ - INFO - BTRUN: WHERE index_name = 'NIFTY'
2025-05-21 14:59:54,661 - __main__ - INFO - BTRUN: AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
2025-05-21 14:59:54,661 - __main__ - INFO - BTRUN: ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 14:59:54,959 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,959 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.309s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 14:59:54,961 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,961 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-21 14:59:54,962 - __main__ - INFO - BTRUN: 2025-05-21 14:59:54,962 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-21 14:59:55,019 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,019 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 14:59:55,044 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,044 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
2025-05-21 14:59:55,045 - __main__ - INFO - BTRUN: SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:55,045 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
2025-05-21 14:59:55,045 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time ASC
2025-05-21 14:59:55,045 - __main__ - INFO - BTRUN: LIMIT 1
2025-05-21 14:59:55,045 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,044 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
2025-05-21 14:59:55,045 - __main__ - INFO - BTRUN: SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:55,045 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '12:00:00'
2025-05-21 14:59:55,045 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time DESC
2025-05-21 14:59:55,046 - __main__ - INFO - BTRUN: LIMIT 1
2025-05-21 14:59:55,086 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,085 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.041s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:55,086 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
2025-05-21 14:59:55,086 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 14:59:55,124 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,124 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.039s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:55,125 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''12:00:00''
2025-05-21 14:59:55,125 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 14:59:55,126 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,125 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
2025-05-21 14:59:55,126 - __main__ - INFO - BTRUN: trade_date                   2025-04-01
2025-05-21 14:59:55,126 - __main__ - INFO - BTRUN: trade_time                     09:16:00
2025-05-21 14:59:55,126 - __main__ - INFO - BTRUN: expiry_date                  2025-04-03
2025-05-21 14:59:55,126 - __main__ - INFO - BTRUN: index_name                        NIFTY
2025-05-21 14:59:55,126 - __main__ - INFO - BTRUN: spot                           23420.45
2025-05-21 14:59:55,126 - __main__ - INFO - BTRUN: atm_strike                      23450.0
2025-05-21 14:59:55,127 - __main__ - INFO - BTRUN: strike                          23450.0
2025-05-21 14:59:55,127 - __main__ - INFO - BTRUN: dte                                   2
2025-05-21 14:59:55,127 - __main__ - INFO - BTRUN: expiry_bucket                        CW
2025-05-21 14:59:55,127 - __main__ - INFO - BTRUN: zone_id                               1
2025-05-21 14:59:55,127 - __main__ - INFO - BTRUN: zone_name                          OPEN
2025-05-21 14:59:55,127 - __main__ - INFO - BTRUN: call_strike_type                    ATM
2025-05-21 14:59:55,127 - __main__ - INFO - BTRUN: put_strike_type                     ATM
2025-05-21 14:59:55,127 - __main__ - INFO - BTRUN: ce_symbol           NIFTY03APR2523450CE
2025-05-21 14:59:55,127 - __main__ - INFO - BTRUN: ce_open                          102.85
2025-05-21 14:59:55,128 - __main__ - INFO - BTRUN: ce_high                          106.65
2025-05-21 14:59:55,128 - __main__ - INFO - BTRUN: ce_low                            97.85
2025-05-21 14:59:55,128 - __main__ - INFO - BTRUN: ce_close                         100.75
2025-05-21 14:59:55,128 - __main__ - INFO - BTRUN: ce_volume                       1015050
2025-05-21 14:59:55,128 - __main__ - INFO - BTRUN: ce_oi                            978075
2025-05-21 14:59:55,128 - __main__ - INFO - BTRUN: ce_coi                                0
2025-05-21 14:59:55,128 - __main__ - INFO - BTRUN: ce_iv                             13.44
2025-05-21 14:59:55,128 - __main__ - INFO - BTRUN: ce_delta                           0.48
2025-05-21 14:59:55,128 - __main__ - INFO - BTRUN: ce_gamma                         0.0015
2025-05-21 14:59:55,128 - __main__ - INFO - BTRUN: ce_theta                         -23.27
2025-05-21 14:59:55,129 - __main__ - INFO - BTRUN: ce_vega                            7.95
2025-05-21 14:59:55,129 - __main__ - INFO - BTRUN: ce_rho                          81.3115
2025-05-21 14:59:55,129 - __main__ - INFO - BTRUN: pe_symbol           NIFTY03APR2523450PE
2025-05-21 14:59:55,129 - __main__ - INFO - BTRUN: pe_open                           118.2
2025-05-21 14:59:55,129 - __main__ - INFO - BTRUN: pe_high                           122.3
2025-05-21 14:59:55,129 - __main__ - INFO - BTRUN: pe_low                            112.2
2025-05-21 14:59:55,129 - __main__ - INFO - BTRUN: pe_close                          115.7
2025-05-21 14:59:55,129 - __main__ - INFO - BTRUN: pe_volume                        877125
2025-05-21 14:59:55,129 - __main__ - INFO - BTRUN: pe_oi                           1618950
2025-05-21 14:59:55,129 - __main__ - INFO - BTRUN: pe_coi                                0
2025-05-21 14:59:55,130 - __main__ - INFO - BTRUN: pe_iv                             13.74
2025-05-21 14:59:55,130 - __main__ - INFO - BTRUN: pe_delta                          -0.52
2025-05-21 14:59:55,130 - __main__ - INFO - BTRUN: pe_gamma                         0.0015
2025-05-21 14:59:55,130 - __main__ - INFO - BTRUN: pe_theta                          -17.3
2025-05-21 14:59:55,130 - __main__ - INFO - BTRUN: pe_vega                            7.95
2025-05-21 14:59:55,130 - __main__ - INFO - BTRUN: pe_rho                         -88.3744
2025-05-21 14:59:55,130 - __main__ - INFO - BTRUN: future_open                     23534.5
2025-05-21 14:59:55,130 - __main__ - INFO - BTRUN: future_high                    23543.95
2025-05-21 14:59:55,130 - __main__ - INFO - BTRUN: future_low                      23527.0
2025-05-21 14:59:55,130 - __main__ - INFO - BTRUN: future_close                   23536.75
2025-05-21 14:59:55,131 - __main__ - INFO - BTRUN: future_volume                     86475
2025-05-21 14:59:55,131 - __main__ - INFO - BTRUN: future_oi                      12605100
2025-05-21 14:59:55,131 - __main__ - INFO - BTRUN: future_coi                            0
2025-05-21 14:59:55,131 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,126 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
2025-05-21 14:59:55,131 - __main__ - INFO - BTRUN: trade_date                   2025-04-01
2025-05-21 14:59:55,131 - __main__ - INFO - BTRUN: trade_time                     12:00:00
2025-05-21 14:59:55,131 - __main__ - INFO - BTRUN: expiry_date                  2025-04-03
2025-05-21 14:59:55,131 - __main__ - INFO - BTRUN: index_name                        NIFTY
2025-05-21 14:59:55,131 - __main__ - INFO - BTRUN: spot                            23195.3
2025-05-21 14:59:55,131 - __main__ - INFO - BTRUN: atm_strike                      23200.0
2025-05-21 14:59:55,132 - __main__ - INFO - BTRUN: strike                          23200.0
2025-05-21 14:59:55,132 - __main__ - INFO - BTRUN: dte                                   2
2025-05-21 14:59:55,132 - __main__ - INFO - BTRUN: expiry_bucket                        CW
2025-05-21 14:59:55,132 - __main__ - INFO - BTRUN: zone_id                               2
2025-05-21 14:59:55,132 - __main__ - INFO - BTRUN: zone_name                      MID_MORN
2025-05-21 14:59:55,132 - __main__ - INFO - BTRUN: call_strike_type                    ATM
2025-05-21 14:59:55,132 - __main__ - INFO - BTRUN: put_strike_type                     ATM
2025-05-21 14:59:55,132 - __main__ - INFO - BTRUN: ce_symbol           NIFTY03APR2523200CE
2025-05-21 14:59:55,133 - __main__ - INFO - BTRUN: ce_open                           123.6
2025-05-21 14:59:55,133 - __main__ - INFO - BTRUN: ce_high                           124.3
2025-05-21 14:59:55,133 - __main__ - INFO - BTRUN: ce_low                           119.25
2025-05-21 14:59:55,133 - __main__ - INFO - BTRUN: ce_close                          124.2
2025-05-21 14:59:55,133 - __main__ - INFO - BTRUN: ce_volume                        326775
2025-05-21 14:59:55,134 - __main__ - INFO - BTRUN: ce_oi                           4374450
2025-05-21 14:59:55,134 - __main__ - INFO - BTRUN: ce_coi                           193725
2025-05-21 14:59:55,134 - __main__ - INFO - BTRUN: ce_iv                             14.99
2025-05-21 14:59:55,134 - __main__ - INFO - BTRUN: ce_delta                           0.52
2025-05-21 14:59:55,134 - __main__ - INFO - BTRUN: ce_gamma                         0.0013
2025-05-21 14:59:55,135 - __main__ - INFO - BTRUN: ce_theta                         -25.57
2025-05-21 14:59:55,135 - __main__ - INFO - BTRUN: ce_vega                            7.87
2025-05-21 14:59:55,135 - __main__ - INFO - BTRUN: ce_rho                          86.2799
2025-05-21 14:59:55,135 - __main__ - INFO - BTRUN: pe_symbol           NIFTY03APR2523200PE
2025-05-21 14:59:55,135 - __main__ - INFO - BTRUN: pe_open                           100.2
2025-05-21 14:59:55,135 - __main__ - INFO - BTRUN: pe_high                           105.2
2025-05-21 14:59:55,136 - __main__ - INFO - BTRUN: pe_low                            99.55
2025-05-21 14:59:55,136 - __main__ - INFO - BTRUN: pe_close                         100.75
2025-05-21 14:59:55,136 - __main__ - INFO - BTRUN: pe_volume                        526875
2025-05-21 14:59:55,136 - __main__ - INFO - BTRUN: pe_oi                           5030025
2025-05-21 14:59:55,136 - __main__ - INFO - BTRUN: pe_coi                           101175
2025-05-21 14:59:55,137 - __main__ - INFO - BTRUN: pe_iv                             13.55
2025-05-21 14:59:55,137 - __main__ - INFO - BTRUN: pe_delta                          -0.48
2025-05-21 14:59:55,137 - __main__ - INFO - BTRUN: pe_gamma                         0.0015
2025-05-21 14:59:55,137 - __main__ - INFO - BTRUN: pe_theta                         -17.08
2025-05-21 14:59:55,137 - __main__ - INFO - BTRUN: pe_vega                            7.87
2025-05-21 14:59:55,137 - __main__ - INFO - BTRUN: pe_rho                         -81.3242
2025-05-21 14:59:55,138 - __main__ - INFO - BTRUN: future_open                     23347.0
2025-05-21 14:59:55,138 - __main__ - INFO - BTRUN: future_high                    23348.85
2025-05-21 14:59:55,138 - __main__ - INFO - BTRUN: future_low                      23339.0
2025-05-21 14:59:55,138 - __main__ - INFO - BTRUN: future_close                    23348.1
2025-05-21 14:59:55,138 - __main__ - INFO - BTRUN: future_volume                     19050
2025-05-21 14:59:55,139 - __main__ - INFO - BTRUN: future_oi                      13928550
2025-05-21 14:59:55,139 - __main__ - INFO - BTRUN: future_coi                       -28275
2025-05-21 14:59:55,139 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,126 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 14:59:55,139 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,127 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-21 14:59:55,139 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,127 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 14:59:55,140 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,127 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140374028661168
2025-05-21 14:59:55,140 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,127 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140374028661168, OptionType.PUT ID: 140374028661280
2025-05-21 14:59:55,140 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,127 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 14:59:55,140 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,127 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-21 14:59:55,140 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,127 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-21 14:59:55,141 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,129 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
2025-05-21 14:59:55,141 - __main__ - INFO - BTRUN: WHERE index_name = 'NIFTY'
2025-05-21 14:59:55,141 - __main__ - INFO - BTRUN: AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
2025-05-21 14:59:55,141 - __main__ - INFO - BTRUN: ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 14:59:55,427 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,427 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.298s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 14:59:55,428 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,428 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-21 14:59:55,429 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,429 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-21 14:59:55,486 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,486 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 14:59:55,505 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,505 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
2025-05-21 14:59:55,505 - __main__ - INFO - BTRUN: SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:55,505 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
2025-05-21 14:59:55,505 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time ASC
2025-05-21 14:59:55,505 - __main__ - INFO - BTRUN: LIMIT 1
2025-05-21 14:59:55,505 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,505 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
2025-05-21 14:59:55,506 - __main__ - INFO - BTRUN: SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:55,506 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '12:00:00'
2025-05-21 14:59:55,506 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time DESC
2025-05-21 14:59:55,506 - __main__ - INFO - BTRUN: LIMIT 1
2025-05-21 14:59:55,546 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,546 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.041s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:55,547 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
2025-05-21 14:59:55,547 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 14:59:55,588 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,588 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.041s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:55,588 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''12:00:00''
2025-05-21 14:59:55,588 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 14:59:55,589 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,589 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
2025-05-21 14:59:55,589 - __main__ - INFO - BTRUN: trade_date                   2025-04-01
2025-05-21 14:59:55,589 - __main__ - INFO - BTRUN: trade_time                     09:16:00
2025-05-21 14:59:55,589 - __main__ - INFO - BTRUN: expiry_date                  2025-04-03
2025-05-21 14:59:55,589 - __main__ - INFO - BTRUN: index_name                        NIFTY
2025-05-21 14:59:55,589 - __main__ - INFO - BTRUN: spot                           23420.45
2025-05-21 14:59:55,590 - __main__ - INFO - BTRUN: atm_strike                      23450.0
2025-05-21 14:59:55,590 - __main__ - INFO - BTRUN: strike                          23450.0
2025-05-21 14:59:55,590 - __main__ - INFO - BTRUN: dte                                   2
2025-05-21 14:59:55,590 - __main__ - INFO - BTRUN: expiry_bucket                        CW
2025-05-21 14:59:55,590 - __main__ - INFO - BTRUN: zone_id                               1
2025-05-21 14:59:55,590 - __main__ - INFO - BTRUN: zone_name                          OPEN
2025-05-21 14:59:55,590 - __main__ - INFO - BTRUN: call_strike_type                    ATM
2025-05-21 14:59:55,590 - __main__ - INFO - BTRUN: put_strike_type                     ATM
2025-05-21 14:59:55,590 - __main__ - INFO - BTRUN: ce_symbol           NIFTY03APR2523450CE
2025-05-21 14:59:55,591 - __main__ - INFO - BTRUN: ce_open                          102.85
2025-05-21 14:59:55,591 - __main__ - INFO - BTRUN: ce_high                          106.65
2025-05-21 14:59:55,591 - __main__ - INFO - BTRUN: ce_low                            97.85
2025-05-21 14:59:55,591 - __main__ - INFO - BTRUN: ce_close                         100.75
2025-05-21 14:59:55,591 - __main__ - INFO - BTRUN: ce_volume                       1015050
2025-05-21 14:59:55,591 - __main__ - INFO - BTRUN: ce_oi                            978075
2025-05-21 14:59:55,591 - __main__ - INFO - BTRUN: ce_coi                                0
2025-05-21 14:59:55,591 - __main__ - INFO - BTRUN: ce_iv                             13.44
2025-05-21 14:59:55,591 - __main__ - INFO - BTRUN: ce_delta                           0.48
2025-05-21 14:59:55,591 - __main__ - INFO - BTRUN: ce_gamma                         0.0015
2025-05-21 14:59:55,592 - __main__ - INFO - BTRUN: ce_theta                         -23.27
2025-05-21 14:59:55,592 - __main__ - INFO - BTRUN: ce_vega                            7.95
2025-05-21 14:59:55,592 - __main__ - INFO - BTRUN: ce_rho                          81.3115
2025-05-21 14:59:55,592 - __main__ - INFO - BTRUN: pe_symbol           NIFTY03APR2523450PE
2025-05-21 14:59:55,592 - __main__ - INFO - BTRUN: pe_open                           118.2
2025-05-21 14:59:55,592 - __main__ - INFO - BTRUN: pe_high                           122.3
2025-05-21 14:59:55,592 - __main__ - INFO - BTRUN: pe_low                            112.2
2025-05-21 14:59:55,592 - __main__ - INFO - BTRUN: pe_close                          115.7
2025-05-21 14:59:55,592 - __main__ - INFO - BTRUN: pe_volume                        877125
2025-05-21 14:59:55,592 - __main__ - INFO - BTRUN: pe_oi                           1618950
2025-05-21 14:59:55,593 - __main__ - INFO - BTRUN: pe_coi                                0
2025-05-21 14:59:55,593 - __main__ - INFO - BTRUN: pe_iv                             13.74
2025-05-21 14:59:55,593 - __main__ - INFO - BTRUN: pe_delta                          -0.52
2025-05-21 14:59:55,593 - __main__ - INFO - BTRUN: pe_gamma                         0.0015
2025-05-21 14:59:55,593 - __main__ - INFO - BTRUN: pe_theta                          -17.3
2025-05-21 14:59:55,593 - __main__ - INFO - BTRUN: pe_vega                            7.95
2025-05-21 14:59:55,593 - __main__ - INFO - BTRUN: pe_rho                         -88.3744
2025-05-21 14:59:55,593 - __main__ - INFO - BTRUN: future_open                     23534.5
2025-05-21 14:59:55,593 - __main__ - INFO - BTRUN: future_high                    23543.95
2025-05-21 14:59:55,593 - __main__ - INFO - BTRUN: future_low                      23527.0
2025-05-21 14:59:55,594 - __main__ - INFO - BTRUN: future_close                   23536.75
2025-05-21 14:59:55,594 - __main__ - INFO - BTRUN: future_volume                     86475
2025-05-21 14:59:55,594 - __main__ - INFO - BTRUN: future_oi                      12605100
2025-05-21 14:59:55,594 - __main__ - INFO - BTRUN: future_coi                            0
2025-05-21 14:59:55,594 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,589 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
2025-05-21 14:59:55,594 - __main__ - INFO - BTRUN: trade_date                   2025-04-01
2025-05-21 14:59:55,594 - __main__ - INFO - BTRUN: trade_time                     12:00:00
2025-05-21 14:59:55,594 - __main__ - INFO - BTRUN: expiry_date                  2025-04-03
2025-05-21 14:59:55,594 - __main__ - INFO - BTRUN: index_name                        NIFTY
2025-05-21 14:59:55,595 - __main__ - INFO - BTRUN: spot                            23195.3
2025-05-21 14:59:55,595 - __main__ - INFO - BTRUN: atm_strike                      23200.0
2025-05-21 14:59:55,595 - __main__ - INFO - BTRUN: strike                          23200.0
2025-05-21 14:59:55,595 - __main__ - INFO - BTRUN: dte                                   2
2025-05-21 14:59:55,595 - __main__ - INFO - BTRUN: expiry_bucket                        CW
2025-05-21 14:59:55,595 - __main__ - INFO - BTRUN: zone_id                               2
2025-05-21 14:59:55,595 - __main__ - INFO - BTRUN: zone_name                      MID_MORN
2025-05-21 14:59:55,595 - __main__ - INFO - BTRUN: call_strike_type                    ATM
2025-05-21 14:59:55,595 - __main__ - INFO - BTRUN: put_strike_type                     ATM
2025-05-21 14:59:55,596 - __main__ - INFO - BTRUN: ce_symbol           NIFTY03APR2523200CE
2025-05-21 14:59:55,596 - __main__ - INFO - BTRUN: ce_open                           123.6
2025-05-21 14:59:55,596 - __main__ - INFO - BTRUN: ce_high                           124.3
2025-05-21 14:59:55,596 - __main__ - INFO - BTRUN: ce_low                           119.25
2025-05-21 14:59:55,596 - __main__ - INFO - BTRUN: ce_close                          124.2
2025-05-21 14:59:55,597 - __main__ - INFO - BTRUN: ce_volume                        326775
2025-05-21 14:59:55,597 - __main__ - INFO - BTRUN: ce_oi                           4374450
2025-05-21 14:59:55,597 - __main__ - INFO - BTRUN: ce_coi                           193725
2025-05-21 14:59:55,597 - __main__ - INFO - BTRUN: ce_iv                             14.99
2025-05-21 14:59:55,597 - __main__ - INFO - BTRUN: ce_delta                           0.52
2025-05-21 14:59:55,598 - __main__ - INFO - BTRUN: ce_gamma                         0.0013
2025-05-21 14:59:55,598 - __main__ - INFO - BTRUN: ce_theta                         -25.57
2025-05-21 14:59:55,598 - __main__ - INFO - BTRUN: ce_vega                            7.87
2025-05-21 14:59:55,598 - __main__ - INFO - BTRUN: ce_rho                          86.2799
2025-05-21 14:59:55,598 - __main__ - INFO - BTRUN: pe_symbol           NIFTY03APR2523200PE
2025-05-21 14:59:55,598 - __main__ - INFO - BTRUN: pe_open                           100.2
2025-05-21 14:59:55,599 - __main__ - INFO - BTRUN: pe_high                           105.2
2025-05-21 14:59:55,599 - __main__ - INFO - BTRUN: pe_low                            99.55
2025-05-21 14:59:55,599 - __main__ - INFO - BTRUN: pe_close                         100.75
2025-05-21 14:59:55,599 - __main__ - INFO - BTRUN: pe_volume                        526875
2025-05-21 14:59:55,599 - __main__ - INFO - BTRUN: pe_oi                           5030025
2025-05-21 14:59:55,600 - __main__ - INFO - BTRUN: pe_coi                           101175
2025-05-21 14:59:55,600 - __main__ - INFO - BTRUN: pe_iv                             13.55
2025-05-21 14:59:55,600 - __main__ - INFO - BTRUN: pe_delta                          -0.48
2025-05-21 14:59:55,600 - __main__ - INFO - BTRUN: pe_gamma                         0.0015
2025-05-21 14:59:55,600 - __main__ - INFO - BTRUN: pe_theta                         -17.08
2025-05-21 14:59:55,600 - __main__ - INFO - BTRUN: pe_vega                            7.87
2025-05-21 14:59:55,601 - __main__ - INFO - BTRUN: pe_rho                         -81.3242
2025-05-21 14:59:55,601 - __main__ - INFO - BTRUN: future_open                     23347.0
2025-05-21 14:59:55,601 - __main__ - INFO - BTRUN: future_high                    23348.85
2025-05-21 14:59:55,601 - __main__ - INFO - BTRUN: future_low                      23339.0
2025-05-21 14:59:55,601 - __main__ - INFO - BTRUN: future_close                    23348.1
2025-05-21 14:59:55,602 - __main__ - INFO - BTRUN: future_volume                     19050
2025-05-21 14:59:55,602 - __main__ - INFO - BTRUN: future_oi                      13928550
2025-05-21 14:59:55,602 - __main__ - INFO - BTRUN: future_coi                       -28275
2025-05-21 14:59:55,602 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,589 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 14:59:55,602 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,590 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-21 14:59:55,603 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,590 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 14:59:55,603 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,590 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140374028661280
2025-05-21 14:59:55,603 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,590 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140374028661168, OptionType.PUT ID: 140374028661280
2025-05-21 14:59:55,603 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,590 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 14:59:55,603 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,590 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-21 14:59:55,603 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,590 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-21 14:59:55,604 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,592 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
2025-05-21 14:59:55,604 - __main__ - INFO - BTRUN: WHERE index_name = 'NIFTY'
2025-05-21 14:59:55,604 - __main__ - INFO - BTRUN: AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
2025-05-21 14:59:55,604 - __main__ - INFO - BTRUN: ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 14:59:55,902 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,901 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.310s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 14:59:55,903 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,903 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-21 14:59:55,904 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,904 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-21 14:59:55,961 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,961 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 14:59:55,970 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,970 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-21 14:59:55,971 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,970 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23420.45, 'atm_strike': 23450.0, 'strike': 23450.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523450CE', 'ce_open': 102.85, 'ce_high': 106.65, 'ce_low': 97.85, 'ce_close': 100.75, 'ce_volume': 1015050, 'ce_oi': 978075, 'ce_coi': 0, 'ce_iv': 13.44, 'ce_delta': 0.48, 'ce_gamma': 0.0015, 'ce_theta': -23.27, 'ce_vega': 7.95, 'ce_rho': 81.3115, 'pe_symbol': 'NIFTY03APR2523450PE', 'pe_open': 118.2, 'pe_high': 122.3, 'pe_low': 112.2, 'pe_close': 115.7, 'pe_volume': 877125, 'pe_oi': 1618950, 'pe_coi': 0, 'pe_iv': 13.74, 'pe_delta': -0.52, 'pe_gamma': 0.0015, 'pe_theta': -17.3, 'pe_vega': 7.95, 'pe_rho': -88.3744, 'future_open': 23534.5, 'future_high': 23543.95, 'future_low': 23527.0, 'future_close': 23536.75, 'future_volume': 86475, 'future_oi': 12605100, 'future_coi': 0}
2025-05-21 14:59:55,971 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,970 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': 91700, 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23195.3, 'atm_strike': 23200.0, 'strike': 23200.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523200CE', 'ce_open': 123.6, 'ce_high': 124.3, 'ce_low': 119.25, 'ce_close': 124.2, 'ce_volume': 326775, 'ce_oi': 4374450, 'ce_coi': 193725, 'ce_iv': 14.99, 'ce_delta': 0.52, 'ce_gamma': 0.0013, 'ce_theta': -25.57, 'ce_vega': 7.87, 'ce_rho': 86.2799, 'pe_symbol': 'NIFTY03APR2523200PE', 'pe_open': 100.2, 'pe_high': 105.2, 'pe_low': 99.55, 'pe_close': 100.75, 'pe_volume': 526875, 'pe_oi': 5030025, 'pe_coi': 101175, 'pe_iv': 13.55, 'pe_delta': -0.48, 'pe_gamma': 0.0015, 'pe_theta': -17.08, 'pe_vega': 7.87, 'pe_rho': -81.3242, 'future_open': 23347.0, 'future_high': 23348.85, 'future_low': 23339.0, 'future_close': 23348.1, 'future_volume': 19050, 'future_oi': 13928550, 'future_coi': -28275, 'close': 2.15, 'datetime': Timestamp('2025-04-01 09:17:00'), 'exit_reason': 'Stop Loss Hit'}
2025-05-21 14:59:55,971 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,971 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-21 14:59:55,971 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,971 - backtester_stable.BTRUN.trade_builder - ERROR - BUILD_TRADE_RECORD: Error parsing dates for exit time override: strptime() argument 1 must be str, not int
2025-05-21 14:59:55,972 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,972 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -747.5000000000001, PnL (Slippage): -747.5000000000001, Net PnL: -747.5000000000001
2025-05-21 14:59:55,972 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,972 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Stop Loss Hit
2025-05-21 14:59:55,972 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,972 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2025-04-01', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2025-04-01', 'exit_time': '09:17:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23450, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 115.7, 'exit_price': 100.75, 'points': -14.950000000000003, 'pointsAfterSlippage': -14.950000000000003, 'pnl': -747.5000000000001, 'pnlAfterSlippage': -747.5000000000001, 'expenses': 0.0, 'netPnlAfterExpenses': -747.5000000000001, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Stop Loss Hit', 'strategy_entry_number': 0, 'index_entry_price': 23420.45, 'index_exit_price': 23195.3, 'max_profit': 0, 'max_loss': 0}
2025-05-21 14:59:55,972 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,972 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
2025-05-21 14:59:55,972 - __main__ - INFO - BTRUN: SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:55,972 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
2025-05-21 14:59:55,972 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time ASC
2025-05-21 14:59:55,973 - __main__ - INFO - BTRUN: LIMIT 1
2025-05-21 14:59:55,973 - __main__ - INFO - BTRUN: 2025-05-21 14:59:55,972 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
2025-05-21 14:59:55,973 - __main__ - INFO - BTRUN: SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:55,973 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '12:00:00'
2025-05-21 14:59:55,973 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time DESC
2025-05-21 14:59:55,973 - __main__ - INFO - BTRUN: LIMIT 1
2025-05-21 14:59:56,003 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,003 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.030s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:56,003 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
2025-05-21 14:59:56,003 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 14:59:56,031 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,031 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.028s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:56,031 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''12:00:00''
2025-05-21 14:59:56,032 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 14:59:56,032 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,032 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
2025-05-21 14:59:56,033 - __main__ - INFO - BTRUN: trade_date                   2025-04-02
2025-05-21 14:59:56,033 - __main__ - INFO - BTRUN: trade_time                     09:16:00
2025-05-21 14:59:56,033 - __main__ - INFO - BTRUN: expiry_date                  2025-04-03
2025-05-21 14:59:56,033 - __main__ - INFO - BTRUN: index_name                        NIFTY
2025-05-21 14:59:56,033 - __main__ - INFO - BTRUN: spot                            23204.8
2025-05-21 14:59:56,033 - __main__ - INFO - BTRUN: atm_strike                      23200.0
2025-05-21 14:59:56,033 - __main__ - INFO - BTRUN: strike                          23200.0
2025-05-21 14:59:56,033 - __main__ - INFO - BTRUN: dte                                   1
2025-05-21 14:59:56,033 - __main__ - INFO - BTRUN: expiry_bucket                        CW
2025-05-21 14:59:56,033 - __main__ - INFO - BTRUN: zone_id                               1
2025-05-21 14:59:56,034 - __main__ - INFO - BTRUN: zone_name                          OPEN
2025-05-21 14:59:56,034 - __main__ - INFO - BTRUN: call_strike_type                    ATM
2025-05-21 14:59:56,034 - __main__ - INFO - BTRUN: put_strike_type                     ATM
2025-05-21 14:59:56,034 - __main__ - INFO - BTRUN: ce_symbol           NIFTY03APR2523200CE
2025-05-21 14:59:56,034 - __main__ - INFO - BTRUN: ce_open                          107.95
2025-05-21 14:59:56,034 - __main__ - INFO - BTRUN: ce_high                           113.5
2025-05-21 14:59:56,034 - __main__ - INFO - BTRUN: ce_low                           105.15
2025-05-21 14:59:56,034 - __main__ - INFO - BTRUN: ce_close                          110.5
2025-05-21 14:59:56,034 - __main__ - INFO - BTRUN: ce_volume                       2666850
2025-05-21 14:59:56,035 - __main__ - INFO - BTRUN: ce_oi                           5862000
2025-05-21 14:59:56,035 - __main__ - INFO - BTRUN: ce_coi                                0
2025-05-21 14:59:56,035 - __main__ - INFO - BTRUN: ce_iv                             16.53
2025-05-21 14:59:56,035 - __main__ - INFO - BTRUN: ce_delta                           0.53
2025-05-21 14:59:56,035 - __main__ - INFO - BTRUN: ce_gamma                         0.0015
2025-05-21 14:59:56,035 - __main__ - INFO - BTRUN: ce_theta                         -34.48
2025-05-21 14:59:56,035 - __main__ - INFO - BTRUN: ce_vega                             6.2
2025-05-21 14:59:56,035 - __main__ - INFO - BTRUN: ce_rho                          54.4841
2025-05-21 14:59:56,035 - __main__ - INFO - BTRUN: pe_symbol           NIFTY03APR2523200PE
2025-05-21 14:59:56,035 - __main__ - INFO - BTRUN: pe_open                            86.9
2025-05-21 14:59:56,036 - __main__ - INFO - BTRUN: pe_high                            94.9
2025-05-21 14:59:56,036 - __main__ - INFO - BTRUN: pe_low                            85.55
2025-05-21 14:59:56,036 - __main__ - INFO - BTRUN: pe_close                          86.15
2025-05-21 14:59:56,036 - __main__ - INFO - BTRUN: pe_volume                       3014850
2025-05-21 14:59:56,036 - __main__ - INFO - BTRUN: pe_oi                           5689575
2025-05-21 14:59:56,036 - __main__ - INFO - BTRUN: pe_coi                                0
2025-05-21 14:59:56,036 - __main__ - INFO - BTRUN: pe_iv                             15.06
2025-05-21 14:59:56,036 - __main__ - INFO - BTRUN: pe_delta                          -0.47
2025-05-21 14:59:56,036 - __main__ - INFO - BTRUN: pe_gamma                         0.0017
2025-05-21 14:59:56,036 - __main__ - INFO - BTRUN: pe_theta                         -25.36
2025-05-21 14:59:56,037 - __main__ - INFO - BTRUN: pe_vega                             6.2
2025-05-21 14:59:56,037 - __main__ - INFO - BTRUN: pe_rho                         -49.7483
2025-05-21 14:59:56,037 - __main__ - INFO - BTRUN: future_open                     23339.0
2025-05-21 14:59:56,037 - __main__ - INFO - BTRUN: future_high                     23344.7
2025-05-21 14:59:56,037 - __main__ - INFO - BTRUN: future_low                     23331.25
2025-05-21 14:59:56,037 - __main__ - INFO - BTRUN: future_close                   23344.15
2025-05-21 14:59:56,037 - __main__ - INFO - BTRUN: future_volume                     43725
2025-05-21 14:59:56,037 - __main__ - INFO - BTRUN: future_oi                      12808425
2025-05-21 14:59:56,038 - __main__ - INFO - BTRUN: future_coi                            0
2025-05-21 14:59:56,038 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,033 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
2025-05-21 14:59:56,038 - __main__ - INFO - BTRUN: trade_date                   2025-04-02
2025-05-21 14:59:56,038 - __main__ - INFO - BTRUN: trade_time                     12:00:00
2025-05-21 14:59:56,038 - __main__ - INFO - BTRUN: expiry_date                  2025-04-03
2025-05-21 14:59:56,039 - __main__ - INFO - BTRUN: index_name                        NIFTY
2025-05-21 14:59:56,039 - __main__ - INFO - BTRUN: spot                           23261.45
2025-05-21 14:59:56,039 - __main__ - INFO - BTRUN: atm_strike                      23250.0
2025-05-21 14:59:56,039 - __main__ - INFO - BTRUN: strike                          23250.0
2025-05-21 14:59:56,039 - __main__ - INFO - BTRUN: dte                                   1
2025-05-21 14:59:56,039 - __main__ - INFO - BTRUN: expiry_bucket                        CW
2025-05-21 14:59:56,040 - __main__ - INFO - BTRUN: zone_id                               2
2025-05-21 14:59:56,040 - __main__ - INFO - BTRUN: zone_name                      MID_MORN
2025-05-21 14:59:56,040 - __main__ - INFO - BTRUN: call_strike_type                    ATM
2025-05-21 14:59:56,040 - __main__ - INFO - BTRUN: put_strike_type                     ATM
2025-05-21 14:59:56,040 - __main__ - INFO - BTRUN: ce_symbol           NIFTY03APR2523250CE
2025-05-21 14:59:56,041 - __main__ - INFO - BTRUN: ce_open                            83.1
2025-05-21 14:59:56,041 - __main__ - INFO - BTRUN: ce_high                            87.9
2025-05-21 14:59:56,041 - __main__ - INFO - BTRUN: ce_low                             83.1
2025-05-21 14:59:56,041 - __main__ - INFO - BTRUN: ce_close                           87.0
2025-05-21 14:59:56,041 - __main__ - INFO - BTRUN: ce_volume                        546750
2025-05-21 14:59:56,041 - __main__ - INFO - BTRUN: ce_oi                           6733800
2025-05-21 14:59:56,042 - __main__ - INFO - BTRUN: ce_coi                                0
2025-05-21 14:59:56,042 - __main__ - INFO - BTRUN: ce_iv                             12.13
2025-05-21 14:59:56,042 - __main__ - INFO - BTRUN: ce_delta                           0.55
2025-05-21 14:59:56,042 - __main__ - INFO - BTRUN: ce_gamma                         0.0021
2025-05-21 14:59:56,042 - __main__ - INFO - BTRUN: ce_theta                         -26.28
2025-05-21 14:59:56,043 - __main__ - INFO - BTRUN: ce_vega                            6.18
2025-05-21 14:59:56,043 - __main__ - INFO - BTRUN: ce_rho                           57.017
2025-05-21 14:59:56,043 - __main__ - INFO - BTRUN: pe_symbol           NIFTY03APR2523250PE
2025-05-21 14:59:56,043 - __main__ - INFO - BTRUN: pe_open                           70.65
2025-05-21 14:59:56,043 - __main__ - INFO - BTRUN: pe_high                           70.65
2025-05-21 14:59:56,043 - __main__ - INFO - BTRUN: pe_low                             65.7
2025-05-21 14:59:56,044 - __main__ - INFO - BTRUN: pe_close                           66.8
2025-05-21 14:59:56,044 - __main__ - INFO - BTRUN: pe_volume                        766500
2025-05-21 14:59:56,044 - __main__ - INFO - BTRUN: pe_oi                           7853325
2025-05-21 14:59:56,044 - __main__ - INFO - BTRUN: pe_coi                                0
2025-05-21 14:59:56,044 - __main__ - INFO - BTRUN: pe_iv                             12.41
2025-05-21 14:59:56,045 - __main__ - INFO - BTRUN: pe_delta                          -0.45
2025-05-21 14:59:56,045 - __main__ - INFO - BTRUN: pe_gamma                          0.002
2025-05-21 14:59:56,045 - __main__ - INFO - BTRUN: pe_theta                         -20.43
2025-05-21 14:59:56,045 - __main__ - INFO - BTRUN: pe_vega                            6.19
2025-05-21 14:59:56,045 - __main__ - INFO - BTRUN: pe_rho                         -47.8132
2025-05-21 14:59:56,045 - __main__ - INFO - BTRUN: future_open                    23386.95
2025-05-21 14:59:56,046 - __main__ - INFO - BTRUN: future_high                     23394.9
2025-05-21 14:59:56,046 - __main__ - INFO - BTRUN: future_low                     23383.35
2025-05-21 14:59:56,046 - __main__ - INFO - BTRUN: future_close                    23390.0
2025-05-21 14:59:56,046 - __main__ - INFO - BTRUN: future_volume                      2475
2025-05-21 14:59:56,046 - __main__ - INFO - BTRUN: future_oi                      12651900
2025-05-21 14:59:56,047 - __main__ - INFO - BTRUN: future_coi                            0
2025-05-21 14:59:56,047 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,033 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 14:59:56,047 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,033 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-21 14:59:56,047 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,033 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 14:59:56,047 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,034 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140374028661168
2025-05-21 14:59:56,047 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,034 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140374028661168, OptionType.PUT ID: 140374028661280
2025-05-21 14:59:56,047 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,034 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 14:59:56,047 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,034 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-21 14:59:56,048 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,034 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-21 14:59:56,048 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,035 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
2025-05-21 14:59:56,048 - __main__ - INFO - BTRUN: WHERE index_name = 'NIFTY'
2025-05-21 14:59:56,048 - __main__ - INFO - BTRUN: AND trade_date >= '2025-04-02' AND trade_date <= '2025-04-02'
2025-05-21 14:59:56,048 - __main__ - INFO - BTRUN: ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 14:59:56,328 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,327 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.292s, returned 18845 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 14:59:56,329 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,329 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18845 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-21 14:59:56,330 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,330 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-21 14:59:56,386 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,385 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 14:59:56,395 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,395 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
2025-05-21 14:59:56,395 - __main__ - INFO - BTRUN: SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:56,395 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
2025-05-21 14:59:56,395 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time ASC
2025-05-21 14:59:56,395 - __main__ - INFO - BTRUN: LIMIT 1
2025-05-21 14:59:56,395 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,395 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
2025-05-21 14:59:56,396 - __main__ - INFO - BTRUN: SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:56,396 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '12:00:00'
2025-05-21 14:59:56,396 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time DESC
2025-05-21 14:59:56,396 - __main__ - INFO - BTRUN: LIMIT 1
2025-05-21 14:59:56,425 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,424 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.029s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:56,425 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
2025-05-21 14:59:56,425 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 14:59:56,452 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,452 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.027s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:56,452 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''12:00:00''
2025-05-21 14:59:56,452 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 14:59:56,453 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,453 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
2025-05-21 14:59:56,453 - __main__ - INFO - BTRUN: trade_date                   2025-04-02
2025-05-21 14:59:56,453 - __main__ - INFO - BTRUN: trade_time                     09:16:00
2025-05-21 14:59:56,454 - __main__ - INFO - BTRUN: expiry_date                  2025-04-03
2025-05-21 14:59:56,454 - __main__ - INFO - BTRUN: index_name                        NIFTY
2025-05-21 14:59:56,454 - __main__ - INFO - BTRUN: spot                            23204.8
2025-05-21 14:59:56,454 - __main__ - INFO - BTRUN: atm_strike                      23200.0
2025-05-21 14:59:56,454 - __main__ - INFO - BTRUN: strike                          23200.0
2025-05-21 14:59:56,454 - __main__ - INFO - BTRUN: dte                                   1
2025-05-21 14:59:56,454 - __main__ - INFO - BTRUN: expiry_bucket                        CW
2025-05-21 14:59:56,454 - __main__ - INFO - BTRUN: zone_id                               1
2025-05-21 14:59:56,454 - __main__ - INFO - BTRUN: zone_name                          OPEN
2025-05-21 14:59:56,455 - __main__ - INFO - BTRUN: call_strike_type                    ATM
2025-05-21 14:59:56,455 - __main__ - INFO - BTRUN: put_strike_type                     ATM
2025-05-21 14:59:56,455 - __main__ - INFO - BTRUN: ce_symbol           NIFTY03APR2523200CE
2025-05-21 14:59:56,455 - __main__ - INFO - BTRUN: ce_open                          107.95
2025-05-21 14:59:56,455 - __main__ - INFO - BTRUN: ce_high                           113.5
2025-05-21 14:59:56,455 - __main__ - INFO - BTRUN: ce_low                           105.15
2025-05-21 14:59:56,455 - __main__ - INFO - BTRUN: ce_close                          110.5
2025-05-21 14:59:56,455 - __main__ - INFO - BTRUN: ce_volume                       2666850
2025-05-21 14:59:56,455 - __main__ - INFO - BTRUN: ce_oi                           5862000
2025-05-21 14:59:56,455 - __main__ - INFO - BTRUN: ce_coi                                0
2025-05-21 14:59:56,456 - __main__ - INFO - BTRUN: ce_iv                             16.53
2025-05-21 14:59:56,456 - __main__ - INFO - BTRUN: ce_delta                           0.53
2025-05-21 14:59:56,456 - __main__ - INFO - BTRUN: ce_gamma                         0.0015
2025-05-21 14:59:56,456 - __main__ - INFO - BTRUN: ce_theta                         -34.48
2025-05-21 14:59:56,456 - __main__ - INFO - BTRUN: ce_vega                             6.2
2025-05-21 14:59:56,456 - __main__ - INFO - BTRUN: ce_rho                          54.4841
2025-05-21 14:59:56,456 - __main__ - INFO - BTRUN: pe_symbol           NIFTY03APR2523200PE
2025-05-21 14:59:56,456 - __main__ - INFO - BTRUN: pe_open                            86.9
2025-05-21 14:59:56,456 - __main__ - INFO - BTRUN: pe_high                            94.9
2025-05-21 14:59:56,456 - __main__ - INFO - BTRUN: pe_low                            85.55
2025-05-21 14:59:56,457 - __main__ - INFO - BTRUN: pe_close                          86.15
2025-05-21 14:59:56,457 - __main__ - INFO - BTRUN: pe_volume                       3014850
2025-05-21 14:59:56,457 - __main__ - INFO - BTRUN: pe_oi                           5689575
2025-05-21 14:59:56,457 - __main__ - INFO - BTRUN: pe_coi                                0
2025-05-21 14:59:56,457 - __main__ - INFO - BTRUN: pe_iv                             15.06
2025-05-21 14:59:56,457 - __main__ - INFO - BTRUN: pe_delta                          -0.47
2025-05-21 14:59:56,457 - __main__ - INFO - BTRUN: pe_gamma                         0.0017
2025-05-21 14:59:56,457 - __main__ - INFO - BTRUN: pe_theta                         -25.36
2025-05-21 14:59:56,457 - __main__ - INFO - BTRUN: pe_vega                             6.2
2025-05-21 14:59:56,457 - __main__ - INFO - BTRUN: pe_rho                         -49.7483
2025-05-21 14:59:56,457 - __main__ - INFO - BTRUN: future_open                     23339.0
2025-05-21 14:59:56,458 - __main__ - INFO - BTRUN: future_high                     23344.7
2025-05-21 14:59:56,458 - __main__ - INFO - BTRUN: future_low                     23331.25
2025-05-21 14:59:56,458 - __main__ - INFO - BTRUN: future_close                   23344.15
2025-05-21 14:59:56,458 - __main__ - INFO - BTRUN: future_volume                     43725
2025-05-21 14:59:56,458 - __main__ - INFO - BTRUN: future_oi                      12808425
2025-05-21 14:59:56,458 - __main__ - INFO - BTRUN: future_coi                            0
2025-05-21 14:59:56,458 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,454 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
2025-05-21 14:59:56,458 - __main__ - INFO - BTRUN: trade_date                   2025-04-02
2025-05-21 14:59:56,458 - __main__ - INFO - BTRUN: trade_time                     12:00:00
2025-05-21 14:59:56,459 - __main__ - INFO - BTRUN: expiry_date                  2025-04-03
2025-05-21 14:59:56,459 - __main__ - INFO - BTRUN: index_name                        NIFTY
2025-05-21 14:59:56,459 - __main__ - INFO - BTRUN: spot                           23261.45
2025-05-21 14:59:56,459 - __main__ - INFO - BTRUN: atm_strike                      23250.0
2025-05-21 14:59:56,459 - __main__ - INFO - BTRUN: strike                          23250.0
2025-05-21 14:59:56,459 - __main__ - INFO - BTRUN: dte                                   1
2025-05-21 14:59:56,459 - __main__ - INFO - BTRUN: expiry_bucket                        CW
2025-05-21 14:59:56,459 - __main__ - INFO - BTRUN: zone_id                               2
2025-05-21 14:59:56,459 - __main__ - INFO - BTRUN: zone_name                      MID_MORN
2025-05-21 14:59:56,459 - __main__ - INFO - BTRUN: call_strike_type                    ATM
2025-05-21 14:59:56,460 - __main__ - INFO - BTRUN: put_strike_type                     ATM
2025-05-21 14:59:56,460 - __main__ - INFO - BTRUN: ce_symbol           NIFTY03APR2523250CE
2025-05-21 14:59:56,460 - __main__ - INFO - BTRUN: ce_open                            83.1
2025-05-21 14:59:56,460 - __main__ - INFO - BTRUN: ce_high                            87.9
2025-05-21 14:59:56,460 - __main__ - INFO - BTRUN: ce_low                             83.1
2025-05-21 14:59:56,461 - __main__ - INFO - BTRUN: ce_close                           87.0
2025-05-21 14:59:56,461 - __main__ - INFO - BTRUN: ce_volume                        546750
2025-05-21 14:59:56,461 - __main__ - INFO - BTRUN: ce_oi                           6733800
2025-05-21 14:59:56,461 - __main__ - INFO - BTRUN: ce_coi                                0
2025-05-21 14:59:56,461 - __main__ - INFO - BTRUN: ce_iv                             12.13
2025-05-21 14:59:56,461 - __main__ - INFO - BTRUN: ce_delta                           0.55
2025-05-21 14:59:56,462 - __main__ - INFO - BTRUN: ce_gamma                         0.0021
2025-05-21 14:59:56,462 - __main__ - INFO - BTRUN: ce_theta                         -26.28
2025-05-21 14:59:56,462 - __main__ - INFO - BTRUN: ce_vega                            6.18
2025-05-21 14:59:56,462 - __main__ - INFO - BTRUN: ce_rho                           57.017
2025-05-21 14:59:56,462 - __main__ - INFO - BTRUN: pe_symbol           NIFTY03APR2523250PE
2025-05-21 14:59:56,463 - __main__ - INFO - BTRUN: pe_open                           70.65
2025-05-21 14:59:56,463 - __main__ - INFO - BTRUN: pe_high                           70.65
2025-05-21 14:59:56,463 - __main__ - INFO - BTRUN: pe_low                             65.7
2025-05-21 14:59:56,463 - __main__ - INFO - BTRUN: pe_close                           66.8
2025-05-21 14:59:56,463 - __main__ - INFO - BTRUN: pe_volume                        766500
2025-05-21 14:59:56,463 - __main__ - INFO - BTRUN: pe_oi                           7853325
2025-05-21 14:59:56,464 - __main__ - INFO - BTRUN: pe_coi                                0
2025-05-21 14:59:56,464 - __main__ - INFO - BTRUN: pe_iv                             12.41
2025-05-21 14:59:56,464 - __main__ - INFO - BTRUN: pe_delta                          -0.45
2025-05-21 14:59:56,464 - __main__ - INFO - BTRUN: pe_gamma                          0.002
2025-05-21 14:59:56,464 - __main__ - INFO - BTRUN: pe_theta                         -20.43
2025-05-21 14:59:56,464 - __main__ - INFO - BTRUN: pe_vega                            6.19
2025-05-21 14:59:56,465 - __main__ - INFO - BTRUN: pe_rho                         -47.8132
2025-05-21 14:59:56,465 - __main__ - INFO - BTRUN: future_open                    23386.95
2025-05-21 14:59:56,465 - __main__ - INFO - BTRUN: future_high                     23394.9
2025-05-21 14:59:56,465 - __main__ - INFO - BTRUN: future_low                     23383.35
2025-05-21 14:59:56,465 - __main__ - INFO - BTRUN: future_close                    23390.0
2025-05-21 14:59:56,465 - __main__ - INFO - BTRUN: future_volume                      2475
2025-05-21 14:59:56,466 - __main__ - INFO - BTRUN: future_oi                      12651900
2025-05-21 14:59:56,466 - __main__ - INFO - BTRUN: future_coi                            0
2025-05-21 14:59:56,466 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,454 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 14:59:56,466 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,454 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-21 14:59:56,466 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,454 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 14:59:56,467 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,454 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140374028661280
2025-05-21 14:59:56,467 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,454 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140374028661168, OptionType.PUT ID: 140374028661280
2025-05-21 14:59:56,467 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,454 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 14:59:56,467 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,455 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-21 14:59:56,467 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,455 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-21 14:59:56,468 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,456 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
2025-05-21 14:59:56,468 - __main__ - INFO - BTRUN: WHERE index_name = 'NIFTY'
2025-05-21 14:59:56,468 - __main__ - INFO - BTRUN: AND trade_date >= '2025-04-02' AND trade_date <= '2025-04-02'
2025-05-21 14:59:56,468 - __main__ - INFO - BTRUN: ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 14:59:56,742 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,742 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.286s, returned 18845 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 14:59:56,744 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,743 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18845 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-21 14:59:56,744 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,744 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-21 14:59:56,800 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,800 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 14:59:56,826 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,826 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
2025-05-21 14:59:56,826 - __main__ - INFO - BTRUN: SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:56,827 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
2025-05-21 14:59:56,827 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time ASC
2025-05-21 14:59:56,827 - __main__ - INFO - BTRUN: LIMIT 1
2025-05-21 14:59:56,827 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,826 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
2025-05-21 14:59:56,827 - __main__ - INFO - BTRUN: SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:56,827 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '12:00:00'
2025-05-21 14:59:56,827 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time DESC
2025-05-21 14:59:56,827 - __main__ - INFO - BTRUN: LIMIT 1
2025-05-21 14:59:56,868 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,868 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.041s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:56,868 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
2025-05-21 14:59:56,868 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 14:59:56,907 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,907 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.039s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:56,907 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''12:00:00''
2025-05-21 14:59:56,908 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 14:59:56,908 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,908 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
2025-05-21 14:59:56,909 - __main__ - INFO - BTRUN: trade_date                   2025-04-02
2025-05-21 14:59:56,909 - __main__ - INFO - BTRUN: trade_time                     09:16:00
2025-05-21 14:59:56,909 - __main__ - INFO - BTRUN: expiry_date                  2025-04-03
2025-05-21 14:59:56,909 - __main__ - INFO - BTRUN: index_name                        NIFTY
2025-05-21 14:59:56,909 - __main__ - INFO - BTRUN: spot                            23204.8
2025-05-21 14:59:56,909 - __main__ - INFO - BTRUN: atm_strike                      23200.0
2025-05-21 14:59:56,909 - __main__ - INFO - BTRUN: strike                          23200.0
2025-05-21 14:59:56,909 - __main__ - INFO - BTRUN: dte                                   1
2025-05-21 14:59:56,909 - __main__ - INFO - BTRUN: expiry_bucket                        CW
2025-05-21 14:59:56,909 - __main__ - INFO - BTRUN: zone_id                               1
2025-05-21 14:59:56,910 - __main__ - INFO - BTRUN: zone_name                          OPEN
2025-05-21 14:59:56,910 - __main__ - INFO - BTRUN: call_strike_type                    ATM
2025-05-21 14:59:56,910 - __main__ - INFO - BTRUN: put_strike_type                     ATM
2025-05-21 14:59:56,910 - __main__ - INFO - BTRUN: ce_symbol           NIFTY03APR2523200CE
2025-05-21 14:59:56,910 - __main__ - INFO - BTRUN: ce_open                          107.95
2025-05-21 14:59:56,910 - __main__ - INFO - BTRUN: ce_high                           113.5
2025-05-21 14:59:56,910 - __main__ - INFO - BTRUN: ce_low                           105.15
2025-05-21 14:59:56,910 - __main__ - INFO - BTRUN: ce_close                          110.5
2025-05-21 14:59:56,910 - __main__ - INFO - BTRUN: ce_volume                       2666850
2025-05-21 14:59:56,911 - __main__ - INFO - BTRUN: ce_oi                           5862000
2025-05-21 14:59:56,911 - __main__ - INFO - BTRUN: ce_coi                                0
2025-05-21 14:59:56,911 - __main__ - INFO - BTRUN: ce_iv                             16.53
2025-05-21 14:59:56,911 - __main__ - INFO - BTRUN: ce_delta                           0.53
2025-05-21 14:59:56,911 - __main__ - INFO - BTRUN: ce_gamma                         0.0015
2025-05-21 14:59:56,911 - __main__ - INFO - BTRUN: ce_theta                         -34.48
2025-05-21 14:59:56,911 - __main__ - INFO - BTRUN: ce_vega                             6.2
2025-05-21 14:59:56,911 - __main__ - INFO - BTRUN: ce_rho                          54.4841
2025-05-21 14:59:56,911 - __main__ - INFO - BTRUN: pe_symbol           NIFTY03APR2523200PE
2025-05-21 14:59:56,911 - __main__ - INFO - BTRUN: pe_open                            86.9
2025-05-21 14:59:56,912 - __main__ - INFO - BTRUN: pe_high                            94.9
2025-05-21 14:59:56,912 - __main__ - INFO - BTRUN: pe_low                            85.55
2025-05-21 14:59:56,912 - __main__ - INFO - BTRUN: pe_close                          86.15
2025-05-21 14:59:56,912 - __main__ - INFO - BTRUN: pe_volume                       3014850
2025-05-21 14:59:56,912 - __main__ - INFO - BTRUN: pe_oi                           5689575
2025-05-21 14:59:56,912 - __main__ - INFO - BTRUN: pe_coi                                0
2025-05-21 14:59:56,912 - __main__ - INFO - BTRUN: pe_iv                             15.06
2025-05-21 14:59:56,912 - __main__ - INFO - BTRUN: pe_delta                          -0.47
2025-05-21 14:59:56,912 - __main__ - INFO - BTRUN: pe_gamma                         0.0017
2025-05-21 14:59:56,912 - __main__ - INFO - BTRUN: pe_theta                         -25.36
2025-05-21 14:59:56,913 - __main__ - INFO - BTRUN: pe_vega                             6.2
2025-05-21 14:59:56,913 - __main__ - INFO - BTRUN: pe_rho                         -49.7483
2025-05-21 14:59:56,913 - __main__ - INFO - BTRUN: future_open                     23339.0
2025-05-21 14:59:56,913 - __main__ - INFO - BTRUN: future_high                     23344.7
2025-05-21 14:59:56,913 - __main__ - INFO - BTRUN: future_low                     23331.25
2025-05-21 14:59:56,913 - __main__ - INFO - BTRUN: future_close                   23344.15
2025-05-21 14:59:56,913 - __main__ - INFO - BTRUN: future_volume                     43725
2025-05-21 14:59:56,913 - __main__ - INFO - BTRUN: future_oi                      12808425
2025-05-21 14:59:56,913 - __main__ - INFO - BTRUN: future_coi                            0
2025-05-21 14:59:56,913 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,909 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
2025-05-21 14:59:56,914 - __main__ - INFO - BTRUN: trade_date                   2025-04-02
2025-05-21 14:59:56,914 - __main__ - INFO - BTRUN: trade_time                     12:00:00
2025-05-21 14:59:56,914 - __main__ - INFO - BTRUN: expiry_date                  2025-04-03
2025-05-21 14:59:56,914 - __main__ - INFO - BTRUN: index_name                        NIFTY
2025-05-21 14:59:56,914 - __main__ - INFO - BTRUN: spot                           23261.45
2025-05-21 14:59:56,914 - __main__ - INFO - BTRUN: atm_strike                      23250.0
2025-05-21 14:59:56,914 - __main__ - INFO - BTRUN: strike                          23250.0
2025-05-21 14:59:56,914 - __main__ - INFO - BTRUN: dte                                   1
2025-05-21 14:59:56,914 - __main__ - INFO - BTRUN: expiry_bucket                        CW
2025-05-21 14:59:56,914 - __main__ - INFO - BTRUN: zone_id                               2
2025-05-21 14:59:56,915 - __main__ - INFO - BTRUN: zone_name                      MID_MORN
2025-05-21 14:59:56,915 - __main__ - INFO - BTRUN: call_strike_type                    ATM
2025-05-21 14:59:56,915 - __main__ - INFO - BTRUN: put_strike_type                     ATM
2025-05-21 14:59:56,915 - __main__ - INFO - BTRUN: ce_symbol           NIFTY03APR2523250CE
2025-05-21 14:59:56,915 - __main__ - INFO - BTRUN: ce_open                            83.1
2025-05-21 14:59:56,915 - __main__ - INFO - BTRUN: ce_high                            87.9
2025-05-21 14:59:56,916 - __main__ - INFO - BTRUN: ce_low                             83.1
2025-05-21 14:59:56,916 - __main__ - INFO - BTRUN: ce_close                           87.0
2025-05-21 14:59:56,916 - __main__ - INFO - BTRUN: ce_volume                        546750
2025-05-21 14:59:56,916 - __main__ - INFO - BTRUN: ce_oi                           6733800
2025-05-21 14:59:56,916 - __main__ - INFO - BTRUN: ce_coi                                0
2025-05-21 14:59:56,916 - __main__ - INFO - BTRUN: ce_iv                             12.13
2025-05-21 14:59:56,917 - __main__ - INFO - BTRUN: ce_delta                           0.55
2025-05-21 14:59:56,917 - __main__ - INFO - BTRUN: ce_gamma                         0.0021
2025-05-21 14:59:56,917 - __main__ - INFO - BTRUN: ce_theta                         -26.28
2025-05-21 14:59:56,917 - __main__ - INFO - BTRUN: ce_vega                            6.18
2025-05-21 14:59:56,917 - __main__ - INFO - BTRUN: ce_rho                           57.017
2025-05-21 14:59:56,918 - __main__ - INFO - BTRUN: pe_symbol           NIFTY03APR2523250PE
2025-05-21 14:59:56,918 - __main__ - INFO - BTRUN: pe_open                           70.65
2025-05-21 14:59:56,918 - __main__ - INFO - BTRUN: pe_high                           70.65
2025-05-21 14:59:56,918 - __main__ - INFO - BTRUN: pe_low                             65.7
2025-05-21 14:59:56,918 - __main__ - INFO - BTRUN: pe_close                           66.8
2025-05-21 14:59:56,919 - __main__ - INFO - BTRUN: pe_volume                        766500
2025-05-21 14:59:56,919 - __main__ - INFO - BTRUN: pe_oi                           7853325
2025-05-21 14:59:56,919 - __main__ - INFO - BTRUN: pe_coi                                0
2025-05-21 14:59:56,919 - __main__ - INFO - BTRUN: pe_iv                             12.41
2025-05-21 14:59:56,919 - __main__ - INFO - BTRUN: pe_delta                          -0.45
2025-05-21 14:59:56,919 - __main__ - INFO - BTRUN: pe_gamma                          0.002
2025-05-21 14:59:56,920 - __main__ - INFO - BTRUN: pe_theta                         -20.43
2025-05-21 14:59:56,920 - __main__ - INFO - BTRUN: pe_vega                            6.19
2025-05-21 14:59:56,920 - __main__ - INFO - BTRUN: pe_rho                         -47.8132
2025-05-21 14:59:56,920 - __main__ - INFO - BTRUN: future_open                    23386.95
2025-05-21 14:59:56,920 - __main__ - INFO - BTRUN: future_high                     23394.9
2025-05-21 14:59:56,921 - __main__ - INFO - BTRUN: future_low                     23383.35
2025-05-21 14:59:56,921 - __main__ - INFO - BTRUN: future_close                    23390.0
2025-05-21 14:59:56,921 - __main__ - INFO - BTRUN: future_volume                      2475
2025-05-21 14:59:56,921 - __main__ - INFO - BTRUN: future_oi                      12651900
2025-05-21 14:59:56,921 - __main__ - INFO - BTRUN: future_coi                            0
2025-05-21 14:59:56,921 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,909 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 14:59:56,922 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,909 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-21 14:59:56,922 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,910 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 14:59:56,922 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,910 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140374028661168
2025-05-21 14:59:56,922 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,910 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140374028661168, OptionType.PUT ID: 140374028661280
2025-05-21 14:59:56,922 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,910 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 14:59:56,923 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,910 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-21 14:59:56,923 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,910 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-21 14:59:56,923 - __main__ - INFO - BTRUN: 2025-05-21 14:59:56,911 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
2025-05-21 14:59:56,923 - __main__ - INFO - BTRUN: WHERE index_name = 'NIFTY'
2025-05-21 14:59:56,923 - __main__ - INFO - BTRUN: AND trade_date >= '2025-04-02' AND trade_date <= '2025-04-02'
2025-05-21 14:59:56,924 - __main__ - INFO - BTRUN: ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 14:59:57,201 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,201 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.290s, returned 18845 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 14:59:57,203 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,203 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18845 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-21 14:59:57,204 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,203 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-21 14:59:57,257 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,257 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 14:59:57,275 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,275 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
2025-05-21 14:59:57,276 - __main__ - INFO - BTRUN: SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:57,276 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
2025-05-21 14:59:57,276 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time ASC
2025-05-21 14:59:57,276 - __main__ - INFO - BTRUN: LIMIT 1
2025-05-21 14:59:57,276 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,275 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
2025-05-21 14:59:57,276 - __main__ - INFO - BTRUN: SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:57,276 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '12:00:00'
2025-05-21 14:59:57,276 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time DESC
2025-05-21 14:59:57,276 - __main__ - INFO - BTRUN: LIMIT 1
2025-05-21 14:59:57,305 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,305 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.029s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:57,305 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
2025-05-21 14:59:57,305 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 14:59:57,345 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,345 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.040s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
2025-05-21 14:59:57,345 - __main__ - INFO - BTRUN: WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''12:00:00''
2025-05-21 14:59:57,345 - __main__ - INFO - BTRUN: ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 14:59:57,346 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,346 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
2025-05-21 14:59:57,346 - __main__ - INFO - BTRUN: trade_date                   2025-04-02
2025-05-21 14:59:57,346 - __main__ - INFO - BTRUN: trade_time                     09:16:00
2025-05-21 14:59:57,346 - __main__ - INFO - BTRUN: expiry_date                  2025-04-03
2025-05-21 14:59:57,346 - __main__ - INFO - BTRUN: index_name                        NIFTY
2025-05-21 14:59:57,347 - __main__ - INFO - BTRUN: spot                            23204.8
2025-05-21 14:59:57,347 - __main__ - INFO - BTRUN: atm_strike                      23200.0
2025-05-21 14:59:57,347 - __main__ - INFO - BTRUN: strike                          23200.0
2025-05-21 14:59:57,347 - __main__ - INFO - BTRUN: dte                                   1
2025-05-21 14:59:57,347 - __main__ - INFO - BTRUN: expiry_bucket                        CW
2025-05-21 14:59:57,347 - __main__ - INFO - BTRUN: zone_id                               1
2025-05-21 14:59:57,347 - __main__ - INFO - BTRUN: zone_name                          OPEN
2025-05-21 14:59:57,347 - __main__ - INFO - BTRUN: call_strike_type                    ATM
2025-05-21 14:59:57,347 - __main__ - INFO - BTRUN: put_strike_type                     ATM
2025-05-21 14:59:57,347 - __main__ - INFO - BTRUN: ce_symbol           NIFTY03APR2523200CE
2025-05-21 14:59:57,348 - __main__ - INFO - BTRUN: ce_open                          107.95
2025-05-21 14:59:57,348 - __main__ - INFO - BTRUN: ce_high                           113.5
2025-05-21 14:59:57,348 - __main__ - INFO - BTRUN: ce_low                           105.15
2025-05-21 14:59:57,348 - __main__ - INFO - BTRUN: ce_close                          110.5
2025-05-21 14:59:57,348 - __main__ - INFO - BTRUN: ce_volume                       2666850
2025-05-21 14:59:57,348 - __main__ - INFO - BTRUN: ce_oi                           5862000
2025-05-21 14:59:57,348 - __main__ - INFO - BTRUN: ce_coi                                0
2025-05-21 14:59:57,348 - __main__ - INFO - BTRUN: ce_iv                             16.53
2025-05-21 14:59:57,348 - __main__ - INFO - BTRUN: ce_delta                           0.53
2025-05-21 14:59:57,348 - __main__ - INFO - BTRUN: ce_gamma                         0.0015
2025-05-21 14:59:57,349 - __main__ - INFO - BTRUN: ce_theta                         -34.48
2025-05-21 14:59:57,349 - __main__ - INFO - BTRUN: ce_vega                             6.2
2025-05-21 14:59:57,349 - __main__ - INFO - BTRUN: ce_rho                          54.4841
2025-05-21 14:59:57,349 - __main__ - INFO - BTRUN: pe_symbol           NIFTY03APR2523200PE
2025-05-21 14:59:57,349 - __main__ - INFO - BTRUN: pe_open                            86.9
2025-05-21 14:59:57,349 - __main__ - INFO - BTRUN: pe_high                            94.9
2025-05-21 14:59:57,349 - __main__ - INFO - BTRUN: pe_low                            85.55
2025-05-21 14:59:57,349 - __main__ - INFO - BTRUN: pe_close                          86.15
2025-05-21 14:59:57,349 - __main__ - INFO - BTRUN: pe_volume                       3014850
2025-05-21 14:59:57,349 - __main__ - INFO - BTRUN: pe_oi                           5689575
2025-05-21 14:59:57,350 - __main__ - INFO - BTRUN: pe_coi                                0
2025-05-21 14:59:57,350 - __main__ - INFO - BTRUN: pe_iv                             15.06
2025-05-21 14:59:57,350 - __main__ - INFO - BTRUN: pe_delta                          -0.47
2025-05-21 14:59:57,350 - __main__ - INFO - BTRUN: pe_gamma                         0.0017
2025-05-21 14:59:57,350 - __main__ - INFO - BTRUN: pe_theta                         -25.36
2025-05-21 14:59:57,350 - __main__ - INFO - BTRUN: pe_vega                             6.2
2025-05-21 14:59:57,350 - __main__ - INFO - BTRUN: pe_rho                         -49.7483
2025-05-21 14:59:57,350 - __main__ - INFO - BTRUN: future_open                     23339.0
2025-05-21 14:59:57,350 - __main__ - INFO - BTRUN: future_high                     23344.7
2025-05-21 14:59:57,350 - __main__ - INFO - BTRUN: future_low                     23331.25
2025-05-21 14:59:57,351 - __main__ - INFO - BTRUN: future_close                   23344.15
2025-05-21 14:59:57,351 - __main__ - INFO - BTRUN: future_volume                     43725
2025-05-21 14:59:57,351 - __main__ - INFO - BTRUN: future_oi                      12808425
2025-05-21 14:59:57,351 - __main__ - INFO - BTRUN: future_coi                            0
2025-05-21 14:59:57,351 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,346 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
2025-05-21 14:59:57,351 - __main__ - INFO - BTRUN: trade_date                   2025-04-02
2025-05-21 14:59:57,351 - __main__ - INFO - BTRUN: trade_time                     12:00:00
2025-05-21 14:59:57,351 - __main__ - INFO - BTRUN: expiry_date                  2025-04-03
2025-05-21 14:59:57,351 - __main__ - INFO - BTRUN: index_name                        NIFTY
2025-05-21 14:59:57,351 - __main__ - INFO - BTRUN: spot                           23261.45
2025-05-21 14:59:57,352 - __main__ - INFO - BTRUN: atm_strike                      23250.0
2025-05-21 14:59:57,352 - __main__ - INFO - BTRUN: strike                          23250.0
2025-05-21 14:59:57,352 - __main__ - INFO - BTRUN: dte                                   1
2025-05-21 14:59:57,352 - __main__ - INFO - BTRUN: expiry_bucket                        CW
2025-05-21 14:59:57,352 - __main__ - INFO - BTRUN: zone_id                               2
2025-05-21 14:59:57,352 - __main__ - INFO - BTRUN: zone_name                      MID_MORN
2025-05-21 14:59:57,352 - __main__ - INFO - BTRUN: call_strike_type                    ATM
2025-05-21 14:59:57,352 - __main__ - INFO - BTRUN: put_strike_type                     ATM
2025-05-21 14:59:57,353 - __main__ - INFO - BTRUN: ce_symbol           NIFTY03APR2523250CE
2025-05-21 14:59:57,353 - __main__ - INFO - BTRUN: ce_open                            83.1
2025-05-21 14:59:57,353 - __main__ - INFO - BTRUN: ce_high                            87.9
2025-05-21 14:59:57,353 - __main__ - INFO - BTRUN: ce_low                             83.1
2025-05-21 14:59:57,353 - __main__ - INFO - BTRUN: ce_close                           87.0
2025-05-21 14:59:57,353 - __main__ - INFO - BTRUN: ce_volume                        546750
2025-05-21 14:59:57,354 - __main__ - INFO - BTRUN: ce_oi                           6733800
2025-05-21 14:59:57,354 - __main__ - INFO - BTRUN: ce_coi                                0
2025-05-21 14:59:57,354 - __main__ - INFO - BTRUN: ce_iv                             12.13
2025-05-21 14:59:57,354 - __main__ - INFO - BTRUN: ce_delta                           0.55
2025-05-21 14:59:57,354 - __main__ - INFO - BTRUN: ce_gamma                         0.0021
2025-05-21 14:59:57,355 - __main__ - INFO - BTRUN: ce_theta                         -26.28
2025-05-21 14:59:57,355 - __main__ - INFO - BTRUN: ce_vega                            6.18
2025-05-21 14:59:57,355 - __main__ - INFO - BTRUN: ce_rho                           57.017
2025-05-21 14:59:57,355 - __main__ - INFO - BTRUN: pe_symbol           NIFTY03APR2523250PE
2025-05-21 14:59:57,355 - __main__ - INFO - BTRUN: pe_open                           70.65
2025-05-21 14:59:57,355 - __main__ - INFO - BTRUN: pe_high                           70.65
2025-05-21 14:59:57,356 - __main__ - INFO - BTRUN: pe_low                             65.7
2025-05-21 14:59:57,356 - __main__ - INFO - BTRUN: pe_close                           66.8
2025-05-21 14:59:57,356 - __main__ - INFO - BTRUN: pe_volume                        766500
2025-05-21 14:59:57,356 - __main__ - INFO - BTRUN: pe_oi                           7853325
2025-05-21 14:59:57,356 - __main__ - INFO - BTRUN: pe_coi                                0
2025-05-21 14:59:57,356 - __main__ - INFO - BTRUN: pe_iv                             12.41
2025-05-21 14:59:57,357 - __main__ - INFO - BTRUN: pe_delta                          -0.45
2025-05-21 14:59:57,357 - __main__ - INFO - BTRUN: pe_gamma                          0.002
2025-05-21 14:59:57,357 - __main__ - INFO - BTRUN: pe_theta                         -20.43
2025-05-21 14:59:57,357 - __main__ - INFO - BTRUN: pe_vega                            6.19
2025-05-21 14:59:57,357 - __main__ - INFO - BTRUN: pe_rho                         -47.8132
2025-05-21 14:59:57,358 - __main__ - INFO - BTRUN: future_open                    23386.95
2025-05-21 14:59:57,358 - __main__ - INFO - BTRUN: future_high                     23394.9
2025-05-21 14:59:57,358 - __main__ - INFO - BTRUN: future_low                     23383.35
2025-05-21 14:59:57,358 - __main__ - INFO - BTRUN: future_close                    23390.0
2025-05-21 14:59:57,358 - __main__ - INFO - BTRUN: future_volume                      2475
2025-05-21 14:59:57,358 - __main__ - INFO - BTRUN: future_oi                      12651900
2025-05-21 14:59:57,359 - __main__ - INFO - BTRUN: future_coi                            0
2025-05-21 14:59:57,359 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,347 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 14:59:57,359 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,347 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-21 14:59:57,359 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,347 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 14:59:57,359 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,347 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140374028661280
2025-05-21 14:59:57,360 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,347 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140374028661168, OptionType.PUT ID: 140374028661280
2025-05-21 14:59:57,360 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,347 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 14:59:57,360 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,347 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-21 14:59:57,360 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,347 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-21 14:59:57,360 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,349 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
2025-05-21 14:59:57,361 - __main__ - INFO - BTRUN: WHERE index_name = 'NIFTY'
2025-05-21 14:59:57,361 - __main__ - INFO - BTRUN: AND trade_date >= '2025-04-02' AND trade_date <= '2025-04-02'
2025-05-21 14:59:57,361 - __main__ - INFO - BTRUN: ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 14:59:57,669 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,669 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.320s, returned 18845 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 14:59:57,671 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,671 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18845 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-21 14:59:57,672 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,672 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-21 14:59:57,729 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,729 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 14:59:57,738 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,738 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-21 14:59:57,738 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,738 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 2), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23204.8, 'atm_strike': 23200.0, 'strike': 23200.0, 'dte': 1, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523200CE', 'ce_open': 107.95, 'ce_high': 113.5, 'ce_low': 105.15, 'ce_close': 110.5, 'ce_volume': 2666850, 'ce_oi': 5862000, 'ce_coi': 0, 'ce_iv': 16.53, 'ce_delta': 0.53, 'ce_gamma': 0.0015, 'ce_theta': -34.48, 'ce_vega': 6.2, 'ce_rho': 54.4841, 'pe_symbol': 'NIFTY03APR2523200PE', 'pe_open': 86.9, 'pe_high': 94.9, 'pe_low': 85.55, 'pe_close': 86.15, 'pe_volume': 3014850, 'pe_oi': 5689575, 'pe_coi': 0, 'pe_iv': 15.06, 'pe_delta': -0.47, 'pe_gamma': 0.0017, 'pe_theta': -25.36, 'pe_vega': 6.2, 'pe_rho': -49.7483, 'future_open': 23339.0, 'future_high': 23344.7, 'future_low': 23331.25, 'future_close': 23344.15, 'future_volume': 43725, 'future_oi': 12808425, 'future_coi': 0}
2025-05-21 14:59:57,739 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,738 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 2), 'trade_time': 91700, 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23261.45, 'atm_strike': 23250.0, 'strike': 23250.0, 'dte': 1, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523250CE', 'ce_open': 83.1, 'ce_high': 87.9, 'ce_low': 83.1, 'ce_close': 87.0, 'ce_volume': 546750, 'ce_oi': 6733800, 'ce_coi': 0, 'ce_iv': 12.13, 'ce_delta': 0.55, 'ce_gamma': 0.0021, 'ce_theta': -26.28, 'ce_vega': 6.18, 'ce_rho': 57.017, 'pe_symbol': 'NIFTY03APR2523250PE', 'pe_open': 70.65, 'pe_high': 70.65, 'pe_low': 65.7, 'pe_close': 66.8, 'pe_volume': 766500, 'pe_oi': 7853325, 'pe_coi': 0, 'pe_iv': 12.41, 'pe_delta': -0.45, 'pe_gamma': 0.002, 'pe_theta': -20.43, 'pe_vega': 6.19, 'pe_rho': -47.8132, 'future_open': 23386.95, 'future_high': 23394.9, 'future_low': 23383.35, 'future_close': 23390.0, 'future_volume': 2475, 'future_oi': 12651900, 'future_coi': 0, 'close': 1.14, 'datetime': Timestamp('2025-04-02 09:17:00'), 'exit_reason': 'Stop Loss Hit'}
2025-05-21 14:59:57,739 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,738 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-21 14:59:57,739 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,739 - backtester_stable.BTRUN.trade_builder - ERROR - BUILD_TRADE_RECORD: Error parsing dates for exit time override: strptime() argument 1 must be str, not int
2025-05-21 14:59:57,739 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,739 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -967.5000000000005, PnL (Slippage): -967.5000000000005, Net PnL: -967.5000000000005
2025-05-21 14:59:57,739 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,739 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Stop Loss Hit
2025-05-21 14:59:57,739 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,739 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2025-04-02', 'entry_time': '09:16:00', 'entry_day': 'Wednesday', 'exit_date': '2025-04-02', 'exit_time': '09:17:00', 'exit_day': 'Wednesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23200, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 86.15, 'exit_price': 66.8, 'points': -19.35000000000001, 'pointsAfterSlippage': -19.35000000000001, 'pnl': -967.5000000000005, 'pnlAfterSlippage': -967.5000000000005, 'expenses': 0.0, 'netPnlAfterExpenses': -967.5000000000005, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Stop Loss Hit', 'strategy_entry_number': 0, 'index_entry_price': 23204.8, 'index_exit_price': 23261.45, 'max_profit': 0, 'max_loss': 0}
2025-05-21 14:59:57,739 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,739 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Number of trade records generated: 2
2025-05-21 14:59:57,739 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,739 - backtester_stable.BTRUN.heavydb_trade_processing - INFO - Generated 2 trade records via model-driven path
2025-05-21 14:59:57,740 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,740 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Closed reusable HeavyDB connection from get_trades_for_portfolio
2025-05-21 14:59:57,759 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,758 - backtester_stable.BTRUN.builders - WARNING - utils.MARGIN_INFO not found or empty - margin requirements will be 0. Attempting to populate.
2025-05-21 14:59:57,759 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,759 - backtester_stable.BTRUN.config - INFO - Found fixture 'margin_symbol_info.json' at /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-21 14:59:57,759 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,759 - backtester_stable.BTRUN.utils - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json (Broker: angel)
2025-05-21 14:59:57,759 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,759 - backtester_stable.BTRUN.utils - INFO - Successfully populated config.MARGIN_INFO for angel.
2025-05-21 14:59:57,759 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,759 - backtester_stable.BTRUN.builders - ERROR - utils.MARGIN_INFO still empty after populate attempt. Margin req will be 0.
2025-05-21 14:59:57,761 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,761 - backtester_stable.BTRUN.builders - DEBUG - Strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL margin requirement: 0.00
2025-05-21 14:59:57,761 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,761 - backtester_stable.BTRUN.builders - INFO - Total portfolio margin requirement: 0.00
2025-05-21 14:59:57,761 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,761 - backtester_stable.BTRUN.builders - DEBUG - Converting tick PnL to daywise DataFrame.
2025-05-21 14:59:57,762 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,762 - backtester_stable.BTRUN.builders - INFO - Backtest response parsed.
2025-05-21 14:59:57,793 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,793 - backtester_stable.BTRUN.runtime - INFO - Generated metrics_df with 50 records for strategies: ['Combined' 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL']
2025-05-21 14:59:57,854 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,853 - backtester_stable.BTRUN.runtime - INFO - Saving Excel output to /srv/samba/shared/Trades/detailed_test_20250521_145950.xlsx
2025-05-21 14:59:57,854 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,854 - backtester_stable.BTRUN.runtime - DEBUG - [DEBUG save_backtest_results] save_excel=True, metrics_df is empty: False
2025-05-21 14:59:57,854 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,854 - backtester_stable.BTRUN.io - INFO - 2025-05-21 14:59:57.854115, Started writing stats to excel file: /srv/samba/shared/Trades/detailed_test_20250521_145950.xlsx
2025-05-21 14:59:57,854 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,854 - backtester_stable.BTRUN.gpu_helpers - DEBUG - Temporarily switching to CPU-only mode.
2025-05-21 14:59:57,900 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,900 - backtester_stable.BTRUN.io - INFO - 2025-05-21 14:59:57.900790, Excel file prepared successfully, Time taken: 0.05s
2025-05-21 14:59:57,901 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,900 - backtester_stable.BTRUN.gpu_helpers - DEBUG - Restored GPU_ENABLED to: False
2025-05-21 14:59:57,901 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,900 - backtester_stable.BTRUN.runtime - INFO - Saving JSON output to /srv/samba/shared/Trades/detailed_test_20250521_145950.json
2025-05-21 14:59:57,920 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,920 - backtester_stable.BTRUN.io - INFO - --- JSON DUMP DEBUG START ---
2025-05-21 14:59:57,920 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,920 - backtester_stable.BTRUN.io - INFO - --- JSON DUMP DEBUG END ---
2025-05-21 14:59:57,921 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,920 - backtester_stable.BTRUN.io - INFO - Successfully wrote JSON output to /srv/samba/shared/Trades/detailed_test_20250521_145950.json
2025-05-21 14:59:57,921 - __main__ - INFO - BTRUN: 2025-05-21 14:59:57,921 - backtester_stable.BTRUN.io - WARNING - pyttsx3 not installed - skipping text-to-speech
2025-05-21 14:59:58,354 - __main__ - ERROR - STDERR: Partially restored config.py is being imported - Phase 10.
USING PARTIALLY RESTORED DEBUG CONFIG.PY - get_table_name active!
USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
ERROR:root:Failed to import BTRUN config module
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:197: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd.rename(columns={"entryDate": "Date", "bookedPnL": "PNL"}, inplace=True)
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:198: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['Date'] = pd.to_datetime(temp_df_pd['Date'])
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:199: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['year'] = temp_df_pd['Date'].dt.year
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:200: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['day_name'] = temp_df_pd['Date'].dt.strftime("%A")
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:235: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd.rename(columns={"entryDate": "Date", "bookedPnL": "PNL"}, inplace=True)
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:236: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['Date'] = pd.to_datetime(temp_df_pd['Date'])
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:237: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['year'] = temp_df_pd['Date'].dt.year
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:238: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['month_name'] = temp_df_pd['Date'].dt.strftime("%B") # Full month name
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:197: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd.rename(columns={"entryDate": "Date", "bookedPnL": "PNL"}, inplace=True)
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:198: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['Date'] = pd.to_datetime(temp_df_pd['Date'])
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:199: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['year'] = temp_df_pd['Date'].dt.year
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:200: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['day_name'] = temp_df_pd['Date'].dt.strftime("%A")
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:235: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd.rename(columns={"entryDate": "Date", "bookedPnL": "PNL"}, inplace=True)
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:236: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['Date'] = pd.to_datetime(temp_df_pd['Date'])
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:237: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['year'] = temp_df_pd['Date'].dt.year
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:238: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['month_name'] = temp_df_pd['Date'].dt.strftime("%B") # Full month name
/home/<USER>/.local/lib/python3.10/site-packages/openpyxl/workbook/child.py:99: UserWarning: Title is more than 31 characters. Some applications may not be able to read the file
  warnings.warn("Title is more than 31 characters. Some applications may not be able to read the file")

2025-05-21 14:59:58,354 - __main__ - INFO - Process exited with return code 0
2025-05-21 14:59:58,354 - __main__ - INFO - Backtester ran successfully, analyzing results...
2025-05-21 14:59:58,467 - __main__ - INFO - Found 2 trades in output file
2025-05-21 14:59:58,467 - __main__ - INFO - Exit time distribution: {'09:17:00': 2}
2025-05-21 14:59:58,468 - __main__ - INFO - Exit reason distribution: {'Stop Loss Hit': 2}
2025-05-21 14:59:58,469 - __main__ - ERROR - ❌ FAILURE: Only 0 of 2 trades exited at 12:00:00
2025-05-21 14:59:58,470 - __main__ - ERROR - Problem trade 0: Exit time = 09:17:00, Reason = Stop Loss Hit
2025-05-21 14:59:58,470 - __main__ - ERROR - Problem trade 1: Exit time = 09:17:00, Reason = Stop Loss Hit
2025-05-21 14:59:58,470 - __main__ - ERROR - === Test Failed: Incorrect exit times ===
2025-05-21 14:59:58,470 - __main__ - INFO - Detailed test output file: /srv/samba/shared/Trades/detailed_test_20250521_145950.xlsx
2025-05-21 14:59:58,470 - __main__ - INFO - Check the detailed_test_*.log file for complete logs
