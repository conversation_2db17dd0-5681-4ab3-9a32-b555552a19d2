2025-05-27 19:39:21,908 - tbs_parity - INFO - Starting TBS parity test with test_data/tbs_test_1day.xlsx
2025-05-27 19:39:21,908 - tbs_parity - INFO - Setting up test environment for date: 20240103
2025-05-27 19:39:21,908 - tbs_parity - INFO - Running legacy TBS backtester...
2025-05-27 19:39:21,909 - tbs_parity - INFO - Legacy command: cd bt/archive/backtester_stable/BTRUN && export MYSQL_HOST=localhost && python BTRunPortfolio.py --excel test_data/tbs_test_1day.xlsx --output debug_output/tbs/legacy_tbs_output.xlsx
2025-05-27 19:39:21,910 - tbs_parity - ERROR - Legacy backtester failed: Command 'cd bt/archive/backtester_stable/BTRUN && export MYSQL_HOST=localhost && python BTRunPortfolio.py --excel test_data/tbs_test_1day.xlsx --output debug_output/tbs/legacy_tbs_output.xlsx' returned non-zero exit status 127.
2025-05-27 19:39:21,910 - tbs_parity - INFO - Running GPU TBS backtester...
2025-05-27 19:39:21,910 - tbs_parity - INFO - GPU command: python -m bt.backtester_stable.BTRUN.BTRunPortfolio_GPU --legacy-excel --portfolio-excel test_data/tbs_test_1day.xlsx --output-path debug_output/tbs/gpu_tbs_output.xlsx
2025-05-27 19:44:55,206 - tbs_parity - INFO - Starting TBS parity test with test_data/tbs_test_1day.xlsx
2025-05-27 19:44:55,206 - tbs_parity - INFO - Setting up test environment for date: 20240103
2025-05-27 19:44:55,206 - tbs_parity - INFO - Running legacy TBS backtester...
2025-05-27 19:44:55,207 - tbs_parity - INFO - Legacy command: cd bt/archive/backtester_stable/BTRUN && export MYSQL_HOST=localhost && python3 BTRunPortfolio.py --excel test_data/tbs_test_1day.xlsx --output debug_output/tbs/legacy_tbs_output.xlsx
2025-05-27 19:44:57,789 - tbs_parity - INFO - Legacy backtester completed. Output: debug_output/tbs/legacy_tbs_output.xlsx
2025-05-27 19:44:57,789 - tbs_parity - INFO - Running GPU TBS backtester...
2025-05-27 19:44:57,789 - tbs_parity - INFO - GPU command: python3 -m bt.backtester_stable.BTRUN.BTRunPortfolio_GPU --legacy-excel --portfolio-excel test_data/tbs_test_1day.xlsx --output-path debug_output/tbs/gpu_tbs_output.xlsx
2025-05-27 19:45:01,056 - tbs_parity - INFO - GPU backtester completed. Output: debug_output/tbs/gpu_tbs_output.xlsx
2025-05-27 19:45:01,057 - tbs_parity - INFO - Comparing legacy and GPU results...
2025-05-27 19:45:01,058 - tbs_parity - ERROR - Comparison failed: [Errno 2] No such file or directory: 'debug_output/tbs/legacy_tbs_output.xlsx'
