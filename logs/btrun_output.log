Partially restored config.py is being imported - Phase 10.
USING PARTIALLY RESTORED DEBUG CONFIG.PY - get_table_name active!
USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
ERROR:root:Failed to import BTRUN config module
WARNING:backtester_stable.BTRUN.config:Fixture 'margin_symbol_info.json' not found in any of the standard locations. Returning constructed path based on INPUT_FILE_FOLDER.
WARNING:backtester_stable.BTRUN.utils:Margin symbol info file '/srv/samba/shared/tests/fixtures/mapping_excels/margin_symbol_info.json' not found. config.MARGIN_INFO will be empty.
WARNING:backtester_stable.BTRUN.builders:utils.MARGIN_INFO not found or empty - margin requirements will be 0. Attempting to populate.
WARNING:backtester_stable.BTRUN.config:Fixture 'margin_symbol_info.json' not found in any of the standard locations. Returning constructed path based on INPUT_FILE_FOLDER.
WARNING:backtester_stable.BTRUN.utils:Margin symbol info file '/srv/samba/shared/tests/fixtures/mapping_excels/margin_symbol_info.json' not found. config.MARGIN_INFO will be empty.
ERROR:backtester_stable.BTRUN.builders:utils.MARGIN_INFO still empty after populate attempt. Margin req will be 0.
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:197: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd.rename(columns={"entryDate": "Date", "bookedPnL": "PNL"}, inplace=True)
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:198: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['Date'] = pd.to_datetime(temp_df_pd['Date'])
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:199: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['year'] = temp_df_pd['Date'].dt.year
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:200: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['day_name'] = temp_df_pd['Date'].dt.strftime("%A")
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:235: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd.rename(columns={"entryDate": "Date", "bookedPnL": "PNL"}, inplace=True)
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:236: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['Date'] = pd.to_datetime(temp_df_pd['Date'])
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:237: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['year'] = temp_df_pd['Date'].dt.year
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:238: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['month_name'] = temp_df_pd['Date'].dt.strftime("%B") # Full month name
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:197: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd.rename(columns={"entryDate": "Date", "bookedPnL": "PNL"}, inplace=True)
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:198: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['Date'] = pd.to_datetime(temp_df_pd['Date'])
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:199: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['year'] = temp_df_pd['Date'].dt.year
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:200: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['day_name'] = temp_df_pd['Date'].dt.strftime("%A")
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:235: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd.rename(columns={"entryDate": "Date", "bookedPnL": "PNL"}, inplace=True)
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:236: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['Date'] = pd.to_datetime(temp_df_pd['Date'])
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:237: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['year'] = temp_df_pd['Date'].dt.year
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:238: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['month_name'] = temp_df_pd['Date'].dt.strftime("%B") # Full month name
WARNING:backtester_stable.BTRUN.io:pyttsx3 not installed - skipping text-to-speech
--- TEMP SNIPPET EXECUTING ---
TEMP SNIPPET: Attempting to read tests/fixtures/mapping_excels/test_main_portfolio.xlsx
TEMP_OUTPUT:PortfolioName=NIF0DTE
TEMP_OUTPUT:StartDate=01_04_2025
TEMP_OUTPUT:StrategyExcelFilePath=/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx
--- TEMP SNIPPET END ---
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
[DEBUG] run_full_backtest bt_params keys: ['portfolio_model', 'portfolio_name', 'start_date', 'end_date']
[DEBUG] bt_response keys: ['data']
