2025-05-25 13:38:13,497 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-25 13:38:13,497 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-25 13:38:13,497 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/../models', '/srv/samba/shared', '/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages', '/srv/samba/shared']
2025-05-25 13:38:13,497 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-25 13:38:13,497 - __main__ - DEBUG - GPU enabled: False
2025-05-25 13:38:13,499 - __main__ - INFO - GPU acceleration enabled: False
2025-05-25 13:38:13,499 - __main__ - INFO - HeavyDB integration available (Host: 127.0.0.1)
2025-05-25 13:38:13,715 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-25 13:38:13,715 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-25 13:38:13,716 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-25 13:38:13,716 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-25 13:38:13,716 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-25 13:38:13,716 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-25 13:38:13,721 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-25 13:38:13,722 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-25 13:38:13,722 - backtester_stable.BTRUN.core.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-25 13:38:13,722 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-25 13:38:13,723 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-25 13:38:13,723 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-25 13:38:13,723 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-25 13:38:13,723 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-25 13:38:13,723 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-25 13:38:13,723 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_fixed.xlsx
2025-05-25 13:38:13,723 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-25 13:38:13,723 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_fixed.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-25 13:38:13,826 - backtester_stable.BTRUN.core.config - WARNING - Portfolio file 'bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs_fixed.xlsx' not found in INPUT_FILE_FOLDER or original input_sheets. Returning path based on INPUT_FILE_FOLDER.
2025-05-25 13:38:13,847 - __main__ - INFO - Successfully parsed 1 portfolio(s) using modern parser.
2025-05-25 13:38:13,847 - __main__ - INFO - Queueing portfolio: NIF0DTE (from modern_portfolio_parser)
2025-05-25 13:38:13,848 - __main__ - INFO - Executing 1 portfolios sequentially (multiprocessing temporarily disabled for debugging)
2025-05-25 13:38:13,848 - __main__ - INFO - Processing payload 1/1: NIF0DTE
2025-05-25 13:38:13,848 - backtester_stable.BTRUN.core.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'NIF0DTE'
2025-05-25 13:38:14,068 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-25 13:38:14,087 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 563 rows. Query: SELECT SUBSTRING('SELECT DISTINCT trade_date FROM nifty_option_chain', 1, 200) ...
2025-05-25 13:38:14,131 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - Cached 563 distinct trade dates
2025-05-25 13:38:14,131 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] portfolio_model_dict keys: ['portfolio_name', 'start_date', 'end_date', 'slippage_percent', 'margin_multiplier', 'is_tick_bt', 'strategies', 'extra_params']
2025-05-25 13:38:14,132 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 13:38:14,132 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 13:38:14,158 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.025s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 13:38:14,182 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.024s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 13:38:14,183 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
trade_date                   2024-12-31
trade_time                     09:16:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                           23559.15
atm_strike                      23550.0
strike                          23550.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY02JAN2523550CE
ce_open                          135.05
ce_high                          139.69
ce_low                            133.4
ce_close                         139.69
ce_volume                        547275
ce_oi                            240150
ce_coi                                0
ce_iv                             15.78
ce_delta                           0.54
ce_gamma                         0.0013
ce_theta                         -27.19
ce_vega                            7.97
ce_rho                          90.4087
pe_symbol           NIFTY02JAN2523550PE
pe_open                          120.05
pe_high                          122.65
pe_low                            115.9
pe_close                          116.0
pe_volume                        813750
pe_oi                            969150
pe_coi                                0
pe_iv                             16.09
pe_delta                          -0.46
pe_gamma                         0.0012
pe_theta                         -21.22
pe_vega                            7.97
pe_rho                         -80.1808
future_open                     23694.4
future_high                     23701.8
future_low                      23685.0
future_close                    23701.8
future_volume                     78275
future_oi                      12385600
future_coi                            0
2025-05-25 13:38:14,184 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2024-12-31
trade_time                     12:00:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                            23595.8
atm_strike                      23650.0
strike                          23650.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY02JAN2523650CE
ce_open                           128.5
ce_high                           133.3
ce_low                            126.8
ce_close                         128.25
ce_volume                        190575
ce_oi                           2261775
ce_coi                            -1050
ce_iv                             18.21
ce_delta                           0.46
ce_gamma                         0.0011
ce_theta                         -30.43
ce_vega                            7.98
ce_rho                          78.1655
pe_symbol           NIFTY02JAN2523650PE
pe_open                           153.0
pe_high                           155.1
pe_low                            148.1
pe_close                          153.1
pe_volume                        145650
pe_oi                           1755600
pe_coi                             5700
pe_iv                             16.68
pe_delta                          -0.54
pe_gamma                         0.0012
pe_theta                          -21.6
pe_vega                            7.97
pe_rho                          -93.616
future_open                    23742.95
future_high                    23756.15
future_low                      23740.5
future_close                   23741.75
future_volume                      6000
future_oi                      12877100
future_coi                         2550
2025-05-25 13:38:14,184 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 13:38:14,184 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 13:38:14,184 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 13:38:14,185 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140187241185568
2025-05-25 13:38:14,185 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140187241185568, OptionType.PUT ID: 140187241185680
2025-05-25 13:38:14,185 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 13:38:14,185 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 13:38:14,185 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 13:38:14,186 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2024-12-31' AND trade_date <= '2024-12-31'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 13:38:14,479 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.292s, returned 25446 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 13:38:14,481 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 25446 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 13:38:14,482 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 13:38:14,551 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 13:38:14,555 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 1 - Tick data for specific strike 23550.0 is EMPTY after filtering windowed data.
2025-05-25 13:38:14,556 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 13:38:14,556 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 13:38:14,556 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 13:38:14,556 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 13:38:14,556 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 13:38:14,588 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.031s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 13:38:14,619 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.031s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 13:38:14,620 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
trade_date                   2024-12-31
trade_time                     09:16:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                           23559.15
atm_strike                      23550.0
strike                          23550.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY02JAN2523550CE
ce_open                          135.05
ce_high                          139.69
ce_low                            133.4
ce_close                         139.69
ce_volume                        547275
ce_oi                            240150
ce_coi                                0
ce_iv                             15.78
ce_delta                           0.54
ce_gamma                         0.0013
ce_theta                         -27.19
ce_vega                            7.97
ce_rho                          90.4087
pe_symbol           NIFTY02JAN2523550PE
pe_open                          120.05
pe_high                          122.65
pe_low                            115.9
pe_close                          116.0
pe_volume                        813750
pe_oi                            969150
pe_coi                                0
pe_iv                             16.09
pe_delta                          -0.46
pe_gamma                         0.0012
pe_theta                         -21.22
pe_vega                            7.97
pe_rho                         -80.1808
future_open                     23694.4
future_high                     23701.8
future_low                      23685.0
future_close                    23701.8
future_volume                     78275
future_oi                      12385600
future_coi                            0
2025-05-25 13:38:14,621 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2024-12-31
trade_time                     12:00:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                            23595.8
atm_strike                      23650.0
strike                          23650.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY02JAN2523650CE
ce_open                           128.5
ce_high                           133.3
ce_low                            126.8
ce_close                         128.25
ce_volume                        190575
ce_oi                           2261775
ce_coi                            -1050
ce_iv                             18.21
ce_delta                           0.46
ce_gamma                         0.0011
ce_theta                         -30.43
ce_vega                            7.98
ce_rho                          78.1655
pe_symbol           NIFTY02JAN2523650PE
pe_open                           153.0
pe_high                           155.1
pe_low                            148.1
pe_close                          153.1
pe_volume                        145650
pe_oi                           1755600
pe_coi                             5700
pe_iv                             16.68
pe_delta                          -0.54
pe_gamma                         0.0012
pe_theta                          -21.6
pe_vega                            7.97
pe_rho                          -93.616
future_open                    23742.95
future_high                    23756.15
future_low                      23740.5
future_close                   23741.75
future_volume                      6000
future_oi                      12877100
future_coi                         2550
2025-05-25 13:38:14,621 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 13:38:14,621 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 13:38:14,621 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 13:38:14,621 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140187241185680
2025-05-25 13:38:14,621 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140187241185568, OptionType.PUT ID: 140187241185680
2025-05-25 13:38:14,621 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 13:38:14,622 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 13:38:14,622 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 13:38:14,623 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2024-12-31' AND trade_date <= '2024-12-31'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 13:38:14,940 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.316s, returned 25446 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 13:38:14,942 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 25446 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 13:38:14,942 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 13:38:15,010 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 13:38:15,013 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 2 - Tick data for specific strike 23550.0 is EMPTY after filtering windowed data.
2025-05-25 13:38:15,014 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 13:38:15,014 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 13:38:15,014 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 13:38:15,014 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.call_strike_type = 'OTM2' AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 13:38:15,014 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.call_strike_type = 'OTM2' AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 13:38:15,502 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.488s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.call_strike_type = ''OTM2'' AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''', 1, 200) ...
2025-05-25 13:38:15,917 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.415s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.call_strike_type = ''OTM2'' AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''', 1, 200) ...
2025-05-25 13:38:15,918 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
trade_date                   2024-12-31
trade_time                     09:16:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                           23559.15
atm_strike                      23550.0
strike                          23650.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY02JAN2523650CE
ce_open                           89.65
ce_high                           93.35
ce_low                             88.8
ce_close                          93.35
ce_volume                        362775
ce_oi                            837675
ce_coi                                0
ce_iv                             15.83
ce_delta                           0.41
ce_gamma                         0.0012
ce_theta                         -25.97
ce_vega                             7.8
ce_rho                           69.451
pe_symbol           NIFTY02JAN2523650PE
pe_open                           174.7
pe_high                           177.6
pe_low                           169.35
pe_close                         169.35
pe_volume                        419700
pe_oi                           1428975
pe_coi                                0
pe_iv                             16.12
pe_delta                          -0.59
pe_gamma                         0.0012
pe_theta                         -19.96
pe_vega                            7.81
pe_rho                        -101.4727
future_open                     23694.4
future_high                     23701.8
future_low                      23685.0
future_close                    23701.8
future_volume                     78275
future_oi                      12385600
future_coi                            0
2025-05-25 13:38:15,919 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2024-12-31
trade_time                     12:00:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                            23595.8
atm_strike                      23650.0
strike                          23750.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY02JAN2523750CE
ce_open                            85.1
ce_high                           88.45
ce_low                            83.95
ce_close                           85.1
ce_volume                        156975
ce_oi                           2209800
ce_coi                           -20775
ce_iv                             17.83
ce_delta                           0.34
ce_gamma                          0.001
ce_theta                         -27.46
ce_vega                            7.47
ce_rho                          59.9379
pe_symbol           NIFTY02JAN2523750PE
pe_open                           209.9
pe_high                          211.95
pe_low                           203.25
pe_close                          209.3
pe_volume                         25125
pe_oi                           1043100
pe_coi                             2925
pe_iv                             16.11
pe_delta                          -0.66
pe_gamma                         0.0011
pe_theta                         -18.06
pe_vega                            7.35
pe_rho                        -114.5129
future_open                    23742.95
future_high                    23756.15
future_low                      23740.5
future_close                   23741.75
future_volume                      6000
future_oi                      12877100
future_coi                         2550
2025-05-25 13:38:15,919 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 13:38:15,919 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 13:38:15,919 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 13:38:15,920 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140187241185568
2025-05-25 13:38:15,920 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140187241185568, OptionType.PUT ID: 140187241185680
2025-05-25 13:38:15,920 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 13:38:15,920 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 13:38:15,920 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 13:38:15,921 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2024-12-31' AND trade_date <= '2024-12-31'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 13:38:16,279 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.358s, returned 25446 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 13:38:16,282 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 25446 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 13:38:16,283 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 13:38:16,387 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 13:38:16,393 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 3 - Tick data for specific strike 23650.0 is EMPTY after filtering windowed data.
2025-05-25 13:38:16,393 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 13:38:16,393 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 13:38:16,393 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 13:38:16,393 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.put_strike_type = 'OTM2' AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 13:38:16,393 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.put_strike_type = 'OTM2' AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 13:38:16,859 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.465s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.put_strike_type = ''OTM2'' AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''0', 1, 200) ...
2025-05-25 13:38:17,285 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.426s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.put_strike_type = ''OTM2'' AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''1', 1, 200) ...
2025-05-25 13:38:17,287 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
trade_date                   2024-12-31
trade_time                     09:16:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                           23559.15
atm_strike                      23550.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY02JAN2523450CE
ce_open                           192.2
ce_high                           198.3
ce_low                            191.0
ce_close                          198.3
ce_volume                         74925
ce_oi                             66750
ce_coi                                0
ce_iv                              15.7
ce_delta                           0.66
ce_gamma                         0.0012
ce_theta                         -26.05
ce_vega                            7.36
ce_rho                         110.9442
pe_symbol           NIFTY02JAN2523450PE
pe_open                           77.09
pe_high                           79.75
pe_low                             74.7
pe_close                           74.7
pe_volume                        399600
pe_oi                           1544025
pe_coi                                0
pe_iv                             16.04
pe_delta                          -0.34
pe_gamma                         0.0011
pe_theta                         -20.16
pe_vega                            7.38
pe_rho                         -59.3506
future_open                     23694.4
future_high                     23701.8
future_low                      23685.0
future_close                    23701.8
future_volume                     78275
future_oi                      12385600
future_coi                            0
2025-05-25 13:38:17,287 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2024-12-31
trade_time                     12:00:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                            23595.8
atm_strike                      23650.0
strike                          23550.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY02JAN2523550CE
ce_open                           185.0
ce_high                           188.9
ce_low                           181.45
ce_close                         182.95
ce_volume                        187800
ce_oi                           1872075
ce_coi                           -10275
ce_iv                             18.68
ce_delta                           0.58
ce_gamma                          0.001
ce_theta                         -31.49
ce_vega                            7.89
ce_rho                          96.0607
pe_symbol           NIFTY02JAN2523550PE
pe_open                           106.2
pe_high                           109.4
pe_low                            103.5
pe_close                          107.8
pe_volume                        282900
pe_oi                           3496350
pe_coi                           -31500
pe_iv                             17.11
pe_delta                          -0.42
pe_gamma                         0.0011
pe_theta                         -22.69
pe_vega                            7.87
pe_rho                         -73.3389
future_open                    23742.95
future_high                    23756.15
future_low                      23740.5
future_close                   23741.75
future_volume                      6000
future_oi                      12877100
future_coi                         2550
2025-05-25 13:38:17,288 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 13:38:17,288 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 13:38:17,288 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 13:38:17,288 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140187241185680
2025-05-25 13:38:17,288 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140187241185568, OptionType.PUT ID: 140187241185680
2025-05-25 13:38:17,288 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 13:38:17,289 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 13:38:17,289 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 13:38:17,290 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2024-12-31' AND trade_date <= '2024-12-31'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 13:38:17,637 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.347s, returned 25446 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 13:38:17,640 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 25446 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 13:38:17,641 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 13:38:17,745 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 13:38:17,750 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 4 - Tick data for specific strike 23450.0 is EMPTY after filtering windowed data.
2025-05-25 13:38:17,750 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 13:38:17,750 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 13:38:17,750 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 13:38:17,750 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 13:38:17,751 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23559.15, 'atm_strike': 23550.0, 'strike': 23450.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY02JAN2523450CE', 'ce_open': 192.2, 'ce_high': 198.3, 'ce_low': 191.0, 'ce_close': 198.3, 'ce_volume': 74925, 'ce_oi': 66750, 'ce_coi': 0, 'ce_iv': 15.7, 'ce_delta': 0.66, 'ce_gamma': 0.0012, 'ce_theta': -26.05, 'ce_vega': 7.36, 'ce_rho': 110.9442, 'pe_symbol': 'NIFTY02JAN2523450PE', 'pe_open': 77.09, 'pe_high': 79.75, 'pe_low': 74.7, 'pe_close': 74.7, 'pe_volume': 399600, 'pe_oi': 1544025, 'pe_coi': 0, 'pe_iv': 16.04, 'pe_delta': -0.34, 'pe_gamma': 0.0011, 'pe_theta': -20.16, 'pe_vega': 7.38, 'pe_rho': -59.3506, 'future_open': 23694.4, 'future_high': 23701.8, 'future_low': 23685.0, 'future_close': 23701.8, 'future_volume': 78275, 'future_oi': 12385600, 'future_coi': 0}
2025-05-25 13:38:17,751 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': datetime.time(12, 0), 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23595.8, 'atm_strike': 23650.0, 'strike': 23550.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY02JAN2523550CE', 'ce_open': 185.0, 'ce_high': 188.9, 'ce_low': 181.45, 'ce_close': 182.95, 'ce_volume': 187800, 'ce_oi': 1872075, 'ce_coi': -10275, 'ce_iv': 18.68, 'ce_delta': 0.58, 'ce_gamma': 0.001, 'ce_theta': -31.49, 'ce_vega': 7.89, 'ce_rho': 96.0607, 'pe_symbol': 'NIFTY02JAN2523550PE', 'pe_open': 106.2, 'pe_high': 109.4, 'pe_low': 103.5, 'pe_close': 107.8, 'pe_volume': 282900, 'pe_oi': 3496350, 'pe_coi': -31500, 'pe_iv': 17.11, 'pe_delta': -0.42, 'pe_gamma': 0.0011, 'pe_theta': -22.69, 'pe_vega': 7.87, 'pe_rho': -73.3389, 'future_open': 23742.95, 'future_high': 23756.15, 'future_low': 23740.5, 'future_close': 23741.75, 'future_volume': 6000, 'future_oi': 12877100, 'future_coi': 2550}
2025-05-25 13:38:17,751 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-25 13:38:17,751 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 13:38:17,751 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 13:38:17,751 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 13:38:17,752 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: 1654.9999999999998, PnL (Slippage): 1654.9999999999998, Net PnL: 1654.9999999999998
2025-05-25 13:38:17,752 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 13:38:17,752 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2024-12-31', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2024-12-31', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-01-02', 'strike': 23450, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 74.7, 'exit_price': 107.8, 'points': 33.099999999999994, 'pointsAfterSlippage': 33.099999999999994, 'pnl': 1654.9999999999998, 'pnlAfterSlippage': 1654.9999999999998, 'expenses': 0.0, 'netPnlAfterExpenses': 1654.9999999999998, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23559.15, 'index_exit_price': 23595.8, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2024-12-31 09:16:00', 'exit_datetime': '2024-12-31 12:00:00'}
2025-05-25 13:38:17,752 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Number of trade records generated: 1
2025-05-25 13:38:17,752 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Generated 1 trade records via model-driven path
2025-05-25 13:38:17,753 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Closed reusable HeavyDB connection from get_trades_for_portfolio
2025-05-25 13:38:17,774 - backtester_stable.BTRUN.builders - WARNING - utils.MARGIN_INFO not found or empty - margin requirements will be 0. Attempting to populate.
2025-05-25 13:38:17,774 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'margin_symbol_info.json' at /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-25 13:38:17,774 - backtester_stable.BTRUN.core.utils - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json (Broker: angel)
2025-05-25 13:38:17,774 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.MARGIN_INFO for angel.
2025-05-25 13:38:17,774 - backtester_stable.BTRUN.builders - ERROR - utils.MARGIN_INFO still empty after populate attempt. Margin req will be 0.
2025-05-25 13:38:17,776 - backtester_stable.BTRUN.builders - DEBUG - Strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL margin requirement: 0.00
2025-05-25 13:38:17,777 - backtester_stable.BTRUN.builders - INFO - Total portfolio margin requirement: 0.00
2025-05-25 13:38:17,777 - backtester_stable.BTRUN.builders - DEBUG - Converting tick PnL to daywise DataFrame.
2025-05-25 13:38:17,778 - backtester_stable.BTRUN.builders - INFO - Backtest response parsed.
2025-05-25 13:38:17,816 - backtester_stable.BTRUN.core.runtime - INFO - Generated metrics_df with 50 records for strategies: ['Combined' 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL']
2025-05-25 13:38:17,889 - backtester_stable.BTRUN.core.runtime - INFO - Saving Excel output to comparison_outputs/new_gpu_fixed.xlsx
2025-05-25 13:38:17,889 - backtester_stable.BTRUN.core.runtime - DEBUG - [DEBUG save_backtest_results] save_excel=True, metrics_df is empty: False
2025-05-25 13:38:17,890 - backtester_stable.BTRUN.core.io - INFO - 2025-05-25 13:38:17.890055, Started writing stats to excel file: comparison_outputs/new_gpu_fixed.xlsx
2025-05-25 13:38:17,890 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Temporarily switching to CPU-only mode.
2025-05-25 13:38:17,932 - backtester_stable.BTRUN.core.io - INFO - 2025-05-25 13:38:17.932526, Excel file prepared successfully, Time taken: 0.04s
2025-05-25 13:38:17,932 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Restored GPU_ENABLED to: False
2025-05-25 13:38:17,932 - backtester_stable.BTRUN.core.runtime - INFO - Saving JSON output to comparison_outputs/new_gpu_fixed.json
2025-05-25 13:38:17,950 - backtester_stable.BTRUN.core.io - INFO - --- JSON DUMP DEBUG START ---
2025-05-25 13:38:17,950 - backtester_stable.BTRUN.core.io - INFO - --- JSON DUMP DEBUG END ---
2025-05-25 13:38:17,951 - backtester_stable.BTRUN.core.io - INFO - Successfully wrote JSON output to comparison_outputs/new_gpu_fixed.json
2025-05-25 13:38:17,955 - backtester_stable.BTRUN.core.io - ERROR - Text-to-speech failed: This means you probably do not have eSpeak or eSpeak-ng installed!
