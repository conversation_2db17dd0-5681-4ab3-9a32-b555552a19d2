2025-05-11 20:53:01,130 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-11 20:53:01,130 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-11 20:53:01,131 - __main__ - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-11 20:53:01,131 - __main__ - DEBUG - HeavyDB host: **************, DB: heavydb
2025-05-11 20:53:01,131 - __main__ - DEBUG - GPU enabled: True
2025-05-11 20:53:01,134 - __main__ - INFO - No config provided – defaulting to Excel input format
2025-05-11 20:53:01,134 - __main__ - INFO - GPU acceleration enabled: True
2025-05-11 20:53:01,239 - __main__ - INFO - GPU Memory: 34535MB free / 40384MB total
2025-05-11 20:53:01,240 - __main__ - INFO - HeavyDB integration available (Host: **************)
2025-05-11 20:53:01,798 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:53:01,798 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-11 20:53:01,799 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-11 20:53:01,802 - bt.backtester_stable.BTRUN.Util - INFO - Running necessary functions before starting BT...
2025-05-11 20:53:01,802 - bt.backtester_stable.BTRUN.config - DEBUG - Found fixture at /srv/samba/shared/LOTSIZE.csv
2025-05-11 20:53:01,803 - bt.backtester_stable.BTRUN.Util - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-11 20:53:01,811 - bt.backtester_stable.BTRUN.Util - INFO - Successfully populated LOT_SIZE: 50 entries.
2025-05-11 20:53:01,811 - bt.backtester_stable.BTRUN.config - WARNING - Fixture margin_symbol_info.json not found in any expected location
2025-05-11 20:53:01,811 - bt.backtester_stable.BTRUN.Util - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/margin_symbol_info.json (Broker: angel)
2025-05-11 20:53:01,811 - bt.backtester_stable.BTRUN.Util - INFO - Pre-BT functions complete.
2025-05-11 20:53:01,811 - __main__ - INFO - Using new Excel input format (PortfolioSetting & StrategySetting)
2025-05-11 20:53:01,811 - __main__ - INFO - Reading PortfolioSetting & StrategySetting from /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-11 20:53:02,000 - __main__ - DEBUG - Loaded 1 PortfolioSetting rows, 21 StrategySetting rows
2025-05-11 20:53:02,000 - __main__ - INFO - Queueing portfolio: NIF0DTE
2025-05-11 20:53:02,001 - __main__ - DEBUG - Building portfolio request for portfolio: NIF0DTE
2025-05-11 20:53:02,003 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:53:02,003 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:53:02,004 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:53:02,004 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:53:02,004 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:53:02,004 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:53:02,005 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:53:02,005 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:53:02,005 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:53:02,005 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:53:02,006 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:53:02,006 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:53:02,006 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:53:02,006 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:53:02,223 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-11 20:53:02,223 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY CALL at 2025-05-11 09:16:00.223577 via method 'ATM'
2025-05-11 20:53:02,223 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-11 20:53:02,224 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY PUT at 2025-05-11 09:16:00.224100 via method 'ATM'
2025-05-11 20:53:02,224 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-11 20:53:02,224 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY CALL at 2025-05-11 09:16:00.224560 via method 'ATM'
2025-05-11 20:53:02,224 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-11 20:53:02,225 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY PUT at 2025-05-11 09:16:00.225009 via method 'ATM'
2025-05-11 20:53:02,225 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:53:02,225 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:53:02,225 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:53:02,226 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:53:02,226 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:53:02,226 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:53:02,226 - __main__ - DEBUG - Built request for NIF0DTE with 1 strategies
2025-05-11 20:53:02,227 - __main__ - DEBUG - First strategy sample: {"id": "", "name": "RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL", "evaluator": "Tbs", "type": "STRATEGYTYPE.INTRADAY", "index": "NSEINDEX.NIFTY", "underlying": "CASH", "entry_time": 33360, "exit_time": 43200, "last_entry_time": 43200, "strike_selection_time": 33360, "multiplier": 1, "is_tick_bt": false, "legs": [{"id": "1", "option_type": "OPTIONTYPE.CALL", "side": "SIDE.SELL", "expiry_type": "EXPIRYTYPE.WEEKLY", "strike_selection": {"type": "BY_ATM_STRIKE", "value": 0.0}, "quantity": 50, "multipl
2025-05-11 20:53:02,227 - __main__ - INFO - Executing 1 portfolios sequentially
2025-05-11 20:53:02,228 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting BTRunPortfolio_GPU.py - Debugging enabled
2025-05-11 20:53:02,228 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Current working directory: /srv/samba/shared
2025-05-11 20:53:02,228 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-11 20:53:02,228 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Importing BTRUN modules...
2025-05-11 20:53:02,228 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Config module imported from: /srv/samba/shared/bt/backtester_stable/BTRUN/config.py
2025-05-11 20:53:02,228 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - HeavyDB settings - Host: **************, Database: heavydb
2025-05-11 20:53:02,229 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - GPU enabled: True
2025-05-11 20:53:02,229 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Successfully imported heavydb_helpers module
2025-05-11 20:53:02,229 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting single backtest for portfolio
2025-05-11 20:53:02,229 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Using HeavyDB for data access
2025-05-11 20:53:02,229 - bt.backtester_stable.BTRUN.runtime - INFO - Fetching trades from local HeavyDB for portfolio ''
2025-05-11 20:53:02,515 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:53:11,687 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 9.172s, returned 563 rows
2025-05-11 20:53:11,729 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Cached 563 distinct trade dates
2025-05-11 20:53:11,736 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Union query failed for 2025-04-01 – falling back to per-leg: entry_time
2025-05-11 20:53:11,737 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Per-leg Entry SQL (2025-04-01 - Leg: L1):
SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-01'
      AND en.expiry_date = DATE '2025-04-03'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    ORDER BY en.trade_time ASC
    LIMIT 1
2025-05-11 20:53:11,737 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Per-leg Exit SQL (2025-04-01 - Leg: L1):
SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-01'
      AND ex.expiry_date = DATE '2025-04-03'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    ORDER BY ex.trade_time DESC
    LIMIT 1
2025-05-11 20:53:11,953 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:53:39,528 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 27.576s, returned 0 rows
2025-05-11 20:53:39,812 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:54:05,524 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 25.712s, returned 0 rows
2025-05-11 20:54:05,524 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Per-leg Entry SQL (2025-04-01 - Leg: L1):
SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-01'
      AND en.expiry_date = DATE '2025-04-03'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    ORDER BY en.trade_time ASC
    LIMIT 1
2025-05-11 20:54:05,525 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Per-leg Exit SQL (2025-04-01 - Leg: L1):
SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-01'
      AND ex.expiry_date = DATE '2025-04-03'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    ORDER BY ex.trade_time DESC
    LIMIT 1
2025-05-11 20:54:05,823 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:54:32,713 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 26.889s, returned 0 rows
2025-05-11 20:54:32,932 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:54:51,924 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 18.993s, returned 0 rows
2025-05-11 20:54:51,925 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Per-leg Entry SQL (2025-04-01 - Leg: L1):
SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-01'
      AND en.expiry_date = DATE '2025-04-03'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    ORDER BY en.trade_time ASC
    LIMIT 1
2025-05-11 20:54:51,926 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Per-leg Exit SQL (2025-04-01 - Leg: L1):
SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-01'
      AND ex.expiry_date = DATE '2025-04-03'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    ORDER BY ex.trade_time DESC
    LIMIT 1
2025-05-11 20:54:52,141 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:55:09,975 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 17.834s, returned 0 rows
2025-05-11 20:55:10,206 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:55:34,056 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 23.849s, returned 0 rows
2025-05-11 20:55:34,057 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Per-leg Entry SQL (2025-04-01 - Leg: L1):
SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-01'
      AND en.expiry_date = DATE '2025-04-03'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    ORDER BY en.trade_time ASC
    LIMIT 1
2025-05-11 20:55:34,057 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Per-leg Exit SQL (2025-04-01 - Leg: L1):
SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-01'
      AND ex.expiry_date = DATE '2025-04-03'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    ORDER BY ex.trade_time DESC
    LIMIT 1
2025-05-11 20:55:34,293 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:55:57,603 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 23.309s, returned 0 rows
2025-05-11 20:55:57,820 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:56:17,332 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 19.512s, returned 0 rows
2025-05-11 20:56:17,333 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Union query failed for 2025-04-02 – falling back to per-leg: entry_time
2025-05-11 20:56:17,333 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Per-leg Entry SQL (2025-04-02 - Leg: L1):
SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-02'
      AND en.expiry_date = DATE '2025-04-03'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    ORDER BY en.trade_time ASC
    LIMIT 1
2025-05-11 20:56:17,333 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Per-leg Exit SQL (2025-04-02 - Leg: L1):
SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-02'
      AND ex.expiry_date = DATE '2025-04-03'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    ORDER BY ex.trade_time DESC
    LIMIT 1
2025-05-11 20:56:17,548 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:56:35,152 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 17.604s, returned 0 rows
2025-05-11 20:56:35,370 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:56:53,022 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 17.652s, returned 0 rows
2025-05-11 20:56:53,023 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Per-leg Entry SQL (2025-04-02 - Leg: L1):
SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-02'
      AND en.expiry_date = DATE '2025-04-03'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    ORDER BY en.trade_time ASC
    LIMIT 1
2025-05-11 20:56:53,023 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Per-leg Exit SQL (2025-04-02 - Leg: L1):
SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-02'
      AND ex.expiry_date = DATE '2025-04-03'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    ORDER BY ex.trade_time DESC
    LIMIT 1
2025-05-11 20:56:53,238 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:57:11,221 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 17.983s, returned 0 rows
2025-05-11 20:57:11,438 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:57:32,833 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 21.395s, returned 0 rows
2025-05-11 20:57:32,833 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Per-leg Entry SQL (2025-04-02 - Leg: L1):
SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-02'
      AND en.expiry_date = DATE '2025-04-03'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    ORDER BY en.trade_time ASC
    LIMIT 1
2025-05-11 20:57:32,834 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Per-leg Exit SQL (2025-04-02 - Leg: L1):
SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-02'
      AND ex.expiry_date = DATE '2025-04-03'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    ORDER BY ex.trade_time DESC
    LIMIT 1
2025-05-11 20:57:33,051 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:57:52,618 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 19.567s, returned 0 rows
2025-05-11 20:57:52,896 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:58:11,202 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 18.306s, returned 0 rows
2025-05-11 20:58:11,203 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Per-leg Entry SQL (2025-04-02 - Leg: L1):
SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-02'
      AND en.expiry_date = DATE '2025-04-03'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    ORDER BY en.trade_time ASC
    LIMIT 1
2025-05-11 20:58:11,203 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Per-leg Exit SQL (2025-04-02 - Leg: L1):
SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-02'
      AND ex.expiry_date = DATE '2025-04-03'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    ORDER BY ex.trade_time DESC
    LIMIT 1
2025-05-11 20:58:11,480 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:58:30,882 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 19.402s, returned 0 rows
2025-05-11 20:58:31,165 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:58:52,038 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 20.874s, returned 0 rows
2025-05-11 20:58:52,039 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Union query failed for 2025-04-03 – falling back to per-leg: entry_time
2025-05-11 20:58:52,040 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Per-leg Entry SQL (2025-04-03 - Leg: L1):
SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-03'
      AND en.expiry_date = DATE '2025-04-03'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    ORDER BY en.trade_time ASC
    LIMIT 1
2025-05-11 20:58:52,040 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Per-leg Exit SQL (2025-04-03 - Leg: L1):
SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-03'
      AND ex.expiry_date = DATE '2025-04-03'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    ORDER BY ex.trade_time DESC
    LIMIT 1
2025-05-11 20:58:52,320 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:59:11,986 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 19.666s, returned 0 rows
2025-05-11 20:59:12,246 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:59:32,671 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 20.425s, returned 0 rows
2025-05-11 20:59:32,672 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Per-leg Entry SQL (2025-04-03 - Leg: L1):
SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-03'
      AND en.expiry_date = DATE '2025-04-03'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    ORDER BY en.trade_time ASC
    LIMIT 1
2025-05-11 20:59:32,672 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Per-leg Exit SQL (2025-04-03 - Leg: L1):
SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-03'
      AND ex.expiry_date = DATE '2025-04-03'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    ORDER BY ex.trade_time DESC
    LIMIT 1
2025-05-11 20:59:32,930 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:59:52,776 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 19.846s, returned 0 rows
2025-05-11 20:59:53,039 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 21:00:12,082 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 19.043s, returned 0 rows
2025-05-11 21:00:12,082 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Per-leg Entry SQL (2025-04-03 - Leg: L1):
SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-03'
      AND en.expiry_date = DATE '2025-04-03'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    ORDER BY en.trade_time ASC
    LIMIT 1
2025-05-11 21:00:12,083 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Per-leg Exit SQL (2025-04-03 - Leg: L1):
SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-03'
      AND ex.expiry_date = DATE '2025-04-03'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    ORDER BY ex.trade_time DESC
    LIMIT 1
2025-05-11 21:00:12,336 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 21:00:32,106 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 19.770s, returned 0 rows
2025-05-11 21:00:32,375 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 21:00:51,439 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 19.064s, returned 0 rows
2025-05-11 21:00:51,440 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Per-leg Entry SQL (2025-04-03 - Leg: L1):
SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-03'
      AND en.expiry_date = DATE '2025-04-03'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    ORDER BY en.trade_time ASC
    LIMIT 1
2025-05-11 21:00:51,440 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Per-leg Exit SQL (2025-04-03 - Leg: L1):
SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-03'
      AND ex.expiry_date = DATE '2025-04-03'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    ORDER BY ex.trade_time DESC
    LIMIT 1
2025-05-11 21:00:51,716 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 21:01:11,218 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 19.502s, returned 0 rows
2025-05-11 21:01:11,487 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 21:01:29,577 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 18.090s, returned 0 rows
2025-05-11 21:01:29,586 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Generated 0 trade records via HeavyDB snapshot across 3 trading days
2025-05-11 21:01:29,587 - bt.backtester_stable.BTRUN.builders - INFO - Parsing backtest response...
2025-05-11 21:01:29,587 - bt.backtester_stable.BTRUN.builders - WARNING - No trades data in backtest response or invalid format.
2025-05-11 21:01:29,587 - bt.backtester_stable.BTRUN.runtime - WARNING - No trades found in backtest response
2025-05-11 21:01:29,588 - bt.backtester_stable.BTRUN.runtime - INFO - Saving Excel output to backtest_output/NIF0DTE_11052025_205302.xlsx
2025-05-11 21:01:29,588 - bt.backtester_stable.BTRUN.runtime - WARNING - Empty metrics DataFrame - not saving Excel output
2025-05-11 21:01:29,588 - bt.backtester_stable.BTRUN.runtime - INFO - Saving JSON output to backtest_output/NIF0DTE_11052025_205302.json
2025-05-11 21:01:29,588 - bt.backtester_stable.BTRUN.runtime - WARNING - Empty metrics DataFrame - not saving JSON output
2025-05-11 21:01:29,589 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Backtest completed in 507.36 seconds
2025-05-11 21:01:29,589 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Portfolio backtest completed successfully with 0 trades
2025-05-11 21:01:29,589 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Saved json file to backtest_output/NIF0DTE_11052025_205302.json
2025-05-11 21:01:29,589 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Saved excel file to backtest_output/NIF0DTE_11052025_205302.xlsx
