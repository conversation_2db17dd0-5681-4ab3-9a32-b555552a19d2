2025-05-11 20:37:55,625 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-11 20:37:55,626 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-11 20:37:55,626 - __main__ - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-11 20:37:55,626 - __main__ - DEBUG - HeavyDB host: **************, DB: heavydb
2025-05-11 20:37:55,626 - __main__ - DEBUG - GPU enabled: True
2025-05-11 20:37:55,627 - __main__ - INFO - No config provided – defaulting to Excel input format
2025-05-11 20:37:55,628 - __main__ - INFO - GPU acceleration enabled: True
2025-05-11 20:37:55,696 - __main__ - INFO - GPU Memory: 35417MB free / 40384MB total
2025-05-11 20:37:55,696 - __main__ - INFO - HeavyDB integration available (Host: **************)
2025-05-11 20:37:55,910 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:37:55,910 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-11 20:37:55,910 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-11 20:37:55,925 - bt.backtester_stable.BTRUN.Util - INFO - Running necessary functions before starting BT...
2025-05-11 20:37:55,925 - bt.backtester_stable.BTRUN.config - DEBUG - Found fixture at /srv/samba/shared/LOTSIZE.csv
2025-05-11 20:37:55,925 - bt.backtester_stable.BTRUN.Util - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-11 20:37:55,930 - bt.backtester_stable.BTRUN.Util - INFO - Successfully populated LOT_SIZE: 50 entries.
2025-05-11 20:37:55,930 - bt.backtester_stable.BTRUN.config - WARNING - Fixture margin_symbol_info.json not found in any expected location
2025-05-11 20:37:55,930 - bt.backtester_stable.BTRUN.Util - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/margin_symbol_info.json (Broker: angel)
2025-05-11 20:37:55,930 - bt.backtester_stable.BTRUN.Util - INFO - Pre-BT functions complete.
2025-05-11 20:37:55,930 - __main__ - INFO - Using new Excel input format (PortfolioSetting & StrategySetting)
2025-05-11 20:37:55,930 - __main__ - INFO - Reading PortfolioSetting & StrategySetting from /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-11 20:37:56,026 - __main__ - DEBUG - Loaded 1 PortfolioSetting rows, 21 StrategySetting rows
2025-05-11 20:37:56,027 - __main__ - INFO - Queueing portfolio: NIF0DTE
2025-05-11 20:37:56,027 - __main__ - DEBUG - Building portfolio request for portfolio: NIF0DTE
2025-05-11 20:37:56,028 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:37:56,028 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:37:56,029 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:37:56,029 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:37:56,029 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:37:56,029 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:37:56,029 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:37:56,029 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:37:56,029 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:37:56,030 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:37:56,030 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:37:56,030 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:37:56,030 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:37:56,030 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:37:56,122 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-11 20:37:56,122 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY CALL at 2025-05-11 09:16:00.122102 via method 'ATM'
2025-05-11 20:37:56,122 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-11 20:37:56,122 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY PUT at 2025-05-11 09:16:00.122415 via method 'ATM'
2025-05-11 20:37:56,122 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-11 20:37:56,122 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY CALL at 2025-05-11 09:16:00.122698 via method 'ATM'
2025-05-11 20:37:56,122 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-11 20:37:56,123 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY PUT at 2025-05-11 09:16:00.122988 via method 'ATM'
2025-05-11 20:37:56,123 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:37:56,123 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:37:56,123 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:37:56,123 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:37:56,123 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:37:56,123 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:37:56,124 - __main__ - DEBUG - Built request for NIF0DTE with 1 strategies
2025-05-11 20:37:56,124 - __main__ - DEBUG - First strategy sample: {"id": "", "name": "RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL", "evaluator": "Tbs", "type": "STRATEGYTYPE.INTRADAY", "index": "NSEINDEX.NIFTY", "underlying": "CASH", "entry_time": 33360, "exit_time": 43200, "last_entry_time": 43200, "strike_selection_time": 33360, "multiplier": 1, "is_tick_bt": false, "legs": [{"id": "1", "option_type": "OPTIONTYPE.CALL", "side": "SIDE.SELL", "expiry_type": "EXPIRYTYPE.WEEKLY", "strike_selection": {"type": "BY_ATM_STRIKE", "value": 0.0}, "quantity": 50, "multipl
2025-05-11 20:37:56,124 - __main__ - INFO - Executing 1 portfolios sequentially
2025-05-11 20:37:56,124 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting BTRunPortfolio_GPU.py - Debugging enabled
2025-05-11 20:37:56,124 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Current working directory: /srv/samba/shared
2025-05-11 20:37:56,125 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-11 20:37:56,125 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Importing BTRUN modules...
2025-05-11 20:37:56,125 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Config module imported from: /srv/samba/shared/bt/backtester_stable/BTRUN/config.py
2025-05-11 20:37:56,125 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - HeavyDB settings - Host: **************, Database: heavydb
2025-05-11 20:37:56,125 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - GPU enabled: True
2025-05-11 20:37:56,125 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Successfully imported heavydb_helpers module
2025-05-11 20:37:56,125 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting single backtest for portfolio
2025-05-11 20:37:56,125 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Using HeavyDB for data access
2025-05-11 20:37:56,125 - bt.backtester_stable.BTRUN.runtime - INFO - Fetching trades from local HeavyDB for portfolio ''
2025-05-11 20:37:56,341 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:37:59,676 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 3.335s, returned 563 rows
2025-05-11 20:37:59,717 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Cached 563 distinct trade dates
2025-05-11 20:37:59,724 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Union query failed for 2025-04-01 – falling back to per-leg: entry_time
2025-05-11 20:37:59,937 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:38:12,737 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.799s, returned 0 rows
2025-05-11 20:38:12,950 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:38:25,557 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.607s, returned 0 rows
2025-05-11 20:38:25,771 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:38:39,256 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 13.485s, returned 0 rows
2025-05-11 20:38:39,471 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:38:51,992 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.520s, returned 0 rows
2025-05-11 20:38:52,204 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:39:05,498 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 13.294s, returned 0 rows
2025-05-11 20:39:05,712 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:39:18,954 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 13.241s, returned 0 rows
2025-05-11 20:39:19,169 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:39:31,930 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.761s, returned 0 rows
2025-05-11 20:39:32,144 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:39:44,723 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.578s, returned 0 rows
2025-05-11 20:39:44,723 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Union query failed for 2025-04-02 – falling back to per-leg: entry_time
2025-05-11 20:39:44,937 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:39:57,445 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.507s, returned 0 rows
2025-05-11 20:39:57,661 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:40:10,218 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.557s, returned 0 rows
2025-05-11 20:40:10,432 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:40:23,989 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 13.556s, returned 0 rows
2025-05-11 20:40:24,201 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:40:36,785 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.584s, returned 0 rows
2025-05-11 20:40:37,002 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:40:50,249 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 13.247s, returned 0 rows
2025-05-11 20:40:50,464 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:41:03,145 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.681s, returned 0 rows
2025-05-11 20:41:03,359 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:41:15,961 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.602s, returned 0 rows
2025-05-11 20:41:16,175 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:41:30,509 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 14.334s, returned 0 rows
2025-05-11 20:41:30,511 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Union query failed for 2025-04-03 – falling back to per-leg: entry_time
2025-05-11 20:41:30,725 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:41:43,504 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.778s, returned 0 rows
2025-05-11 20:41:43,720 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:41:56,587 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.867s, returned 0 rows
2025-05-11 20:41:56,801 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:42:09,276 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.475s, returned 0 rows
2025-05-11 20:42:09,490 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:42:22,883 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 13.392s, returned 0 rows
2025-05-11 20:42:23,101 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:42:35,508 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.407s, returned 0 rows
2025-05-11 20:42:35,723 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:42:49,375 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 13.651s, returned 0 rows
2025-05-11 20:42:49,592 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:43:02,314 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.722s, returned 0 rows
2025-05-11 20:43:02,528 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:43:15,375 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.847s, returned 0 rows
2025-05-11 20:43:15,376 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Union query failed for 2025-04-04 – falling back to per-leg: entry_time
2025-05-11 20:43:15,595 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:43:31,168 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 15.572s, returned 0 rows
2025-05-11 20:43:31,381 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:43:44,033 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.652s, returned 0 rows
2025-05-11 20:43:44,250 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:43:57,509 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 13.259s, returned 0 rows
2025-05-11 20:43:57,724 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:44:10,464 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.739s, returned 0 rows
2025-05-11 20:44:10,678 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:44:23,158 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.480s, returned 0 rows
2025-05-11 20:44:23,373 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:44:37,241 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 13.867s, returned 0 rows
2025-05-11 20:44:37,457 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:44:50,339 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.882s, returned 0 rows
2025-05-11 20:44:50,560 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:45:03,185 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.624s, returned 0 rows
2025-05-11 20:45:03,186 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Union query failed for 2025-04-07 – falling back to per-leg: entry_time
2025-05-11 20:45:03,400 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:45:16,986 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 13.585s, returned 0 rows
2025-05-11 20:45:17,203 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:45:32,223 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 15.020s, returned 0 rows
2025-05-11 20:45:32,441 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:45:45,129 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.688s, returned 0 rows
2025-05-11 20:45:45,347 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:45:58,053 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.706s, returned 0 rows
2025-05-11 20:45:58,271 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:46:11,141 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.870s, returned 0 rows
2025-05-11 20:46:11,357 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:46:24,059 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.702s, returned 0 rows
2025-05-11 20:46:24,277 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:46:37,108 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.831s, returned 0 rows
2025-05-11 20:46:37,326 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:46:49,837 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.511s, returned 0 rows
2025-05-11 20:46:49,838 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Union query failed for 2025-04-08 – falling back to per-leg: entry_time
2025-05-11 20:46:50,052 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:47:02,490 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 12.437s, returned 0 rows
2025-05-11 20:47:02,705 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:47:15,942 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 13.237s, returned 0 rows
2025-05-11 20:47:16,158 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:47:29,573 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 13.415s, returned 0 rows
2025-05-11 20:47:29,790 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:47:47,083 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 17.292s, returned 0 rows
2025-05-11 20:47:47,300 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:48:04,801 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 17.500s, returned 0 rows
2025-05-11 20:48:05,019 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:48:22,279 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 17.260s, returned 0 rows
2025-05-11 20:48:22,495 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:48:39,817 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 17.322s, returned 0 rows
2025-05-11 20:48:40,045 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:48:57,425 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 17.379s, returned 0 rows
2025-05-11 20:48:57,425 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Union query failed for 2025-04-09 – falling back to per-leg: entry_time
2025-05-11 20:48:57,642 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:49:15,799 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 18.157s, returned 0 rows
2025-05-11 20:49:16,015 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:49:33,991 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 17.976s, returned 0 rows
2025-05-11 20:49:34,208 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:49:52,284 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 18.076s, returned 0 rows
2025-05-11 20:49:52,587 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:50:10,771 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 18.184s, returned 0 rows
2025-05-11 20:50:11,082 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:50:29,025 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 17.943s, returned 0 rows
2025-05-11 20:50:29,309 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:50:47,908 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 18.598s, returned 0 rows
2025-05-11 20:50:48,187 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:51:08,772 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 20.585s, returned 0 rows
2025-05-11 20:51:09,054 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:51:28,597 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 19.542s, returned 0 rows
2025-05-11 20:51:28,598 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Union query failed for 2025-04-11 – falling back to per-leg: entry_time
2025-05-11 20:51:28,877 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:51:51,819 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 22.941s, returned 0 rows
2025-05-11 20:51:52,068 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:52:11,083 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 19.015s, returned 0 rows
2025-05-11 20:52:11,620 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:52:30,326 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 18.706s, returned 0 rows
2025-05-11 20:52:30,630 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:52:50,487 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 19.857s, returned 0 rows
2025-05-11 20:52:50,792 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:53:11,441 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 20.649s, returned 0 rows
2025-05-11 20:53:11,674 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:53:38,278 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 26.604s, returned 0 rows
2025-05-11 20:53:38,536 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:54:04,494 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 25.957s, returned 0 rows
2025-05-11 20:54:04,806 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:54:30,659 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 25.853s, returned 0 rows
2025-05-11 20:54:30,660 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Generated 0 trade records via HeavyDB snapshot across 8 trading days
2025-05-11 20:54:30,661 - bt.backtester_stable.BTRUN.builders - INFO - Parsing backtest response...
2025-05-11 20:54:30,661 - bt.backtester_stable.BTRUN.builders - WARNING - No trades data in backtest response or invalid format.
2025-05-11 20:54:30,661 - bt.backtester_stable.BTRUN.runtime - WARNING - No trades found in backtest response
2025-05-11 20:54:30,663 - bt.backtester_stable.BTRUN.runtime - INFO - Saving Excel output to backtest_output/NIF0DTE_11052025_203756.xlsx
2025-05-11 20:54:30,664 - bt.backtester_stable.BTRUN.runtime - WARNING - Empty metrics DataFrame - not saving Excel output
2025-05-11 20:54:30,664 - bt.backtester_stable.BTRUN.runtime - INFO - Saving JSON output to backtest_output/NIF0DTE_11052025_203756.json
2025-05-11 20:54:30,664 - bt.backtester_stable.BTRUN.runtime - WARNING - Empty metrics DataFrame - not saving JSON output
2025-05-11 20:54:30,664 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Backtest completed in 994.54 seconds
2025-05-11 20:54:30,664 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Portfolio backtest completed successfully with 0 trades
2025-05-11 20:54:30,665 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Saved json file to backtest_output/NIF0DTE_11052025_203756.json
2025-05-11 20:54:30,665 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Saved excel file to backtest_output/NIF0DTE_11052025_203756.xlsx
