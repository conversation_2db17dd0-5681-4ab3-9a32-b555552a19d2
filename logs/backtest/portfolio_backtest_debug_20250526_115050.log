2025-05-26 11:50:50,776 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-26 11:50:50,776 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-26 11:50:50,777 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/../models', '/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages', '/srv/samba/shared']
2025-05-26 11:50:50,777 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-26 11:50:50,777 - __main__ - DEBUG - GPU enabled: False
2025-05-26 11:50:50,779 - __main__ - INFO - GPU acceleration enabled: False
2025-05-26 11:50:50,779 - __main__ - INFO - HeavyDB integration available (Host: 127.0.0.1)
2025-05-26 11:50:51,006 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-26 11:50:51,006 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-26 11:50:51,007 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-26 11:50:51,007 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-26 11:50:51,007 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-26 11:50:51,007 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-26 11:50:51,013 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-26 11:50:51,014 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-26 11:50:51,014 - backtester_stable.BTRUN.core.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-26 11:50:51,014 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-26 11:50:51,014 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-26 11:50:51,014 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-26 11:50:51,014 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-26 11:50:51,014 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-26 11:50:51,014 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-26 11:50:51,014 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-26 11:50:51,014 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-26 11:50:51,014 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-26 11:50:51,139 - __main__ - INFO - Successfully parsed 1 portfolio(s) using modern parser.
2025-05-26 11:50:51,139 - __main__ - INFO - Queueing portfolio: NIF0DTE (from modern_portfolio_parser)
2025-05-26 11:50:51,140 - __main__ - INFO - Executing 1 portfolios sequentially (multiprocessing temporarily disabled for debugging)
2025-05-26 11:50:51,140 - __main__ - INFO - Processing payload 1/1: NIF0DTE
2025-05-26 11:50:51,140 - backtester_stable.BTRUN.core.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'NIF0DTE'
2025-05-26 11:50:51,364 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-26 11:50:51,383 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 563 rows. Query: SELECT SUBSTRING('SELECT DISTINCT trade_date FROM nifty_option_chain', 1, 200) ...
2025-05-26 11:50:51,427 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - Cached 563 distinct trade dates
2025-05-26 11:50:51,427 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] portfolio_model_dict keys: ['portfolio_name', 'start_date', 'end_date', 'slippage_percent', 'margin_multiplier', 'is_tick_bt', 'strategies', 'extra_params']
2025-05-26 11:50:51,428 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 1 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 11:50:51,428 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-26 11:50:51,428 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-26 11:50:51,470 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.042s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-26 11:50:51,501 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.031s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-26 11:50:51,501 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Query results: entry_df size=1, exit_df size=1
2025-05-26 11:50:51,502 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Got entry and exit data, proceeding...
2025-05-26 11:50:51,502 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
                        trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523450CE
ce_open                          102.85
ce_high                          106.65
ce_low                            97.85
ce_close                         100.75
ce_volume                       1015050
ce_oi                            978075
ce_coi                                0
ce_iv                             13.44
ce_delta                           0.48
ce_gamma                         0.0015
ce_theta                         -23.27
ce_vega                            7.95
ce_rho                          81.3115
pe_symbol           NIFTY03APR2523450PE
pe_open                           118.2
pe_high                           122.3
pe_low                            112.2
pe_close                          115.7
pe_volume                        877125
pe_oi                           1618950
pe_coi                                0
pe_iv                             13.74
pe_delta                          -0.52
pe_gamma                         0.0015
pe_theta                          -17.3
pe_vega                            7.95
pe_rho                         -88.3744
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-26 11:50:51,503 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23200.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                           123.6
ce_high                           124.3
ce_low                           119.25
ce_close                          124.2
ce_volume                        326775
ce_oi                           4374450
ce_coi                           193725
ce_iv                             14.99
ce_delta                           0.52
ce_gamma                         0.0013
ce_theta                         -25.57
ce_vega                            7.87
ce_rho                          86.2799
pe_symbol           NIFTY03APR2523200PE
pe_open                           100.2
pe_high                           105.2
pe_low                            99.55
pe_close                         100.75
pe_volume                        526875
pe_oi                           5030025
pe_coi                           101175
pe_iv                             13.55
pe_delta                          -0.48
pe_gamma                         0.0015
pe_theta                         -17.08
pe_vega                            7.87
pe_rho                         -81.3242
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-26 11:50:51,503 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-26 11:50:51,503 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-26 11:50:51,503 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-26 11:50:51,503 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Entering risk rules block
2025-05-26 11:50:51,504 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-26 11:50:51,504 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140604346862496
2025-05-26 11:50:51,504 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140604346862496, OptionType.PUT ID: 140604346862608
2025-05-26 11:50:51,504 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-26 11:50:51,504 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-26 11:50:51,504 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-26 11:50:51,506 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-26 11:50:51,789 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.283s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-26 11:50:51,791 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-26 11:50:51,792 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-26 11:50:51,850 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-26 11:50:51,853 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 1 - Tick data for specific strike 23450.0 is EMPTY after filtering windowed data.
2025-05-26 11:50:51,854 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Starting risk rules loop, has 3 rules
2025-05-26 11:50:51,854 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 1 of 3
2025-05-26 11:50:51,854 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-26 11:50:51,857 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - evaluate_risk_rule returned: triggered=True
2025-05-26 11:50:51,859 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Breaking out of risk rules loop due to triggered rule
2025-05-26 11:50:51,859 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-26 11:50:51,859 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Reached trade building section (outside all conditionals)
2025-05-26 11:50:51,859 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 1
2025-05-26 11:50:51,860 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 1, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 11:50:51,860 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23420.45, 'atm_strike': 23450.0, 'strike': 23450.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523450CE', 'ce_open': 102.85, 'ce_high': 106.65, 'ce_low': 97.85, 'ce_close': 100.75, 'ce_volume': 1015050, 'ce_oi': 978075, 'ce_coi': 0, 'ce_iv': 13.44, 'ce_delta': 0.48, 'ce_gamma': 0.0015, 'ce_theta': -23.27, 'ce_vega': 7.95, 'ce_rho': 81.3115, 'pe_symbol': 'NIFTY03APR2523450PE', 'pe_open': 118.2, 'pe_high': 122.3, 'pe_low': 112.2, 'pe_close': 115.7, 'pe_volume': 877125, 'pe_oi': 1618950, 'pe_coi': 0, 'pe_iv': 13.74, 'pe_delta': -0.52, 'pe_gamma': 0.0015, 'pe_theta': -17.3, 'pe_vega': 7.95, 'pe_rho': -88.3744, 'future_open': 23534.5, 'future_high': 23543.95, 'future_low': 23527.0, 'future_close': 23536.75, 'future_volume': 86475, 'future_oi': 12605100, 'future_coi': 0}
2025-05-26 11:50:51,860 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23195.3, 'atm_strike': 23200.0, 'strike': 23200.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523200CE', 'ce_open': 123.6, 'ce_high': 124.3, 'ce_low': 119.25, 'ce_close': 124.2, 'ce_volume': 326775, 'ce_oi': 4374450, 'ce_coi': 193725, 'ce_iv': 14.99, 'ce_delta': 0.52, 'ce_gamma': 0.0013, 'ce_theta': -25.57, 'ce_vega': 7.87, 'ce_rho': 86.2799, 'pe_symbol': 'NIFTY03APR2523200PE', 'pe_open': 100.2, 'pe_high': 105.2, 'pe_low': 99.55, 'pe_close': 100.75, 'pe_volume': 526875, 'pe_oi': 5030025, 'pe_coi': 101175, 'pe_iv': 13.55, 'pe_delta': -0.48, 'pe_gamma': 0.0015, 'pe_theta': -17.08, 'pe_vega': 7.87, 'pe_rho': -81.3242, 'future_open': 23347.0, 'future_high': 23348.85, 'future_low': 23339.0, 'future_close': 23348.1, 'future_volume': 19050, 'future_oi': 13928550, 'future_coi': -28275, 'close': 1450.2, 'datetime': Timestamp('2025-04-01 12:00:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-26 11:50:51,860 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=CALL, Transaction=SELL, Lots=1
2025-05-26 11:50:51,860 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-26 11:50:51,860 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-26 11:50:51,860 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-26 11:50:51,860 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -1172.5000000000002, PnL (Slippage): -1172.5000000000002, Net PnL: -1172.5000000000002
2025-05-26 11:50:51,860 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-26 11:50:51,860 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '1', 'entry_date': '2025-04-01', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2025-04-01', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23450, 'instrument_type': 'CE', 'side': 'SELL', 'filled_quantity': 50.0, 'entry_price': 100.75, 'exit_price': 124.2, 'points': -23.450000000000003, 'pointsAfterSlippage': -23.450000000000003, 'pnl': -1172.5000000000002, 'pnlAfterSlippage': -1172.5000000000002, 'expenses': 0.0, 'netPnlAfterExpenses': -1172.5000000000002, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23420.45, 'index_exit_price': 23195.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-01 09:16:00', 'exit_datetime': '2025-04-01 12:00:00'}
2025-05-26 11:50:51,861 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 1, about to append. Total records before: 0
2025-05-26 11:50:51,861 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 1. Total records now: 1
2025-05-26 11:50:51,861 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 2 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 11:50:51,861 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-26 11:50:51,861 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-26 11:50:51,890 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.028s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-26 11:50:51,929 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.039s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-26 11:50:51,929 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Query results: entry_df size=1, exit_df size=1
2025-05-26 11:50:51,929 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Got entry and exit data, proceeding...
2025-05-26 11:50:51,930 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
                        trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523450CE
ce_open                          102.85
ce_high                          106.65
ce_low                            97.85
ce_close                         100.75
ce_volume                       1015050
ce_oi                            978075
ce_coi                                0
ce_iv                             13.44
ce_delta                           0.48
ce_gamma                         0.0015
ce_theta                         -23.27
ce_vega                            7.95
ce_rho                          81.3115
pe_symbol           NIFTY03APR2523450PE
pe_open                           118.2
pe_high                           122.3
pe_low                            112.2
pe_close                          115.7
pe_volume                        877125
pe_oi                           1618950
pe_coi                                0
pe_iv                             13.74
pe_delta                          -0.52
pe_gamma                         0.0015
pe_theta                          -17.3
pe_vega                            7.95
pe_rho                         -88.3744
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-26 11:50:51,931 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23200.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                           123.6
ce_high                           124.3
ce_low                           119.25
ce_close                          124.2
ce_volume                        326775
ce_oi                           4374450
ce_coi                           193725
ce_iv                             14.99
ce_delta                           0.52
ce_gamma                         0.0013
ce_theta                         -25.57
ce_vega                            7.87
ce_rho                          86.2799
pe_symbol           NIFTY03APR2523200PE
pe_open                           100.2
pe_high                           105.2
pe_low                            99.55
pe_close                         100.75
pe_volume                        526875
pe_oi                           5030025
pe_coi                           101175
pe_iv                             13.55
pe_delta                          -0.48
pe_gamma                         0.0015
pe_theta                         -17.08
pe_vega                            7.87
pe_rho                         -81.3242
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-26 11:50:51,931 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-26 11:50:51,931 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-26 11:50:51,931 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-26 11:50:51,931 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Entering risk rules block
2025-05-26 11:50:51,932 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-26 11:50:51,932 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140604346862608
2025-05-26 11:50:51,932 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140604346862496, OptionType.PUT ID: 140604346862608
2025-05-26 11:50:51,932 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-26 11:50:51,932 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-26 11:50:51,932 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-26 11:50:51,934 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-26 11:50:52,222 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.288s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-26 11:50:52,224 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-26 11:50:52,224 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-26 11:50:52,280 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-26 11:50:52,283 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 2 - Tick data for specific strike 23450.0 is EMPTY after filtering windowed data.
2025-05-26 11:50:52,283 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Starting risk rules loop, has 3 rules
2025-05-26 11:50:52,283 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 1 of 3
2025-05-26 11:50:52,283 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-26 11:50:52,293 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-26 11:50:52,294 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 1
2025-05-26 11:50:52,294 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 2 of 3
2025-05-26 11:50:52,294 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type TAKEPROFIT
2025-05-26 11:50:52,304 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-26 11:50:52,304 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 2
2025-05-26 11:50:52,304 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 3 of 3
2025-05-26 11:50:52,304 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type TRAIL
2025-05-26 11:50:52,307 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-26 11:50:52,307 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 3
2025-05-26 11:50:52,307 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-26 11:50:52,307 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Reached trade building section (outside all conditionals)
2025-05-26 11:50:52,307 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 2
2025-05-26 11:50:52,307 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 2, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 11:50:52,307 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23420.45, 'atm_strike': 23450.0, 'strike': 23450.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523450CE', 'ce_open': 102.85, 'ce_high': 106.65, 'ce_low': 97.85, 'ce_close': 100.75, 'ce_volume': 1015050, 'ce_oi': 978075, 'ce_coi': 0, 'ce_iv': 13.44, 'ce_delta': 0.48, 'ce_gamma': 0.0015, 'ce_theta': -23.27, 'ce_vega': 7.95, 'ce_rho': 81.3115, 'pe_symbol': 'NIFTY03APR2523450PE', 'pe_open': 118.2, 'pe_high': 122.3, 'pe_low': 112.2, 'pe_close': 115.7, 'pe_volume': 877125, 'pe_oi': 1618950, 'pe_coi': 0, 'pe_iv': 13.74, 'pe_delta': -0.52, 'pe_gamma': 0.0015, 'pe_theta': -17.3, 'pe_vega': 7.95, 'pe_rho': -88.3744, 'future_open': 23534.5, 'future_high': 23543.95, 'future_low': 23527.0, 'future_close': 23536.75, 'future_volume': 86475, 'future_oi': 12605100, 'future_coi': 0}
2025-05-26 11:50:52,307 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(12, 0), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23195.3, 'atm_strike': 23200.0, 'strike': 23200.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523200CE', 'ce_open': 123.6, 'ce_high': 124.3, 'ce_low': 119.25, 'ce_close': 124.2, 'ce_volume': 326775, 'ce_oi': 4374450, 'ce_coi': 193725, 'ce_iv': 14.99, 'ce_delta': 0.52, 'ce_gamma': 0.0013, 'ce_theta': -25.57, 'ce_vega': 7.87, 'ce_rho': 86.2799, 'pe_symbol': 'NIFTY03APR2523200PE', 'pe_open': 100.2, 'pe_high': 105.2, 'pe_low': 99.55, 'pe_close': 100.75, 'pe_volume': 526875, 'pe_oi': 5030025, 'pe_coi': 101175, 'pe_iv': 13.55, 'pe_delta': -0.48, 'pe_gamma': 0.0015, 'pe_theta': -17.08, 'pe_vega': 7.87, 'pe_rho': -81.3242, 'future_open': 23347.0, 'future_high': 23348.85, 'future_low': 23339.0, 'future_close': 23348.1, 'future_volume': 19050, 'future_oi': 13928550, 'future_coi': -28275}
2025-05-26 11:50:52,308 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=SELL, Lots=1
2025-05-26 11:50:52,308 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-26 11:50:52,308 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-26 11:50:52,308 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-26 11:50:52,308 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: 747.5000000000001, PnL (Slippage): 747.5000000000001, Net PnL: 747.5000000000001
2025-05-26 11:50:52,308 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-26 11:50:52,308 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '2', 'entry_date': '2025-04-01', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2025-04-01', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23450, 'instrument_type': 'PE', 'side': 'SELL', 'filled_quantity': 50.0, 'entry_price': 115.7, 'exit_price': 100.75, 'points': 14.950000000000003, 'pointsAfterSlippage': 14.950000000000003, 'pnl': 747.5000000000001, 'pnlAfterSlippage': 747.5000000000001, 'expenses': 0.0, 'netPnlAfterExpenses': 747.5000000000001, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23420.45, 'index_exit_price': 23195.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-01 09:16:00', 'exit_datetime': '2025-04-01 12:00:00'}
2025-05-26 11:50:52,308 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 2, about to append. Total records before: 1
2025-05-26 11:50:52,308 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 2. Total records now: 2
2025-05-26 11:50:52,308 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 3 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 11:50:52,308 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-26 11:50:52,308 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-26 11:50:52,337 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.029s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-26 11:50:52,376 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.039s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-26 11:50:52,376 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Query results: entry_df size=1, exit_df size=1
2025-05-26 11:50:52,377 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Got entry and exit data, proceeding...
2025-05-26 11:50:52,378 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
                        trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23550.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY03APR2523550CE
ce_open                           61.15
ce_high                           61.75
ce_low                             55.0
ce_close                          56.45
ce_volume                       1147575
ce_oi                           2744250
ce_coi                                0
ce_iv                             12.93
ce_delta                           0.34
ce_gamma                         0.0014
ce_theta                         -19.86
ce_vega                            7.25
ce_rho                          56.1949
pe_symbol           NIFTY03APR2523550PE
pe_open                           174.0
pe_high                           180.1
pe_low                            167.8
pe_close                          175.5
pe_volume                        326550
pe_oi                           1975725
pe_coi                                0
pe_iv                             13.83
pe_delta                          -0.66
pe_gamma                         0.0013
pe_theta                         -14.93
pe_vega                            7.34
pe_rho                        -112.5433
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-26 11:50:52,378 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23300.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY03APR2523300CE
ce_open                           75.59
ce_high                           76.15
ce_low                            73.15
ce_close                          76.15
ce_volume                        250950
ce_oi                           7928250
ce_coi                           109950
ce_iv                             14.54
ce_delta                           0.37
ce_gamma                         0.0013
ce_theta                         -23.12
ce_vega                            7.53
ce_rho                          63.7039
pe_symbol           NIFTY03APR2523300PE
pe_open                           152.9
pe_high                          158.35
pe_low                           151.65
pe_close                         153.44
pe_volume                        252000
pe_oi                           4303500
pe_coi                           -10725
pe_iv                             13.14
pe_delta                          -0.63
pe_gamma                         0.0015
pe_theta                         -14.47
pe_vega                            7.45
pe_rho                        -107.0027
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-26 11:50:52,379 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-26 11:50:52,379 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-26 11:50:52,379 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-26 11:50:52,379 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Entering risk rules block
2025-05-26 11:50:52,379 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-26 11:50:52,379 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140604346862496
2025-05-26 11:50:52,379 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140604346862496, OptionType.PUT ID: 140604346862608
2025-05-26 11:50:52,379 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-26 11:50:52,379 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-26 11:50:52,380 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-26 11:50:52,381 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-26 11:50:52,686 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.305s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-26 11:50:52,688 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-26 11:50:52,688 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-26 11:50:52,749 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-26 11:50:52,754 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 3 - Tick data for specific strike 23550.0 is EMPTY after filtering windowed data.
2025-05-26 11:50:52,754 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Starting risk rules loop, has 3 rules
2025-05-26 11:50:52,754 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 1 of 3
2025-05-26 11:50:52,754 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-26 11:50:52,765 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - evaluate_risk_rule returned: triggered=False
2025-05-26 11:50:52,765 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Finished processing risk rule 1
2025-05-26 11:50:52,765 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 2 of 3
2025-05-26 11:50:52,765 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - About to call evaluate_risk_rule for rule type TAKEPROFIT
2025-05-26 11:50:52,769 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - evaluate_risk_rule returned: triggered=True
2025-05-26 11:50:52,771 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Breaking out of risk rules loop due to triggered rule
2025-05-26 11:50:52,771 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-26 11:50:52,771 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Reached trade building section (outside all conditionals)
2025-05-26 11:50:52,771 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 3
2025-05-26 11:50:52,771 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 3, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 11:50:52,771 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23420.45, 'atm_strike': 23450.0, 'strike': 23550.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'OTM2', 'put_strike_type': 'ITM2', 'ce_symbol': 'NIFTY03APR2523550CE', 'ce_open': 61.15, 'ce_high': 61.75, 'ce_low': 55.0, 'ce_close': 56.45, 'ce_volume': 1147575, 'ce_oi': 2744250, 'ce_coi': 0, 'ce_iv': 12.93, 'ce_delta': 0.34, 'ce_gamma': 0.0014, 'ce_theta': -19.86, 'ce_vega': 7.25, 'ce_rho': 56.1949, 'pe_symbol': 'NIFTY03APR2523550PE', 'pe_open': 174.0, 'pe_high': 180.1, 'pe_low': 167.8, 'pe_close': 175.5, 'pe_volume': 326550, 'pe_oi': 1975725, 'pe_coi': 0, 'pe_iv': 13.83, 'pe_delta': -0.66, 'pe_gamma': 0.0013, 'pe_theta': -14.93, 'pe_vega': 7.34, 'pe_rho': -112.5433, 'future_open': 23534.5, 'future_high': 23543.95, 'future_low': 23527.0, 'future_close': 23536.75, 'future_volume': 86475, 'future_oi': 12605100, 'future_coi': 0}
2025-05-26 11:50:52,771 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23195.3, 'atm_strike': 23200.0, 'strike': 23300.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'OTM2', 'put_strike_type': 'ITM2', 'ce_symbol': 'NIFTY03APR2523300CE', 'ce_open': 75.59, 'ce_high': 76.15, 'ce_low': 73.15, 'ce_close': 76.15, 'ce_volume': 250950, 'ce_oi': 7928250, 'ce_coi': 109950, 'ce_iv': 14.54, 'ce_delta': 0.37, 'ce_gamma': 0.0013, 'ce_theta': -23.12, 'ce_vega': 7.53, 'ce_rho': 63.7039, 'pe_symbol': 'NIFTY03APR2523300PE', 'pe_open': 152.9, 'pe_high': 158.35, 'pe_low': 151.65, 'pe_close': 153.44, 'pe_volume': 252000, 'pe_oi': 4303500, 'pe_coi': -10725, 'pe_iv': 13.14, 'pe_delta': -0.63, 'pe_gamma': 0.0015, 'pe_theta': -14.47, 'pe_vega': 7.45, 'pe_rho': -107.0027, 'future_open': 23347.0, 'future_high': 23348.85, 'future_low': 23339.0, 'future_close': 23348.1, 'future_volume': 19050, 'future_oi': 13928550, 'future_coi': -28275, 'close': 1450.2, 'datetime': Timestamp('2025-04-01 12:00:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-26 11:50:52,771 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=CALL, Transaction=BUY, Lots=1
2025-05-26 11:50:52,772 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-26 11:50:52,772 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-26 11:50:52,772 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-26 11:50:52,772 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: 985.0000000000001, PnL (Slippage): 985.0000000000001, Net PnL: 985.0000000000001
2025-05-26 11:50:52,772 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-26 11:50:52,772 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '3', 'entry_date': '2025-04-01', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2025-04-01', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23550, 'instrument_type': 'CE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 56.45, 'exit_price': 76.15, 'points': 19.700000000000003, 'pointsAfterSlippage': 19.700000000000003, 'pnl': 985.0000000000001, 'pnlAfterSlippage': 985.0000000000001, 'expenses': 0.0, 'netPnlAfterExpenses': 985.0000000000001, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23420.45, 'index_exit_price': 23195.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-01 09:16:00', 'exit_datetime': '2025-04-01 12:00:00'}
2025-05-26 11:50:52,772 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 3, about to append. Total records before: 2
2025-05-26 11:50:52,772 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 3. Total records now: 3
2025-05-26 11:50:52,772 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 4 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 11:50:52,772 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-26 11:50:52,772 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-26 11:50:52,803 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.030s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-26 11:50:52,843 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.040s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-26 11:50:52,843 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Query results: entry_df size=1, exit_df size=1
2025-05-26 11:50:52,843 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Got entry and exit data, proceeding...
2025-05-26 11:50:52,844 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
                        trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23350.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY03APR2523350CE
ce_open                           164.4
ce_high                           166.5
ce_low                            156.3
ce_close                          158.0
ce_volume                        408675
ce_oi                            381750
ce_coi                                0
ce_iv                             13.71
ce_delta                           0.62
ce_gamma                         0.0014
ce_theta                         -23.53
ce_vega                            7.54
ce_rho                         105.3755
pe_symbol           NIFTY03APR2523350PE
pe_open                            78.3
pe_high                           80.95
pe_low                            71.84
pe_close                          76.59
pe_volume                       1021875
pe_oi                           1656000
pe_coi                                0
pe_iv                             14.49
pe_delta                          -0.38
pe_gamma                         0.0013
pe_theta                         -18.33
pe_vega                            7.58
pe_rho                         -64.7877
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-26 11:50:52,845 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23100.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY03APR2523100CE
ce_open                           186.9
ce_high                           188.0
ce_low                            181.8
ce_close                          187.2
ce_volume                         77775
ce_oi                            957825
ce_coi                            12900
ce_iv                             15.68
ce_delta                           0.66
ce_gamma                         0.0012
ce_theta                         -25.85
ce_vega                            7.36
ce_rho                         106.8743
pe_symbol           NIFTY03APR2523100PE
pe_open                            63.7
pe_high                            67.2
pe_low                            63.05
pe_close                          64.15
pe_volume                        269475
pe_oi                           3243600
pe_coi                            78900
pe_iv                             14.17
pe_delta                          -0.34
pe_gamma                         0.0013
pe_theta                         -17.23
pe_vega                            7.25
pe_rho                         -57.8964
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-26 11:50:52,845 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-26 11:50:52,845 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-26 11:50:52,845 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-26 11:50:52,845 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Entering risk rules block
2025-05-26 11:50:52,846 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-26 11:50:52,846 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140604346862608
2025-05-26 11:50:52,846 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140604346862496, OptionType.PUT ID: 140604346862608
2025-05-26 11:50:52,846 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-26 11:50:52,846 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-26 11:50:52,846 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-26 11:50:52,847 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-26 11:50:53,141 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.294s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-26 11:50:53,143 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-26 11:50:53,144 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-26 11:50:53,198 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-26 11:50:53,201 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 4 - Tick data for specific strike 23350.0 is EMPTY after filtering windowed data.
2025-05-26 11:50:53,201 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Starting risk rules loop, has 3 rules
2025-05-26 11:50:53,201 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 1 of 3
2025-05-26 11:50:53,202 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-26 11:50:53,205 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - evaluate_risk_rule returned: triggered=True
2025-05-26 11:50:53,207 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Breaking out of risk rules loop due to triggered rule
2025-05-26 11:50:53,207 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-26 11:50:53,207 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Reached trade building section (outside all conditionals)
2025-05-26 11:50:53,207 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 4
2025-05-26 11:50:53,207 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 11:50:53,207 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23420.45, 'atm_strike': 23450.0, 'strike': 23350.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY03APR2523350CE', 'ce_open': 164.4, 'ce_high': 166.5, 'ce_low': 156.3, 'ce_close': 158.0, 'ce_volume': 408675, 'ce_oi': 381750, 'ce_coi': 0, 'ce_iv': 13.71, 'ce_delta': 0.62, 'ce_gamma': 0.0014, 'ce_theta': -23.53, 'ce_vega': 7.54, 'ce_rho': 105.3755, 'pe_symbol': 'NIFTY03APR2523350PE', 'pe_open': 78.3, 'pe_high': 80.95, 'pe_low': 71.84, 'pe_close': 76.59, 'pe_volume': 1021875, 'pe_oi': 1656000, 'pe_coi': 0, 'pe_iv': 14.49, 'pe_delta': -0.38, 'pe_gamma': 0.0013, 'pe_theta': -18.33, 'pe_vega': 7.58, 'pe_rho': -64.7877, 'future_open': 23534.5, 'future_high': 23543.95, 'future_low': 23527.0, 'future_close': 23536.75, 'future_volume': 86475, 'future_oi': 12605100, 'future_coi': 0}
2025-05-26 11:50:53,207 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23195.3, 'atm_strike': 23200.0, 'strike': 23100.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY03APR2523100CE', 'ce_open': 186.9, 'ce_high': 188.0, 'ce_low': 181.8, 'ce_close': 187.2, 'ce_volume': 77775, 'ce_oi': 957825, 'ce_coi': 12900, 'ce_iv': 15.68, 'ce_delta': 0.66, 'ce_gamma': 0.0012, 'ce_theta': -25.85, 'ce_vega': 7.36, 'ce_rho': 106.8743, 'pe_symbol': 'NIFTY03APR2523100PE', 'pe_open': 63.7, 'pe_high': 67.2, 'pe_low': 63.05, 'pe_close': 64.15, 'pe_volume': 269475, 'pe_oi': 3243600, 'pe_coi': 78900, 'pe_iv': 14.17, 'pe_delta': -0.34, 'pe_gamma': 0.0013, 'pe_theta': -17.23, 'pe_vega': 7.25, 'pe_rho': -57.8964, 'future_open': 23347.0, 'future_high': 23348.85, 'future_low': 23339.0, 'future_close': 23348.1, 'future_volume': 19050, 'future_oi': 13928550, 'future_coi': -28275, 'close': 2.15, 'datetime': Timestamp('2025-04-01 12:00:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-26 11:50:53,208 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-26 11:50:53,208 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-26 11:50:53,208 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-26 11:50:53,208 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-26 11:50:53,208 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -621.9999999999999, PnL (Slippage): -621.9999999999999, Net PnL: -621.9999999999999
2025-05-26 11:50:53,208 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-26 11:50:53,208 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2025-04-01', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2025-04-01', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23350, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 76.59, 'exit_price': 64.15, 'points': -12.439999999999998, 'pointsAfterSlippage': -12.439999999999998, 'pnl': -621.9999999999999, 'pnlAfterSlippage': -621.9999999999999, 'expenses': 0.0, 'netPnlAfterExpenses': -621.9999999999999, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23420.45, 'index_exit_price': 23195.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-01 09:16:00', 'exit_datetime': '2025-04-01 12:00:00'}
2025-05-26 11:50:53,208 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 4, about to append. Total records before: 3
2025-05-26 11:50:53,208 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 4. Total records now: 4
2025-05-26 11:50:53,208 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Number of trade records generated: 4
2025-05-26 11:50:53,208 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Generated 4 trade records via model-driven path
2025-05-26 11:50:53,208 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Calculating tick-by-tick P&L for Max Profit/Loss tracking
2025-05-26 11:50:53,539 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.331s, returned 375 rows. Query: SELECT SUBSTRING('SELECT DISTINCT EXTRACT(HOUR FROM trade_time) * 10000 + 
                                       EXTRACT(MINUTE FROM trade_time) * 100 + 
                                       EXTRACT(SECOND FROM trad', 1, 200) ...
2025-05-26 11:50:53,861 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.321s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:53,878 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:53,898 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:53,914 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:53,930 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:53,949 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:53,970 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:53,989 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,009 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,029 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,049 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,070 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,089 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,109 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,128 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,148 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,168 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,188 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,207 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,227 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,247 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,267 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,286 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,306 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,326 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,346 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,365 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,385 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,405 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,425 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,444 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,464 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,484 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,504 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,524 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,543 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,563 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,583 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,603 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,622 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,642 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,662 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,681 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,701 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,721 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,741 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,761 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,780 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,800 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,820 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,840 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,859 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,879 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,899 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,919 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,939 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,958 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,978 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:54,998 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,018 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,038 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,057 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,077 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,097 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,117 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,137 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,156 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,176 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,196 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,216 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,236 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,256 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,276 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,295 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,315 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,335 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,355 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,375 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,395 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,414 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,434 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,454 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,474 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,494 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,514 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,534 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,554 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,574 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,594 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,614 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,634 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,653 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,673 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,694 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,714 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,734 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,754 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,774 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,830 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.056s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,850 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,870 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,890 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,910 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,930 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,950 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,970 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:55,990 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,010 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,030 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,050 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,069 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,089 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,109 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,129 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,149 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,169 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,190 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,209 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,229 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,248 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,268 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,288 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,308 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,327 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,347 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,367 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,386 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,406 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,426 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,446 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,465 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,485 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,505 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,524 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,544 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,563 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,583 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,603 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,624 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.021s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,644 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,664 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,683 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,703 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,723 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,743 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,763 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,783 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,802 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,822 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,842 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,861 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,881 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,901 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,920 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,940 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,959 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,979 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:56,999 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,018 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,038 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,058 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,077 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,097 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,117 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,137 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,157 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,177 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,196 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,217 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,236 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,256 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,276 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,295 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,315 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,334 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,354 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,374 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,393 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,413 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,432 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,452 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,472 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,492 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,511 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,531 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,551 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,571 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,590 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,611 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,630 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,650 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,670 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,689 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,709 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,729 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,748 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,768 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,788 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,807 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,827 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,843 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,862 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,877 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,897 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,916 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,936 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,955 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,975 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:57,995 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,015 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,034 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,054 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,074 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,093 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,113 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,132 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,152 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,172 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,192 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,212 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,231 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,251 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,270 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,290 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,309 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,329 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,349 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,368 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,388 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,407 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,427 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,447 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,467 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,486 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,506 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,525 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,545 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,565 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,584 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,604 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,624 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,643 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,663 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,683 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,703 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,722 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,742 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,761 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,781 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,801 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,819 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,838 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,855 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,874 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,894 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,913 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,933 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,953 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,973 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:58,992 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,012 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,032 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,052 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,071 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,091 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,111 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,131 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,151 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,171 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,191 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,211 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,231 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,254 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.022s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,273 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,293 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,313 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,333 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,352 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,372 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,392 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,412 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,431 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,451 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,471 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,490 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,510 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,530 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,550 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,569 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,589 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,609 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,629 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,648 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,668 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,688 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,708 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,728 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,747 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,767 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,787 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,807 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,826 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,843 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,863 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,883 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,902 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,922 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,941 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,961 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,980 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:50:59,999 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,019 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,039 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,058 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,078 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,097 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,117 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,137 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,156 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,176 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,196 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,215 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,235 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,255 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,275 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,295 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,315 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,334 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,354 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,374 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,393 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,413 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,433 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,453 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,473 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,492 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,512 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,532 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,551 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,571 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,591 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,611 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,631 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,650 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,670 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,689 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,709 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,728 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,748 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,768 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,824 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.056s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,844 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,864 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,884 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,903 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,923 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,942 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,962 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:00,981 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,001 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,020 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,040 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,060 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,080 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,099 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,119 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,138 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,158 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,178 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,197 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,217 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,237 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,257 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,276 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,297 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,316 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,336 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,356 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,377 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,396 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,416 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,436 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,456 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,476 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,496 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,516 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,535 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,555 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,576 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,595 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,615 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,635 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,655 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,674 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,694 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,714 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,734 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,753 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,773 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,793 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,813 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,833 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,853 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,873 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,893 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,913 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,932 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,953 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,973 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:01,993 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,013 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,032 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,052 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,071 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,091 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,110 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,130 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,150 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,169 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,189 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,208 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,228 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,248 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,267 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,287 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,307 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,332 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.025s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,352 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,372 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,392 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,411 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,431 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,451 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,470 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,490 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,509 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,529 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,548 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,568 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,587 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,607 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,627 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,646 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,666 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,685 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,705 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,724 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,744 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,764 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,783 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,803 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,820 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,839 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,855 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,870 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,889 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,905 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,924 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,944 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,963 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:02,984 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,004 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,024 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,044 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,064 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,084 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,103 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,123 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,142 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,162 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,182 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,201 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,221 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,241 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,260 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,280 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,300 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,319 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,339 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,360 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.021s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,380 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,399 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,419 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,439 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,458 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,478 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,498 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,519 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,539 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,559 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,579 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,598 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,619 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,638 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,658 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,678 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,697 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,717 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,737 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,757 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,776 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,796 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,815 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,835 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,855 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,875 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,895 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,914 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,934 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,954 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,973 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:03,993 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,013 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,033 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,053 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,073 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,092 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,112 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,132 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,151 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,171 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,191 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,211 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,230 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,250 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,270 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,290 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,309 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,329 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,349 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,368 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,388 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,407 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,427 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,447 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,467 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,486 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,506 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,526 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,546 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,565 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,585 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,605 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,625 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,645 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,665 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,684 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,704 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,724 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,743 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,763 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,782 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,802 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,819 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,839 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,859 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,878 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,898 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,917 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,937 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,956 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,976 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:04,996 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,015 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,035 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,055 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,075 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,094 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,114 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,134 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,154 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,174 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,193 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,213 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,232 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,252 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,272 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,291 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,311 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,330 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,350 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,369 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,389 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,408 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,428 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,448 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,468 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,487 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,507 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,527 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,546 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,565 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,585 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,605 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,624 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,644 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,664 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,683 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,703 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,723 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,743 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,762 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,782 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,838 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.056s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,858 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,878 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,898 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,917 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,937 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,957 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,976 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:05,996 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,016 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,036 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,055 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,075 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,095 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,115 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,134 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,154 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,174 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,194 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,214 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,234 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,253 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,273 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,293 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,313 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,333 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,352 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,372 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,392 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,412 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,432 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,452 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,472 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,492 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,512 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,532 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,552 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,572 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,591 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,611 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,631 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,650 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,670 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,690 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,709 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,729 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,749 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,769 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,789 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,808 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,828 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,848 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,868 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,887 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,907 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,927 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,947 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:51:06,970 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Calculated tick P&L for date 250401: 375 timestamps
2025-05-26 11:51:06,970 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Closed reusable HeavyDB connection from get_trades_for_portfolio
2025-05-26 11:51:07,005 - backtester_stable.BTRUN.builders - WARNING - utils.MARGIN_INFO not found or empty - margin requirements will be 0. Attempting to populate.
2025-05-26 11:51:07,006 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'margin_symbol_info.json' at /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-26 11:51:07,006 - backtester_stable.BTRUN.core.utils - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json (Broker: angel)
2025-05-26 11:51:07,006 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.MARGIN_INFO for angel.
2025-05-26 11:51:07,006 - backtester_stable.BTRUN.builders - ERROR - utils.MARGIN_INFO still empty after populate attempt. Margin req will be 0.
2025-05-26 11:51:07,012 - backtester_stable.BTRUN.builders - DEBUG - Strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL margin requirement: 0.00
2025-05-26 11:51:07,012 - backtester_stable.BTRUN.builders - INFO - Total portfolio margin requirement: 0.00
2025-05-26 11:51:07,013 - backtester_stable.BTRUN.builders - DEBUG - Converting tick PnL to daywise DataFrame.
2025-05-26 11:51:07,014 - backtester_stable.BTRUN.builders - INFO - Backtest response parsed.
2025-05-26 11:51:07,058 - backtester_stable.BTRUN.core.runtime - INFO - Generated metrics_df with 50 records for strategies: ['Combined' 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL']
2025-05-26 11:51:07,136 - backtester_stable.BTRUN.core.runtime - INFO - Saving Excel output to output/tbs_apr3_heavydb.xlsx
2025-05-26 11:51:07,137 - backtester_stable.BTRUN.core.runtime - DEBUG - [DEBUG save_backtest_results] save_excel=True, metrics_df is empty: False
2025-05-26 11:51:07,137 - backtester_stable.BTRUN.core.io - INFO - 2025-05-26 11:51:07.137101, Started writing stats to excel file: output/tbs_apr3_heavydb.xlsx
2025-05-26 11:51:07,137 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Temporarily switching to CPU-only mode.
2025-05-26 11:51:07,180 - backtester_stable.BTRUN.core.io - INFO - Added PortfolioParameter sheet in legacy 2x2 format
2025-05-26 11:51:07,213 - backtester_stable.BTRUN.core.io - INFO - Added GeneralParameter sheet from input files
2025-05-26 11:51:07,225 - backtester_stable.BTRUN.core.io - INFO - Added LegParameter sheet from input files
2025-05-26 11:51:07,225 - backtester_stable.BTRUN.core.io - INFO - Filtered metrics to show only 'Combined' entries: 25 rows
2025-05-26 11:51:07,279 - backtester_stable.BTRUN.core.io - INFO - Added PORTFOLIO Results sheet in legacy format
2025-05-26 11:51:07,339 - backtester_stable.BTRUN.core.io - INFO - 2025-05-26 11:51:07.339068, Excel file prepared successfully, Time taken: 0.2s
2025-05-26 11:51:07,339 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Restored GPU_ENABLED to: False
2025-05-26 11:51:07,339 - backtester_stable.BTRUN.core.runtime - INFO - Saving JSON output to output/tbs_apr3_heavydb.json
2025-05-26 11:51:07,364 - backtester_stable.BTRUN.core.io - INFO - --- JSON DUMP DEBUG START ---
2025-05-26 11:51:07,364 - backtester_stable.BTRUN.core.io - INFO - --- JSON DUMP DEBUG END ---
2025-05-26 11:51:07,365 - backtester_stable.BTRUN.core.io - INFO - Successfully wrote JSON output to output/tbs_apr3_heavydb.json
2025-05-26 11:51:07,370 - backtester_stable.BTRUN.core.io - ERROR - Text-to-speech failed: This means you probably do not have eSpeak or eSpeak-ng installed!
