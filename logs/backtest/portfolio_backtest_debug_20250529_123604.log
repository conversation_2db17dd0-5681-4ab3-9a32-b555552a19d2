2025-05-29 12:36:04,088 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-29 12:36:04,089 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-29 12:36:04,089 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/../models', '/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages', '/srv/samba/shared']
2025-05-29 12:36:04,089 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-29 12:36:04,089 - __main__ - DEBUG - GPU enabled: False
2025-05-29 12:36:04,091 - __main__ - INFO - GPU acceleration enabled: False
2025-05-29 12:36:04,091 - __main__ - INFO - HeavyDB integration available (Host: 127.0.0.1)
2025-05-29 12:36:04,314 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-29 12:36:04,314 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-29 12:36:04,314 - __main__ - INFO - Runtime flags – workers: 2, retry_cpu: False
2025-05-29 12:36:04,314 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-29 12:36:04,314 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-29 12:36:04,315 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-29 12:36:04,320 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-29 12:36:04,321 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-29 12:36:04,321 - backtester_stable.BTRUN.core.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-29 12:36:04,321 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-29 12:36:04,321 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-29 12:36:04,321 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-29 12:36:04,321 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-29 12:36:04,321 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-29 12:36:04,321 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-29 12:36:04,321 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-29 12:36:04,321 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-29 12:36:04,322 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-29 12:36:04,448 - __main__ - INFO - Successfully parsed 1 portfolio(s) using modern parser.
2025-05-29 12:36:04,448 - __main__ - INFO - Queueing portfolio: NIF0DTE (from modern_portfolio_parser)
2025-05-29 12:36:04,448 - __main__ - INFO - Executing 1 portfolios sequentially (multiprocessing temporarily disabled for debugging)
2025-05-29 12:36:04,449 - __main__ - INFO - Processing payload 1/1: NIF0DTE
2025-05-29 12:36:04,449 - backtester_stable.BTRUN.core.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'NIF0DTE'
2025-05-29 12:36:04,667 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-29 12:36:04,709 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.041s, returned 563 rows. Query: SELECT SUBSTRING('SELECT DISTINCT trade_date FROM nifty_option_chain', 1, 200) ...
2025-05-29 12:36:04,750 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - Cached 563 distinct trade dates
2025-05-29 12:36:04,750 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] portfolio_model_dict keys: ['portfolio_name', 'start_date', 'end_date', 'slippage_percent', 'margin_multiplier', 'is_tick_bt', 'strategies', 'extra_params']
2025-05-29 12:36:04,750 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 1 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-29 12:36:04,750 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Error during get_trades_for_portfolio main logic: _ensure_hhmmss could not interpret value 'None'
Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/query_builder/entry_exit_sql.py", line 54, in _ensure_hhmmss
    v_int = int(s)
ValueError: invalid literal for int() with base 10: 'None'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/heavydb_trade_processing.py", line 107, in get_trades_for_portfolio
    entry_sql = build_entry_sql(leg, current_trade_date_obj.isoformat(), strategy_exit_time_int)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/query_builder/entry_exit_sql.py", line 221, in build_entry_sql
    time_int = _ensure_hhmmss(entry_time_hhmmss)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/query_builder/entry_exit_sql.py", line 70, in _ensure_hhmmss
    raise ValueError(f"_ensure_hhmmss could not interpret value '{value}'") from exc
ValueError: _ensure_hhmmss could not interpret value 'None'
2025-05-29 12:36:04,751 - debug_detail - ERROR - TRADE_PROC_DETAIL: Exception in get_trades_for_portfolio: _ensure_hhmmss could not interpret value 'None'
Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/query_builder/entry_exit_sql.py", line 54, in _ensure_hhmmss
    v_int = int(s)
ValueError: invalid literal for int() with base 10: 'None'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/heavydb_trade_processing.py", line 107, in get_trades_for_portfolio
    entry_sql = build_entry_sql(leg, current_trade_date_obj.isoformat(), strategy_exit_time_int)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/query_builder/entry_exit_sql.py", line 221, in build_entry_sql
    time_int = _ensure_hhmmss(entry_time_hhmmss)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/query_builder/entry_exit_sql.py", line 70, in _ensure_hhmmss
    raise ValueError(f"_ensure_hhmmss could not interpret value '{value}'") from exc
ValueError: _ensure_hhmmss could not interpret value 'None'
2025-05-29 12:36:04,752 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Closed reusable HeavyDB connection from get_trades_for_portfolio
2025-05-29 12:36:04,752 - debug_detail - WARNING - BUILDERS: No 'trades' data in bt_response['data'] or invalid format.
2025-05-29 12:36:04,752 - backtester_stable.BTRUN.core.runtime - WARNING - No trades found in backtest response after parsing in runtime.
2025-05-29 12:36:04,754 - backtester_stable.BTRUN.core.runtime - INFO - Saving Excel output to bt/backtester_stable/BTRUN/output/tbs_2024_january.xlsx
2025-05-29 12:36:04,754 - backtester_stable.BTRUN.core.runtime - WARNING - Empty metrics DataFrame - not saving Excel output
2025-05-29 12:36:04,754 - backtester_stable.BTRUN.core.runtime - INFO - Saving JSON output to bt/backtester_stable/BTRUN/output/tbs_2024_january.json
2025-05-29 12:36:04,754 - backtester_stable.BTRUN.core.runtime - WARNING - Empty metrics DataFrame - not saving JSON output
2025-05-29 12:36:04,758 - backtester_stable.BTRUN.core.io - ERROR - Text-to-speech failed: This means you probably do not have eSpeak or eSpeak-ng installed!
