2025-05-25 12:35:46,887 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-25 12:35:46,887 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-25 12:35:46,888 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/../models', '/srv/samba/shared', '/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages', '/srv/samba/shared']
2025-05-25 12:35:46,888 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-25 12:35:46,888 - __main__ - DEBUG - GPU enabled: False
2025-05-25 12:35:46,890 - __main__ - INFO - GPU acceleration enabled: False
2025-05-25 12:35:46,890 - __main__ - INFO - HeavyDB integration available (Host: 127.0.0.1)
2025-05-25 12:35:47,104 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-25 12:35:47,105 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-25 12:35:47,105 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-25 12:35:47,105 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-25 12:35:47,105 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-25 12:35:47,105 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-25 12:35:47,110 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-25 12:35:47,110 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-25 12:35:47,111 - backtester_stable.BTRUN.core.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-25 12:35:47,111 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-25 12:35:47,112 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-25 12:35:47,112 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-25 12:35:47,112 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-25 12:35:47,112 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-25 12:35:47,112 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-25 12:35:47,112 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-25 12:35:47,112 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-25 12:35:47,112 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-25 12:35:47,232 - __main__ - INFO - Successfully parsed 1 portfolio(s) using modern parser.
2025-05-25 12:35:47,233 - __main__ - INFO - Queueing portfolio: NIF0DTE (from modern_portfolio_parser)
2025-05-25 12:35:47,233 - __main__ - INFO - Executing 1 portfolios sequentially (multiprocessing temporarily disabled for debugging)
2025-05-25 12:35:47,233 - __main__ - INFO - Processing payload 1/1: NIF0DTE
2025-05-25 12:35:47,233 - backtester_stable.BTRUN.core.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'NIF0DTE'
2025-05-25 12:35:47,448 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-25 12:35:47,467 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 563 rows. Query: SELECT SUBSTRING('SELECT DISTINCT trade_date FROM nifty_option_chain', 1, 200) ...
2025-05-25 12:35:47,508 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - Cached 563 distinct trade dates
2025-05-25 12:35:47,509 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] portfolio_model_dict keys: ['portfolio_name', 'start_date', 'end_date', 'slippage_percent', 'margin_multiplier', 'is_tick_bt', 'strategies', 'extra_params']
2025-05-25 12:35:47,509 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:35:47,509 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:35:47,545 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.035s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:35:47,582 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.037s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:35:47,583 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523450CE
ce_open                          102.85
ce_high                          106.65
ce_low                            97.85
ce_close                         100.75
ce_volume                       1015050
ce_oi                            978075
ce_coi                                0
ce_iv                             13.44
ce_delta                           0.48
ce_gamma                         0.0015
ce_theta                         -23.27
ce_vega                            7.95
ce_rho                          81.3115
pe_symbol           NIFTY03APR2523450PE
pe_open                           118.2
pe_high                           122.3
pe_low                            112.2
pe_close                          115.7
pe_volume                        877125
pe_oi                           1618950
pe_coi                                0
pe_iv                             13.74
pe_delta                          -0.52
pe_gamma                         0.0015
pe_theta                          -17.3
pe_vega                            7.95
pe_rho                         -88.3744
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-25 12:35:47,584 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23200.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                           123.6
ce_high                           124.3
ce_low                           119.25
ce_close                          124.2
ce_volume                        326775
ce_oi                           4374450
ce_coi                           193725
ce_iv                             14.99
ce_delta                           0.52
ce_gamma                         0.0013
ce_theta                         -25.57
ce_vega                            7.87
ce_rho                          86.2799
pe_symbol           NIFTY03APR2523200PE
pe_open                           100.2
pe_high                           105.2
pe_low                            99.55
pe_close                         100.75
pe_volume                        526875
pe_oi                           5030025
pe_coi                           101175
pe_iv                             13.55
pe_delta                          -0.48
pe_gamma                         0.0015
pe_theta                         -17.08
pe_vega                            7.87
pe_rho                         -81.3242
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-25 12:35:47,584 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:35:47,584 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:35:47,584 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:35:47,585 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 139866552090912
2025-05-25 12:35:47,585 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:35:47,585 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:35:47,585 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:35:47,585 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:35:47,586 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:35:47,869 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.283s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:35:47,871 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:35:47,871 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:35:47,926 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:35:47,930 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 1 - Tick data for specific strike 23450.0 is EMPTY after filtering windowed data.
2025-05-25 12:35:47,930 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:47,931 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:47,931 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:47,931 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:35:47,931 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:35:47,960 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.029s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:35:47,981 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.021s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:35:47,982 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523450CE
ce_open                          102.85
ce_high                          106.65
ce_low                            97.85
ce_close                         100.75
ce_volume                       1015050
ce_oi                            978075
ce_coi                                0
ce_iv                             13.44
ce_delta                           0.48
ce_gamma                         0.0015
ce_theta                         -23.27
ce_vega                            7.95
ce_rho                          81.3115
pe_symbol           NIFTY03APR2523450PE
pe_open                           118.2
pe_high                           122.3
pe_low                            112.2
pe_close                          115.7
pe_volume                        877125
pe_oi                           1618950
pe_coi                                0
pe_iv                             13.74
pe_delta                          -0.52
pe_gamma                         0.0015
pe_theta                          -17.3
pe_vega                            7.95
pe_rho                         -88.3744
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-25 12:35:47,983 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23200.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                           123.6
ce_high                           124.3
ce_low                           119.25
ce_close                          124.2
ce_volume                        326775
ce_oi                           4374450
ce_coi                           193725
ce_iv                             14.99
ce_delta                           0.52
ce_gamma                         0.0013
ce_theta                         -25.57
ce_vega                            7.87
ce_rho                          86.2799
pe_symbol           NIFTY03APR2523200PE
pe_open                           100.2
pe_high                           105.2
pe_low                            99.55
pe_close                         100.75
pe_volume                        526875
pe_oi                           5030025
pe_coi                           101175
pe_iv                             13.55
pe_delta                          -0.48
pe_gamma                         0.0015
pe_theta                         -17.08
pe_vega                            7.87
pe_rho                         -81.3242
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-25 12:35:47,983 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:35:47,983 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:35:47,984 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:35:47,984 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 139866552091024
2025-05-25 12:35:47,984 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:35:47,984 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:35:47,984 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:35:47,984 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:35:47,985 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:35:48,265 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.280s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:35:48,267 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 12:35:48,268 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 12:35:48,323 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:35:48,327 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 2 - Tick data for specific strike 23450.0 is EMPTY after filtering windowed data.
2025-05-25 12:35:48,327 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:48,327 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:48,327 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:48,327 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:35:48,327 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:35:48,348 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:35:48,366 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:35:48,367 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23550.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY03APR2523550CE
ce_open                           61.15
ce_high                           61.75
ce_low                             55.0
ce_close                          56.45
ce_volume                       1147575
ce_oi                           2744250
ce_coi                                0
ce_iv                             12.93
ce_delta                           0.34
ce_gamma                         0.0014
ce_theta                         -19.86
ce_vega                            7.25
ce_rho                          56.1949
pe_symbol           NIFTY03APR2523550PE
pe_open                           174.0
pe_high                           180.1
pe_low                            167.8
pe_close                          175.5
pe_volume                        326550
pe_oi                           1975725
pe_coi                                0
pe_iv                             13.83
pe_delta                          -0.66
pe_gamma                         0.0013
pe_theta                         -14.93
pe_vega                            7.34
pe_rho                        -112.5433
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-25 12:35:48,368 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23300.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY03APR2523300CE
ce_open                           75.59
ce_high                           76.15
ce_low                            73.15
ce_close                          76.15
ce_volume                        250950
ce_oi                           7928250
ce_coi                           109950
ce_iv                             14.54
ce_delta                           0.37
ce_gamma                         0.0013
ce_theta                         -23.12
ce_vega                            7.53
ce_rho                          63.7039
pe_symbol           NIFTY03APR2523300PE
pe_open                           152.9
pe_high                          158.35
pe_low                           151.65
pe_close                         153.44
pe_volume                        252000
pe_oi                           4303500
pe_coi                           -10725
pe_iv                             13.14
pe_delta                          -0.63
pe_gamma                         0.0015
pe_theta                         -14.47
pe_vega                            7.45
pe_rho                        -107.0027
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-25 12:35:48,368 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:35:48,368 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:35:48,368 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:35:48,369 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 139866552090912
2025-05-25 12:35:48,369 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:35:48,369 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:35:48,369 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:35:48,369 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:35:48,370 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:35:48,678 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.308s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:35:48,680 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:35:48,681 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:35:48,735 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:35:48,738 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 3 - Tick data for specific strike 23550.0 is EMPTY after filtering windowed data.
2025-05-25 12:35:48,738 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:48,739 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:48,739 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:48,739 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:35:48,739 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:35:48,768 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.029s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:35:48,800 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.032s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:35:48,801 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23350.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY03APR2523350CE
ce_open                           164.4
ce_high                           166.5
ce_low                            156.3
ce_close                          158.0
ce_volume                        408675
ce_oi                            381750
ce_coi                                0
ce_iv                             13.71
ce_delta                           0.62
ce_gamma                         0.0014
ce_theta                         -23.53
ce_vega                            7.54
ce_rho                         105.3755
pe_symbol           NIFTY03APR2523350PE
pe_open                            78.3
pe_high                           80.95
pe_low                            71.84
pe_close                          76.59
pe_volume                       1021875
pe_oi                           1656000
pe_coi                                0
pe_iv                             14.49
pe_delta                          -0.38
pe_gamma                         0.0013
pe_theta                         -18.33
pe_vega                            7.58
pe_rho                         -64.7877
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-25 12:35:48,802 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23100.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY03APR2523100CE
ce_open                           186.9
ce_high                           188.0
ce_low                            181.8
ce_close                          187.2
ce_volume                         77775
ce_oi                            957825
ce_coi                            12900
ce_iv                             15.68
ce_delta                           0.66
ce_gamma                         0.0012
ce_theta                         -25.85
ce_vega                            7.36
ce_rho                         106.8743
pe_symbol           NIFTY03APR2523100PE
pe_open                            63.7
pe_high                            67.2
pe_low                            63.05
pe_close                          64.15
pe_volume                        269475
pe_oi                           3243600
pe_coi                            78900
pe_iv                             14.17
pe_delta                          -0.34
pe_gamma                         0.0013
pe_theta                         -17.23
pe_vega                            7.25
pe_rho                         -57.8964
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-25 12:35:48,802 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:35:48,802 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:35:48,802 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:35:48,802 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 139866552091024
2025-05-25 12:35:48,803 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:35:48,803 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:35:48,803 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:35:48,803 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:35:48,804 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:35:49,104 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.300s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:35:49,106 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 12:35:49,107 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 12:35:49,191 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:35:49,195 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 4 - Tick data for specific strike 23350.0 is EMPTY after filtering windowed data.
2025-05-25 12:35:49,196 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:49,196 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:49,196 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:49,196 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 12:35:49,196 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23420.45, 'atm_strike': 23450.0, 'strike': 23350.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY03APR2523350CE', 'ce_open': 164.4, 'ce_high': 166.5, 'ce_low': 156.3, 'ce_close': 158.0, 'ce_volume': 408675, 'ce_oi': 381750, 'ce_coi': 0, 'ce_iv': 13.71, 'ce_delta': 0.62, 'ce_gamma': 0.0014, 'ce_theta': -23.53, 'ce_vega': 7.54, 'ce_rho': 105.3755, 'pe_symbol': 'NIFTY03APR2523350PE', 'pe_open': 78.3, 'pe_high': 80.95, 'pe_low': 71.84, 'pe_close': 76.59, 'pe_volume': 1021875, 'pe_oi': 1656000, 'pe_coi': 0, 'pe_iv': 14.49, 'pe_delta': -0.38, 'pe_gamma': 0.0013, 'pe_theta': -18.33, 'pe_vega': 7.58, 'pe_rho': -64.7877, 'future_open': 23534.5, 'future_high': 23543.95, 'future_low': 23527.0, 'future_close': 23536.75, 'future_volume': 86475, 'future_oi': 12605100, 'future_coi': 0}
2025-05-25 12:35:49,196 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(12, 0), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23195.3, 'atm_strike': 23200.0, 'strike': 23100.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY03APR2523100CE', 'ce_open': 186.9, 'ce_high': 188.0, 'ce_low': 181.8, 'ce_close': 187.2, 'ce_volume': 77775, 'ce_oi': 957825, 'ce_coi': 12900, 'ce_iv': 15.68, 'ce_delta': 0.66, 'ce_gamma': 0.0012, 'ce_theta': -25.85, 'ce_vega': 7.36, 'ce_rho': 106.8743, 'pe_symbol': 'NIFTY03APR2523100PE', 'pe_open': 63.7, 'pe_high': 67.2, 'pe_low': 63.05, 'pe_close': 64.15, 'pe_volume': 269475, 'pe_oi': 3243600, 'pe_coi': 78900, 'pe_iv': 14.17, 'pe_delta': -0.34, 'pe_gamma': 0.0013, 'pe_theta': -17.23, 'pe_vega': 7.25, 'pe_rho': -57.8964, 'future_open': 23347.0, 'future_high': 23348.85, 'future_low': 23339.0, 'future_close': 23348.1, 'future_volume': 19050, 'future_oi': 13928550, 'future_coi': -28275}
2025-05-25 12:35:49,196 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-25 12:35:49,197 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 12:35:49,197 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 12:35:49,197 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 12:35:49,197 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -621.9999999999999, PnL (Slippage): -621.9999999999999, Net PnL: -621.9999999999999
2025-05-25 12:35:49,197 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 12:35:49,197 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2025-04-01', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2025-04-01', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23350, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 76.59, 'exit_price': 64.15, 'points': -12.439999999999998, 'pointsAfterSlippage': -12.439999999999998, 'pnl': -621.9999999999999, 'pnlAfterSlippage': -621.9999999999999, 'expenses': 0.0, 'netPnlAfterExpenses': -621.9999999999999, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23420.45, 'index_exit_price': 23195.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-01 09:16:00', 'exit_datetime': '2025-04-01 12:00:00'}
2025-05-25 12:35:49,198 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:35:49,198 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:35:49,222 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.024s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:35:49,250 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.028s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:35:49,251 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
trade_date                   2025-04-02
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23204.8
atm_strike                      23200.0
strike                          23200.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                          107.95
ce_high                           113.5
ce_low                           105.15
ce_close                          110.5
ce_volume                       2666850
ce_oi                           5862000
ce_coi                                0
ce_iv                             16.53
ce_delta                           0.53
ce_gamma                         0.0015
ce_theta                         -34.48
ce_vega                             6.2
ce_rho                          54.4841
pe_symbol           NIFTY03APR2523200PE
pe_open                            86.9
pe_high                            94.9
pe_low                            85.55
pe_close                          86.15
pe_volume                       3014850
pe_oi                           5689575
pe_coi                                0
pe_iv                             15.06
pe_delta                          -0.47
pe_gamma                         0.0017
pe_theta                         -25.36
pe_vega                             6.2
pe_rho                         -49.7483
future_open                     23339.0
future_high                     23344.7
future_low                     23331.25
future_close                   23344.15
future_volume                     43725
future_oi                      12808425
future_coi                            0
2025-05-25 12:35:49,252 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2025-04-02
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23261.45
atm_strike                      23250.0
strike                          23250.0
dte                                   1
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523250CE
ce_open                            83.1
ce_high                            87.9
ce_low                             83.1
ce_close                           87.0
ce_volume                        546750
ce_oi                           6733800
ce_coi                                0
ce_iv                             12.13
ce_delta                           0.55
ce_gamma                         0.0021
ce_theta                         -26.28
ce_vega                            6.18
ce_rho                           57.017
pe_symbol           NIFTY03APR2523250PE
pe_open                           70.65
pe_high                           70.65
pe_low                             65.7
pe_close                           66.8
pe_volume                        766500
pe_oi                           7853325
pe_coi                                0
pe_iv                             12.41
pe_delta                          -0.45
pe_gamma                          0.002
pe_theta                         -20.43
pe_vega                            6.19
pe_rho                         -47.8132
future_open                    23386.95
future_high                     23394.9
future_low                     23383.35
future_close                    23390.0
future_volume                      2475
future_oi                      12651900
future_coi                            0
2025-05-25 12:35:49,252 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:35:49,252 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:35:49,253 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:35:49,253 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 139866552090912
2025-05-25 12:35:49,253 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:35:49,253 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:35:49,253 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:35:49,253 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:35:49,255 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-02' AND trade_date <= '2025-04-02'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:35:49,544 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.289s, returned 18845 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:35:49,547 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18845 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:35:49,547 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:35:49,628 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:35:49,633 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 1 - Tick data for specific strike 23200.0 is EMPTY after filtering windowed data.
2025-05-25 12:35:49,633 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:49,633 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:49,634 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:49,634 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:35:49,634 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:35:49,659 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.024s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:35:49,679 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:35:49,681 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
trade_date                   2025-04-02
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23204.8
atm_strike                      23200.0
strike                          23200.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                          107.95
ce_high                           113.5
ce_low                           105.15
ce_close                          110.5
ce_volume                       2666850
ce_oi                           5862000
ce_coi                                0
ce_iv                             16.53
ce_delta                           0.53
ce_gamma                         0.0015
ce_theta                         -34.48
ce_vega                             6.2
ce_rho                          54.4841
pe_symbol           NIFTY03APR2523200PE
pe_open                            86.9
pe_high                            94.9
pe_low                            85.55
pe_close                          86.15
pe_volume                       3014850
pe_oi                           5689575
pe_coi                                0
pe_iv                             15.06
pe_delta                          -0.47
pe_gamma                         0.0017
pe_theta                         -25.36
pe_vega                             6.2
pe_rho                         -49.7483
future_open                     23339.0
future_high                     23344.7
future_low                     23331.25
future_close                   23344.15
future_volume                     43725
future_oi                      12808425
future_coi                            0
2025-05-25 12:35:49,681 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2025-04-02
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23261.45
atm_strike                      23250.0
strike                          23250.0
dte                                   1
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523250CE
ce_open                            83.1
ce_high                            87.9
ce_low                             83.1
ce_close                           87.0
ce_volume                        546750
ce_oi                           6733800
ce_coi                                0
ce_iv                             12.13
ce_delta                           0.55
ce_gamma                         0.0021
ce_theta                         -26.28
ce_vega                            6.18
ce_rho                           57.017
pe_symbol           NIFTY03APR2523250PE
pe_open                           70.65
pe_high                           70.65
pe_low                             65.7
pe_close                           66.8
pe_volume                        766500
pe_oi                           7853325
pe_coi                                0
pe_iv                             12.41
pe_delta                          -0.45
pe_gamma                          0.002
pe_theta                         -20.43
pe_vega                            6.19
pe_rho                         -47.8132
future_open                    23386.95
future_high                     23394.9
future_low                     23383.35
future_close                    23390.0
future_volume                      2475
future_oi                      12651900
future_coi                            0
2025-05-25 12:35:49,682 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:35:49,682 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:35:49,682 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:35:49,682 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 139866552091024
2025-05-25 12:35:49,682 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:35:49,682 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:35:49,683 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:35:49,683 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:35:49,684 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-02' AND trade_date <= '2025-04-02'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:35:50,004 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.320s, returned 18845 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:35:50,006 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18845 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 12:35:50,007 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 12:35:50,087 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:35:50,091 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 2 - Tick data for specific strike 23200.0 is EMPTY after filtering windowed data.
2025-05-25 12:35:50,092 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:50,092 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:50,092 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:50,092 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:35:50,092 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:35:50,126 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.034s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:35:50,169 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.042s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:35:50,170 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
trade_date                   2025-04-02
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23204.8
atm_strike                      23200.0
strike                          23300.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY03APR2523300CE
ce_open                           60.05
ce_high                           63.35
ce_low                             58.4
ce_close                           61.2
ce_volume                       1729575
ce_oi                           7721850
ce_coi                                0
ce_iv                              15.7
ce_delta                           0.35
ce_gamma                         0.0015
ce_theta                         -30.31
ce_vega                            5.86
ce_rho                           38.077
pe_symbol           NIFTY03APR2523300PE
pe_open                          139.19
pe_high                           146.4
pe_low                            135.0
pe_close                         136.65
pe_volume                        594075
pe_oi                           3743250
pe_coi                                0
pe_iv                             14.12
pe_delta                          -0.65
pe_gamma                         0.0017
pe_theta                         -20.66
pe_vega                            5.78
pe_rho                         -68.3662
future_open                     23339.0
future_high                     23344.7
future_low                     23331.25
future_close                   23344.15
future_volume                     43725
future_oi                      12808425
future_coi                            0
2025-05-25 12:35:50,171 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2025-04-02
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23261.45
atm_strike                      23250.0
strike                          23350.0
dte                                   1
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY03APR2523350CE
ce_open                           39.45
ce_high                            41.9
ce_low                            39.45
ce_close                           41.4
ce_volume                        402525
ce_oi                           7047450
ce_coi                                0
ce_iv                             11.85
ce_delta                           0.34
ce_gamma                          0.002
ce_theta                         -22.72
ce_vega                            5.71
ce_rho                            35.28
pe_symbol           NIFTY03APR2523350PE
pe_open                           127.0
pe_high                           127.0
pe_low                            119.5
pe_close                         121.55
pe_volume                        254850
pe_oi                           2255850
pe_coi                                0
pe_iv                             12.22
pe_delta                          -0.66
pe_gamma                         0.0019
pe_theta                         -17.11
pe_vega                            5.74
pe_rho                         -69.4036
future_open                    23386.95
future_high                     23394.9
future_low                     23383.35
future_close                    23390.0
future_volume                      2475
future_oi                      12651900
future_coi                            0
2025-05-25 12:35:50,171 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:35:50,171 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:35:50,172 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:35:50,172 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 139866552090912
2025-05-25 12:35:50,172 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:35:50,172 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:35:50,172 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:35:50,172 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:35:50,174 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-02' AND trade_date <= '2025-04-02'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:35:50,476 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.302s, returned 18845 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:35:50,478 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18845 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:35:50,479 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:35:50,559 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:35:50,564 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 3 - Tick data for specific strike 23300.0 is EMPTY after filtering windowed data.
2025-05-25 12:35:50,564 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:50,564 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:50,564 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:50,565 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:35:50,565 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:35:50,606 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.041s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:35:50,648 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.042s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:35:50,650 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
trade_date                   2025-04-02
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23204.8
atm_strike                      23200.0
strike                          23100.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY03APR2523100CE
ce_open                           174.7
ce_high                           179.7
ce_low                           169.05
ce_close                         176.25
ce_volume                        387150
ce_oi                           1343325
ce_coi                                0
ce_iv                              17.6
ce_delta                           0.68
ce_gamma                         0.0013
ce_theta                         -34.53
ce_vega                            5.67
ce_rho                          68.7777
pe_symbol           NIFTY03APR2523100PE
pe_open                            52.5
pe_high                            58.9
pe_low                            52.05
pe_close                           53.0
pe_volume                       1594950
pe_oi                           3751950
pe_coi                                0
pe_iv                             16.17
pe_delta                          -0.32
pe_gamma                         0.0014
pe_theta                         -25.38
pe_vega                            5.58
pe_rho                          -33.841
future_open                     23339.0
future_high                     23344.7
future_low                     23331.25
future_close                   23344.15
future_volume                     43725
future_oi                      12808425
future_coi                            0
2025-05-25 12:35:50,650 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2025-04-02
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23261.45
atm_strike                      23250.0
strike                          23150.0
dte                                   1
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY03APR2523150CE
ce_open                           152.1
ce_high                           158.3
ce_low                            152.1
ce_close                         156.69
ce_volume                         88050
ce_oi                           1554375
ce_coi                             -150
ce_iv                             13.15
ce_delta                           0.72
ce_gamma                         0.0016
ce_theta                         -25.39
ce_vega                             5.2
ce_rho                          75.3507
pe_symbol           NIFTY03APR2523150PE
pe_open                            38.2
pe_high                            38.2
pe_low                            35.65
pe_close                           36.4
pe_volume                        348300
pe_oi                           7258425
pe_coi                            63000
pe_iv                             13.45
pe_delta                          -0.28
pe_gamma                         0.0016
pe_theta                         -19.67
pe_vega                            5.25
pe_rho                         -29.3928
future_open                    23386.95
future_high                     23394.9
future_low                     23383.35
future_close                    23390.0
future_volume                      2475
future_oi                      12651900
future_coi                            0
2025-05-25 12:35:50,651 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:35:50,651 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:35:50,651 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:35:50,651 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 139866552091024
2025-05-25 12:35:50,651 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:35:50,652 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:35:50,652 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:35:50,652 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:35:50,653 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-02' AND trade_date <= '2025-04-02'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:35:50,965 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.312s, returned 18845 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:35:50,968 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18845 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 12:35:50,968 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 12:35:51,048 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:35:51,053 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 4 - Tick data for specific strike 23100.0 is EMPTY after filtering windowed data.
2025-05-25 12:35:51,053 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:51,053 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:51,053 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:51,054 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 12:35:51,054 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 2), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23204.8, 'atm_strike': 23200.0, 'strike': 23100.0, 'dte': 1, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY03APR2523100CE', 'ce_open': 174.7, 'ce_high': 179.7, 'ce_low': 169.05, 'ce_close': 176.25, 'ce_volume': 387150, 'ce_oi': 1343325, 'ce_coi': 0, 'ce_iv': 17.6, 'ce_delta': 0.68, 'ce_gamma': 0.0013, 'ce_theta': -34.53, 'ce_vega': 5.67, 'ce_rho': 68.7777, 'pe_symbol': 'NIFTY03APR2523100PE', 'pe_open': 52.5, 'pe_high': 58.9, 'pe_low': 52.05, 'pe_close': 53.0, 'pe_volume': 1594950, 'pe_oi': 3751950, 'pe_coi': 0, 'pe_iv': 16.17, 'pe_delta': -0.32, 'pe_gamma': 0.0014, 'pe_theta': -25.38, 'pe_vega': 5.58, 'pe_rho': -33.841, 'future_open': 23339.0, 'future_high': 23344.7, 'future_low': 23331.25, 'future_close': 23344.15, 'future_volume': 43725, 'future_oi': 12808425, 'future_coi': 0}
2025-05-25 12:35:51,054 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 2), 'trade_time': datetime.time(12, 0), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23261.45, 'atm_strike': 23250.0, 'strike': 23150.0, 'dte': 1, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY03APR2523150CE', 'ce_open': 152.1, 'ce_high': 158.3, 'ce_low': 152.1, 'ce_close': 156.69, 'ce_volume': 88050, 'ce_oi': 1554375, 'ce_coi': -150, 'ce_iv': 13.15, 'ce_delta': 0.72, 'ce_gamma': 0.0016, 'ce_theta': -25.39, 'ce_vega': 5.2, 'ce_rho': 75.3507, 'pe_symbol': 'NIFTY03APR2523150PE', 'pe_open': 38.2, 'pe_high': 38.2, 'pe_low': 35.65, 'pe_close': 36.4, 'pe_volume': 348300, 'pe_oi': 7258425, 'pe_coi': 63000, 'pe_iv': 13.45, 'pe_delta': -0.28, 'pe_gamma': 0.0016, 'pe_theta': -19.67, 'pe_vega': 5.25, 'pe_rho': -29.3928, 'future_open': 23386.95, 'future_high': 23394.9, 'future_low': 23383.35, 'future_close': 23390.0, 'future_volume': 2475, 'future_oi': 12651900, 'future_coi': 0}
2025-05-25 12:35:51,054 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-25 12:35:51,054 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 12:35:51,054 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 12:35:51,054 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 12:35:51,055 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -830.0000000000001, PnL (Slippage): -830.0000000000001, Net PnL: -830.0000000000001
2025-05-25 12:35:51,055 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 12:35:51,055 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2025-04-02', 'entry_time': '09:16:00', 'entry_day': 'Wednesday', 'exit_date': '2025-04-02', 'exit_time': '12:00:00', 'exit_day': 'Wednesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23100, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 53.0, 'exit_price': 36.4, 'points': -16.6, 'pointsAfterSlippage': -16.6, 'pnl': -830.0000000000001, 'pnlAfterSlippage': -830.0000000000001, 'expenses': 0.0, 'netPnlAfterExpenses': -830.0000000000001, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23204.8, 'index_exit_price': 23261.45, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-02 09:16:00', 'exit_datetime': '2025-04-02 12:00:00'}
2025-05-25 12:35:51,055 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:35:51,055 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:35:51,080 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.024s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:35:51,101 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:35:51,102 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
trade_date                   2025-04-03
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23194.15
atm_strike                      23200.0
strike                          23200.0
dte                                   0
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                           48.25
ce_high                            52.7
ce_low                             43.1
ce_close                           52.0
ce_volume                       7644525
ce_oi                           5338050
ce_coi                                0
ce_iv                             13.59
ce_delta                            0.5
ce_gamma                          0.003
ce_theta                         -44.09
ce_vega                            3.89
ce_rho                          20.2449
pe_symbol           NIFTY03APR2523200PE
pe_open                           75.55
pe_high                           75.55
pe_low                            59.25
pe_close                           63.0
pe_volume                       7756050
pe_oi                          11666925
pe_coi                                0
pe_iv                             15.97
pe_delta                           -0.5
pe_gamma                         0.0026
pe_theta                         -44.91
pe_vega                            3.89
pe_rho                          -20.746
future_open                    23285.85
future_high                     23297.1
future_low                      23281.5
future_close                    23292.0
future_volume                    153000
future_oi                      12612825
future_coi                            0
2025-05-25 12:35:51,103 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2025-04-03
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23287.7
atm_strike                      23300.0
strike                          23300.0
dte                                   0
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523300CE
ce_open                            9.69
ce_high                            10.7
ce_low                             9.25
ce_close                           9.25
ce_volume                       5130975
ce_oi                          44777775
ce_coi                                0
ce_iv                              3.31
ce_delta                           0.46
ce_gamma                         0.0119
ce_theta                         -12.26
ce_vega                            3.78
ce_rho                          16.4808
pe_symbol           NIFTY03APR2523300PE
pe_open                            33.2
pe_high                            34.1
pe_low                            31.65
pe_close                          34.04
pe_volume                       2499675
pe_oi                          24266925
pe_coi                                0
pe_iv                              7.62
pe_delta                          -0.54
pe_gamma                         0.0053
pe_theta                         -19.45
pe_vega                            3.88
pe_rho                         -22.4198
future_open                    23387.05
future_high                    23389.95
future_low                     23387.05
future_close                   23387.05
future_volume                      9600
future_oi                      12922125
future_coi                            0
2025-05-25 12:35:51,103 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:35:51,103 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:35:51,103 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:35:51,104 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 139866552090912
2025-05-25 12:35:51,104 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:35:51,104 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:35:51,104 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:35:51,104 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:35:51,105 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-03' AND trade_date <= '2025-04-03'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:35:51,406 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.301s, returned 18336 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:35:51,409 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18336 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:35:51,409 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:35:51,487 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:35:51,492 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 1 - Tick data for specific strike 23200.0 is EMPTY after filtering windowed data.
2025-05-25 12:35:51,492 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:51,492 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:51,493 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:51,493 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:35:51,493 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:35:51,534 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.040s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:35:51,574 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.040s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:35:51,575 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
trade_date                   2025-04-03
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23194.15
atm_strike                      23200.0
strike                          23200.0
dte                                   0
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                           48.25
ce_high                            52.7
ce_low                             43.1
ce_close                           52.0
ce_volume                       7644525
ce_oi                           5338050
ce_coi                                0
ce_iv                             13.59
ce_delta                            0.5
ce_gamma                          0.003
ce_theta                         -44.09
ce_vega                            3.89
ce_rho                          20.2449
pe_symbol           NIFTY03APR2523200PE
pe_open                           75.55
pe_high                           75.55
pe_low                            59.25
pe_close                           63.0
pe_volume                       7756050
pe_oi                          11666925
pe_coi                                0
pe_iv                             15.97
pe_delta                           -0.5
pe_gamma                         0.0026
pe_theta                         -44.91
pe_vega                            3.89
pe_rho                          -20.746
future_open                    23285.85
future_high                     23297.1
future_low                      23281.5
future_close                    23292.0
future_volume                    153000
future_oi                      12612825
future_coi                            0
2025-05-25 12:35:51,576 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2025-04-03
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23287.7
atm_strike                      23300.0
strike                          23300.0
dte                                   0
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523300CE
ce_open                            9.69
ce_high                            10.7
ce_low                             9.25
ce_close                           9.25
ce_volume                       5130975
ce_oi                          44777775
ce_coi                                0
ce_iv                              3.31
ce_delta                           0.46
ce_gamma                         0.0119
ce_theta                         -12.26
ce_vega                            3.78
ce_rho                          16.4808
pe_symbol           NIFTY03APR2523300PE
pe_open                            33.2
pe_high                            34.1
pe_low                            31.65
pe_close                          34.04
pe_volume                       2499675
pe_oi                          24266925
pe_coi                                0
pe_iv                              7.62
pe_delta                          -0.54
pe_gamma                         0.0053
pe_theta                         -19.45
pe_vega                            3.88
pe_rho                         -22.4198
future_open                    23387.05
future_high                    23389.95
future_low                     23387.05
future_close                   23387.05
future_volume                      9600
future_oi                      12922125
future_coi                            0
2025-05-25 12:35:51,576 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:35:51,577 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:35:51,577 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:35:51,577 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 139866552091024
2025-05-25 12:35:51,577 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:35:51,577 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:35:51,578 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:35:51,578 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:35:51,579 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-03' AND trade_date <= '2025-04-03'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:35:51,899 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.319s, returned 18336 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:35:51,901 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18336 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 12:35:51,902 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 12:35:51,981 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:35:51,986 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 2 - Tick data for specific strike 23200.0 is EMPTY after filtering windowed data.
2025-05-25 12:35:51,986 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:51,986 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:51,986 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:51,987 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:35:51,987 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:35:52,054 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.067s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:35:52,086 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.032s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:35:52,088 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
trade_date                   2025-04-03
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23194.15
atm_strike                      23200.0
strike                          23300.0
dte                                   0
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY03APR2523300CE
ce_open                            14.7
ce_high                           14.85
ce_low                             10.4
ce_close                          14.85
ce_volume                       7280925
ce_oi                          12405825
ce_coi                                0
ce_iv                             12.86
ce_delta                           0.25
ce_gamma                         0.0023
ce_theta                         -29.31
ce_vega                            2.81
ce_rho                           8.5804
pe_symbol           NIFTY03APR2523300PE
pe_open                          141.69
pe_high                          141.69
pe_low                            122.2
pe_close                          125.0
pe_volume                       2597625
pe_oi                           8601675
pe_coi                                0
pe_iv                             15.68
pe_delta                          -0.75
pe_gamma                         0.0021
pe_theta                         -33.23
pe_vega                            3.13
pe_rho                          -30.797
future_open                    23285.85
future_high                     23297.1
future_low                      23281.5
future_close                    23292.0
future_volume                    153000
future_oi                      12612825
future_coi                            0
2025-05-25 12:35:52,088 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2025-04-03
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23287.7
atm_strike                      23300.0
strike                          23400.0
dte                                   0
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY03APR2523400CE
ce_open                            2.45
ce_high                            2.75
ce_low                             2.45
ce_close                           2.45
ce_volume                        896100
ce_oi                          13683450
ce_coi                                0
ce_iv                               7.6
ce_delta                           0.23
ce_gamma                         0.0019
ce_theta                          -8.53
ce_vega                            1.37
ce_rho                           3.0344
pe_symbol           NIFTY03APR2523400PE
pe_open                          126.15
pe_high                          127.95
pe_low                           123.95
pe_close                         127.95
pe_volume                        206850
pe_oi                           3432525
pe_coi                                0
pe_iv                             14.99
pe_delta                          -0.77
pe_gamma                         0.0021
pe_theta                         -29.76
pe_vega                            2.99
pe_rho                         -31.8492
future_open                    23387.05
future_high                    23389.95
future_low                     23387.05
future_close                   23387.05
future_volume                      9600
future_oi                      12922125
future_coi                            0
2025-05-25 12:35:52,089 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:35:52,089 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:35:52,089 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:35:52,089 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 139866552090912
2025-05-25 12:35:52,089 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:35:52,090 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:35:52,090 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:35:52,090 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:35:52,092 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-03' AND trade_date <= '2025-04-03'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:35:52,413 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.321s, returned 18336 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:35:52,415 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18336 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:35:52,415 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:35:52,466 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:35:52,469 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 3 - Tick data for specific strike 23300.0 is EMPTY after filtering windowed data.
2025-05-25 12:35:52,470 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:52,470 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:52,470 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:52,470 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:35:52,470 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:35:52,504 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.033s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:35:52,539 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.035s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:35:52,540 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
trade_date                   2025-04-03
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23194.15
atm_strike                      23200.0
strike                          23100.0
dte                                   0
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY03APR2523100CE
ce_open                           115.2
ce_high                           124.4
ce_low                            110.0
ce_close                          121.8
ce_volume                       1040850
ce_oi                           1116150
ce_coi                                0
ce_iv                             15.56
ce_delta                           0.71
ce_gamma                         0.0021
ce_theta                         -42.59
ce_vega                            3.15
ce_rho                          30.2501
pe_symbol           NIFTY03APR2523100PE
pe_open                            39.7
pe_high                            39.7
pe_low                             31.1
pe_close                          33.79
pe_volume                       4969050
pe_oi                           7260300
pe_coi                                0
pe_iv                             18.69
pe_delta                          -0.29
pe_gamma                         0.0019
pe_theta                         -46.73
pe_vega                            3.36
pe_rho                         -12.0865
future_open                    23285.85
future_high                     23297.1
future_low                      23281.5
future_close                    23292.0
future_volume                    153000
future_oi                      12612825
future_coi                            0
2025-05-25 12:35:52,541 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2025-04-03
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23287.7
atm_strike                      23300.0
strike                          23200.0
dte                                   0
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY03APR2523200CE
ce_open                            80.7
ce_high                            82.9
ce_low                            79.45
ce_close                          79.45
ce_volume                        874575
ce_oi                           9757050
ce_coi                                0
ce_iv                              0.01
ce_delta                           0.89
ce_gamma                            0.0
ce_theta                          -6.36
ce_vega                             0.0
ce_rho                          41.0149
pe_symbol           NIFTY03APR2523200PE
pe_open                            4.25
pe_high                            4.25
pe_low                             3.85
pe_close                           4.25
pe_volume                       2352450
pe_oi                          39314925
pe_coi                                0
pe_iv                              7.81
pe_delta                          -0.11
pe_gamma                         0.0025
pe_theta                          -10.7
pe_vega                            1.89
pe_rho                          -4.7056
future_open                    23387.05
future_high                    23389.95
future_low                     23387.05
future_close                   23387.05
future_volume                      9600
future_oi                      12922125
future_coi                            0
2025-05-25 12:35:52,541 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:35:52,541 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:35:52,542 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:35:52,542 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 139866552091024
2025-05-25 12:35:52,542 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:35:52,542 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:35:52,542 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:35:52,542 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:35:52,544 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-03' AND trade_date <= '2025-04-03'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:35:52,859 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.315s, returned 18336 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:35:52,861 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18336 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 12:35:52,862 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 12:35:52,940 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:35:52,945 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 4 - Tick data for specific strike 23100.0 is EMPTY after filtering windowed data.
2025-05-25 12:35:52,945 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:52,945 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:52,945 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:52,945 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 12:35:52,946 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 3), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23194.15, 'atm_strike': 23200.0, 'strike': 23100.0, 'dte': 0, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY03APR2523100CE', 'ce_open': 115.2, 'ce_high': 124.4, 'ce_low': 110.0, 'ce_close': 121.8, 'ce_volume': 1040850, 'ce_oi': 1116150, 'ce_coi': 0, 'ce_iv': 15.56, 'ce_delta': 0.71, 'ce_gamma': 0.0021, 'ce_theta': -42.59, 'ce_vega': 3.15, 'ce_rho': 30.2501, 'pe_symbol': 'NIFTY03APR2523100PE', 'pe_open': 39.7, 'pe_high': 39.7, 'pe_low': 31.1, 'pe_close': 33.79, 'pe_volume': 4969050, 'pe_oi': 7260300, 'pe_coi': 0, 'pe_iv': 18.69, 'pe_delta': -0.29, 'pe_gamma': 0.0019, 'pe_theta': -46.73, 'pe_vega': 3.36, 'pe_rho': -12.0865, 'future_open': 23285.85, 'future_high': 23297.1, 'future_low': 23281.5, 'future_close': 23292.0, 'future_volume': 153000, 'future_oi': 12612825, 'future_coi': 0}
2025-05-25 12:35:52,946 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 3), 'trade_time': datetime.time(12, 0), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23287.7, 'atm_strike': 23300.0, 'strike': 23200.0, 'dte': 0, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY03APR2523200CE', 'ce_open': 80.7, 'ce_high': 82.9, 'ce_low': 79.45, 'ce_close': 79.45, 'ce_volume': 874575, 'ce_oi': 9757050, 'ce_coi': 0, 'ce_iv': 0.01, 'ce_delta': 0.89, 'ce_gamma': 0.0, 'ce_theta': -6.36, 'ce_vega': 0.0, 'ce_rho': 41.0149, 'pe_symbol': 'NIFTY03APR2523200PE', 'pe_open': 4.25, 'pe_high': 4.25, 'pe_low': 3.85, 'pe_close': 4.25, 'pe_volume': 2352450, 'pe_oi': 39314925, 'pe_coi': 0, 'pe_iv': 7.81, 'pe_delta': -0.11, 'pe_gamma': 0.0025, 'pe_theta': -10.7, 'pe_vega': 1.89, 'pe_rho': -4.7056, 'future_open': 23387.05, 'future_high': 23389.95, 'future_low': 23387.05, 'future_close': 23387.05, 'future_volume': 9600, 'future_oi': 12922125, 'future_coi': 0}
2025-05-25 12:35:52,946 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-25 12:35:52,946 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 12:35:52,946 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 12:35:52,946 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 12:35:52,946 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -1477.0, PnL (Slippage): -1477.0, Net PnL: -1477.0
2025-05-25 12:35:52,947 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 12:35:52,947 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2025-04-03', 'entry_time': '09:16:00', 'entry_day': 'Thursday', 'exit_date': '2025-04-03', 'exit_time': '12:00:00', 'exit_day': 'Thursday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23100, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 33.79, 'exit_price': 4.25, 'points': -29.54, 'pointsAfterSlippage': -29.54, 'pnl': -1477.0, 'pnlAfterSlippage': -1477.0, 'expenses': 0.0, 'netPnlAfterExpenses': -1477.0, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23194.15, 'index_exit_price': 23287.7, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-03 09:16:00', 'exit_datetime': '2025-04-03 12:00:00'}
2025-05-25 12:35:52,947 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-04' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:35:52,947 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-04' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:35:52,976 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.028s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-04'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:35:53,002 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.026s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-04'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:35:53,003 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
trade_date                   2025-04-04
trade_time                     09:16:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           23118.85
atm_strike                      23100.0
strike                          23100.0
dte                                   3
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY09APR2523100CE
ce_open                           166.6
ce_high                           169.3
ce_low                            156.1
ce_close                         162.75
ce_volume                       1043625
ce_oi                            722625
ce_coi                                0
ce_iv                             11.68
ce_delta                           0.55
ce_gamma                         0.0012
ce_theta                         -15.24
ce_vega                            11.3
ce_rho                         200.2749
pe_symbol           NIFTY09APR2523100PE
pe_open                           165.3
pe_high                           169.7
pe_low                            157.5
pe_close                          162.5
pe_volume                       2219700
pe_oi                           2414925
pe_coi                                0
pe_iv                             16.46
pe_delta                          -0.45
pe_gamma                         0.0008
pe_theta                          -13.7
pe_vega                           11.38
pe_rho                        -163.3277
future_open                    23181.95
future_high                     23189.9
future_low                      23168.4
future_close                   23179.25
future_volume                    175425
future_oi                      13065450
future_coi                            0
2025-05-25 12:35:53,004 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2025-04-04
trade_time                     12:00:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                            22964.3
atm_strike                      22950.0
strike                          22950.0
dte                                   3
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY09APR2522950CE
ce_open                           133.4
ce_high                          134.05
ce_low                           129.35
ce_close                         131.44
ce_volume                        244650
ce_oi                           2162850
ce_coi                          -124125
ce_iv                               9.2
ce_delta                           0.56
ce_gamma                         0.0015
ce_theta                         -12.71
ce_vega                           11.18
ce_rho                         202.9337
pe_symbol           NIFTY09APR2522950PE
pe_open                           121.7
pe_high                          126.15
pe_low                            121.5
pe_close                          124.1
pe_volume                        408300
pe_oi                           3639150
pe_coi                           -52950
pe_iv                             12.98
pe_delta                          -0.44
pe_gamma                         0.0011
pe_theta                         -10.14
pe_vega                           11.28
pe_rho                        -159.2931
future_open                    23041.35
future_high                    23041.35
future_low                      23030.3
future_close                   23034.95
future_volume                      7650
future_oi                      13635450
future_coi                        16350
2025-05-25 12:35:53,004 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:35:53,004 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:35:53,004 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:35:53,004 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 139866552090912
2025-05-25 12:35:53,005 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:35:53,005 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:35:53,005 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:35:53,005 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:35:53,006 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-04' AND trade_date <= '2025-04-04'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:35:53,321 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.314s, returned 18524 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:35:53,323 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18524 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:35:53,324 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:35:53,404 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:35:53,409 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 1 - Tick data for specific strike 23100.0 is EMPTY after filtering windowed data.
2025-05-25 12:35:53,409 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:53,409 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:53,409 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:53,410 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-04' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:35:53,410 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-04' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:35:53,438 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.028s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-04'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:35:53,467 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.028s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-04'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:35:53,468 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
trade_date                   2025-04-04
trade_time                     09:16:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           23118.85
atm_strike                      23100.0
strike                          23100.0
dte                                   3
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY09APR2523100CE
ce_open                           166.6
ce_high                           169.3
ce_low                            156.1
ce_close                         162.75
ce_volume                       1043625
ce_oi                            722625
ce_coi                                0
ce_iv                             11.68
ce_delta                           0.55
ce_gamma                         0.0012
ce_theta                         -15.24
ce_vega                            11.3
ce_rho                         200.2749
pe_symbol           NIFTY09APR2523100PE
pe_open                           165.3
pe_high                           169.7
pe_low                            157.5
pe_close                          162.5
pe_volume                       2219700
pe_oi                           2414925
pe_coi                                0
pe_iv                             16.46
pe_delta                          -0.45
pe_gamma                         0.0008
pe_theta                          -13.7
pe_vega                           11.38
pe_rho                        -163.3277
future_open                    23181.95
future_high                     23189.9
future_low                      23168.4
future_close                   23179.25
future_volume                    175425
future_oi                      13065450
future_coi                            0
2025-05-25 12:35:53,469 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2025-04-04
trade_time                     12:00:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                            22964.3
atm_strike                      22950.0
strike                          22950.0
dte                                   3
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY09APR2522950CE
ce_open                           133.4
ce_high                          134.05
ce_low                           129.35
ce_close                         131.44
ce_volume                        244650
ce_oi                           2162850
ce_coi                          -124125
ce_iv                               9.2
ce_delta                           0.56
ce_gamma                         0.0015
ce_theta                         -12.71
ce_vega                           11.18
ce_rho                         202.9337
pe_symbol           NIFTY09APR2522950PE
pe_open                           121.7
pe_high                          126.15
pe_low                            121.5
pe_close                          124.1
pe_volume                        408300
pe_oi                           3639150
pe_coi                           -52950
pe_iv                             12.98
pe_delta                          -0.44
pe_gamma                         0.0011
pe_theta                         -10.14
pe_vega                           11.28
pe_rho                        -159.2931
future_open                    23041.35
future_high                    23041.35
future_low                      23030.3
future_close                   23034.95
future_volume                      7650
future_oi                      13635450
future_coi                        16350
2025-05-25 12:35:53,469 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:35:53,469 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:35:53,469 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:35:53,469 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 139866552091024
2025-05-25 12:35:53,470 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:35:53,470 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:35:53,470 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:35:53,470 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:35:53,471 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-04' AND trade_date <= '2025-04-04'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:35:53,756 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.285s, returned 18524 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:35:53,759 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18524 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 12:35:53,759 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 12:35:53,837 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:35:53,842 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 2 - Tick data for specific strike 23100.0 is EMPTY after filtering windowed data.
2025-05-25 12:35:53,842 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:53,843 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:53,843 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:53,843 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-04' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:35:53,843 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-04' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:35:53,884 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.040s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-04'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:35:53,907 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.023s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-04'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:35:53,909 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
trade_date                   2025-04-04
trade_time                     09:16:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           23118.85
atm_strike                      23100.0
strike                          23200.0
dte                                   3
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY09APR2523200CE
ce_open                          118.25
ce_high                           120.9
ce_low                           110.35
ce_close                          114.9
ce_volume                       1367325
ce_oi                           2543550
ce_coi                                0
ce_iv                             11.88
ce_delta                           0.47
ce_gamma                         0.0012
ce_theta                          -14.8
ce_vega                           11.38
ce_rho                         159.1016
pe_symbol           NIFTY09APR2523200PE
pe_open                           217.4
pe_high                           223.8
pe_low                           210.75
pe_close                          216.2
pe_volume                       1542600
pe_oi                           4037325
pe_coi                                0
pe_iv                             16.79
pe_delta                          -0.53
pe_gamma                         0.0008
pe_theta                         -13.56
pe_vega                           11.43
pe_rho                        -193.8948
future_open                    23181.95
future_high                     23189.9
future_low                      23168.4
future_close                   23179.25
future_volume                    175425
future_oi                      13065450
future_coi                            0
2025-05-25 12:35:53,910 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2025-04-04
trade_time                     12:00:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                            22964.3
atm_strike                      22950.0
strike                          23050.0
dte                                   3
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY09APR2523050CE
ce_open                            87.3
ce_high                            88.0
ce_low                             84.5
ce_close                           86.1
ce_volume                        195825
ce_oi                           3286875
ce_coi                             1275
ce_iv                              9.59
ce_delta                           0.45
ce_gamma                         0.0014
ce_theta                         -12.21
ce_vega                           11.21
ce_rho                         151.2397
pe_symbol           NIFTY09APR2523050PE
pe_open                           175.5
pe_high                           181.0
pe_low                           175.35
pe_close                         178.45
pe_volume                        110025
pe_oi                           1267200
pe_coi                           -25875
pe_iv                             13.34
pe_delta                          -0.55
pe_gamma                          0.001
pe_theta                          -9.86
pe_vega                            11.3
pe_rho                        -197.6342
future_open                    23041.35
future_high                    23041.35
future_low                      23030.3
future_close                   23034.95
future_volume                      7650
future_oi                      13635450
future_coi                        16350
2025-05-25 12:35:53,910 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:35:53,910 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:35:53,910 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:35:53,910 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 139866552090912
2025-05-25 12:35:53,911 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:35:53,911 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:35:53,911 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:35:53,911 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:35:53,912 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-04' AND trade_date <= '2025-04-04'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:35:54,240 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.327s, returned 18524 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:35:54,242 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18524 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:35:54,243 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:35:54,321 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:35:54,326 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 3 - Tick data for specific strike 23200.0 is EMPTY after filtering windowed data.
2025-05-25 12:35:54,326 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:54,326 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:54,326 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:54,327 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-04' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:35:54,327 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-04' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:35:54,363 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.036s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-04'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:35:54,391 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.028s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-04'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:35:54,392 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
trade_date                   2025-04-04
trade_time                     09:16:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           23118.85
atm_strike                      23100.0
strike                          23000.0
dte                                   3
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY09APR2523000CE
ce_open                           222.6
ce_high                          227.55
ce_low                            212.0
ce_close                          220.3
ce_volume                        306000
ce_oi                           1428300
ce_coi                                0
ce_iv                             11.26
ce_delta                           0.63
ce_gamma                         0.0011
ce_theta                         -14.45
ce_vega                           10.19
ce_rho                         241.8919
pe_symbol           NIFTY09APR2523000PE
pe_open                           123.8
pe_high                           125.9
pe_low                            116.5
pe_close                         120.85
pe_volume                       2390100
pe_oi                           6541875
pe_coi                                0
pe_iv                             16.45
pe_delta                          -0.37
pe_gamma                         0.0008
pe_theta                         -13.43
pe_vega                           10.83
pe_rho                        -133.2973
future_open                    23181.95
future_high                     23189.9
future_low                      23168.4
future_close                   23179.25
future_volume                    175425
future_oi                      13065450
future_coi                            0
2025-05-25 12:35:54,393 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2025-04-04
trade_time                     12:00:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                            22964.3
atm_strike                      22950.0
strike                          22850.0
dte                                   3
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY09APR2522850CE
ce_open                          193.25
ce_high                           193.9
ce_low                            188.0
ce_close                         190.85
ce_volume                         30825
ce_oi                            343275
ce_coi                            18975
ce_iv                              8.68
ce_delta                           0.66
ce_gamma                         0.0013
ce_theta                          -11.8
ce_vega                            9.45
ce_rho                         255.9896
pe_symbol           NIFTY09APR2522850PE
pe_open                           81.25
pe_high                            84.5
pe_low                            80.59
pe_close                          83.05
pe_volume                        219150
pe_oi                           2070450
pe_coi                           -23775
pe_iv                             12.84
pe_delta                          -0.34
pe_gamma                          0.001
pe_theta                          -9.73
pe_vega                           10.44
pe_rho                         -121.345
future_open                    23041.35
future_high                    23041.35
future_low                      23030.3
future_close                   23034.95
future_volume                      7650
future_oi                      13635450
future_coi                        16350
2025-05-25 12:35:54,393 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:35:54,393 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:35:54,394 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:35:54,394 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 139866552091024
2025-05-25 12:35:54,394 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:35:54,394 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:35:54,394 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:35:54,394 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:35:54,396 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-04' AND trade_date <= '2025-04-04'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:35:54,730 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.334s, returned 18524 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:35:54,732 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18524 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 12:35:54,733 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 12:35:54,811 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:35:54,816 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 4 - Tick data for specific strike 23000.0 is EMPTY after filtering windowed data.
2025-05-25 12:35:54,816 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:54,816 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:54,816 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:54,816 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 12:35:54,816 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 4), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 9), 'index_name': 'NIFTY', 'spot': 23118.85, 'atm_strike': 23100.0, 'strike': 23000.0, 'dte': 3, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY09APR2523000CE', 'ce_open': 222.6, 'ce_high': 227.55, 'ce_low': 212.0, 'ce_close': 220.3, 'ce_volume': 306000, 'ce_oi': 1428300, 'ce_coi': 0, 'ce_iv': 11.26, 'ce_delta': 0.63, 'ce_gamma': 0.0011, 'ce_theta': -14.45, 'ce_vega': 10.19, 'ce_rho': 241.8919, 'pe_symbol': 'NIFTY09APR2523000PE', 'pe_open': 123.8, 'pe_high': 125.9, 'pe_low': 116.5, 'pe_close': 120.85, 'pe_volume': 2390100, 'pe_oi': 6541875, 'pe_coi': 0, 'pe_iv': 16.45, 'pe_delta': -0.37, 'pe_gamma': 0.0008, 'pe_theta': -13.43, 'pe_vega': 10.83, 'pe_rho': -133.2973, 'future_open': 23181.95, 'future_high': 23189.9, 'future_low': 23168.4, 'future_close': 23179.25, 'future_volume': 175425, 'future_oi': 13065450, 'future_coi': 0}
2025-05-25 12:35:54,817 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 4), 'trade_time': datetime.time(12, 0), 'expiry_date': datetime.date(2025, 4, 9), 'index_name': 'NIFTY', 'spot': 22964.3, 'atm_strike': 22950.0, 'strike': 22850.0, 'dte': 3, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY09APR2522850CE', 'ce_open': 193.25, 'ce_high': 193.9, 'ce_low': 188.0, 'ce_close': 190.85, 'ce_volume': 30825, 'ce_oi': 343275, 'ce_coi': 18975, 'ce_iv': 8.68, 'ce_delta': 0.66, 'ce_gamma': 0.0013, 'ce_theta': -11.8, 'ce_vega': 9.45, 'ce_rho': 255.9896, 'pe_symbol': 'NIFTY09APR2522850PE', 'pe_open': 81.25, 'pe_high': 84.5, 'pe_low': 80.59, 'pe_close': 83.05, 'pe_volume': 219150, 'pe_oi': 2070450, 'pe_coi': -23775, 'pe_iv': 12.84, 'pe_delta': -0.34, 'pe_gamma': 0.001, 'pe_theta': -9.73, 'pe_vega': 10.44, 'pe_rho': -121.345, 'future_open': 23041.35, 'future_high': 23041.35, 'future_low': 23030.3, 'future_close': 23034.95, 'future_volume': 7650, 'future_oi': 13635450, 'future_coi': 16350}
2025-05-25 12:35:54,817 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-25 12:35:54,817 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 12:35:54,817 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 12:35:54,817 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 12:35:54,817 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -1889.9999999999998, PnL (Slippage): -1889.9999999999998, Net PnL: -1889.9999999999998
2025-05-25 12:35:54,817 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 12:35:54,818 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2025-04-04', 'entry_time': '09:16:00', 'entry_day': 'Friday', 'exit_date': '2025-04-04', 'exit_time': '12:00:00', 'exit_day': 'Friday', 'symbol': 'NIFTY', 'expiry': '2025-04-09', 'strike': 23000, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 120.85, 'exit_price': 83.05, 'points': -37.8, 'pointsAfterSlippage': -37.8, 'pnl': -1889.9999999999998, 'pnlAfterSlippage': -1889.9999999999998, 'expenses': 0.0, 'netPnlAfterExpenses': -1889.9999999999998, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23118.85, 'index_exit_price': 22964.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-04 09:16:00', 'exit_datetime': '2025-04-04 12:00:00'}
2025-05-25 12:35:54,818 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-07' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:35:54,818 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-07' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:35:54,846 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.027s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-07'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:35:54,885 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.039s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-07'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:35:54,887 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
trade_date                   2025-04-07
trade_time                     09:16:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           22070.15
atm_strike                      22150.0
strike                          22150.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY09APR2522150CE
ce_open                          360.55
ce_high                          360.55
ce_low                            285.8
ce_close                         299.14
ce_volume                        366975
ce_oi                               525
ce_coi                                0
ce_iv                             43.98
ce_delta                           0.47
ce_gamma                         0.0005
ce_theta                         -65.02
ce_vega                            7.48
ce_rho                          74.0471
pe_symbol           NIFTY09APR2522150PE
pe_open                           279.0
pe_high                          322.75
pe_low                           269.55
pe_close                         314.14
pe_volume                        484050
pe_oi                            707850
pe_coi                                0
pe_iv                             37.46
pe_delta                          -0.53
pe_gamma                         0.0006
pe_theta                         -49.64
pe_vega                            7.47
pe_rho                         -86.9676
future_open                    22170.75
future_high                    22171.55
future_low                     22139.15
future_close                    22150.0
future_volume                    589575
future_oi                      13865025
future_coi                            0
2025-05-25 12:35:54,887 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2025-04-07
trade_time                     12:00:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           21955.35
atm_strike                      21950.0
strike                          21950.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY09APR2521950CE
ce_open                          313.75
ce_high                          314.35
ce_low                           304.45
ce_close                         305.39
ce_volume                         70200
ce_oi                            593775
ce_coi                            49050
ce_iv                             39.55
ce_delta                           0.52
ce_gamma                         0.0005
ce_theta                         -58.72
ce_vega                            7.45
ce_rho                          80.2007
pe_symbol           NIFTY09APR2521950PE
pe_open                           274.3
pe_high                          283.64
pe_low                           274.05
pe_close                         283.45
pe_volume                        110025
pe_oi                           1299300
pe_coi                            21450
pe_iv                             39.45
pe_delta                          -0.48
pe_gamma                         0.0005
pe_theta                         -52.58
pe_vega                            7.45
pe_rho                         -78.6809
future_open                     22044.3
future_high                     22044.3
future_low                      22026.0
future_close                   22026.15
future_volume                     13875
future_oi                      14835750
future_coi                        17625
2025-05-25 12:35:54,888 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:35:54,888 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:35:54,888 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:35:54,888 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 139866552090912
2025-05-25 12:35:54,888 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:35:54,888 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:35:54,889 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:35:54,889 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:35:54,890 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-07' AND trade_date <= '2025-04-07'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:35:55,382 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.491s, returned 30500 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:35:55,385 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 30500 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:35:55,386 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:35:55,511 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 374 rows.
2025-05-25 12:35:55,517 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 1 - Tick data for specific strike 22150.0 is EMPTY after filtering windowed data.
2025-05-25 12:35:55,517 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:55,517 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:55,517 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:55,517 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-07' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:35:55,517 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-07' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:35:55,547 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.029s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-07'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:35:55,576 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.028s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-07'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:35:55,577 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
trade_date                   2025-04-07
trade_time                     09:16:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           22070.15
atm_strike                      22150.0
strike                          22150.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY09APR2522150CE
ce_open                          360.55
ce_high                          360.55
ce_low                            285.8
ce_close                         299.14
ce_volume                        366975
ce_oi                               525
ce_coi                                0
ce_iv                             43.98
ce_delta                           0.47
ce_gamma                         0.0005
ce_theta                         -65.02
ce_vega                            7.48
ce_rho                          74.0471
pe_symbol           NIFTY09APR2522150PE
pe_open                           279.0
pe_high                          322.75
pe_low                           269.55
pe_close                         314.14
pe_volume                        484050
pe_oi                            707850
pe_coi                                0
pe_iv                             37.46
pe_delta                          -0.53
pe_gamma                         0.0006
pe_theta                         -49.64
pe_vega                            7.47
pe_rho                         -86.9676
future_open                    22170.75
future_high                    22171.55
future_low                     22139.15
future_close                    22150.0
future_volume                    589575
future_oi                      13865025
future_coi                            0
2025-05-25 12:35:55,578 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2025-04-07
trade_time                     12:00:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           21955.35
atm_strike                      21950.0
strike                          21950.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY09APR2521950CE
ce_open                          313.75
ce_high                          314.35
ce_low                           304.45
ce_close                         305.39
ce_volume                         70200
ce_oi                            593775
ce_coi                            49050
ce_iv                             39.55
ce_delta                           0.52
ce_gamma                         0.0005
ce_theta                         -58.72
ce_vega                            7.45
ce_rho                          80.2007
pe_symbol           NIFTY09APR2521950PE
pe_open                           274.3
pe_high                          283.64
pe_low                           274.05
pe_close                         283.45
pe_volume                        110025
pe_oi                           1299300
pe_coi                            21450
pe_iv                             39.45
pe_delta                          -0.48
pe_gamma                         0.0005
pe_theta                         -52.58
pe_vega                            7.45
pe_rho                         -78.6809
future_open                     22044.3
future_high                     22044.3
future_low                      22026.0
future_close                   22026.15
future_volume                     13875
future_oi                      14835750
future_coi                        17625
2025-05-25 12:35:55,578 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:35:55,578 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:35:55,579 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:35:55,579 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 139866552091024
2025-05-25 12:35:55,579 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:35:55,579 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:35:55,579 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:35:55,579 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:35:55,581 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-07' AND trade_date <= '2025-04-07'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:35:56,054 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.473s, returned 30500 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:35:56,058 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 30500 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 12:35:56,058 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 12:35:56,183 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 374 rows.
2025-05-25 12:35:56,188 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 2 - Tick data for specific strike 22150.0 is EMPTY after filtering windowed data.
2025-05-25 12:35:56,188 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:56,188 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:56,188 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:56,189 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-07' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:35:56,189 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-07' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:35:56,235 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.046s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-07'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:35:56,274 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.039s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-07'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:35:56,275 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
trade_date                   2025-04-07
trade_time                     09:16:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           22070.15
atm_strike                      22150.0
strike                          22250.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY09APR2522250CE
ce_open                          283.95
ce_high                          283.95
ce_low                           221.65
ce_close                         236.35
ce_volume                        570000
ce_oi                             19725
ce_coi                                0
ce_iv                              41.4
ce_delta                           0.41
ce_gamma                         0.0005
ce_theta                          -60.1
ce_vega                            7.36
ce_rho                          66.0448
pe_symbol           NIFTY09APR2522250PE
pe_open                          302.14
pe_high                          363.65
pe_low                           302.14
pe_close                         354.15
pe_volume                        496575
pe_oi                            683250
pe_coi                                0
pe_iv                             35.13
pe_delta                          -0.59
pe_gamma                         0.0006
pe_theta                         -44.79
pe_vega                            7.29
pe_rho                         -97.1826
future_open                    22170.75
future_high                    22171.55
future_low                     22139.15
future_close                    22150.0
future_volume                    589575
future_oi                      13865025
future_coi                            0
2025-05-25 12:35:56,276 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2025-04-07
trade_time                     12:00:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           21955.35
atm_strike                      21950.0
strike                          22050.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY09APR2522050CE
ce_open                           257.1
ce_high                           257.1
ce_low                            247.5
ce_close                          248.8
ce_volume                         61200
ce_oi                           1251525
ce_coi                            14700
ce_iv                             38.35
ce_delta                           0.46
ce_gamma                         0.0006
ce_theta                         -56.55
ce_vega                            7.42
ce_rho                          71.8098
pe_symbol           NIFTY09APR2522050PE
pe_open                           317.5
pe_high                           327.7
pe_low                           315.64
pe_close                         326.75
pe_volume                         49575
pe_oi                           1311225
pe_coi                           -26850
pe_iv                             38.26
pe_delta                          -0.54
pe_gamma                         0.0006
pe_theta                         -50.37
pe_vega                            7.42
pe_rho                         -87.8177
future_open                     22044.3
future_high                     22044.3
future_low                      22026.0
future_close                   22026.15
future_volume                     13875
future_oi                      14835750
future_coi                        17625
2025-05-25 12:35:56,276 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:35:56,276 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:35:56,277 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:35:56,277 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 139866552090912
2025-05-25 12:35:56,277 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:35:56,277 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:35:56,277 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:35:56,277 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:35:56,278 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-07' AND trade_date <= '2025-04-07'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:35:56,726 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.447s, returned 30500 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:35:56,728 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 30500 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:35:56,729 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:35:56,809 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 374 rows.
2025-05-25 12:35:56,813 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 3 - Tick data for specific strike 22250.0 is EMPTY after filtering windowed data.
2025-05-25 12:35:56,813 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:56,814 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:56,814 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:56,814 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-07' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:35:56,814 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-07' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:35:56,850 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.036s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-07'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:35:56,882 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.032s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-07'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:35:56,883 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
trade_date                   2025-04-07
trade_time                     09:16:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           22070.15
atm_strike                      22150.0
strike                          22050.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY09APR2522050CE
ce_open                           421.5
ce_high                          422.25
ce_low                            354.8
ce_close                         365.35
ce_volume                        308025
ce_oi                              1575
ce_coi                                0
ce_iv                             46.34
ce_delta                           0.53
ce_gamma                         0.0005
ce_theta                         -68.62
ce_vega                            7.48
ce_rho                          81.1941
pe_symbol           NIFTY09APR2522050PE
pe_open                          263.55
pe_high                          293.64
pe_low                            247.8
pe_close                         284.75
pe_volume                        471225
pe_oi                            984600
pe_coi                                0
pe_iv                              40.4
pe_delta                          -0.47
pe_gamma                         0.0005
pe_theta                         -54.18
pe_vega                            7.48
pe_rho                         -77.8679
future_open                    22170.75
future_high                    22171.55
future_low                     22139.15
future_close                    22150.0
future_volume                    589575
future_oi                      13865025
future_coi                            0
2025-05-25 12:35:56,884 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2025-04-07
trade_time                     12:00:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           21955.35
atm_strike                      21950.0
strike                          21850.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY09APR2521850CE
ce_open                           377.3
ce_high                           377.4
ce_low                            365.8
ce_close                         366.75
ce_volume                          9375
ce_oi                            287475
ce_coi                             3225
ce_iv                             40.67
ce_delta                           0.57
ce_gamma                         0.0005
ce_theta                         -59.77
ce_vega                            7.34
ce_rho                          88.0492
pe_symbol           NIFTY09APR2521850PE
pe_open                          238.15
pe_high                           245.5
pe_low                           236.95
pe_close                          244.5
pe_volume                         46725
pe_oi                            807525
pe_coi                           -13650
pe_iv                             40.52
pe_delta                          -0.43
pe_gamma                         0.0005
pe_theta                         -53.58
pe_vega                            7.34
pe_rho                         -70.0752
future_open                     22044.3
future_high                     22044.3
future_low                      22026.0
future_close                   22026.15
future_volume                     13875
future_oi                      14835750
future_coi                        17625
2025-05-25 12:35:56,884 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:35:56,884 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:35:56,885 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:35:56,885 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 139866552091024
2025-05-25 12:35:56,885 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:35:56,885 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:35:56,885 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:35:56,885 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:35:56,886 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-07' AND trade_date <= '2025-04-07'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:35:57,357 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.470s, returned 30500 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:35:57,361 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 30500 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 12:35:57,361 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 12:35:57,489 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 374 rows.
2025-05-25 12:35:57,495 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 4 - Tick data for specific strike 22050.0 is EMPTY after filtering windowed data.
2025-05-25 12:35:57,496 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:57,496 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:57,496 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:57,496 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 12:35:57,496 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 7), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 9), 'index_name': 'NIFTY', 'spot': 22070.15, 'atm_strike': 22150.0, 'strike': 22050.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY09APR2522050CE', 'ce_open': 421.5, 'ce_high': 422.25, 'ce_low': 354.8, 'ce_close': 365.35, 'ce_volume': 308025, 'ce_oi': 1575, 'ce_coi': 0, 'ce_iv': 46.34, 'ce_delta': 0.53, 'ce_gamma': 0.0005, 'ce_theta': -68.62, 'ce_vega': 7.48, 'ce_rho': 81.1941, 'pe_symbol': 'NIFTY09APR2522050PE', 'pe_open': 263.55, 'pe_high': 293.64, 'pe_low': 247.8, 'pe_close': 284.75, 'pe_volume': 471225, 'pe_oi': 984600, 'pe_coi': 0, 'pe_iv': 40.4, 'pe_delta': -0.47, 'pe_gamma': 0.0005, 'pe_theta': -54.18, 'pe_vega': 7.48, 'pe_rho': -77.8679, 'future_open': 22170.75, 'future_high': 22171.55, 'future_low': 22139.15, 'future_close': 22150.0, 'future_volume': 589575, 'future_oi': 13865025, 'future_coi': 0}
2025-05-25 12:35:57,496 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 7), 'trade_time': datetime.time(12, 0), 'expiry_date': datetime.date(2025, 4, 9), 'index_name': 'NIFTY', 'spot': 21955.35, 'atm_strike': 21950.0, 'strike': 21850.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY09APR2521850CE', 'ce_open': 377.3, 'ce_high': 377.4, 'ce_low': 365.8, 'ce_close': 366.75, 'ce_volume': 9375, 'ce_oi': 287475, 'ce_coi': 3225, 'ce_iv': 40.67, 'ce_delta': 0.57, 'ce_gamma': 0.0005, 'ce_theta': -59.77, 'ce_vega': 7.34, 'ce_rho': 88.0492, 'pe_symbol': 'NIFTY09APR2521850PE', 'pe_open': 238.15, 'pe_high': 245.5, 'pe_low': 236.95, 'pe_close': 244.5, 'pe_volume': 46725, 'pe_oi': 807525, 'pe_coi': -13650, 'pe_iv': 40.52, 'pe_delta': -0.43, 'pe_gamma': 0.0005, 'pe_theta': -53.58, 'pe_vega': 7.34, 'pe_rho': -70.0752, 'future_open': 22044.3, 'future_high': 22044.3, 'future_low': 22026.0, 'future_close': 22026.15, 'future_volume': 13875, 'future_oi': 14835750, 'future_coi': 17625}
2025-05-25 12:35:57,497 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-25 12:35:57,497 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 12:35:57,497 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 12:35:57,497 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 12:35:57,497 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -2012.5, PnL (Slippage): -2012.5, Net PnL: -2012.5
2025-05-25 12:35:57,497 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 12:35:57,497 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2025-04-07', 'entry_time': '09:16:00', 'entry_day': 'Monday', 'exit_date': '2025-04-07', 'exit_time': '12:00:00', 'exit_day': 'Monday', 'symbol': 'NIFTY', 'expiry': '2025-04-09', 'strike': 22050, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 284.75, 'exit_price': 244.5, 'points': -40.25, 'pointsAfterSlippage': -40.25, 'pnl': -2012.5, 'pnlAfterSlippage': -2012.5, 'expenses': 0.0, 'netPnlAfterExpenses': -2012.5, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 22070.15, 'index_exit_price': 21955.35, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-07 09:16:00', 'exit_datetime': '2025-04-07 12:00:00'}
2025-05-25 12:35:57,498 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-08' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:35:57,498 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-08' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:35:57,527 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.029s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-08'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:35:57,564 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.036s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-08'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:35:57,565 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
trade_date                   2025-04-08
trade_time                     09:16:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                            22535.2
atm_strike                      22550.0
strike                          22550.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY09APR2522550CE
ce_open                          195.25
ce_high                          195.25
ce_low                           165.75
ce_close                         171.95
ce_volume                       1072575
ce_oi                            949425
ce_coi                                0
ce_iv                             28.87
ce_delta                            0.5
ce_gamma                         0.0009
ce_theta                         -56.01
ce_vega                            6.03
ce_rho                          49.9597
pe_symbol           NIFTY09APR2522550PE
pe_open                           190.9
pe_high                           199.7
pe_low                            175.1
pe_close                          182.5
pe_volume                        815625
pe_oi                            661425
pe_coi                                0
pe_iv                             29.85
pe_delta                           -0.5
pe_gamma                         0.0009
pe_theta                         -51.63
pe_vega                            6.03
pe_rho                         -51.6051
future_open                     22615.0
future_high                     22615.0
future_low                      22584.3
future_close                   22592.05
future_volume                    148575
future_oi                      13773225
future_coi                            0
2025-05-25 12:35:57,566 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2025-04-08
trade_time                     12:00:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           22421.35
atm_strike                      22450.0
strike                          22450.0
dte                                   1
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY09APR2522450CE
ce_open                           178.6
ce_high                          183.35
ce_low                           173.25
ce_close                         173.25
ce_volume                        247575
ce_oi                           1845150
ce_coi                                0
ce_iv                             30.36
ce_delta                           0.49
ce_gamma                         0.0009
ce_theta                         -58.34
ce_vega                             6.0
ce_rho                          48.5117
pe_symbol           NIFTY09APR2522450PE
pe_open                          191.65
pe_high                           195.2
pe_low                            186.7
pe_close                          195.2
pe_volume                        178875
pe_oi                           1083225
pe_coi                                0
pe_iv                             30.93
pe_delta                          -0.51
pe_gamma                         0.0009
pe_theta                         -53.24
pe_vega                             6.0
pe_rho                         -52.5811
future_open                    22495.25
future_high                     22503.0
future_low                      22489.1
future_close                    22489.1
future_volume                      7650
future_oi                      13928175
future_coi                            0
2025-05-25 12:35:57,566 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:35:57,566 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:35:57,567 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:35:57,567 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 139866552090912
2025-05-25 12:35:57,567 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:35:57,567 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:35:57,567 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:35:57,567 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:35:57,569 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-08' AND trade_date <= '2025-04-08'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:35:58,014 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.445s, returned 27102 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:35:58,017 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 27102 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:35:58,017 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:35:58,130 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:35:58,135 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 1 - Tick data for specific strike 22550.0 is EMPTY after filtering windowed data.
2025-05-25 12:35:58,136 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:58,136 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:58,136 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:58,136 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-08' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:35:58,136 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-08' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:35:58,165 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.028s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-08'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:35:58,192 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.027s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-08'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:35:58,193 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
trade_date                   2025-04-08
trade_time                     09:16:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                            22535.2
atm_strike                      22550.0
strike                          22550.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY09APR2522550CE
ce_open                          195.25
ce_high                          195.25
ce_low                           165.75
ce_close                         171.95
ce_volume                       1072575
ce_oi                            949425
ce_coi                                0
ce_iv                             28.87
ce_delta                            0.5
ce_gamma                         0.0009
ce_theta                         -56.01
ce_vega                            6.03
ce_rho                          49.9597
pe_symbol           NIFTY09APR2522550PE
pe_open                           190.9
pe_high                           199.7
pe_low                            175.1
pe_close                          182.5
pe_volume                        815625
pe_oi                            661425
pe_coi                                0
pe_iv                             29.85
pe_delta                           -0.5
pe_gamma                         0.0009
pe_theta                         -51.63
pe_vega                            6.03
pe_rho                         -51.6051
future_open                     22615.0
future_high                     22615.0
future_low                      22584.3
future_close                   22592.05
future_volume                    148575
future_oi                      13773225
future_coi                            0
2025-05-25 12:35:58,194 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2025-04-08
trade_time                     12:00:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           22421.35
atm_strike                      22450.0
strike                          22450.0
dte                                   1
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY09APR2522450CE
ce_open                           178.6
ce_high                          183.35
ce_low                           173.25
ce_close                         173.25
ce_volume                        247575
ce_oi                           1845150
ce_coi                                0
ce_iv                             30.36
ce_delta                           0.49
ce_gamma                         0.0009
ce_theta                         -58.34
ce_vega                             6.0
ce_rho                          48.5117
pe_symbol           NIFTY09APR2522450PE
pe_open                          191.65
pe_high                           195.2
pe_low                            186.7
pe_close                          195.2
pe_volume                        178875
pe_oi                           1083225
pe_coi                                0
pe_iv                             30.93
pe_delta                          -0.51
pe_gamma                         0.0009
pe_theta                         -53.24
pe_vega                             6.0
pe_rho                         -52.5811
future_open                    22495.25
future_high                     22503.0
future_low                      22489.1
future_close                    22489.1
future_volume                      7650
future_oi                      13928175
future_coi                            0
2025-05-25 12:35:58,194 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:35:58,195 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:35:58,195 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:35:58,195 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 139866552091024
2025-05-25 12:35:58,195 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:35:58,195 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:35:58,195 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:35:58,195 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:35:58,197 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-08' AND trade_date <= '2025-04-08'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:35:58,566 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.369s, returned 27102 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:35:58,568 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 27102 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 12:35:58,569 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 12:35:58,645 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:35:58,650 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 2 - Tick data for specific strike 22550.0 is EMPTY after filtering windowed data.
2025-05-25 12:35:58,650 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:58,650 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:58,650 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:58,650 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-08' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:35:58,651 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-08' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:35:58,690 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.039s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-08'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:35:58,713 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.023s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-08'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:35:58,714 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
trade_date                   2025-04-08
trade_time                     09:16:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                            22535.2
atm_strike                      22550.0
strike                          22650.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY09APR2522650CE
ce_open                           137.1
ce_high                           137.1
ce_low                           112.55
ce_close                         117.55
ce_volume                        623025
ce_oi                            975450
ce_coi                                0
ce_iv                             27.21
ce_delta                           0.41
ce_gamma                         0.0009
ce_theta                         -50.91
ce_vega                            5.86
ce_rho                          40.4398
pe_symbol           NIFTY09APR2522650PE
pe_open                           233.1
pe_high                           242.8
pe_low                            220.0
pe_close                          228.8
pe_volume                        188400
pe_oi                            580650
pe_coi                                0
pe_iv                             28.34
pe_delta                          -0.59
pe_gamma                         0.0009
pe_theta                         -46.88
pe_vega                            5.87
pe_rho                         -61.1917
future_open                     22615.0
future_high                     22615.0
future_low                      22584.3
future_close                   22592.05
future_volume                    148575
future_oi                      13773225
future_coi                            0
2025-05-25 12:35:58,715 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2025-04-08
trade_time                     12:00:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           22421.35
atm_strike                      22450.0
strike                          22550.0
dte                                   1
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY09APR2522550CE
ce_open                           126.8
ce_high                           128.9
ce_low                            121.7
ce_close                          121.7
ce_volume                        180300
ce_oi                           2089125
ce_coi                                0
ce_iv                              29.0
ce_delta                            0.4
ce_gamma                         0.0009
ce_theta                         -53.57
ce_vega                             5.8
ce_rho                          39.5649
pe_symbol           NIFTY09APR2522550PE
pe_open                          238.25
pe_high                           244.2
pe_low                           233.45
pe_close                          244.2
pe_volume                         40650
pe_oi                            996225
pe_coi                                0
pe_iv                             29.69
pe_delta                           -0.6
pe_gamma                         0.0009
pe_theta                         -48.71
pe_vega                            5.81
pe_rho                         -61.7643
future_open                    22495.25
future_high                     22503.0
future_low                      22489.1
future_close                    22489.1
future_volume                      7650
future_oi                      13928175
future_coi                            0
2025-05-25 12:35:58,715 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:35:58,715 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:35:58,715 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:35:58,716 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 139866552090912
2025-05-25 12:35:58,716 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:35:58,716 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:35:58,716 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:35:58,716 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:35:58,717 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-08' AND trade_date <= '2025-04-08'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:35:59,041 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.324s, returned 27102 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:35:59,044 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 27102 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:35:59,044 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:35:59,117 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:35:59,121 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 3 - Tick data for specific strike 22650.0 is EMPTY after filtering windowed data.
2025-05-25 12:35:59,121 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:59,122 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:59,122 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:59,122 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-08' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:35:59,122 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-08' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:35:59,163 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.040s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-08'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:35:59,202 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.039s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-08'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:35:59,203 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
trade_date                   2025-04-08
trade_time                     09:16:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                            22535.2
atm_strike                      22550.0
strike                          22450.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY09APR2522450CE
ce_open                          261.39
ce_high                          261.39
ce_low                           228.15
ce_close                         236.65
ce_volume                        460950
ce_oi                            944550
ce_coi                                0
ce_iv                             30.74
ce_delta                           0.58
ce_gamma                         0.0008
ce_theta                         -58.64
ce_vega                             5.9
ce_rho                          58.3784
pe_symbol           NIFTY09APR2522450PE
pe_open                          154.85
pe_high                           162.6
pe_low                           139.19
pe_close                          145.8
pe_volume                        532500
pe_oi                            424200
pe_coi                                0
pe_iv                             31.49
pe_delta                          -0.42
pe_gamma                         0.0008
pe_theta                          -53.9
pe_vega                             5.9
pe_rho                         -42.9423
future_open                     22615.0
future_high                     22615.0
future_low                      22584.3
future_close                   22592.05
future_volume                    148575
future_oi                      13773225
future_coi                            0
2025-05-25 12:35:59,204 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2025-04-08
trade_time                     12:00:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           22421.35
atm_strike                      22450.0
strike                          22350.0
dte                                   1
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY09APR2522350CE
ce_open                          242.85
ce_high                          246.95
ce_low                            235.0
ce_close                          235.0
ce_volume                         96525
ce_oi                           1054125
ce_coi                                0
ce_iv                             31.96
ce_delta                           0.57
ce_gamma                         0.0008
ce_theta                         -60.84
ce_vega                            5.91
ce_rho                          56.6849
pe_symbol           NIFTY09APR2522350PE
pe_open                          155.35
pe_high                          158.19
pe_low                            151.6
pe_close                         158.19
pe_volume                         85350
pe_oi                           1307175
pe_coi                                0
pe_iv                             32.74
pe_delta                          -0.43
pe_gamma                         0.0008
pe_theta                         -56.15
pe_vega                            5.91
pe_rho                         -44.1505
future_open                    22495.25
future_high                     22503.0
future_low                      22489.1
future_close                    22489.1
future_volume                      7650
future_oi                      13928175
future_coi                            0
2025-05-25 12:35:59,204 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:35:59,204 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:35:59,204 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:35:59,204 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 139866552091024
2025-05-25 12:35:59,204 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:35:59,204 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:35:59,205 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:35:59,205 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:35:59,206 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-08' AND trade_date <= '2025-04-08'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:35:59,612 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.405s, returned 27102 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:35:59,614 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 27102 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 12:35:59,614 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 12:35:59,686 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:35:59,690 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 4 - Tick data for specific strike 22450.0 is EMPTY after filtering windowed data.
2025-05-25 12:35:59,690 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:59,690 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:59,690 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:35:59,691 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 12:35:59,691 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 8), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 9), 'index_name': 'NIFTY', 'spot': 22535.2, 'atm_strike': 22550.0, 'strike': 22450.0, 'dte': 1, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY09APR2522450CE', 'ce_open': 261.39, 'ce_high': 261.39, 'ce_low': 228.15, 'ce_close': 236.65, 'ce_volume': 460950, 'ce_oi': 944550, 'ce_coi': 0, 'ce_iv': 30.74, 'ce_delta': 0.58, 'ce_gamma': 0.0008, 'ce_theta': -58.64, 'ce_vega': 5.9, 'ce_rho': 58.3784, 'pe_symbol': 'NIFTY09APR2522450PE', 'pe_open': 154.85, 'pe_high': 162.6, 'pe_low': 139.19, 'pe_close': 145.8, 'pe_volume': 532500, 'pe_oi': 424200, 'pe_coi': 0, 'pe_iv': 31.49, 'pe_delta': -0.42, 'pe_gamma': 0.0008, 'pe_theta': -53.9, 'pe_vega': 5.9, 'pe_rho': -42.9423, 'future_open': 22615.0, 'future_high': 22615.0, 'future_low': 22584.3, 'future_close': 22592.05, 'future_volume': 148575, 'future_oi': 13773225, 'future_coi': 0}
2025-05-25 12:35:59,691 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 8), 'trade_time': datetime.time(12, 0), 'expiry_date': datetime.date(2025, 4, 9), 'index_name': 'NIFTY', 'spot': 22421.35, 'atm_strike': 22450.0, 'strike': 22350.0, 'dte': 1, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY09APR2522350CE', 'ce_open': 242.85, 'ce_high': 246.95, 'ce_low': 235.0, 'ce_close': 235.0, 'ce_volume': 96525, 'ce_oi': 1054125, 'ce_coi': 0, 'ce_iv': 31.96, 'ce_delta': 0.57, 'ce_gamma': 0.0008, 'ce_theta': -60.84, 'ce_vega': 5.91, 'ce_rho': 56.6849, 'pe_symbol': 'NIFTY09APR2522350PE', 'pe_open': 155.35, 'pe_high': 158.19, 'pe_low': 151.6, 'pe_close': 158.19, 'pe_volume': 85350, 'pe_oi': 1307175, 'pe_coi': 0, 'pe_iv': 32.74, 'pe_delta': -0.43, 'pe_gamma': 0.0008, 'pe_theta': -56.15, 'pe_vega': 5.91, 'pe_rho': -44.1505, 'future_open': 22495.25, 'future_high': 22503.0, 'future_low': 22489.1, 'future_close': 22489.1, 'future_volume': 7650, 'future_oi': 13928175, 'future_coi': 0}
2025-05-25 12:35:59,691 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-25 12:35:59,691 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 12:35:59,691 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 12:35:59,691 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 12:35:59,691 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: 619.4999999999993, PnL (Slippage): 619.4999999999993, Net PnL: 619.4999999999993
2025-05-25 12:35:59,691 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 12:35:59,692 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2025-04-08', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2025-04-08', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-04-09', 'strike': 22450, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 145.8, 'exit_price': 158.19, 'points': 12.389999999999986, 'pointsAfterSlippage': 12.389999999999986, 'pnl': 619.4999999999993, 'pnlAfterSlippage': 619.4999999999993, 'expenses': 0.0, 'netPnlAfterExpenses': 619.4999999999993, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 22535.2, 'index_exit_price': 22421.35, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-08 09:16:00', 'exit_datetime': '2025-04-08 12:00:00'}
2025-05-25 12:35:59,692 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-09' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:35:59,692 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-09' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:35:59,716 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.024s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-09'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:35:59,739 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.023s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-09'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:35:59,740 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
trade_date                   2025-04-09
trade_time                     09:16:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                            22401.4
atm_strike                      22400.0
strike                          22400.0
dte                                   0
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY09APR2522400CE
ce_open                          143.05
ce_high                           152.5
ce_low                           120.05
ce_close                          130.0
ce_volume                       3514725
ce_oi                           1836750
ce_coi                                0
ce_iv                             33.88
ce_delta                           0.51
ce_gamma                         0.0012
ce_theta                         -101.7
ce_vega                            3.76
ce_rho                          19.9531
pe_symbol           NIFTY09APR2522400PE
pe_open                          147.55
pe_high                          151.55
pe_low                            133.6
pe_close                         147.35
pe_volume                       3689775
pe_oi                           3514950
pe_coi                                0
pe_iv                             39.92
pe_delta                          -0.49
pe_gamma                         0.0011
pe_theta                        -113.15
pe_vega                            3.76
pe_rho                         -19.7077
future_open                    22475.35
future_high                     22501.8
future_low                      22462.0
future_close                   22468.25
future_volume                    144975
future_oi                      13421100
future_coi                            0
2025-05-25 12:35:59,741 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2025-04-09
trade_time                     12:00:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                            22413.0
atm_strike                      22400.0
strike                          22400.0
dte                                   0
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY09APR2522400CE
ce_open                            18.3
ce_high                            18.7
ce_low                            16.95
ce_close                          18.14
ce_volume                       5803125
ce_oi                          27893775
ce_coi                                0
ce_iv                              1.69
ce_delta                            0.6
ce_gamma                         0.0142
ce_theta                          -8.06
ce_vega                            2.14
ce_rho                          33.8933
pe_symbol           NIFTY09APR2522400PE
pe_open                           19.25
pe_high                           19.95
pe_low                            18.25
pe_close                          19.55
pe_volume                       4164750
pe_oi                          32636025
pe_coi                                0
pe_iv                              7.24
pe_delta                           -0.4
pe_gamma                         0.0057
pe_theta                         -17.96
pe_vega                            3.64
pe_rho                         -15.9328
future_open                     22473.7
future_high                    22479.65
future_low                      22470.0
future_close                    22470.0
future_volume                      8025
future_oi                      13626975
future_coi                            0
2025-05-25 12:35:59,741 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:35:59,741 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:35:59,741 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:35:59,741 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 139866552090912
2025-05-25 12:35:59,741 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:35:59,741 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:35:59,742 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:35:59,742 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:35:59,743 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-09' AND trade_date <= '2025-04-09'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:36:00,152 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.409s, returned 24122 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:36:00,155 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 24122 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:36:00,156 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:36:00,258 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:36:00,263 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 1 - Tick data for specific strike 22400.0 is EMPTY after filtering windowed data.
2025-05-25 12:36:00,264 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:36:00,264 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:36:00,264 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:36:00,264 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-09' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:36:00,264 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-09' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:36:00,293 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.028s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-09'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:36:00,323 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.030s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-09'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:36:00,325 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
trade_date                   2025-04-09
trade_time                     09:16:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                            22401.4
atm_strike                      22400.0
strike                          22400.0
dte                                   0
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY09APR2522400CE
ce_open                          143.05
ce_high                           152.5
ce_low                           120.05
ce_close                          130.0
ce_volume                       3514725
ce_oi                           1836750
ce_coi                                0
ce_iv                             33.88
ce_delta                           0.51
ce_gamma                         0.0012
ce_theta                         -101.7
ce_vega                            3.76
ce_rho                          19.9531
pe_symbol           NIFTY09APR2522400PE
pe_open                          147.55
pe_high                          151.55
pe_low                            133.6
pe_close                         147.35
pe_volume                       3689775
pe_oi                           3514950
pe_coi                                0
pe_iv                             39.92
pe_delta                          -0.49
pe_gamma                         0.0011
pe_theta                        -113.15
pe_vega                            3.76
pe_rho                         -19.7077
future_open                    22475.35
future_high                     22501.8
future_low                      22462.0
future_close                   22468.25
future_volume                    144975
future_oi                      13421100
future_coi                            0
2025-05-25 12:36:00,325 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2025-04-09
trade_time                     12:00:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                            22413.0
atm_strike                      22400.0
strike                          22400.0
dte                                   0
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY09APR2522400CE
ce_open                            18.3
ce_high                            18.7
ce_low                            16.95
ce_close                          18.14
ce_volume                       5803125
ce_oi                          27893775
ce_coi                                0
ce_iv                              1.69
ce_delta                            0.6
ce_gamma                         0.0142
ce_theta                          -8.06
ce_vega                            2.14
ce_rho                          33.8933
pe_symbol           NIFTY09APR2522400PE
pe_open                           19.25
pe_high                           19.95
pe_low                            18.25
pe_close                          19.55
pe_volume                       4164750
pe_oi                          32636025
pe_coi                                0
pe_iv                              7.24
pe_delta                           -0.4
pe_gamma                         0.0057
pe_theta                         -17.96
pe_vega                            3.64
pe_rho                         -15.9328
future_open                     22473.7
future_high                    22479.65
future_low                      22470.0
future_close                    22470.0
future_volume                      8025
future_oi                      13626975
future_coi                            0
2025-05-25 12:36:00,326 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:36:00,326 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:36:00,326 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:36:00,326 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 139866552091024
2025-05-25 12:36:00,326 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:36:00,326 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:36:00,327 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:36:00,327 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:36:00,328 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-09' AND trade_date <= '2025-04-09'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:36:00,703 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.374s, returned 24122 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:36:00,705 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 24122 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 12:36:00,706 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 12:36:00,805 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:36:00,810 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 2 - Tick data for specific strike 22400.0 is EMPTY after filtering windowed data.
2025-05-25 12:36:00,810 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:36:00,810 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:36:00,810 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:36:00,811 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-09' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:36:00,811 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-09' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:36:00,852 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.041s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-09'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:36:00,891 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.038s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-09'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:36:00,892 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
trade_date                   2025-04-09
trade_time                     09:16:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                            22401.4
atm_strike                      22400.0
strike                          22500.0
dte                                   0
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY09APR2522500CE
ce_open                           94.45
ce_high                          103.35
ce_low                            78.75
ce_close                           86.1
ce_volume                       2929050
ce_oi                           4685325
ce_coi                                0
ce_iv                             33.96
ce_delta                           0.41
ce_gamma                         0.0012
ce_theta                          -97.2
ce_vega                            3.61
ce_rho                          15.1632
pe_symbol           NIFTY09APR2522500PE
pe_open                           199.5
pe_high                           209.9
pe_low                            184.6
pe_close                         204.85
pe_volume                       1615725
pe_oi                           6684525
pe_coi                                0
pe_iv                              40.6
pe_delta                          -0.59
pe_gamma                          0.001
pe_theta                         -111.2
pe_vega                            3.65
pe_rho                         -23.8982
future_open                    22475.35
future_high                     22501.8
future_low                      22462.0
future_close                   22468.25
future_volume                    144975
future_oi                      13421100
future_coi                            0
2025-05-25 12:36:00,893 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2025-04-09
trade_time                     12:00:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                            22413.0
atm_strike                      22400.0
strike                          22500.0
dte                                   0
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY09APR2522500CE
ce_open                             6.0
ce_high                             6.0
ce_low                              5.6
ce_close                           5.85
ce_volume                       1941375
ce_oi                          22489800
ce_coi                                0
ce_iv                              8.31
ce_delta                           0.28
ce_gamma                         0.0029
ce_theta                         -14.75
ce_vega                            2.15
ce_rho                           5.7546
pe_symbol           NIFTY09APR2522500PE
pe_open                          106.95
pe_high                          108.65
pe_low                            105.8
pe_close                         107.75
pe_volume                        592575
pe_oi                           9179100
pe_coi                                0
pe_iv                             15.09
pe_delta                          -0.72
pe_gamma                         0.0024
pe_theta                         -32.73
pe_vega                            3.18
pe_rho                         -28.6782
future_open                     22473.7
future_high                    22479.65
future_low                      22470.0
future_close                    22470.0
future_volume                      8025
future_oi                      13626975
future_coi                            0
2025-05-25 12:36:00,893 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:36:00,893 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:36:00,894 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:36:00,894 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 139866552090912
2025-05-25 12:36:00,894 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:36:00,894 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:36:00,894 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:36:00,894 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:36:00,896 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-09' AND trade_date <= '2025-04-09'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:36:01,248 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.352s, returned 24122 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:36:01,251 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 24122 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:36:01,252 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:36:01,354 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:36:01,359 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 3 - Tick data for specific strike 22500.0 is EMPTY after filtering windowed data.
2025-05-25 12:36:01,360 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:36:01,360 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:36:01,360 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:36:01,360 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-09' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:36:01,360 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-09' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:36:01,403 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.042s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-09'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:36:01,446 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.043s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-09'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:36:01,448 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
trade_date                   2025-04-09
trade_time                     09:16:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                            22401.4
atm_strike                      22400.0
strike                          22300.0
dte                                   0
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY09APR2522300CE
ce_open                           203.6
ce_high                          214.65
ce_low                            176.8
ce_close                         186.75
ce_volume                        652875
ce_oi                           1091775
ce_coi                                0
ce_iv                             33.93
ce_delta                           0.61
ce_gamma                         0.0012
ce_theta                         -97.14
ce_vega                            3.55
ce_rho                          24.7089
pe_symbol           NIFTY09APR2522300PE
pe_open                           108.5
pe_high                           108.5
pe_low                             96.9
pe_close                          104.5
pe_volume                       2263125
pe_oi                           3060750
pe_coi                                0
pe_iv                             40.38
pe_delta                          -0.39
pe_gamma                          0.001
pe_theta                        -110.43
pe_vega                            3.61
pe_rho                         -15.5296
future_open                    22475.35
future_high                     22501.8
future_low                      22462.0
future_close                   22468.25
future_volume                    144975
future_oi                      13421100
future_coi                            0
2025-05-25 12:36:01,448 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2025-04-09
trade_time                     12:00:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                            22413.0
atm_strike                      22400.0
strike                          22300.0
dte                                   0
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY09APR2522300CE
ce_open                          104.35
ce_high                          105.45
ce_low                           101.95
ce_close                          102.9
ce_volume                        510300
ce_oi                           4288800
ce_coi                                0
ce_iv                              0.01
ce_delta                           0.89
ce_gamma                            0.0
ce_theta                          -6.11
ce_vega                             0.0
ce_rho                          39.4238
pe_symbol           NIFTY09APR2522300PE
pe_open                             4.8
pe_high                            4.84
pe_low                             4.55
pe_close                            4.8
pe_volume                       1139250
pe_oi                          18139275
pe_coi                                0
pe_iv                              9.99
pe_delta                          -0.11
pe_gamma                         0.0019
pe_theta                         -12.72
pe_vega                            1.73
pe_rho                          -4.2157
future_open                     22473.7
future_high                    22479.65
future_low                      22470.0
future_close                    22470.0
future_volume                      8025
future_oi                      13626975
future_coi                            0
2025-05-25 12:36:01,449 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:36:01,449 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:36:01,449 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:36:01,449 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 139866552091024
2025-05-25 12:36:01,449 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:36:01,450 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:36:01,450 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:36:01,450 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:36:01,452 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-09' AND trade_date <= '2025-04-09'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:36:01,837 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.385s, returned 24122 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:36:01,839 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 24122 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 12:36:01,840 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 12:36:01,940 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:36:01,945 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 4 - Tick data for specific strike 22300.0 is EMPTY after filtering windowed data.
2025-05-25 12:36:01,946 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:36:01,946 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:36:01,946 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:36:01,946 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 12:36:01,946 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 9), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 9), 'index_name': 'NIFTY', 'spot': 22401.4, 'atm_strike': 22400.0, 'strike': 22300.0, 'dte': 0, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY09APR2522300CE', 'ce_open': 203.6, 'ce_high': 214.65, 'ce_low': 176.8, 'ce_close': 186.75, 'ce_volume': 652875, 'ce_oi': 1091775, 'ce_coi': 0, 'ce_iv': 33.93, 'ce_delta': 0.61, 'ce_gamma': 0.0012, 'ce_theta': -97.14, 'ce_vega': 3.55, 'ce_rho': 24.7089, 'pe_symbol': 'NIFTY09APR2522300PE', 'pe_open': 108.5, 'pe_high': 108.5, 'pe_low': 96.9, 'pe_close': 104.5, 'pe_volume': 2263125, 'pe_oi': 3060750, 'pe_coi': 0, 'pe_iv': 40.38, 'pe_delta': -0.39, 'pe_gamma': 0.001, 'pe_theta': -110.43, 'pe_vega': 3.61, 'pe_rho': -15.5296, 'future_open': 22475.35, 'future_high': 22501.8, 'future_low': 22462.0, 'future_close': 22468.25, 'future_volume': 144975, 'future_oi': 13421100, 'future_coi': 0}
2025-05-25 12:36:01,946 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 9), 'trade_time': datetime.time(12, 0), 'expiry_date': datetime.date(2025, 4, 9), 'index_name': 'NIFTY', 'spot': 22413.0, 'atm_strike': 22400.0, 'strike': 22300.0, 'dte': 0, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY09APR2522300CE', 'ce_open': 104.35, 'ce_high': 105.45, 'ce_low': 101.95, 'ce_close': 102.9, 'ce_volume': 510300, 'ce_oi': 4288800, 'ce_coi': 0, 'ce_iv': 0.01, 'ce_delta': 0.89, 'ce_gamma': 0.0, 'ce_theta': -6.11, 'ce_vega': 0.0, 'ce_rho': 39.4238, 'pe_symbol': 'NIFTY09APR2522300PE', 'pe_open': 4.8, 'pe_high': 4.84, 'pe_low': 4.55, 'pe_close': 4.8, 'pe_volume': 1139250, 'pe_oi': 18139275, 'pe_coi': 0, 'pe_iv': 9.99, 'pe_delta': -0.11, 'pe_gamma': 0.0019, 'pe_theta': -12.72, 'pe_vega': 1.73, 'pe_rho': -4.2157, 'future_open': 22473.7, 'future_high': 22479.65, 'future_low': 22470.0, 'future_close': 22470.0, 'future_volume': 8025, 'future_oi': 13626975, 'future_coi': 0}
2025-05-25 12:36:01,946 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-25 12:36:01,947 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 12:36:01,947 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 12:36:01,947 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 12:36:01,947 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -4985.0, PnL (Slippage): -4985.0, Net PnL: -4985.0
2025-05-25 12:36:01,947 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 12:36:01,947 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2025-04-09', 'entry_time': '09:16:00', 'entry_day': 'Wednesday', 'exit_date': '2025-04-09', 'exit_time': '12:00:00', 'exit_day': 'Wednesday', 'symbol': 'NIFTY', 'expiry': '2025-04-09', 'strike': 22300, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 104.5, 'exit_price': 4.8, 'points': -99.7, 'pointsAfterSlippage': -99.7, 'pnl': -4985.0, 'pnlAfterSlippage': -4985.0, 'expenses': 0.0, 'netPnlAfterExpenses': -4985.0, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 22401.4, 'index_exit_price': 22413.0, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-09 09:16:00', 'exit_datetime': '2025-04-09 12:00:00'}
2025-05-25 12:36:01,948 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-11' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:36:01,948 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-11' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:36:01,977 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.029s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-11'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:36:02,016 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.039s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-11'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:36:02,018 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
trade_date                   2025-04-11
trade_time                     09:16:00
expiry_date                  2025-04-17
index_name                        NIFTY
spot                            22746.3
atm_strike                      22800.0
strike                          22800.0
dte                                   3
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY17APR2522800CE
ce_open                          228.05
ce_high                           252.9
ce_low                           228.05
ce_close                         234.45
ce_volume                        963225
ce_oi                           1342425
ce_coi                                0
ce_iv                             19.65
ce_delta                            0.5
ce_gamma                         0.0007
ce_theta                         -21.14
ce_vega                           12.24
ce_rho                         201.5044
pe_symbol           NIFTY17APR2522800PE
pe_open                          247.65
pe_high                          247.65
pe_low                           229.55
pe_close                          236.8
pe_volume                        772050
pe_oi                            423075
pe_coi                                0
pe_iv                             18.84
pe_delta                           -0.5
pe_gamma                         0.0007
pe_theta                         -14.16
pe_vega                           12.24
pe_rho                        -212.6483
future_open                     22819.9
future_high                    22849.95
future_low                      22815.1
future_close                   22830.35
future_volume                    253275
future_oi                      13164525
future_coi                            0
2025-05-25 12:36:02,018 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2025-04-11
trade_time                     12:00:00
expiry_date                  2025-04-17
index_name                        NIFTY
spot                            22878.6
atm_strike                      22900.0
strike                          22900.0
dte                                   3
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY17APR2522900CE
ce_open                           210.4
ce_high                          215.35
ce_low                            210.4
ce_close                          212.0
ce_volume                        241575
ce_oi                           3744075
ce_coi                                0
ce_iv                             16.39
ce_delta                           0.52
ce_gamma                         0.0008
ce_theta                         -18.38
ce_vega                            12.3
ce_rho                         212.7604
pe_symbol           NIFTY17APR2522900PE
pe_open                          205.95
pe_high                           206.0
pe_low                            200.7
pe_close                         204.35
pe_volume                        270375
pe_oi                           3040800
pe_coi                                0
pe_iv                             17.42
pe_delta                          -0.48
pe_gamma                         0.0007
pe_theta                         -13.06
pe_vega                            12.3
pe_rho                        -203.6588
future_open                    22951.15
future_high                     22965.0
future_low                      22951.1
future_close                    22955.0
future_volume                     13500
future_oi                      13245900
future_coi                            0
2025-05-25 12:36:02,019 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:36:02,019 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:36:02,019 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:36:02,019 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 139866552090912
2025-05-25 12:36:02,019 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:36:02,019 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:36:02,020 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:36:02,020 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:36:02,021 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-11' AND trade_date <= '2025-04-11'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:36:02,432 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.410s, returned 25264 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:36:02,435 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 25264 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:36:02,435 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:36:02,538 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:36:02,543 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 1 - Tick data for specific strike 22800.0 is EMPTY after filtering windowed data.
2025-05-25 12:36:02,544 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:36:02,544 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:36:02,544 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:36:02,544 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-11' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:36:02,544 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-11' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:36:02,581 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.036s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-11'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:36:02,616 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.035s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-11'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:36:02,618 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
trade_date                   2025-04-11
trade_time                     09:16:00
expiry_date                  2025-04-17
index_name                        NIFTY
spot                            22746.3
atm_strike                      22800.0
strike                          22800.0
dte                                   3
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY17APR2522800CE
ce_open                          228.05
ce_high                           252.9
ce_low                           228.05
ce_close                         234.45
ce_volume                        963225
ce_oi                           1342425
ce_coi                                0
ce_iv                             19.65
ce_delta                            0.5
ce_gamma                         0.0007
ce_theta                         -21.14
ce_vega                           12.24
ce_rho                         201.5044
pe_symbol           NIFTY17APR2522800PE
pe_open                          247.65
pe_high                          247.65
pe_low                           229.55
pe_close                          236.8
pe_volume                        772050
pe_oi                            423075
pe_coi                                0
pe_iv                             18.84
pe_delta                           -0.5
pe_gamma                         0.0007
pe_theta                         -14.16
pe_vega                           12.24
pe_rho                        -212.6483
future_open                     22819.9
future_high                    22849.95
future_low                      22815.1
future_close                   22830.35
future_volume                    253275
future_oi                      13164525
future_coi                            0
2025-05-25 12:36:02,618 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2025-04-11
trade_time                     12:00:00
expiry_date                  2025-04-17
index_name                        NIFTY
spot                            22878.6
atm_strike                      22900.0
strike                          22900.0
dte                                   3
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY17APR2522900CE
ce_open                           210.4
ce_high                          215.35
ce_low                            210.4
ce_close                          212.0
ce_volume                        241575
ce_oi                           3744075
ce_coi                                0
ce_iv                             16.39
ce_delta                           0.52
ce_gamma                         0.0008
ce_theta                         -18.38
ce_vega                            12.3
ce_rho                         212.7604
pe_symbol           NIFTY17APR2522900PE
pe_open                          205.95
pe_high                           206.0
pe_low                            200.7
pe_close                         204.35
pe_volume                        270375
pe_oi                           3040800
pe_coi                                0
pe_iv                             17.42
pe_delta                          -0.48
pe_gamma                         0.0007
pe_theta                         -13.06
pe_vega                            12.3
pe_rho                        -203.6588
future_open                    22951.15
future_high                     22965.0
future_low                      22951.1
future_close                    22955.0
future_volume                     13500
future_oi                      13245900
future_coi                            0
2025-05-25 12:36:02,619 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:36:02,619 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:36:02,619 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:36:02,619 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 139866552091024
2025-05-25 12:36:02,619 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:36:02,620 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:36:02,620 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:36:02,620 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:36:02,621 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-11' AND trade_date <= '2025-04-11'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:36:03,033 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.412s, returned 25264 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:36:03,036 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 25264 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 12:36:03,037 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 12:36:03,141 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:36:03,146 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 2 - Tick data for specific strike 22800.0 is EMPTY after filtering windowed data.
2025-05-25 12:36:03,147 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:36:03,147 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:36:03,147 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:36:03,147 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-11' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:36:03,147 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-11' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:36:03,178 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.030s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-11'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:36:03,207 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.029s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-11'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:36:03,208 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
trade_date                   2025-04-11
trade_time                     09:16:00
expiry_date                  2025-04-17
index_name                        NIFTY
spot                            22746.3
atm_strike                      22800.0
strike                          22900.0
dte                                   3
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY17APR2522900CE
ce_open                          180.65
ce_high                           201.5
ce_low                           180.25
ce_close                          185.3
ce_volume                        599025
ce_oi                           1004175
ce_coi                                0
ce_iv                             19.33
ce_delta                           0.43
ce_gamma                         0.0007
ce_theta                         -20.17
ce_vega                           12.05
ce_rho                         174.7366
pe_symbol           NIFTY17APR2522900PE
pe_open                          295.55
pe_high                          295.55
pe_low                           277.39
pe_close                         285.75
pe_volume                        184050
pe_oi                            255075
pe_coi                                0
pe_iv                             18.36
pe_delta                          -0.57
pe_gamma                         0.0007
pe_theta                         -12.98
pe_vega                           12.03
pe_rho                        -242.6822
future_open                     22819.9
future_high                    22849.95
future_low                      22815.1
future_close                   22830.35
future_volume                    253275
future_oi                      13164525
future_coi                            0
2025-05-25 12:36:03,209 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2025-04-11
trade_time                     12:00:00
expiry_date                  2025-04-17
index_name                        NIFTY
spot                            22878.6
atm_strike                      22900.0
strike                          23000.0
dte                                   3
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY17APR2523000CE
ce_open                          160.65
ce_high                           165.0
ce_low                           160.65
ce_close                         162.75
ce_volume                        266625
ce_oi                           5868900
ce_coi                                0
ce_iv                             16.22
ce_delta                           0.45
ce_gamma                         0.0008
ce_theta                         -17.59
ce_vega                           12.18
ce_rho                         180.7252
pe_symbol           NIFTY17APR2523000PE
pe_open                           257.8
pe_high                           257.8
pe_low                           250.55
pe_close                          254.9
pe_volume                         75150
pe_oi                           2188575
pe_coi                                0
pe_iv                             17.25
pe_delta                          -0.55
pe_gamma                         0.0007
pe_theta                         -12.29
pe_vega                            12.2
pe_rho                        -235.5611
future_open                    22951.15
future_high                     22965.0
future_low                      22951.1
future_close                    22955.0
future_volume                     13500
future_oi                      13245900
future_coi                            0
2025-05-25 12:36:03,209 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:36:03,209 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:36:03,210 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:36:03,210 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 139866552090912
2025-05-25 12:36:03,210 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:36:03,210 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:36:03,210 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:36:03,210 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:36:03,212 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-11' AND trade_date <= '2025-04-11'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:36:03,603 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.391s, returned 25264 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:36:03,606 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 25264 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:36:03,606 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:36:03,711 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:36:03,716 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 3 - Tick data for specific strike 22900.0 is EMPTY after filtering windowed data.
2025-05-25 12:36:03,716 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:36:03,716 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:36:03,717 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:36:03,717 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-11' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:36:03,717 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-11' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:36:03,759 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.041s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-11'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:36:03,800 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.041s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-11'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:36:03,801 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
trade_date                   2025-04-11
trade_time                     09:16:00
expiry_date                  2025-04-17
index_name                        NIFTY
spot                            22746.3
atm_strike                      22800.0
strike                          22700.0
dte                                   3
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY17APR2522700CE
ce_open                           286.3
ce_high                          313.64
ce_low                            286.3
ce_close                         292.85
ce_volume                        892650
ce_oi                           1013250
ce_coi                                0
ce_iv                             20.19
ce_delta                           0.56
ce_gamma                         0.0006
ce_theta                          -21.8
ce_vega                           12.09
ce_rho                         227.1442
pe_symbol           NIFTY17APR2522700PE
pe_open                           202.0
pe_high                           202.5
pe_low                           189.25
pe_close                         194.75
pe_volume                        909975
pe_oi                            613875
pe_coi                                0
pe_iv                             19.31
pe_delta                          -0.44
pe_gamma                         0.0007
pe_theta                          -14.8
pe_vega                           12.08
pe_rho                        -184.0002
future_open                     22819.9
future_high                    22849.95
future_low                      22815.1
future_close                   22830.35
future_volume                    253275
future_oi                      13164525
future_coi                            0
2025-05-25 12:36:03,802 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2025-04-11
trade_time                     12:00:00
expiry_date                  2025-04-17
index_name                        NIFTY
spot                            22878.6
atm_strike                      22900.0
strike                          22800.0
dte                                   3
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY17APR2522800CE
ce_open                           269.5
ce_high                           275.7
ce_low                            269.5
ce_close                         271.35
ce_volume                        157725
ce_oi                           3344925
ce_coi                                0
ce_iv                             16.76
ce_delta                           0.59
ce_gamma                         0.0007
ce_theta                         -18.74
ce_vega                           11.95
ce_rho                          243.333
pe_symbol           NIFTY17APR2522800PE
pe_open                          165.15
pe_high                          165.15
pe_low                           160.44
pe_close                         163.75
pe_volume                        219525
pe_oi                           5862450
pe_coi                                0
pe_iv                              17.8
pe_delta                          -0.41
pe_gamma                         0.0007
pe_theta                         -13.46
pe_vega                           11.99
pe_rho                        -173.0744
future_open                    22951.15
future_high                     22965.0
future_low                      22951.1
future_close                    22955.0
future_volume                     13500
future_oi                      13245900
future_coi                            0
2025-05-25 12:36:03,802 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:36:03,802 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:36:03,803 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:36:03,803 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 139866552091024
2025-05-25 12:36:03,803 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139866552090912, OptionType.PUT ID: 139866552091024
2025-05-25 12:36:03,803 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:36:03,803 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:36:03,804 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:36:03,805 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-11' AND trade_date <= '2025-04-11'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:36:04,170 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.365s, returned 25264 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:36:04,173 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 25264 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 12:36:04,174 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 12:36:04,279 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:36:04,284 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 4 - Tick data for specific strike 22700.0 is EMPTY after filtering windowed data.
2025-05-25 12:36:04,284 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:36:04,284 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:36:04,284 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:36:04,285 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 12:36:04,285 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 11), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 17), 'index_name': 'NIFTY', 'spot': 22746.3, 'atm_strike': 22800.0, 'strike': 22700.0, 'dte': 3, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY17APR2522700CE', 'ce_open': 286.3, 'ce_high': 313.64, 'ce_low': 286.3, 'ce_close': 292.85, 'ce_volume': 892650, 'ce_oi': 1013250, 'ce_coi': 0, 'ce_iv': 20.19, 'ce_delta': 0.56, 'ce_gamma': 0.0006, 'ce_theta': -21.8, 'ce_vega': 12.09, 'ce_rho': 227.1442, 'pe_symbol': 'NIFTY17APR2522700PE', 'pe_open': 202.0, 'pe_high': 202.5, 'pe_low': 189.25, 'pe_close': 194.75, 'pe_volume': 909975, 'pe_oi': 613875, 'pe_coi': 0, 'pe_iv': 19.31, 'pe_delta': -0.44, 'pe_gamma': 0.0007, 'pe_theta': -14.8, 'pe_vega': 12.08, 'pe_rho': -184.0002, 'future_open': 22819.9, 'future_high': 22849.95, 'future_low': 22815.1, 'future_close': 22830.35, 'future_volume': 253275, 'future_oi': 13164525, 'future_coi': 0}
2025-05-25 12:36:04,285 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 11), 'trade_time': datetime.time(12, 0), 'expiry_date': datetime.date(2025, 4, 17), 'index_name': 'NIFTY', 'spot': 22878.6, 'atm_strike': 22900.0, 'strike': 22800.0, 'dte': 3, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY17APR2522800CE', 'ce_open': 269.5, 'ce_high': 275.7, 'ce_low': 269.5, 'ce_close': 271.35, 'ce_volume': 157725, 'ce_oi': 3344925, 'ce_coi': 0, 'ce_iv': 16.76, 'ce_delta': 0.59, 'ce_gamma': 0.0007, 'ce_theta': -18.74, 'ce_vega': 11.95, 'ce_rho': 243.333, 'pe_symbol': 'NIFTY17APR2522800PE', 'pe_open': 165.15, 'pe_high': 165.15, 'pe_low': 160.44, 'pe_close': 163.75, 'pe_volume': 219525, 'pe_oi': 5862450, 'pe_coi': 0, 'pe_iv': 17.8, 'pe_delta': -0.41, 'pe_gamma': 0.0007, 'pe_theta': -13.46, 'pe_vega': 11.99, 'pe_rho': -173.0744, 'future_open': 22951.15, 'future_high': 22965.0, 'future_low': 22951.1, 'future_close': 22955.0, 'future_volume': 13500, 'future_oi': 13245900, 'future_coi': 0}
2025-05-25 12:36:04,285 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-25 12:36:04,285 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 12:36:04,285 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 12:36:04,285 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 12:36:04,286 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -1550.0, PnL (Slippage): -1550.0, Net PnL: -1550.0
2025-05-25 12:36:04,286 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 12:36:04,286 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2025-04-11', 'entry_time': '09:16:00', 'entry_day': 'Friday', 'exit_date': '2025-04-11', 'exit_time': '12:00:00', 'exit_day': 'Friday', 'symbol': 'NIFTY', 'expiry': '2025-04-17', 'strike': 22700, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 194.75, 'exit_price': 163.75, 'points': -31.0, 'pointsAfterSlippage': -31.0, 'pnl': -1550.0, 'pnlAfterSlippage': -1550.0, 'expenses': 0.0, 'netPnlAfterExpenses': -1550.0, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 22746.3, 'index_exit_price': 22878.6, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-11 09:16:00', 'exit_datetime': '2025-04-11 12:00:00'}
2025-05-25 12:36:04,286 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Number of trade records generated: 8
2025-05-25 12:36:04,286 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Generated 8 trade records via model-driven path
2025-05-25 12:36:04,286 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Closed reusable HeavyDB connection from get_trades_for_portfolio
2025-05-25 12:36:04,310 - backtester_stable.BTRUN.builders - WARNING - utils.MARGIN_INFO not found or empty - margin requirements will be 0. Attempting to populate.
2025-05-25 12:36:04,310 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'margin_symbol_info.json' at /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-25 12:36:04,310 - backtester_stable.BTRUN.core.utils - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json (Broker: angel)
2025-05-25 12:36:04,311 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.MARGIN_INFO for angel.
2025-05-25 12:36:04,311 - backtester_stable.BTRUN.builders - ERROR - utils.MARGIN_INFO still empty after populate attempt. Margin req will be 0.
2025-05-25 12:36:04,313 - backtester_stable.BTRUN.builders - DEBUG - Strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL margin requirement: 0.00
2025-05-25 12:36:04,313 - backtester_stable.BTRUN.builders - INFO - Total portfolio margin requirement: 0.00
2025-05-25 12:36:04,313 - backtester_stable.BTRUN.builders - DEBUG - Converting tick PnL to daywise DataFrame.
2025-05-25 12:36:04,315 - backtester_stable.BTRUN.builders - INFO - Backtest response parsed.
2025-05-25 12:36:04,364 - backtester_stable.BTRUN.core.runtime - INFO - Generated metrics_df with 50 records for strategies: ['Combined' 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL']
2025-05-25 12:36:04,442 - backtester_stable.BTRUN.core.runtime - INFO - Saving Excel output to /srv/samba/shared/bt/backtester_stable/BTRUN/output/direct_test_output.xlsx
2025-05-25 12:36:04,442 - backtester_stable.BTRUN.core.runtime - DEBUG - [DEBUG save_backtest_results] save_excel=True, metrics_df is empty: False
2025-05-25 12:36:04,442 - backtester_stable.BTRUN.core.io - INFO - 2025-05-25 12:36:04.442499, Started writing stats to excel file: /srv/samba/shared/bt/backtester_stable/BTRUN/output/direct_test_output.xlsx
2025-05-25 12:36:04,442 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Temporarily switching to CPU-only mode.
2025-05-25 12:36:04,505 - backtester_stable.BTRUN.core.io - INFO - 2025-05-25 12:36:04.505443, Excel file prepared successfully, Time taken: 0.06s
2025-05-25 12:36:04,505 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Restored GPU_ENABLED to: False
2025-05-25 12:36:04,505 - backtester_stable.BTRUN.core.runtime - INFO - Saving JSON output to /srv/samba/shared/bt/backtester_stable/BTRUN/output/direct_test_output.json
2025-05-25 12:36:04,523 - backtester_stable.BTRUN.core.io - INFO - --- JSON DUMP DEBUG START ---
2025-05-25 12:36:04,523 - backtester_stable.BTRUN.core.io - INFO - --- JSON DUMP DEBUG END ---
2025-05-25 12:36:04,524 - backtester_stable.BTRUN.core.io - INFO - Successfully wrote JSON output to /srv/samba/shared/bt/backtester_stable/BTRUN/output/direct_test_output.json
2025-05-25 12:36:04,529 - backtester_stable.BTRUN.core.io - ERROR - Text-to-speech failed: This means you probably do not have eSpeak or eSpeak-ng installed!
