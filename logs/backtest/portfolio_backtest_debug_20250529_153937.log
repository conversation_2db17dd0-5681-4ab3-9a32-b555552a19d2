2025-05-29 15:39:37,008 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-29 15:39:37,008 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-29 15:39:37,008 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/../models', '/srv/samba/shared', '/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages', '/srv/samba/shared']
2025-05-29 15:39:37,008 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-29 15:39:37,009 - __main__ - DEBUG - GPU enabled: False
2025-05-29 15:39:37,011 - bt.common.gpu_worker_pool - INFO - Calculated optimal workers: 8 (CPU: 72, Memory: 251.8GB)
2025-05-29 15:39:37,012 - __main__ - INFO - Running in GPU-optimized parallel mode with 8 workers.
2025-05-29 15:39:37,012 - __main__ - INFO - Initialized GPU worker pool with 8 workers
2025-05-29 15:39:37,012 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-29 15:39:37,012 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-29 15:39:37,012 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-29 15:39:37,018 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-29 15:39:37,018 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-29 15:39:37,018 - backtester_stable.BTRUN.core.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-29 15:39:37,018 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-29 15:39:37,019 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-29 15:39:37,019 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-29 15:39:37,019 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-29 15:39:37,019 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-29 15:39:37,019 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-29 15:39:37,019 - __main__ - INFO - Using portfolio Excel from CLI argument: bt/backtester_stable/BTRUN/input_sheets/input_portfolio_2024_4months.xlsx
2025-05-29 15:39:37,019 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: bt/backtester_stable/BTRUN/input_sheets
2025-05-29 15:39:37,019 - __main__ - INFO - Parsing main portfolio Excel: bt/backtester_stable/BTRUN/input_sheets/input_portfolio_2024_4months.xlsx using modern parser. INPUT_FILE_FOLDER is currently: bt/backtester_stable/BTRUN/input_sheets
2025-05-29 15:39:37,144 - __main__ - INFO - Portfolio 'NIF0DTE': Date range 2024-04-01 to 2024-08-31 split into 22 batches of approx 7 days.
2025-05-29 15:39:37,146 - __main__ - INFO - Submitting 22 total date-batched tasks to GPU worker pool.
2025-05-29 15:39:37,146 - __main__ - ERROR - Failed to get HeavyDB connection: No HeavyDB connection available in worker
2025-05-29 15:39:37,146 - __main__ - ERROR - Failed to get HeavyDB connection: No HeavyDB connection available in worker
2025-05-29 15:39:37,147 - __main__ - ERROR - Failed to get HeavyDB connection: No HeavyDB connection available in worker
2025-05-29 15:39:37,147 - __main__ - ERROR - Failed to get HeavyDB connection: No HeavyDB connection available in worker
2025-05-29 15:39:37,147 - __main__ - ERROR - Failed to get HeavyDB connection: No HeavyDB connection available in worker
2025-05-29 15:39:37,148 - __main__ - ERROR - Failed to get HeavyDB connection: No HeavyDB connection available in worker
2025-05-29 15:39:37,148 - __main__ - ERROR - Failed to get HeavyDB connection: No HeavyDB connection available in worker
2025-05-29 15:39:37,148 - __main__ - ERROR - Failed to get HeavyDB connection: No HeavyDB connection available in worker
2025-05-29 15:39:37,149 - __main__ - ERROR - Failed to get HeavyDB connection: No HeavyDB connection available in worker
2025-05-29 15:39:37,149 - __main__ - ERROR - Failed to get HeavyDB connection: No HeavyDB connection available in worker
2025-05-29 15:39:37,149 - __main__ - ERROR - Failed to get HeavyDB connection: No HeavyDB connection available in worker
2025-05-29 15:39:37,149 - __main__ - ERROR - Failed to get HeavyDB connection: No HeavyDB connection available in worker
2025-05-29 15:39:37,149 - __main__ - ERROR - Failed to get HeavyDB connection: No HeavyDB connection available in worker
2025-05-29 15:39:37,150 - __main__ - ERROR - Failed to get HeavyDB connection: No HeavyDB connection available in worker
2025-05-29 15:39:37,150 - __main__ - ERROR - Failed to get HeavyDB connection: No HeavyDB connection available in worker
2025-05-29 15:39:37,150 - __main__ - ERROR - Failed to get HeavyDB connection: No HeavyDB connection available in worker
2025-05-29 15:39:37,150 - __main__ - ERROR - Failed to get HeavyDB connection: No HeavyDB connection available in worker
2025-05-29 15:39:37,150 - __main__ - ERROR - Failed to get HeavyDB connection: No HeavyDB connection available in worker
2025-05-29 15:39:37,151 - __main__ - ERROR - Failed to get HeavyDB connection: No HeavyDB connection available in worker
2025-05-29 15:39:37,151 - __main__ - ERROR - Failed to get HeavyDB connection: No HeavyDB connection available in worker
2025-05-29 15:39:37,151 - __main__ - ERROR - Failed to get HeavyDB connection: No HeavyDB connection available in worker
2025-05-29 15:39:37,151 - __main__ - ERROR - Failed to get HeavyDB connection: No HeavyDB connection available in worker
2025-05-29 15:39:37,152 - __main__ - INFO - Combining 22 batch(es) for portfolio: NIF0DTE
2025-05-29 15:39:37,153 - __main__ - ERROR - All batches failed for portfolio: NIF0DTE. First error: No HeavyDB connection available in worker
2025-05-29 15:39:37,153 - __main__ - INFO - Attempting to save combined backtest results for 1 portfolio(s) to bt/backtester_stable/BTRUN/output/tbs_2024_4months.xlsx
2025-05-29 15:39:37,153 - __main__ - ERROR - Portfolio 'NIF0DTE' failed, results not included in Excel. Error: All 22 batches failed for NIF0DTE
2025-05-29 15:39:37,154 - __main__ - ERROR - All portfolios failed or produced no successful results to save.
2025-05-29 15:39:37,161 - __main__ - INFO - Backtest results saved to bt/backtester_stable/BTRUN/output/tbs_2024_4months.xlsx
