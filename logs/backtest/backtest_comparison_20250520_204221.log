2025-05-20 20:42:21,548 - backtest_comparison - INFO - Running command: python3 bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py --portfolio-excel input_portfolio.xlsx --output-path gpu_backtest_output.xlsx --slippage 0.1
2025-05-20 20:42:29,361 - backtest_comparison - INFO - Command finished with return code: 0
2025-05-20 20:42:29,361 - backtest_comparison - INFO - Command stdout:
--- TEMP SNIPPET EXECUTING ---
TEMP SNIPPET: Attempting to read tests/fixtures/mapping_excels/test_main_portfolio.xlsx
TEMP_OUTPUT:PortfolioName=NIF0DTE
TEMP_OUTPUT:StartDate=01_04_2025
TEMP_OUTPUT:StrategyExcelFilePath=/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx
--- TEMP SNIPPET END ---
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
[DEBUG] run_full_backtest bt_params keys: ['portfolio_model', 'portfolio_name', 'start_date', 'end_date']
[DEBUG] bt_response keys: ['data']

2025-05-20 20:42:29,361 - backtest_comparison - WARNING - Command stderr:
Partially restored config.py is being imported - Phase 10.
USING PARTIALLY RESTORED DEBUG CONFIG.PY - get_table_name active!
USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
ERROR:root:Failed to import BTRUN config module
WARNING:backtester_stable.BTRUN.config:Fixture 'margin_symbol_info.json' not found in any of the standard locations. Returning constructed path based on INPUT_FILE_FOLDER.
WARNING:backtester_stable.BTRUN.utils:Margin symbol info file '/srv/samba/shared/tests/fixtures/mapping_excels/margin_symbol_info.json' not found. config.MARGIN_INFO will be empty.
WARNING:backtester_stable.BTRUN.builders:utils.MARGIN_INFO not found or empty - margin requirements will be 0. Attempting to populate.
WARNING:backtester_stable.BTRUN.config:Fixture 'margin_symbol_info.json' not found in any of the standard locations. Returning constructed path based on INPUT_FILE_FOLDER.
WARNING:backtester_stable.BTRUN.utils:Margin symbol info file '/srv/samba/shared/tests/fixtures/mapping_excels/margin_symbol_info.json' not found. config.MARGIN_INFO will be empty.
ERROR:backtester_stable.BTRUN.builders:utils.MARGIN_INFO still empty after populate attempt. Margin req will be 0.
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:197: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd.rename(columns={"entryDate": "Date", "bookedPnL": "PNL"}, inplace=True)
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:198: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['Date'] = pd.to_datetime(temp_df_pd['Date'])
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:199: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['year'] = temp_df_pd['Date'].dt.year
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:200: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['day_name'] = temp_df_pd['Date'].dt.strftime("%A")
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:235: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd.rename(columns={"entryDate": "Date", "bookedPnL": "PNL"}, inplace=True)
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:236: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['Date'] = pd.to_datetime(temp_df_pd['Date'])
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:237: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['year'] = temp_df_pd['Date'].dt.year
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:238: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['month_name'] = temp_df_pd['Date'].dt.strftime("%B") # Full month name
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:197: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd.rename(columns={"entryDate": "Date", "bookedPnL": "PNL"}, inplace=True)
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:198: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['Date'] = pd.to_datetime(temp_df_pd['Date'])
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:199: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['year'] = temp_df_pd['Date'].dt.year
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:200: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['day_name'] = temp_df_pd['Date'].dt.strftime("%A")
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:235: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd.rename(columns={"entryDate": "Date", "bookedPnL": "PNL"}, inplace=True)
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:236: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['Date'] = pd.to_datetime(temp_df_pd['Date'])
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:237: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['year'] = temp_df_pd['Date'].dt.year
/srv/samba/shared/bt/backtester_stable/BTRUN/stats.py:238: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['month_name'] = temp_df_pd['Date'].dt.strftime("%B") # Full month name
WARNING:backtester_stable.BTRUN.io:pyttsx3 not installed - skipping text-to-speech

2025-05-20 20:42:29,476 - backtest_comparison - INFO - Golden file sheets: ['PortfolioParameter', 'GeneralParameter', 'LegParameter', 'Metrics', 'Max Profit and Loss', 'PORTFOLIO Trans', 'PORTFOLIO Results', 'RS,916-1200,ATM-SELL,OTM2-BUY W', 'RS_ATM_OTM_Results']
2025-05-20 20:42:29,476 - backtest_comparison - INFO - Output file sheets: ['PortfolioParameter', 'GeneralParameter', 'LegParameter', 'Metrics', 'Max Profit and Loss', 'PORTFOLIO Trans', 'PORTFOLIO Results', 'RS,916-1200,ATM-SELL,OTM2-BUY W', 'RS_ATM_OTM_Results']
2025-05-20 20:42:29,476 - backtest_comparison - INFO - Comparing sheet: Metrics
2025-05-20 20:42:29,498 - backtest_comparison - INFO - Sheet 'Metrics' matches
2025-05-20 20:42:29,498 - backtest_comparison - INFO - Comparing sheet: PORTFOLIO Results
2025-05-20 20:42:29,520 - backtest_comparison - INFO - Sheet 'PORTFOLIO Results' matches
2025-05-20 20:42:29,520 - backtest_comparison - INFO - Comparing sheet: PortfolioParameter
2025-05-20 20:42:29,540 - backtest_comparison - INFO - Sheet 'PortfolioParameter' matches
2025-05-20 20:42:29,540 - backtest_comparison - INFO - Comparing sheet: GeneralParameter
2025-05-20 20:42:29,578 - backtest_comparison - INFO - Sheet 'GeneralParameter' matches
2025-05-20 20:42:29,578 - backtest_comparison - INFO - Comparing sheet: Max Profit and Loss
2025-05-20 20:42:29,598 - backtest_comparison - INFO - Sheet 'Max Profit and Loss' matches
2025-05-20 20:42:29,598 - backtest_comparison - INFO - Comparing sheet: LegParameter
2025-05-20 20:42:29,627 - backtest_comparison - INFO - Sheet 'LegParameter' matches
2025-05-20 20:42:29,628 - backtest_comparison - INFO - Comparing sheet: RS_ATM_OTM_Results
2025-05-20 20:42:29,649 - backtest_comparison - INFO - Sheet 'RS_ATM_OTM_Results' matches
2025-05-20 20:42:29,649 - backtest_comparison - INFO - Comparing sheet: RS,916-1200,ATM-SELL,OTM2-BUY W
2025-05-20 20:42:29,675 - backtest_comparison - WARNING - Sheet 'RS,916-1200,ATM-SELL,OTM2-BUY W', column 'Reason' values don't match (different types: float64 vs int64)
2025-05-20 20:42:29,678 - backtest_comparison - WARNING - Sheet 'RS,916-1200,ATM-SELL,OTM2-BUY W' has differences
2025-05-20 20:42:29,678 - backtest_comparison - INFO - Comparing sheet: PORTFOLIO Trans
2025-05-20 20:42:29,705 - backtest_comparison - WARNING - Sheet 'PORTFOLIO Trans', column 'Reason' values don't match (different types: float64 vs int64)
2025-05-20 20:42:29,708 - backtest_comparison - WARNING - Sheet 'PORTFOLIO Trans' has differences
2025-05-20 20:42:29,708 - backtest_comparison - WARNING - Some sheets have differences
