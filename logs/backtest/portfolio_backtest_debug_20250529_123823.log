2025-05-29 12:38:23,677 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-29 12:38:23,677 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-29 12:38:23,677 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/../models', '/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages', '/srv/samba/shared']
2025-05-29 12:38:23,677 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-29 12:38:23,678 - __main__ - DEBUG - GPU enabled: False
2025-05-29 12:38:23,679 - __main__ - INFO - GPU acceleration enabled: False
2025-05-29 12:38:23,680 - __main__ - INFO - HeavyDB integration available (Host: 127.0.0.1)
2025-05-29 12:38:23,902 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-29 12:38:23,902 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-29 12:38:23,902 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-29 12:38:23,903 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-29 12:38:23,903 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-29 12:38:23,903 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-29 12:38:23,909 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-29 12:38:23,909 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-29 12:38:23,910 - backtester_stable.BTRUN.core.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-29 12:38:23,910 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-29 12:38:23,910 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-29 12:38:23,910 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-29 12:38:23,910 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-29 12:38:23,910 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-29 12:38:23,910 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-29 12:38:23,910 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_exit_120000.xlsx
2025-05-29 12:38:23,910 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-29 12:38:23,911 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_exit_120000.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-29 12:38:24,041 - __main__ - INFO - Successfully parsed 1 portfolio(s) using modern parser.
2025-05-29 12:38:24,041 - __main__ - INFO - Queueing portfolio: NIF0DTE (from modern_portfolio_parser)
2025-05-29 12:38:24,041 - __main__ - INFO - Executing 1 portfolios sequentially (multiprocessing temporarily disabled for debugging)
2025-05-29 12:38:24,042 - __main__ - INFO - Processing payload 1/1: NIF0DTE
2025-05-29 12:38:24,042 - backtester_stable.BTRUN.core.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'NIF0DTE'
2025-05-29 12:38:24,264 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-29 12:38:24,307 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.041s, returned 563 rows. Query: SELECT SUBSTRING('SELECT DISTINCT trade_date FROM nifty_option_chain', 1, 200) ...
2025-05-29 12:38:24,351 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - Cached 563 distinct trade dates
2025-05-29 12:38:24,351 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] portfolio_model_dict keys: ['portfolio_name', 'start_date', 'end_date', 'slippage_percent', 'margin_multiplier', 'is_tick_bt', 'strategies', 'extra_params']
2025-05-29 12:38:24,351 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 1 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-29 12:38:24,351 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-29 12:38:24,351 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-29 12:38:24,396 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.044s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-29 12:38:24,449 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.053s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-29 12:38:24,449 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Query results: entry_df size=1, exit_df size=1
2025-05-29 12:38:24,451 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-29 12:38:24,451 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 139698914100832
2025-05-29 12:38:24,451 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139698914100832, OptionType.PUT ID: 139698914100944
2025-05-29 12:38:24,451 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-29 12:38:24,451 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-29 12:38:24,451 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-29 12:38:24,673 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-29 12:38:24,673 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-29 12:38:24,975 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.302s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-29 12:38:24,978 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-29 12:38:24,978 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-29 12:38:25,061 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-29 12:38:25,063 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Error during get_trades_for_portfolio main logic: Invalid comparison between dtype=datetime64[ns] and time
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/arrays/datetimelike.py", line 1054, in _cmp_method
    other = self._validate_comparison_value(other)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/arrays/datetimelike.py", line 588, in _validate_comparison_value
    raise InvalidComparison(other)
pandas.core.arrays.datetimelike.InvalidComparison: 09:16:00

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/heavydb_trade_processing.py", line 187, in get_trades_for_portfolio
    (price_df['datetime'] >= entry_time) &
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/ops/common.py", line 72, in new_method
    return method(self, other)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/arraylike.py", line 62, in __ge__
    return self._cmp_method(other, operator.ge)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/series.py", line 6243, in _cmp_method
    res_values = ops.comparison_op(lvalues, rvalues, op)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/ops/array_ops.py", line 273, in comparison_op
    res_values = op(lvalues, rvalues)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/ops/common.py", line 72, in new_method
    return method(self, other)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/arraylike.py", line 62, in __ge__
    return self._cmp_method(other, operator.ge)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/arrays/datetimelike.py", line 1056, in _cmp_method
    return invalid_comparison(self, other, op)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/ops/invalid.py", line 36, in invalid_comparison
    raise TypeError(f"Invalid comparison between dtype={left.dtype} and {typ}")
TypeError: Invalid comparison between dtype=datetime64[ns] and time
2025-05-29 12:38:25,065 - debug_detail - ERROR - TRADE_PROC_DETAIL: Exception in get_trades_for_portfolio: Invalid comparison between dtype=datetime64[ns] and time
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/arrays/datetimelike.py", line 1054, in _cmp_method
    other = self._validate_comparison_value(other)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/arrays/datetimelike.py", line 588, in _validate_comparison_value
    raise InvalidComparison(other)
pandas.core.arrays.datetimelike.InvalidComparison: 09:16:00

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/heavydb_trade_processing.py", line 187, in get_trades_for_portfolio
    (price_df['datetime'] >= entry_time) &
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/ops/common.py", line 72, in new_method
    return method(self, other)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/arraylike.py", line 62, in __ge__
    return self._cmp_method(other, operator.ge)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/series.py", line 6243, in _cmp_method
    res_values = ops.comparison_op(lvalues, rvalues, op)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/ops/array_ops.py", line 273, in comparison_op
    res_values = op(lvalues, rvalues)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/ops/common.py", line 72, in new_method
    return method(self, other)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/arraylike.py", line 62, in __ge__
    return self._cmp_method(other, operator.ge)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/arrays/datetimelike.py", line 1056, in _cmp_method
    return invalid_comparison(self, other, op)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/ops/invalid.py", line 36, in invalid_comparison
    raise TypeError(f"Invalid comparison between dtype={left.dtype} and {typ}")
TypeError: Invalid comparison between dtype=datetime64[ns] and time
2025-05-29 12:38:25,066 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Closed reusable HeavyDB connection from get_trades_for_portfolio
2025-05-29 12:38:25,066 - debug_detail - WARNING - BUILDERS: No 'trades' data in bt_response['data'] or invalid format.
2025-05-29 12:38:25,066 - backtester_stable.BTRUN.core.runtime - WARNING - No trades found in backtest response after parsing in runtime.
2025-05-29 12:38:25,068 - backtester_stable.BTRUN.core.runtime - INFO - Saving Excel output to bt/backtester_stable/BTRUN/output/tbs_2024_test_fixed.xlsx
2025-05-29 12:38:25,068 - backtester_stable.BTRUN.core.runtime - WARNING - Empty metrics DataFrame - not saving Excel output
2025-05-29 12:38:25,068 - backtester_stable.BTRUN.core.runtime - INFO - Saving JSON output to bt/backtester_stable/BTRUN/output/tbs_2024_test_fixed.json
2025-05-29 12:38:25,068 - backtester_stable.BTRUN.core.runtime - WARNING - Empty metrics DataFrame - not saving JSON output
2025-05-29 12:38:25,073 - backtester_stable.BTRUN.core.io - ERROR - Text-to-speech failed: This means you probably do not have eSpeak or eSpeak-ng installed!
