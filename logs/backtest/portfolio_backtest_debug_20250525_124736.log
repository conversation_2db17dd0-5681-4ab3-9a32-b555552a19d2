2025-05-25 12:47:36,700 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-25 12:47:36,700 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-25 12:47:36,700 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/../models', '/srv/samba/shared', '/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages', '/srv/samba/shared']
2025-05-25 12:47:36,700 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-25 12:47:36,700 - __main__ - DEBUG - GPU enabled: False
2025-05-25 12:47:36,702 - __main__ - INFO - GPU acceleration enabled: False
2025-05-25 12:47:36,702 - __main__ - INFO - HeavyDB integration available (Host: 127.0.0.1)
2025-05-25 12:47:36,921 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-25 12:47:36,922 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-25 12:47:36,922 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-25 12:47:36,922 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-25 12:47:36,922 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-25 12:47:36,922 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-25 12:47:36,928 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-25 12:47:36,928 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-25 12:47:36,928 - backtester_stable.BTRUN.core.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-25 12:47:36,928 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-25 12:47:36,929 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-25 12:47:36,930 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-25 12:47:36,930 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-25 12:47:36,930 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-25 12:47:36,930 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-25 12:47:36,930 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-25 12:47:36,930 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-25 12:47:36,930 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-25 12:47:37,063 - __main__ - INFO - Successfully parsed 1 portfolio(s) using modern parser.
2025-05-25 12:47:37,063 - __main__ - INFO - Queueing portfolio: NIF0DTE (from modern_portfolio_parser)
2025-05-25 12:47:37,063 - __main__ - INFO - Executing 1 portfolios sequentially (multiprocessing temporarily disabled for debugging)
2025-05-25 12:47:37,064 - __main__ - INFO - Processing payload 1/1: NIF0DTE
2025-05-25 12:47:37,064 - backtester_stable.BTRUN.core.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'NIF0DTE'
2025-05-25 12:47:37,283 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-25 12:47:37,303 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 563 rows. Query: SELECT SUBSTRING('SELECT DISTINCT trade_date FROM nifty_option_chain', 1, 200) ...
2025-05-25 12:47:37,347 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - Cached 563 distinct trade dates
2025-05-25 12:47:37,347 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] portfolio_model_dict keys: ['portfolio_name', 'start_date', 'end_date', 'slippage_percent', 'margin_multiplier', 'is_tick_bt', 'strategies', 'extra_params']
2025-05-25 12:47:37,348 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:47:37,348 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:47:37,387 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.038s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:47:37,425 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.038s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:47:37,426 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523450CE
ce_open                          102.85
ce_high                          106.65
ce_low                            97.85
ce_close                         100.75
ce_volume                       1015050
ce_oi                            978075
ce_coi                                0
ce_iv                             13.44
ce_delta                           0.48
ce_gamma                         0.0015
ce_theta                         -23.27
ce_vega                            7.95
ce_rho                          81.3115
pe_symbol           NIFTY03APR2523450PE
pe_open                           118.2
pe_high                           122.3
pe_low                            112.2
pe_close                          115.7
pe_volume                        877125
pe_oi                           1618950
pe_coi                                0
pe_iv                             13.74
pe_delta                          -0.52
pe_gamma                         0.0015
pe_theta                          -17.3
pe_vega                            7.95
pe_rho                         -88.3744
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-25 12:47:37,427 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23200.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                           123.6
ce_high                           124.3
ce_low                           119.25
ce_close                          124.2
ce_volume                        326775
ce_oi                           4374450
ce_coi                           193725
ce_iv                             14.99
ce_delta                           0.52
ce_gamma                         0.0013
ce_theta                         -25.57
ce_vega                            7.87
ce_rho                          86.2799
pe_symbol           NIFTY03APR2523200PE
pe_open                           100.2
pe_high                           105.2
pe_low                            99.55
pe_close                         100.75
pe_volume                        526875
pe_oi                           5030025
pe_coi                           101175
pe_iv                             13.55
pe_delta                          -0.48
pe_gamma                         0.0015
pe_theta                         -17.08
pe_vega                            7.87
pe_rho                         -81.3242
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-25 12:47:37,427 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:47:37,427 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:47:37,427 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:47:37,428 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140526232273408
2025-05-25 12:47:37,428 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140526232273408, OptionType.PUT ID: 140526232273520
2025-05-25 12:47:37,428 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:47:37,428 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:47:37,428 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:47:37,430 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:47:37,710 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.280s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:47:37,712 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:47:37,713 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:47:37,771 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:47:37,775 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 1 - Tick data for specific strike 23450.0 is EMPTY after filtering windowed data.
2025-05-25 12:47:37,775 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:37,776 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:37,776 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:37,776 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:47:37,776 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:47:37,800 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.024s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:47:37,823 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.022s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:47:37,824 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523450CE
ce_open                          102.85
ce_high                          106.65
ce_low                            97.85
ce_close                         100.75
ce_volume                       1015050
ce_oi                            978075
ce_coi                                0
ce_iv                             13.44
ce_delta                           0.48
ce_gamma                         0.0015
ce_theta                         -23.27
ce_vega                            7.95
ce_rho                          81.3115
pe_symbol           NIFTY03APR2523450PE
pe_open                           118.2
pe_high                           122.3
pe_low                            112.2
pe_close                          115.7
pe_volume                        877125
pe_oi                           1618950
pe_coi                                0
pe_iv                             13.74
pe_delta                          -0.52
pe_gamma                         0.0015
pe_theta                          -17.3
pe_vega                            7.95
pe_rho                         -88.3744
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-25 12:47:37,825 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23200.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                           123.6
ce_high                           124.3
ce_low                           119.25
ce_close                          124.2
ce_volume                        326775
ce_oi                           4374450
ce_coi                           193725
ce_iv                             14.99
ce_delta                           0.52
ce_gamma                         0.0013
ce_theta                         -25.57
ce_vega                            7.87
ce_rho                          86.2799
pe_symbol           NIFTY03APR2523200PE
pe_open                           100.2
pe_high                           105.2
pe_low                            99.55
pe_close                         100.75
pe_volume                        526875
pe_oi                           5030025
pe_coi                           101175
pe_iv                             13.55
pe_delta                          -0.48
pe_gamma                         0.0015
pe_theta                         -17.08
pe_vega                            7.87
pe_rho                         -81.3242
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-25 12:47:37,825 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:47:37,825 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:47:37,825 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:47:37,826 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140526232273520
2025-05-25 12:47:37,826 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140526232273408, OptionType.PUT ID: 140526232273520
2025-05-25 12:47:37,826 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:47:37,826 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:47:37,826 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:47:37,827 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:47:38,068 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.240s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:47:38,070 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 12:47:38,070 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 12:47:38,126 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:47:38,130 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 2 - Tick data for specific strike 23450.0 is EMPTY after filtering windowed data.
2025-05-25 12:47:38,130 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:38,130 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:38,131 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:38,131 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:47:38,131 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:47:38,173 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.042s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:47:38,213 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.040s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:47:38,215 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23550.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY03APR2523550CE
ce_open                           61.15
ce_high                           61.75
ce_low                             55.0
ce_close                          56.45
ce_volume                       1147575
ce_oi                           2744250
ce_coi                                0
ce_iv                             12.93
ce_delta                           0.34
ce_gamma                         0.0014
ce_theta                         -19.86
ce_vega                            7.25
ce_rho                          56.1949
pe_symbol           NIFTY03APR2523550PE
pe_open                           174.0
pe_high                           180.1
pe_low                            167.8
pe_close                          175.5
pe_volume                        326550
pe_oi                           1975725
pe_coi                                0
pe_iv                             13.83
pe_delta                          -0.66
pe_gamma                         0.0013
pe_theta                         -14.93
pe_vega                            7.34
pe_rho                        -112.5433
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-25 12:47:38,215 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23300.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY03APR2523300CE
ce_open                           75.59
ce_high                           76.15
ce_low                            73.15
ce_close                          76.15
ce_volume                        250950
ce_oi                           7928250
ce_coi                           109950
ce_iv                             14.54
ce_delta                           0.37
ce_gamma                         0.0013
ce_theta                         -23.12
ce_vega                            7.53
ce_rho                          63.7039
pe_symbol           NIFTY03APR2523300PE
pe_open                           152.9
pe_high                          158.35
pe_low                           151.65
pe_close                         153.44
pe_volume                        252000
pe_oi                           4303500
pe_coi                           -10725
pe_iv                             13.14
pe_delta                          -0.63
pe_gamma                         0.0015
pe_theta                         -14.47
pe_vega                            7.45
pe_rho                        -107.0027
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-25 12:47:38,216 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:47:38,216 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:47:38,216 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:47:38,216 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140526232273408
2025-05-25 12:47:38,216 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140526232273408, OptionType.PUT ID: 140526232273520
2025-05-25 12:47:38,216 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:47:38,217 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:47:38,217 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:47:38,218 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:47:38,499 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.280s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:47:38,500 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:47:38,501 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:47:38,553 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:47:38,557 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 3 - Tick data for specific strike 23550.0 is EMPTY after filtering windowed data.
2025-05-25 12:47:38,557 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:38,557 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:38,558 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:38,558 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:47:38,558 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:47:38,582 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.023s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:47:38,605 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.023s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:47:38,606 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23350.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY03APR2523350CE
ce_open                           164.4
ce_high                           166.5
ce_low                            156.3
ce_close                          158.0
ce_volume                        408675
ce_oi                            381750
ce_coi                                0
ce_iv                             13.71
ce_delta                           0.62
ce_gamma                         0.0014
ce_theta                         -23.53
ce_vega                            7.54
ce_rho                         105.3755
pe_symbol           NIFTY03APR2523350PE
pe_open                            78.3
pe_high                           80.95
pe_low                            71.84
pe_close                          76.59
pe_volume                       1021875
pe_oi                           1656000
pe_coi                                0
pe_iv                             14.49
pe_delta                          -0.38
pe_gamma                         0.0013
pe_theta                         -18.33
pe_vega                            7.58
pe_rho                         -64.7877
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-25 12:47:38,606 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23100.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY03APR2523100CE
ce_open                           186.9
ce_high                           188.0
ce_low                            181.8
ce_close                          187.2
ce_volume                         77775
ce_oi                            957825
ce_coi                            12900
ce_iv                             15.68
ce_delta                           0.66
ce_gamma                         0.0012
ce_theta                         -25.85
ce_vega                            7.36
ce_rho                         106.8743
pe_symbol           NIFTY03APR2523100PE
pe_open                            63.7
pe_high                            67.2
pe_low                            63.05
pe_close                          64.15
pe_volume                        269475
pe_oi                           3243600
pe_coi                            78900
pe_iv                             14.17
pe_delta                          -0.34
pe_gamma                         0.0013
pe_theta                         -17.23
pe_vega                            7.25
pe_rho                         -57.8964
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-25 12:47:38,607 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:47:38,607 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:47:38,607 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:47:38,607 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140526232273520
2025-05-25 12:47:38,607 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140526232273408, OptionType.PUT ID: 140526232273520
2025-05-25 12:47:38,607 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:47:38,607 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:47:38,607 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:47:38,609 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:47:38,874 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.265s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:47:38,876 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 12:47:38,876 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 12:47:38,932 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:47:38,936 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 4 - Tick data for specific strike 23350.0 is EMPTY after filtering windowed data.
2025-05-25 12:47:38,936 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:38,936 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:38,936 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:38,936 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 12:47:38,936 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23420.45, 'atm_strike': 23450.0, 'strike': 23350.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY03APR2523350CE', 'ce_open': 164.4, 'ce_high': 166.5, 'ce_low': 156.3, 'ce_close': 158.0, 'ce_volume': 408675, 'ce_oi': 381750, 'ce_coi': 0, 'ce_iv': 13.71, 'ce_delta': 0.62, 'ce_gamma': 0.0014, 'ce_theta': -23.53, 'ce_vega': 7.54, 'ce_rho': 105.3755, 'pe_symbol': 'NIFTY03APR2523350PE', 'pe_open': 78.3, 'pe_high': 80.95, 'pe_low': 71.84, 'pe_close': 76.59, 'pe_volume': 1021875, 'pe_oi': 1656000, 'pe_coi': 0, 'pe_iv': 14.49, 'pe_delta': -0.38, 'pe_gamma': 0.0013, 'pe_theta': -18.33, 'pe_vega': 7.58, 'pe_rho': -64.7877, 'future_open': 23534.5, 'future_high': 23543.95, 'future_low': 23527.0, 'future_close': 23536.75, 'future_volume': 86475, 'future_oi': 12605100, 'future_coi': 0}
2025-05-25 12:47:38,936 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(12, 0), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23195.3, 'atm_strike': 23200.0, 'strike': 23100.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY03APR2523100CE', 'ce_open': 186.9, 'ce_high': 188.0, 'ce_low': 181.8, 'ce_close': 187.2, 'ce_volume': 77775, 'ce_oi': 957825, 'ce_coi': 12900, 'ce_iv': 15.68, 'ce_delta': 0.66, 'ce_gamma': 0.0012, 'ce_theta': -25.85, 'ce_vega': 7.36, 'ce_rho': 106.8743, 'pe_symbol': 'NIFTY03APR2523100PE', 'pe_open': 63.7, 'pe_high': 67.2, 'pe_low': 63.05, 'pe_close': 64.15, 'pe_volume': 269475, 'pe_oi': 3243600, 'pe_coi': 78900, 'pe_iv': 14.17, 'pe_delta': -0.34, 'pe_gamma': 0.0013, 'pe_theta': -17.23, 'pe_vega': 7.25, 'pe_rho': -57.8964, 'future_open': 23347.0, 'future_high': 23348.85, 'future_low': 23339.0, 'future_close': 23348.1, 'future_volume': 19050, 'future_oi': 13928550, 'future_coi': -28275}
2025-05-25 12:47:38,937 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-25 12:47:38,937 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 12:47:38,937 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 12:47:38,937 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 12:47:38,937 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -621.9999999999999, PnL (Slippage): -621.9999999999999, Net PnL: -621.9999999999999
2025-05-25 12:47:38,937 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 12:47:38,937 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2025-04-01', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2025-04-01', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23350, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 76.59, 'exit_price': 64.15, 'points': -12.439999999999998, 'pointsAfterSlippage': -12.439999999999998, 'pnl': -621.9999999999999, 'pnlAfterSlippage': -621.9999999999999, 'expenses': 0.0, 'netPnlAfterExpenses': -621.9999999999999, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23420.45, 'index_exit_price': 23195.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-01 09:16:00', 'exit_datetime': '2025-04-01 12:00:00'}
2025-05-25 12:47:38,937 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:47:38,937 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:47:38,962 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.024s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:47:38,984 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.022s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:47:38,985 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
trade_date                   2025-04-02
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23204.8
atm_strike                      23200.0
strike                          23200.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                          107.95
ce_high                           113.5
ce_low                           105.15
ce_close                          110.5
ce_volume                       2666850
ce_oi                           5862000
ce_coi                                0
ce_iv                             16.53
ce_delta                           0.53
ce_gamma                         0.0015
ce_theta                         -34.48
ce_vega                             6.2
ce_rho                          54.4841
pe_symbol           NIFTY03APR2523200PE
pe_open                            86.9
pe_high                            94.9
pe_low                            85.55
pe_close                          86.15
pe_volume                       3014850
pe_oi                           5689575
pe_coi                                0
pe_iv                             15.06
pe_delta                          -0.47
pe_gamma                         0.0017
pe_theta                         -25.36
pe_vega                             6.2
pe_rho                         -49.7483
future_open                     23339.0
future_high                     23344.7
future_low                     23331.25
future_close                   23344.15
future_volume                     43725
future_oi                      12808425
future_coi                            0
2025-05-25 12:47:38,986 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2025-04-02
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23261.45
atm_strike                      23250.0
strike                          23250.0
dte                                   1
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523250CE
ce_open                            83.1
ce_high                            87.9
ce_low                             83.1
ce_close                           87.0
ce_volume                        546750
ce_oi                           6733800
ce_coi                                0
ce_iv                             12.13
ce_delta                           0.55
ce_gamma                         0.0021
ce_theta                         -26.28
ce_vega                            6.18
ce_rho                           57.017
pe_symbol           NIFTY03APR2523250PE
pe_open                           70.65
pe_high                           70.65
pe_low                             65.7
pe_close                           66.8
pe_volume                        766500
pe_oi                           7853325
pe_coi                                0
pe_iv                             12.41
pe_delta                          -0.45
pe_gamma                          0.002
pe_theta                         -20.43
pe_vega                            6.19
pe_rho                         -47.8132
future_open                    23386.95
future_high                     23394.9
future_low                     23383.35
future_close                    23390.0
future_volume                      2475
future_oi                      12651900
future_coi                            0
2025-05-25 12:47:38,986 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:47:38,986 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:47:38,986 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:47:38,986 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140526232273408
2025-05-25 12:47:38,986 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140526232273408, OptionType.PUT ID: 140526232273520
2025-05-25 12:47:38,987 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:47:38,987 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:47:38,987 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:47:38,988 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-02' AND trade_date <= '2025-04-02'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:47:39,237 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.249s, returned 18845 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:47:39,240 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18845 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:47:39,240 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:47:39,292 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:47:39,296 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 1 - Tick data for specific strike 23200.0 is EMPTY after filtering windowed data.
2025-05-25 12:47:39,296 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:39,296 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:39,296 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:39,297 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:47:39,297 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:47:39,336 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.039s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:47:39,359 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.023s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:47:39,360 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
trade_date                   2025-04-02
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23204.8
atm_strike                      23200.0
strike                          23200.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                          107.95
ce_high                           113.5
ce_low                           105.15
ce_close                          110.5
ce_volume                       2666850
ce_oi                           5862000
ce_coi                                0
ce_iv                             16.53
ce_delta                           0.53
ce_gamma                         0.0015
ce_theta                         -34.48
ce_vega                             6.2
ce_rho                          54.4841
pe_symbol           NIFTY03APR2523200PE
pe_open                            86.9
pe_high                            94.9
pe_low                            85.55
pe_close                          86.15
pe_volume                       3014850
pe_oi                           5689575
pe_coi                                0
pe_iv                             15.06
pe_delta                          -0.47
pe_gamma                         0.0017
pe_theta                         -25.36
pe_vega                             6.2
pe_rho                         -49.7483
future_open                     23339.0
future_high                     23344.7
future_low                     23331.25
future_close                   23344.15
future_volume                     43725
future_oi                      12808425
future_coi                            0
2025-05-25 12:47:39,361 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2025-04-02
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23261.45
atm_strike                      23250.0
strike                          23250.0
dte                                   1
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523250CE
ce_open                            83.1
ce_high                            87.9
ce_low                             83.1
ce_close                           87.0
ce_volume                        546750
ce_oi                           6733800
ce_coi                                0
ce_iv                             12.13
ce_delta                           0.55
ce_gamma                         0.0021
ce_theta                         -26.28
ce_vega                            6.18
ce_rho                           57.017
pe_symbol           NIFTY03APR2523250PE
pe_open                           70.65
pe_high                           70.65
pe_low                             65.7
pe_close                           66.8
pe_volume                        766500
pe_oi                           7853325
pe_coi                                0
pe_iv                             12.41
pe_delta                          -0.45
pe_gamma                          0.002
pe_theta                         -20.43
pe_vega                            6.19
pe_rho                         -47.8132
future_open                    23386.95
future_high                     23394.9
future_low                     23383.35
future_close                    23390.0
future_volume                      2475
future_oi                      12651900
future_coi                            0
2025-05-25 12:47:39,361 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:47:39,361 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:47:39,361 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:47:39,361 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140526232273520
2025-05-25 12:47:39,362 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140526232273408, OptionType.PUT ID: 140526232273520
2025-05-25 12:47:39,362 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:47:39,362 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:47:39,362 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:47:39,363 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-02' AND trade_date <= '2025-04-02'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:47:39,640 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.277s, returned 18845 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:47:39,642 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18845 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 12:47:39,643 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 12:47:39,722 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:47:39,727 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 2 - Tick data for specific strike 23200.0 is EMPTY after filtering windowed data.
2025-05-25 12:47:39,727 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:39,727 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:39,727 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:39,727 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:47:39,728 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:47:39,754 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.026s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:47:39,778 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.024s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:47:39,780 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
trade_date                   2025-04-02
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23204.8
atm_strike                      23200.0
strike                          23300.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY03APR2523300CE
ce_open                           60.05
ce_high                           63.35
ce_low                             58.4
ce_close                           61.2
ce_volume                       1729575
ce_oi                           7721850
ce_coi                                0
ce_iv                              15.7
ce_delta                           0.35
ce_gamma                         0.0015
ce_theta                         -30.31
ce_vega                            5.86
ce_rho                           38.077
pe_symbol           NIFTY03APR2523300PE
pe_open                          139.19
pe_high                           146.4
pe_low                            135.0
pe_close                         136.65
pe_volume                        594075
pe_oi                           3743250
pe_coi                                0
pe_iv                             14.12
pe_delta                          -0.65
pe_gamma                         0.0017
pe_theta                         -20.66
pe_vega                            5.78
pe_rho                         -68.3662
future_open                     23339.0
future_high                     23344.7
future_low                     23331.25
future_close                   23344.15
future_volume                     43725
future_oi                      12808425
future_coi                            0
2025-05-25 12:47:39,781 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2025-04-02
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23261.45
atm_strike                      23250.0
strike                          23350.0
dte                                   1
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY03APR2523350CE
ce_open                           39.45
ce_high                            41.9
ce_low                            39.45
ce_close                           41.4
ce_volume                        402525
ce_oi                           7047450
ce_coi                                0
ce_iv                             11.85
ce_delta                           0.34
ce_gamma                          0.002
ce_theta                         -22.72
ce_vega                            5.71
ce_rho                            35.28
pe_symbol           NIFTY03APR2523350PE
pe_open                           127.0
pe_high                           127.0
pe_low                            119.5
pe_close                         121.55
pe_volume                        254850
pe_oi                           2255850
pe_coi                                0
pe_iv                             12.22
pe_delta                          -0.66
pe_gamma                         0.0019
pe_theta                         -17.11
pe_vega                            5.74
pe_rho                         -69.4036
future_open                    23386.95
future_high                     23394.9
future_low                     23383.35
future_close                    23390.0
future_volume                      2475
future_oi                      12651900
future_coi                            0
2025-05-25 12:47:39,781 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:47:39,781 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:47:39,781 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:47:39,782 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140526232273408
2025-05-25 12:47:39,782 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140526232273408, OptionType.PUT ID: 140526232273520
2025-05-25 12:47:39,782 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:47:39,782 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:47:39,782 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:47:39,784 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-02' AND trade_date <= '2025-04-02'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:47:40,046 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.263s, returned 18845 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:47:40,049 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18845 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:47:40,049 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:47:40,129 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:47:40,134 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 3 - Tick data for specific strike 23300.0 is EMPTY after filtering windowed data.
2025-05-25 12:47:40,134 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:40,134 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:40,134 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:40,135 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:47:40,135 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:47:40,178 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.043s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:47:40,220 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.042s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:47:40,221 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
trade_date                   2025-04-02
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23204.8
atm_strike                      23200.0
strike                          23100.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY03APR2523100CE
ce_open                           174.7
ce_high                           179.7
ce_low                           169.05
ce_close                         176.25
ce_volume                        387150
ce_oi                           1343325
ce_coi                                0
ce_iv                              17.6
ce_delta                           0.68
ce_gamma                         0.0013
ce_theta                         -34.53
ce_vega                            5.67
ce_rho                          68.7777
pe_symbol           NIFTY03APR2523100PE
pe_open                            52.5
pe_high                            58.9
pe_low                            52.05
pe_close                           53.0
pe_volume                       1594950
pe_oi                           3751950
pe_coi                                0
pe_iv                             16.17
pe_delta                          -0.32
pe_gamma                         0.0014
pe_theta                         -25.38
pe_vega                            5.58
pe_rho                          -33.841
future_open                     23339.0
future_high                     23344.7
future_low                     23331.25
future_close                   23344.15
future_volume                     43725
future_oi                      12808425
future_coi                            0
2025-05-25 12:47:40,222 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2025-04-02
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23261.45
atm_strike                      23250.0
strike                          23150.0
dte                                   1
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY03APR2523150CE
ce_open                           152.1
ce_high                           158.3
ce_low                            152.1
ce_close                         156.69
ce_volume                         88050
ce_oi                           1554375
ce_coi                             -150
ce_iv                             13.15
ce_delta                           0.72
ce_gamma                         0.0016
ce_theta                         -25.39
ce_vega                             5.2
ce_rho                          75.3507
pe_symbol           NIFTY03APR2523150PE
pe_open                            38.2
pe_high                            38.2
pe_low                            35.65
pe_close                           36.4
pe_volume                        348300
pe_oi                           7258425
pe_coi                            63000
pe_iv                             13.45
pe_delta                          -0.28
pe_gamma                         0.0016
pe_theta                         -19.67
pe_vega                            5.25
pe_rho                         -29.3928
future_open                    23386.95
future_high                     23394.9
future_low                     23383.35
future_close                    23390.0
future_volume                      2475
future_oi                      12651900
future_coi                            0
2025-05-25 12:47:40,222 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:47:40,223 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:47:40,223 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:47:40,223 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140526232273520
2025-05-25 12:47:40,223 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140526232273408, OptionType.PUT ID: 140526232273520
2025-05-25 12:47:40,223 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:47:40,224 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:47:40,224 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:47:40,226 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-02' AND trade_date <= '2025-04-02'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:47:40,545 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.319s, returned 18845 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:47:40,547 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18845 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 12:47:40,548 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 12:47:40,627 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:47:40,632 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 4 - Tick data for specific strike 23100.0 is EMPTY after filtering windowed data.
2025-05-25 12:47:40,632 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:40,632 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:40,632 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:40,633 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 12:47:40,633 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 2), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23204.8, 'atm_strike': 23200.0, 'strike': 23100.0, 'dte': 1, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY03APR2523100CE', 'ce_open': 174.7, 'ce_high': 179.7, 'ce_low': 169.05, 'ce_close': 176.25, 'ce_volume': 387150, 'ce_oi': 1343325, 'ce_coi': 0, 'ce_iv': 17.6, 'ce_delta': 0.68, 'ce_gamma': 0.0013, 'ce_theta': -34.53, 'ce_vega': 5.67, 'ce_rho': 68.7777, 'pe_symbol': 'NIFTY03APR2523100PE', 'pe_open': 52.5, 'pe_high': 58.9, 'pe_low': 52.05, 'pe_close': 53.0, 'pe_volume': 1594950, 'pe_oi': 3751950, 'pe_coi': 0, 'pe_iv': 16.17, 'pe_delta': -0.32, 'pe_gamma': 0.0014, 'pe_theta': -25.38, 'pe_vega': 5.58, 'pe_rho': -33.841, 'future_open': 23339.0, 'future_high': 23344.7, 'future_low': 23331.25, 'future_close': 23344.15, 'future_volume': 43725, 'future_oi': 12808425, 'future_coi': 0}
2025-05-25 12:47:40,633 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 2), 'trade_time': datetime.time(12, 0), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23261.45, 'atm_strike': 23250.0, 'strike': 23150.0, 'dte': 1, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY03APR2523150CE', 'ce_open': 152.1, 'ce_high': 158.3, 'ce_low': 152.1, 'ce_close': 156.69, 'ce_volume': 88050, 'ce_oi': 1554375, 'ce_coi': -150, 'ce_iv': 13.15, 'ce_delta': 0.72, 'ce_gamma': 0.0016, 'ce_theta': -25.39, 'ce_vega': 5.2, 'ce_rho': 75.3507, 'pe_symbol': 'NIFTY03APR2523150PE', 'pe_open': 38.2, 'pe_high': 38.2, 'pe_low': 35.65, 'pe_close': 36.4, 'pe_volume': 348300, 'pe_oi': 7258425, 'pe_coi': 63000, 'pe_iv': 13.45, 'pe_delta': -0.28, 'pe_gamma': 0.0016, 'pe_theta': -19.67, 'pe_vega': 5.25, 'pe_rho': -29.3928, 'future_open': 23386.95, 'future_high': 23394.9, 'future_low': 23383.35, 'future_close': 23390.0, 'future_volume': 2475, 'future_oi': 12651900, 'future_coi': 0}
2025-05-25 12:47:40,633 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-25 12:47:40,633 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 12:47:40,633 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 12:47:40,634 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 12:47:40,634 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -830.0000000000001, PnL (Slippage): -830.0000000000001, Net PnL: -830.0000000000001
2025-05-25 12:47:40,634 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 12:47:40,634 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2025-04-02', 'entry_time': '09:16:00', 'entry_day': 'Wednesday', 'exit_date': '2025-04-02', 'exit_time': '12:00:00', 'exit_day': 'Wednesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23100, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 53.0, 'exit_price': 36.4, 'points': -16.6, 'pointsAfterSlippage': -16.6, 'pnl': -830.0000000000001, 'pnlAfterSlippage': -830.0000000000001, 'expenses': 0.0, 'netPnlAfterExpenses': -830.0000000000001, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23204.8, 'index_exit_price': 23261.45, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-02 09:16:00', 'exit_datetime': '2025-04-02 12:00:00'}
2025-05-25 12:47:40,634 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:47:40,634 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:47:40,675 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.040s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:47:40,715 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.040s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:47:40,717 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
trade_date                   2025-04-03
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23194.15
atm_strike                      23200.0
strike                          23200.0
dte                                   0
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                           48.25
ce_high                            52.7
ce_low                             43.1
ce_close                           52.0
ce_volume                       7644525
ce_oi                           5338050
ce_coi                                0
ce_iv                             13.59
ce_delta                            0.5
ce_gamma                          0.003
ce_theta                         -44.09
ce_vega                            3.89
ce_rho                          20.2449
pe_symbol           NIFTY03APR2523200PE
pe_open                           75.55
pe_high                           75.55
pe_low                            59.25
pe_close                           63.0
pe_volume                       7756050
pe_oi                          11666925
pe_coi                                0
pe_iv                             15.97
pe_delta                           -0.5
pe_gamma                         0.0026
pe_theta                         -44.91
pe_vega                            3.89
pe_rho                          -20.746
future_open                    23285.85
future_high                     23297.1
future_low                      23281.5
future_close                    23292.0
future_volume                    153000
future_oi                      12612825
future_coi                            0
2025-05-25 12:47:40,718 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2025-04-03
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23287.7
atm_strike                      23300.0
strike                          23300.0
dte                                   0
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523300CE
ce_open                            9.69
ce_high                            10.7
ce_low                             9.25
ce_close                           9.25
ce_volume                       5130975
ce_oi                          44777775
ce_coi                                0
ce_iv                              3.31
ce_delta                           0.46
ce_gamma                         0.0119
ce_theta                         -12.26
ce_vega                            3.78
ce_rho                          16.4808
pe_symbol           NIFTY03APR2523300PE
pe_open                            33.2
pe_high                            34.1
pe_low                            31.65
pe_close                          34.04
pe_volume                       2499675
pe_oi                          24266925
pe_coi                                0
pe_iv                              7.62
pe_delta                          -0.54
pe_gamma                         0.0053
pe_theta                         -19.45
pe_vega                            3.88
pe_rho                         -22.4198
future_open                    23387.05
future_high                    23389.95
future_low                     23387.05
future_close                   23387.05
future_volume                      9600
future_oi                      12922125
future_coi                            0
2025-05-25 12:47:40,718 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:47:40,718 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:47:40,719 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:47:40,719 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140526232273408
2025-05-25 12:47:40,719 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140526232273408, OptionType.PUT ID: 140526232273520
2025-05-25 12:47:40,719 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:47:40,719 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:47:40,719 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:47:40,721 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-03' AND trade_date <= '2025-04-03'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:47:41,055 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.334s, returned 18336 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:47:41,057 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18336 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:47:41,058 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:47:41,134 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:47:41,139 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 1 - Tick data for specific strike 23200.0 is EMPTY after filtering windowed data.
2025-05-25 12:47:41,139 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:41,139 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:41,140 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:41,140 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:47:41,140 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:47:41,169 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.028s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:47:41,198 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.029s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:47:41,199 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
trade_date                   2025-04-03
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23194.15
atm_strike                      23200.0
strike                          23200.0
dte                                   0
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                           48.25
ce_high                            52.7
ce_low                             43.1
ce_close                           52.0
ce_volume                       7644525
ce_oi                           5338050
ce_coi                                0
ce_iv                             13.59
ce_delta                            0.5
ce_gamma                          0.003
ce_theta                         -44.09
ce_vega                            3.89
ce_rho                          20.2449
pe_symbol           NIFTY03APR2523200PE
pe_open                           75.55
pe_high                           75.55
pe_low                            59.25
pe_close                           63.0
pe_volume                       7756050
pe_oi                          11666925
pe_coi                                0
pe_iv                             15.97
pe_delta                           -0.5
pe_gamma                         0.0026
pe_theta                         -44.91
pe_vega                            3.89
pe_rho                          -20.746
future_open                    23285.85
future_high                     23297.1
future_low                      23281.5
future_close                    23292.0
future_volume                    153000
future_oi                      12612825
future_coi                            0
2025-05-25 12:47:41,200 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2025-04-03
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23287.7
atm_strike                      23300.0
strike                          23300.0
dte                                   0
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523300CE
ce_open                            9.69
ce_high                            10.7
ce_low                             9.25
ce_close                           9.25
ce_volume                       5130975
ce_oi                          44777775
ce_coi                                0
ce_iv                              3.31
ce_delta                           0.46
ce_gamma                         0.0119
ce_theta                         -12.26
ce_vega                            3.78
ce_rho                          16.4808
pe_symbol           NIFTY03APR2523300PE
pe_open                            33.2
pe_high                            34.1
pe_low                            31.65
pe_close                          34.04
pe_volume                       2499675
pe_oi                          24266925
pe_coi                                0
pe_iv                              7.62
pe_delta                          -0.54
pe_gamma                         0.0053
pe_theta                         -19.45
pe_vega                            3.88
pe_rho                         -22.4198
future_open                    23387.05
future_high                    23389.95
future_low                     23387.05
future_close                   23387.05
future_volume                      9600
future_oi                      12922125
future_coi                            0
2025-05-25 12:47:41,200 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:47:41,200 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:47:41,201 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:47:41,201 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140526232273520
2025-05-25 12:47:41,201 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140526232273408, OptionType.PUT ID: 140526232273520
2025-05-25 12:47:41,201 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:47:41,201 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:47:41,202 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:47:41,203 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-03' AND trade_date <= '2025-04-03'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:47:41,514 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.310s, returned 18336 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:47:41,516 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18336 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 12:47:41,517 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 12:47:41,594 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:47:41,599 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 2 - Tick data for specific strike 23200.0 is EMPTY after filtering windowed data.
2025-05-25 12:47:41,599 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:41,600 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:41,600 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:41,600 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:47:41,600 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:47:41,641 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.041s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:47:41,683 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.042s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:47:41,685 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
trade_date                   2025-04-03
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23194.15
atm_strike                      23200.0
strike                          23300.0
dte                                   0
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY03APR2523300CE
ce_open                            14.7
ce_high                           14.85
ce_low                             10.4
ce_close                          14.85
ce_volume                       7280925
ce_oi                          12405825
ce_coi                                0
ce_iv                             12.86
ce_delta                           0.25
ce_gamma                         0.0023
ce_theta                         -29.31
ce_vega                            2.81
ce_rho                           8.5804
pe_symbol           NIFTY03APR2523300PE
pe_open                          141.69
pe_high                          141.69
pe_low                            122.2
pe_close                          125.0
pe_volume                       2597625
pe_oi                           8601675
pe_coi                                0
pe_iv                             15.68
pe_delta                          -0.75
pe_gamma                         0.0021
pe_theta                         -33.23
pe_vega                            3.13
pe_rho                          -30.797
future_open                    23285.85
future_high                     23297.1
future_low                      23281.5
future_close                    23292.0
future_volume                    153000
future_oi                      12612825
future_coi                            0
2025-05-25 12:47:41,686 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2025-04-03
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23287.7
atm_strike                      23300.0
strike                          23400.0
dte                                   0
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY03APR2523400CE
ce_open                            2.45
ce_high                            2.75
ce_low                             2.45
ce_close                           2.45
ce_volume                        896100
ce_oi                          13683450
ce_coi                                0
ce_iv                               7.6
ce_delta                           0.23
ce_gamma                         0.0019
ce_theta                          -8.53
ce_vega                            1.37
ce_rho                           3.0344
pe_symbol           NIFTY03APR2523400PE
pe_open                          126.15
pe_high                          127.95
pe_low                           123.95
pe_close                         127.95
pe_volume                        206850
pe_oi                           3432525
pe_coi                                0
pe_iv                             14.99
pe_delta                          -0.77
pe_gamma                         0.0021
pe_theta                         -29.76
pe_vega                            2.99
pe_rho                         -31.8492
future_open                    23387.05
future_high                    23389.95
future_low                     23387.05
future_close                   23387.05
future_volume                      9600
future_oi                      12922125
future_coi                            0
2025-05-25 12:47:41,686 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:47:41,686 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:47:41,686 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:47:41,687 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140526232273408
2025-05-25 12:47:41,687 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140526232273408, OptionType.PUT ID: 140526232273520
2025-05-25 12:47:41,687 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:47:41,687 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:47:41,687 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:47:41,689 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-03' AND trade_date <= '2025-04-03'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:47:42,004 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.315s, returned 18336 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:47:42,006 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18336 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:47:42,007 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:47:42,083 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:47:42,088 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 3 - Tick data for specific strike 23300.0 is EMPTY after filtering windowed data.
2025-05-25 12:47:42,089 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:42,089 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:42,089 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:42,089 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:47:42,089 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:47:42,131 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.041s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:47:42,161 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.030s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:47:42,162 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
trade_date                   2025-04-03
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23194.15
atm_strike                      23200.0
strike                          23100.0
dte                                   0
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY03APR2523100CE
ce_open                           115.2
ce_high                           124.4
ce_low                            110.0
ce_close                          121.8
ce_volume                       1040850
ce_oi                           1116150
ce_coi                                0
ce_iv                             15.56
ce_delta                           0.71
ce_gamma                         0.0021
ce_theta                         -42.59
ce_vega                            3.15
ce_rho                          30.2501
pe_symbol           NIFTY03APR2523100PE
pe_open                            39.7
pe_high                            39.7
pe_low                             31.1
pe_close                          33.79
pe_volume                       4969050
pe_oi                           7260300
pe_coi                                0
pe_iv                             18.69
pe_delta                          -0.29
pe_gamma                         0.0019
pe_theta                         -46.73
pe_vega                            3.36
pe_rho                         -12.0865
future_open                    23285.85
future_high                     23297.1
future_low                      23281.5
future_close                    23292.0
future_volume                    153000
future_oi                      12612825
future_coi                            0
2025-05-25 12:47:42,163 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2025-04-03
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23287.7
atm_strike                      23300.0
strike                          23200.0
dte                                   0
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY03APR2523200CE
ce_open                            80.7
ce_high                            82.9
ce_low                            79.45
ce_close                          79.45
ce_volume                        874575
ce_oi                           9757050
ce_coi                                0
ce_iv                              0.01
ce_delta                           0.89
ce_gamma                            0.0
ce_theta                          -6.36
ce_vega                             0.0
ce_rho                          41.0149
pe_symbol           NIFTY03APR2523200PE
pe_open                            4.25
pe_high                            4.25
pe_low                             3.85
pe_close                           4.25
pe_volume                       2352450
pe_oi                          39314925
pe_coi                                0
pe_iv                              7.81
pe_delta                          -0.11
pe_gamma                         0.0025
pe_theta                          -10.7
pe_vega                            1.89
pe_rho                          -4.7056
future_open                    23387.05
future_high                    23389.95
future_low                     23387.05
future_close                   23387.05
future_volume                      9600
future_oi                      12922125
future_coi                            0
2025-05-25 12:47:42,163 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:47:42,164 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:47:42,164 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:47:42,164 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140526232273520
2025-05-25 12:47:42,164 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140526232273408, OptionType.PUT ID: 140526232273520
2025-05-25 12:47:42,164 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:47:42,165 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:47:42,165 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:47:42,166 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-03' AND trade_date <= '2025-04-03'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:47:42,474 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.307s, returned 18336 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:47:42,476 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18336 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 12:47:42,477 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 12:47:42,553 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:47:42,558 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 4 - Tick data for specific strike 23100.0 is EMPTY after filtering windowed data.
2025-05-25 12:47:42,558 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:42,558 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:42,559 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:42,559 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 12:47:42,559 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 3), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23194.15, 'atm_strike': 23200.0, 'strike': 23100.0, 'dte': 0, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY03APR2523100CE', 'ce_open': 115.2, 'ce_high': 124.4, 'ce_low': 110.0, 'ce_close': 121.8, 'ce_volume': 1040850, 'ce_oi': 1116150, 'ce_coi': 0, 'ce_iv': 15.56, 'ce_delta': 0.71, 'ce_gamma': 0.0021, 'ce_theta': -42.59, 'ce_vega': 3.15, 'ce_rho': 30.2501, 'pe_symbol': 'NIFTY03APR2523100PE', 'pe_open': 39.7, 'pe_high': 39.7, 'pe_low': 31.1, 'pe_close': 33.79, 'pe_volume': 4969050, 'pe_oi': 7260300, 'pe_coi': 0, 'pe_iv': 18.69, 'pe_delta': -0.29, 'pe_gamma': 0.0019, 'pe_theta': -46.73, 'pe_vega': 3.36, 'pe_rho': -12.0865, 'future_open': 23285.85, 'future_high': 23297.1, 'future_low': 23281.5, 'future_close': 23292.0, 'future_volume': 153000, 'future_oi': 12612825, 'future_coi': 0}
2025-05-25 12:47:42,559 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 3), 'trade_time': datetime.time(12, 0), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23287.7, 'atm_strike': 23300.0, 'strike': 23200.0, 'dte': 0, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY03APR2523200CE', 'ce_open': 80.7, 'ce_high': 82.9, 'ce_low': 79.45, 'ce_close': 79.45, 'ce_volume': 874575, 'ce_oi': 9757050, 'ce_coi': 0, 'ce_iv': 0.01, 'ce_delta': 0.89, 'ce_gamma': 0.0, 'ce_theta': -6.36, 'ce_vega': 0.0, 'ce_rho': 41.0149, 'pe_symbol': 'NIFTY03APR2523200PE', 'pe_open': 4.25, 'pe_high': 4.25, 'pe_low': 3.85, 'pe_close': 4.25, 'pe_volume': 2352450, 'pe_oi': 39314925, 'pe_coi': 0, 'pe_iv': 7.81, 'pe_delta': -0.11, 'pe_gamma': 0.0025, 'pe_theta': -10.7, 'pe_vega': 1.89, 'pe_rho': -4.7056, 'future_open': 23387.05, 'future_high': 23389.95, 'future_low': 23387.05, 'future_close': 23387.05, 'future_volume': 9600, 'future_oi': 12922125, 'future_coi': 0}
2025-05-25 12:47:42,559 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-25 12:47:42,559 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 12:47:42,560 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 12:47:42,560 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 12:47:42,560 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -1477.0, PnL (Slippage): -1477.0, Net PnL: -1477.0
2025-05-25 12:47:42,560 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 12:47:42,560 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2025-04-03', 'entry_time': '09:16:00', 'entry_day': 'Thursday', 'exit_date': '2025-04-03', 'exit_time': '12:00:00', 'exit_day': 'Thursday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23100, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 33.79, 'exit_price': 4.25, 'points': -29.54, 'pointsAfterSlippage': -29.54, 'pnl': -1477.0, 'pnlAfterSlippage': -1477.0, 'expenses': 0.0, 'netPnlAfterExpenses': -1477.0, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23194.15, 'index_exit_price': 23287.7, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-03 09:16:00', 'exit_datetime': '2025-04-03 12:00:00'}
2025-05-25 12:47:42,560 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-04' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:47:42,561 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-04' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:47:42,601 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.039s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-04'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:47:42,641 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.040s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-04'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:47:42,643 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
trade_date                   2025-04-04
trade_time                     09:16:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           23118.85
atm_strike                      23100.0
strike                          23100.0
dte                                   3
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY09APR2523100CE
ce_open                           166.6
ce_high                           169.3
ce_low                            156.1
ce_close                         162.75
ce_volume                       1043625
ce_oi                            722625
ce_coi                                0
ce_iv                             11.68
ce_delta                           0.55
ce_gamma                         0.0012
ce_theta                         -15.24
ce_vega                            11.3
ce_rho                         200.2749
pe_symbol           NIFTY09APR2523100PE
pe_open                           165.3
pe_high                           169.7
pe_low                            157.5
pe_close                          162.5
pe_volume                       2219700
pe_oi                           2414925
pe_coi                                0
pe_iv                             16.46
pe_delta                          -0.45
pe_gamma                         0.0008
pe_theta                          -13.7
pe_vega                           11.38
pe_rho                        -163.3277
future_open                    23181.95
future_high                     23189.9
future_low                      23168.4
future_close                   23179.25
future_volume                    175425
future_oi                      13065450
future_coi                            0
2025-05-25 12:47:42,644 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2025-04-04
trade_time                     12:00:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                            22964.3
atm_strike                      22950.0
strike                          22950.0
dte                                   3
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY09APR2522950CE
ce_open                           133.4
ce_high                          134.05
ce_low                           129.35
ce_close                         131.44
ce_volume                        244650
ce_oi                           2162850
ce_coi                          -124125
ce_iv                               9.2
ce_delta                           0.56
ce_gamma                         0.0015
ce_theta                         -12.71
ce_vega                           11.18
ce_rho                         202.9337
pe_symbol           NIFTY09APR2522950PE
pe_open                           121.7
pe_high                          126.15
pe_low                            121.5
pe_close                          124.1
pe_volume                        408300
pe_oi                           3639150
pe_coi                           -52950
pe_iv                             12.98
pe_delta                          -0.44
pe_gamma                         0.0011
pe_theta                         -10.14
pe_vega                           11.28
pe_rho                        -159.2931
future_open                    23041.35
future_high                    23041.35
future_low                      23030.3
future_close                   23034.95
future_volume                      7650
future_oi                      13635450
future_coi                        16350
2025-05-25 12:47:42,644 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:47:42,644 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:47:42,645 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:47:42,645 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140526232273408
2025-05-25 12:47:42,645 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140526232273408, OptionType.PUT ID: 140526232273520
2025-05-25 12:47:42,645 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:47:42,645 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:47:42,645 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:47:42,647 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-04' AND trade_date <= '2025-04-04'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:47:42,980 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.333s, returned 18524 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:47:42,982 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18524 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:47:42,983 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:47:43,060 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:47:43,065 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 1 - Tick data for specific strike 23100.0 is EMPTY after filtering windowed data.
2025-05-25 12:47:43,066 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:43,066 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:43,066 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:43,066 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-04' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:47:43,066 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-04' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:47:43,096 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.029s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-04'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:47:43,124 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.029s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-04'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:47:43,126 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
trade_date                   2025-04-04
trade_time                     09:16:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           23118.85
atm_strike                      23100.0
strike                          23100.0
dte                                   3
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY09APR2523100CE
ce_open                           166.6
ce_high                           169.3
ce_low                            156.1
ce_close                         162.75
ce_volume                       1043625
ce_oi                            722625
ce_coi                                0
ce_iv                             11.68
ce_delta                           0.55
ce_gamma                         0.0012
ce_theta                         -15.24
ce_vega                            11.3
ce_rho                         200.2749
pe_symbol           NIFTY09APR2523100PE
pe_open                           165.3
pe_high                           169.7
pe_low                            157.5
pe_close                          162.5
pe_volume                       2219700
pe_oi                           2414925
pe_coi                                0
pe_iv                             16.46
pe_delta                          -0.45
pe_gamma                         0.0008
pe_theta                          -13.7
pe_vega                           11.38
pe_rho                        -163.3277
future_open                    23181.95
future_high                     23189.9
future_low                      23168.4
future_close                   23179.25
future_volume                    175425
future_oi                      13065450
future_coi                            0
2025-05-25 12:47:43,126 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2025-04-04
trade_time                     12:00:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                            22964.3
atm_strike                      22950.0
strike                          22950.0
dte                                   3
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY09APR2522950CE
ce_open                           133.4
ce_high                          134.05
ce_low                           129.35
ce_close                         131.44
ce_volume                        244650
ce_oi                           2162850
ce_coi                          -124125
ce_iv                               9.2
ce_delta                           0.56
ce_gamma                         0.0015
ce_theta                         -12.71
ce_vega                           11.18
ce_rho                         202.9337
pe_symbol           NIFTY09APR2522950PE
pe_open                           121.7
pe_high                          126.15
pe_low                            121.5
pe_close                          124.1
pe_volume                        408300
pe_oi                           3639150
pe_coi                           -52950
pe_iv                             12.98
pe_delta                          -0.44
pe_gamma                         0.0011
pe_theta                         -10.14
pe_vega                           11.28
pe_rho                        -159.2931
future_open                    23041.35
future_high                    23041.35
future_low                      23030.3
future_close                   23034.95
future_volume                      7650
future_oi                      13635450
future_coi                        16350
2025-05-25 12:47:43,127 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:47:43,127 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:47:43,127 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:47:43,127 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140526232273520
2025-05-25 12:47:43,127 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140526232273408, OptionType.PUT ID: 140526232273520
2025-05-25 12:47:43,128 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:47:43,128 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:47:43,128 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:47:43,129 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-04' AND trade_date <= '2025-04-04'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:47:43,432 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.302s, returned 18524 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:47:43,434 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18524 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 12:47:43,435 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 12:47:43,512 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:47:43,517 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 2 - Tick data for specific strike 23100.0 is EMPTY after filtering windowed data.
2025-05-25 12:47:43,517 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:43,517 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:43,517 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:43,518 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-04' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:47:43,518 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-04' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:47:43,560 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.042s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-04'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:47:43,590 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.030s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-04'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:47:43,592 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
trade_date                   2025-04-04
trade_time                     09:16:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           23118.85
atm_strike                      23100.0
strike                          23200.0
dte                                   3
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY09APR2523200CE
ce_open                          118.25
ce_high                           120.9
ce_low                           110.35
ce_close                          114.9
ce_volume                       1367325
ce_oi                           2543550
ce_coi                                0
ce_iv                             11.88
ce_delta                           0.47
ce_gamma                         0.0012
ce_theta                          -14.8
ce_vega                           11.38
ce_rho                         159.1016
pe_symbol           NIFTY09APR2523200PE
pe_open                           217.4
pe_high                           223.8
pe_low                           210.75
pe_close                          216.2
pe_volume                       1542600
pe_oi                           4037325
pe_coi                                0
pe_iv                             16.79
pe_delta                          -0.53
pe_gamma                         0.0008
pe_theta                         -13.56
pe_vega                           11.43
pe_rho                        -193.8948
future_open                    23181.95
future_high                     23189.9
future_low                      23168.4
future_close                   23179.25
future_volume                    175425
future_oi                      13065450
future_coi                            0
2025-05-25 12:47:43,593 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2025-04-04
trade_time                     12:00:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                            22964.3
atm_strike                      22950.0
strike                          23050.0
dte                                   3
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY09APR2523050CE
ce_open                            87.3
ce_high                            88.0
ce_low                             84.5
ce_close                           86.1
ce_volume                        195825
ce_oi                           3286875
ce_coi                             1275
ce_iv                              9.59
ce_delta                           0.45
ce_gamma                         0.0014
ce_theta                         -12.21
ce_vega                           11.21
ce_rho                         151.2397
pe_symbol           NIFTY09APR2523050PE
pe_open                           175.5
pe_high                           181.0
pe_low                           175.35
pe_close                         178.45
pe_volume                        110025
pe_oi                           1267200
pe_coi                           -25875
pe_iv                             13.34
pe_delta                          -0.55
pe_gamma                          0.001
pe_theta                          -9.86
pe_vega                            11.3
pe_rho                        -197.6342
future_open                    23041.35
future_high                    23041.35
future_low                      23030.3
future_close                   23034.95
future_volume                      7650
future_oi                      13635450
future_coi                        16350
2025-05-25 12:47:43,593 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:47:43,593 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:47:43,594 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:47:43,594 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140526232273408
2025-05-25 12:47:43,594 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140526232273408, OptionType.PUT ID: 140526232273520
2025-05-25 12:47:43,594 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:47:43,594 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:47:43,594 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:47:43,596 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-04' AND trade_date <= '2025-04-04'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:47:43,898 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.302s, returned 18524 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:47:43,900 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18524 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:47:43,901 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:47:43,978 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:47:43,984 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 3 - Tick data for specific strike 23200.0 is EMPTY after filtering windowed data.
2025-05-25 12:47:43,984 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:43,984 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:43,984 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:43,984 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-04' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:47:43,984 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-04' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:47:44,026 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.041s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-04'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:47:44,067 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.041s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-04'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:47:44,068 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
trade_date                   2025-04-04
trade_time                     09:16:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           23118.85
atm_strike                      23100.0
strike                          23000.0
dte                                   3
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY09APR2523000CE
ce_open                           222.6
ce_high                          227.55
ce_low                            212.0
ce_close                          220.3
ce_volume                        306000
ce_oi                           1428300
ce_coi                                0
ce_iv                             11.26
ce_delta                           0.63
ce_gamma                         0.0011
ce_theta                         -14.45
ce_vega                           10.19
ce_rho                         241.8919
pe_symbol           NIFTY09APR2523000PE
pe_open                           123.8
pe_high                           125.9
pe_low                            116.5
pe_close                         120.85
pe_volume                       2390100
pe_oi                           6541875
pe_coi                                0
pe_iv                             16.45
pe_delta                          -0.37
pe_gamma                         0.0008
pe_theta                         -13.43
pe_vega                           10.83
pe_rho                        -133.2973
future_open                    23181.95
future_high                     23189.9
future_low                      23168.4
future_close                   23179.25
future_volume                    175425
future_oi                      13065450
future_coi                            0
2025-05-25 12:47:44,069 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2025-04-04
trade_time                     12:00:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                            22964.3
atm_strike                      22950.0
strike                          22850.0
dte                                   3
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY09APR2522850CE
ce_open                          193.25
ce_high                           193.9
ce_low                            188.0
ce_close                         190.85
ce_volume                         30825
ce_oi                            343275
ce_coi                            18975
ce_iv                              8.68
ce_delta                           0.66
ce_gamma                         0.0013
ce_theta                          -11.8
ce_vega                            9.45
ce_rho                         255.9896
pe_symbol           NIFTY09APR2522850PE
pe_open                           81.25
pe_high                            84.5
pe_low                            80.59
pe_close                          83.05
pe_volume                        219150
pe_oi                           2070450
pe_coi                           -23775
pe_iv                             12.84
pe_delta                          -0.34
pe_gamma                          0.001
pe_theta                          -9.73
pe_vega                           10.44
pe_rho                         -121.345
future_open                    23041.35
future_high                    23041.35
future_low                      23030.3
future_close                   23034.95
future_volume                      7650
future_oi                      13635450
future_coi                        16350
2025-05-25 12:47:44,069 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:47:44,069 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:47:44,070 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:47:44,070 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140526232273520
2025-05-25 12:47:44,070 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140526232273408, OptionType.PUT ID: 140526232273520
2025-05-25 12:47:44,070 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:47:44,071 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:47:44,071 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:47:44,072 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-04' AND trade_date <= '2025-04-04'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:47:44,406 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.334s, returned 18524 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:47:44,408 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18524 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 12:47:44,409 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 12:47:44,487 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 12:47:44,492 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 4 - Tick data for specific strike 23000.0 is EMPTY after filtering windowed data.
2025-05-25 12:47:44,492 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:44,493 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:44,493 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 4: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:44,493 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 12:47:44,493 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 4), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 9), 'index_name': 'NIFTY', 'spot': 23118.85, 'atm_strike': 23100.0, 'strike': 23000.0, 'dte': 3, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY09APR2523000CE', 'ce_open': 222.6, 'ce_high': 227.55, 'ce_low': 212.0, 'ce_close': 220.3, 'ce_volume': 306000, 'ce_oi': 1428300, 'ce_coi': 0, 'ce_iv': 11.26, 'ce_delta': 0.63, 'ce_gamma': 0.0011, 'ce_theta': -14.45, 'ce_vega': 10.19, 'ce_rho': 241.8919, 'pe_symbol': 'NIFTY09APR2523000PE', 'pe_open': 123.8, 'pe_high': 125.9, 'pe_low': 116.5, 'pe_close': 120.85, 'pe_volume': 2390100, 'pe_oi': 6541875, 'pe_coi': 0, 'pe_iv': 16.45, 'pe_delta': -0.37, 'pe_gamma': 0.0008, 'pe_theta': -13.43, 'pe_vega': 10.83, 'pe_rho': -133.2973, 'future_open': 23181.95, 'future_high': 23189.9, 'future_low': 23168.4, 'future_close': 23179.25, 'future_volume': 175425, 'future_oi': 13065450, 'future_coi': 0}
2025-05-25 12:47:44,493 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 4), 'trade_time': datetime.time(12, 0), 'expiry_date': datetime.date(2025, 4, 9), 'index_name': 'NIFTY', 'spot': 22964.3, 'atm_strike': 22950.0, 'strike': 22850.0, 'dte': 3, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY09APR2522850CE', 'ce_open': 193.25, 'ce_high': 193.9, 'ce_low': 188.0, 'ce_close': 190.85, 'ce_volume': 30825, 'ce_oi': 343275, 'ce_coi': 18975, 'ce_iv': 8.68, 'ce_delta': 0.66, 'ce_gamma': 0.0013, 'ce_theta': -11.8, 'ce_vega': 9.45, 'ce_rho': 255.9896, 'pe_symbol': 'NIFTY09APR2522850PE', 'pe_open': 81.25, 'pe_high': 84.5, 'pe_low': 80.59, 'pe_close': 83.05, 'pe_volume': 219150, 'pe_oi': 2070450, 'pe_coi': -23775, 'pe_iv': 12.84, 'pe_delta': -0.34, 'pe_gamma': 0.001, 'pe_theta': -9.73, 'pe_vega': 10.44, 'pe_rho': -121.345, 'future_open': 23041.35, 'future_high': 23041.35, 'future_low': 23030.3, 'future_close': 23034.95, 'future_volume': 7650, 'future_oi': 13635450, 'future_coi': 16350}
2025-05-25 12:47:44,493 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-25 12:47:44,494 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 12:47:44,494 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 12:47:44,494 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 12:47:44,494 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -1889.9999999999998, PnL (Slippage): -1889.9999999999998, Net PnL: -1889.9999999999998
2025-05-25 12:47:44,494 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 12:47:44,494 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2025-04-04', 'entry_time': '09:16:00', 'entry_day': 'Friday', 'exit_date': '2025-04-04', 'exit_time': '12:00:00', 'exit_day': 'Friday', 'symbol': 'NIFTY', 'expiry': '2025-04-09', 'strike': 23000, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 120.85, 'exit_price': 83.05, 'points': -37.8, 'pointsAfterSlippage': -37.8, 'pnl': -1889.9999999999998, 'pnlAfterSlippage': -1889.9999999999998, 'expenses': 0.0, 'netPnlAfterExpenses': -1889.9999999999998, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23118.85, 'index_exit_price': 22964.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-04 09:16:00', 'exit_datetime': '2025-04-04 12:00:00'}
2025-05-25 12:47:44,495 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-07' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:47:44,495 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-07' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:47:44,531 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.036s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-07'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:47:44,570 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.038s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-07'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:47:44,572 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
trade_date                   2025-04-07
trade_time                     09:16:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           22070.15
atm_strike                      22150.0
strike                          22150.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY09APR2522150CE
ce_open                          360.55
ce_high                          360.55
ce_low                            285.8
ce_close                         299.14
ce_volume                        366975
ce_oi                               525
ce_coi                                0
ce_iv                             43.98
ce_delta                           0.47
ce_gamma                         0.0005
ce_theta                         -65.02
ce_vega                            7.48
ce_rho                          74.0471
pe_symbol           NIFTY09APR2522150PE
pe_open                           279.0
pe_high                          322.75
pe_low                           269.55
pe_close                         314.14
pe_volume                        484050
pe_oi                            707850
pe_coi                                0
pe_iv                             37.46
pe_delta                          -0.53
pe_gamma                         0.0006
pe_theta                         -49.64
pe_vega                            7.47
pe_rho                         -86.9676
future_open                    22170.75
future_high                    22171.55
future_low                     22139.15
future_close                    22150.0
future_volume                    589575
future_oi                      13865025
future_coi                            0
2025-05-25 12:47:44,573 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2025-04-07
trade_time                     12:00:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           21955.35
atm_strike                      21950.0
strike                          21950.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY09APR2521950CE
ce_open                          313.75
ce_high                          314.35
ce_low                           304.45
ce_close                         305.39
ce_volume                         70200
ce_oi                            593775
ce_coi                            49050
ce_iv                             39.55
ce_delta                           0.52
ce_gamma                         0.0005
ce_theta                         -58.72
ce_vega                            7.45
ce_rho                          80.2007
pe_symbol           NIFTY09APR2521950PE
pe_open                           274.3
pe_high                          283.64
pe_low                           274.05
pe_close                         283.45
pe_volume                        110025
pe_oi                           1299300
pe_coi                            21450
pe_iv                             39.45
pe_delta                          -0.48
pe_gamma                         0.0005
pe_theta                         -52.58
pe_vega                            7.45
pe_rho                         -78.6809
future_open                     22044.3
future_high                     22044.3
future_low                      22026.0
future_close                   22026.15
future_volume                     13875
future_oi                      14835750
future_coi                        17625
2025-05-25 12:47:44,573 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:47:44,573 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:47:44,574 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:47:44,574 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140526232273408
2025-05-25 12:47:44,574 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140526232273408, OptionType.PUT ID: 140526232273520
2025-05-25 12:47:44,574 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:47:44,574 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:47:44,574 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:47:44,576 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-07' AND trade_date <= '2025-04-07'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:47:45,061 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.485s, returned 30500 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:47:45,065 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 30500 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:47:45,066 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:47:45,189 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 374 rows.
2025-05-25 12:47:45,195 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 1 - Tick data for specific strike 22150.0 is EMPTY after filtering windowed data.
2025-05-25 12:47:45,195 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:45,195 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:45,196 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 1: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:45,196 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-07' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:47:45,196 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-07' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:47:45,232 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.035s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-07'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 12:47:45,268 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.035s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-07'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 12:47:45,269 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
trade_date                   2025-04-07
trade_time                     09:16:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           22070.15
atm_strike                      22150.0
strike                          22150.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY09APR2522150CE
ce_open                          360.55
ce_high                          360.55
ce_low                            285.8
ce_close                         299.14
ce_volume                        366975
ce_oi                               525
ce_coi                                0
ce_iv                             43.98
ce_delta                           0.47
ce_gamma                         0.0005
ce_theta                         -65.02
ce_vega                            7.48
ce_rho                          74.0471
pe_symbol           NIFTY09APR2522150PE
pe_open                           279.0
pe_high                          322.75
pe_low                           269.55
pe_close                         314.14
pe_volume                        484050
pe_oi                            707850
pe_coi                                0
pe_iv                             37.46
pe_delta                          -0.53
pe_gamma                         0.0006
pe_theta                         -49.64
pe_vega                            7.47
pe_rho                         -86.9676
future_open                    22170.75
future_high                    22171.55
future_low                     22139.15
future_close                    22150.0
future_volume                    589575
future_oi                      13865025
future_coi                            0
2025-05-25 12:47:45,270 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2025-04-07
trade_time                     12:00:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           21955.35
atm_strike                      21950.0
strike                          21950.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY09APR2521950CE
ce_open                          313.75
ce_high                          314.35
ce_low                           304.45
ce_close                         305.39
ce_volume                         70200
ce_oi                            593775
ce_coi                            49050
ce_iv                             39.55
ce_delta                           0.52
ce_gamma                         0.0005
ce_theta                         -58.72
ce_vega                            7.45
ce_rho                          80.2007
pe_symbol           NIFTY09APR2521950PE
pe_open                           274.3
pe_high                          283.64
pe_low                           274.05
pe_close                         283.45
pe_volume                        110025
pe_oi                           1299300
pe_coi                            21450
pe_iv                             39.45
pe_delta                          -0.48
pe_gamma                         0.0005
pe_theta                         -52.58
pe_vega                            7.45
pe_rho                         -78.6809
future_open                     22044.3
future_high                     22044.3
future_low                      22026.0
future_close                   22026.15
future_volume                     13875
future_oi                      14835750
future_coi                        17625
2025-05-25 12:47:45,270 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:47:45,270 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:47:45,271 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:47:45,271 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140526232273520
2025-05-25 12:47:45,271 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140526232273408, OptionType.PUT ID: 140526232273520
2025-05-25 12:47:45,271 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:47:45,271 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:47:45,272 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:47:45,273 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-07' AND trade_date <= '2025-04-07'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:47:45,713 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.440s, returned 30500 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:47:45,716 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 30500 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 12:47:45,717 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 12:47:45,798 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 374 rows.
2025-05-25 12:47:45,803 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 2 - Tick data for specific strike 22150.0 is EMPTY after filtering windowed data.
2025-05-25 12:47:45,804 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:45,804 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:45,804 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 2: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:45,804 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-07' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:47:45,804 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-07' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:47:45,843 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.038s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-07'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:47:45,871 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.028s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-07'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:47:45,872 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
trade_date                   2025-04-07
trade_time                     09:16:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           22070.15
atm_strike                      22150.0
strike                          22250.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY09APR2522250CE
ce_open                          283.95
ce_high                          283.95
ce_low                           221.65
ce_close                         236.35
ce_volume                        570000
ce_oi                             19725
ce_coi                                0
ce_iv                              41.4
ce_delta                           0.41
ce_gamma                         0.0005
ce_theta                          -60.1
ce_vega                            7.36
ce_rho                          66.0448
pe_symbol           NIFTY09APR2522250PE
pe_open                          302.14
pe_high                          363.65
pe_low                           302.14
pe_close                         354.15
pe_volume                        496575
pe_oi                            683250
pe_coi                                0
pe_iv                             35.13
pe_delta                          -0.59
pe_gamma                         0.0006
pe_theta                         -44.79
pe_vega                            7.29
pe_rho                         -97.1826
future_open                    22170.75
future_high                    22171.55
future_low                     22139.15
future_close                    22150.0
future_volume                    589575
future_oi                      13865025
future_coi                            0
2025-05-25 12:47:45,872 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2025-04-07
trade_time                     12:00:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           21955.35
atm_strike                      21950.0
strike                          22050.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY09APR2522050CE
ce_open                           257.1
ce_high                           257.1
ce_low                            247.5
ce_close                          248.8
ce_volume                         61200
ce_oi                           1251525
ce_coi                            14700
ce_iv                             38.35
ce_delta                           0.46
ce_gamma                         0.0006
ce_theta                         -56.55
ce_vega                            7.42
ce_rho                          71.8098
pe_symbol           NIFTY09APR2522050PE
pe_open                           317.5
pe_high                           327.7
pe_low                           315.64
pe_close                         326.75
pe_volume                         49575
pe_oi                           1311225
pe_coi                           -26850
pe_iv                             38.26
pe_delta                          -0.54
pe_gamma                         0.0006
pe_theta                         -50.37
pe_vega                            7.42
pe_rho                         -87.8177
future_open                     22044.3
future_high                     22044.3
future_low                      22026.0
future_close                   22026.15
future_volume                     13875
future_oi                      14835750
future_coi                        17625
2025-05-25 12:47:45,873 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:47:45,873 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:47:45,873 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:47:45,873 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140526232273408
2025-05-25 12:47:45,873 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140526232273408, OptionType.PUT ID: 140526232273520
2025-05-25 12:47:45,873 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:47:45,874 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 12:47:45,874 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 12:47:45,875 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-07' AND trade_date <= '2025-04-07'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 12:47:46,302 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.427s, returned 30500 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 12:47:46,305 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 30500 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 12:47:46,305 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 12:47:46,384 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 374 rows.
2025-05-25 12:47:46,388 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 3 - Tick data for specific strike 22250.0 is EMPTY after filtering windowed data.
2025-05-25 12:47:46,389 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.STOPLOSS: 'STOPLOSS'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=50.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:46,389 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TAKEPROFIT: 'TAKEPROFIT'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=100.0 params={}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:46,389 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Failed to validate risk rule model for leg 3: rule_type=<RiskRuleType.TRAIL: 'TRAIL'> number_type=<NumberType.PERCENTAGE: 'PERCENTAGE'> value=0.0 params={'trail_by': 0.0}, error: 'RiskRule' object does not support item assignment
2025-05-25 12:47:46,389 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-07' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 12:47:46,389 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-07' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 12:47:46,417 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.027s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-07'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 12:47:46,440 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.023s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-07'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 12:47:46,441 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
trade_date                   2025-04-07
trade_time                     09:16:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           22070.15
atm_strike                      22150.0
strike                          22050.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY09APR2522050CE
ce_open                           421.5
ce_high                          422.25
ce_low                            354.8
ce_close                         365.35
ce_volume                        308025
ce_oi                              1575
ce_coi                                0
ce_iv                             46.34
ce_delta                           0.53
ce_gamma                         0.0005
ce_theta                         -68.62
ce_vega                            7.48
ce_rho                          81.1941
pe_symbol           NIFTY09APR2522050PE
pe_open                          263.55
pe_high                          293.64
pe_low                            247.8
pe_close                         284.75
pe_volume                        471225
pe_oi                            984600
pe_coi                                0
pe_iv                              40.4
pe_delta                          -0.47
pe_gamma                         0.0005
pe_theta                         -54.18
pe_vega                            7.48
pe_rho                         -77.8679
future_open                    22170.75
future_high                    22171.55
future_low                     22139.15
future_close                    22150.0
future_volume                    589575
future_oi                      13865025
future_coi                            0
2025-05-25 12:47:46,441 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2025-04-07
trade_time                     12:00:00
expiry_date                  2025-04-09
index_name                        NIFTY
spot                           21955.35
atm_strike                      21950.0
strike                          21850.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY09APR2521850CE
ce_open                           377.3
ce_high                           377.4
ce_low                            365.8
ce_close                         366.75
ce_volume                          9375
ce_oi                            287475
ce_coi                             3225
ce_iv                             40.67
ce_delta                           0.57
ce_gamma                         0.0005
ce_theta                         -59.77
ce_vega                            7.34
ce_rho                          88.0492
pe_symbol           NIFTY09APR2521850PE
pe_open                          238.15
pe_high                           245.5
pe_low                           236.95
pe_close                          244.5
pe_volume                         46725
pe_oi                            807525
pe_coi                           -13650
pe_iv                             40.52
pe_delta                          -0.43
pe_gamma                         0.0005
pe_theta                         -53.58
pe_vega                            7.34
pe_rho                         -70.0752
future_open                     22044.3
future_high                     22044.3
future_low                      22026.0
future_close                   22026.15
future_volume                     13875
future_oi                      14835750
future_coi                        17625
2025-05-25 12:47:46,442 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 12:47:46,442 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 12:47:46,442 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 12:47:46,442 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140526232273520
2025-05-25 12:47:46,442 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140526232273408, OptionType.PUT ID: 140526232273520
2025-05-25 12:47:46,442 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 12:47:46,442 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 12:47:46,442 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 12:47:46,444 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-07' AND trade_date <= '2025-04-07'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
