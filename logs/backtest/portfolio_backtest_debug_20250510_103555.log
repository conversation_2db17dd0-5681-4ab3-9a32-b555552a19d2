2025-05-10 10:35:55,319 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-10 10:35:55,320 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-10 10:35:55,320 - __main__ - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-10 10:35:55,320 - __main__ - DEBUG - HeavyDB host: **************, DB: heavydb
2025-05-10 10:35:55,320 - __main__ - DEBUG - GPU enabled: True
2025-05-10 10:35:55,320 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting BTRunPortfolio_GPU.py - Debugging enabled
2025-05-10 10:35:55,321 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Current working directory: /srv/samba/shared
2025-05-10 10:35:55,321 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-10 10:35:55,321 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Importing BTRUN modules...
2025-05-10 10:35:55,321 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Config module imported from: /srv/samba/shared/bt/backtester_stable/BTRUN/config.py
2025-05-10 10:35:55,321 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - HeavyDB settings - Host: **************, Database: heavydb
2025-05-10 10:35:55,321 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - GPU enabled: True
2025-05-10 10:35:55,321 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Successfully imported heavydb_helpers module
2025-05-10 10:35:55,321 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Entering main function
2025-05-10 10:35:55,321 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Parsing command line arguments
2025-05-10 10:35:55,322 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Parsed arguments: Namespace(config=None, legacy_excel=True, strategies=None, start_date=None, end_date=None, output_dir='Trades', portfolio_name=None, slippage=0.1, capital=None, margin_multiplier=1.0, no_json=False, no_excel=False, no_charts=False, cpu_only=False, merge_output=False, debug=False)
2025-05-10 10:35:55,323 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Text-to-speech notifications not available
2025-05-10 10:35:55,323 - __main__ - INFO - GPU acceleration enabled: True
2025-05-10 10:35:55,392 - __main__ - INFO - GPU Memory: 35421MB free / 40384MB total
2025-05-10 10:35:55,392 - __main__ - INFO - HeavyDB integration available (Host: **************)
2025-05-10 10:35:55,608 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-10 10:35:55,608 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-10 10:35:55,608 - __main__ - INFO - Using new Excel input format (PortfolioSetting & StrategySetting)
2025-05-10 10:35:55,608 - bt.backtester_stable.BTRUN.config - INFO - Found portfolio file at /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-10 10:35:55,608 - __main__ - INFO - Reading PortfolioSetting & StrategySetting from /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-10 10:35:55,707 - __main__ - DEBUG - Loaded 1 PortfolioSetting rows, 21 StrategySetting rows
2025-05-10 10:35:55,786 - __main__ - DEBUG - Loaded 1 rows from /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx
2025-05-10 10:35:55,786 - __main__ - INFO - Processing portfolio: NIF0DTE
2025-05-10 10:35:55,786 - __main__ - DEBUG - Building portfolio request for portfolio: NIF0DTE
2025-05-10 10:35:55,788 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting single backtest for NIF0DTE
2025-05-10 10:35:55,788 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Using HeavyDB for data access
2025-05-10 10:35:55,788 - bt.backtester_stable.BTRUN.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'NIF0DTE'
2025-05-10 10:35:56,004 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-10 10:36:00,940 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 4.936s, returned 5998 rows
2025-05-10 10:36:01,398 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Generated 11996 synthetic trade rows from option chain
2025-05-10 10:36:01,399 - bt.backtester_stable.BTRUN.builders - INFO - Parsing backtest response...
2025-05-10 10:36:02,293 - bt.backtester_stable.BTRUN.runtime - ERROR - Error processing backtest results: no implementation found for 'numpy.where' on types that implement __array_function__: [<class 'cudf.core.series.Series'>]
2025-05-10 10:36:02,293 - bt.backtester_stable.BTRUN.runtime - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/runtime.py", line 222, in run_full_backtest
    order_df, metrics_df, trans_dfs, day_stats, month_stats, daily_max_pl_df = process_backtest_results(
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/runtime.py", line 58, in process_backtest_results
    order_df, margin_req, daily_max_pl_df = builders.parse_backtest_response(
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/builders.py", line 73, in parse_backtest_response
    order_df['entry_price_slippage'] = order_df['entry_price'] * np.where(order_df['side'] == "SELL", (1 - slippage_percent / 100), (1 + slippage_percent / 100))
  File "<__array_function__ internals>", line 200, in where
TypeError: no implementation found for 'numpy.where' on types that implement __array_function__: [<class 'cudf.core.series.Series'>]

2025-05-10 10:36:02,294 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Backtest completed in 6.51 seconds
2025-05-10 10:36:02,294 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - ERROR - Portfolio backtest failed: Processing failed: no implementation found for 'numpy.where' on types that implement __array_function__: [<class 'cudf.core.series.Series'>]
2025-05-10 10:36:02,297 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Backtest completed with 0 results
2025-05-10 10:36:02,297 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Exiting main function
