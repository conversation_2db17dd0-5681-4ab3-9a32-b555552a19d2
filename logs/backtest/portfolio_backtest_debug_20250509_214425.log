2025-05-09 21:44:25,343 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-09 21:44:25,343 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-09 21:44:25,343 - __main__ - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-09 21:44:25,343 - __main__ - DEBUG - HeavyDB host: **************, DB: heavydb
2025-05-09 21:44:25,343 - __main__ - DEBUG - GPU enabled: True
2025-05-09 21:44:25,344 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting BTRunPortfolio_GPU.py - Debugging enabled
2025-05-09 21:44:25,344 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Current working directory: /srv/samba/shared
2025-05-09 21:44:25,344 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-09 21:44:25,344 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Importing BTRUN modules...
2025-05-09 21:44:25,344 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Config module imported from: /srv/samba/shared/bt/backtester_stable/BTRUN/config.py
2025-05-09 21:44:25,344 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - HeavyDB settings - Host: **************, Database: heavydb
2025-05-09 21:44:25,344 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - GPU enabled: True
2025-05-09 21:44:25,344 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Successfully imported heavydb_helpers module
2025-05-09 21:44:25,344 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Entering main function
2025-05-09 21:44:25,344 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Parsing command line arguments
2025-05-09 21:44:25,345 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Parsed arguments: Namespace(config=None, legacy_excel=True, strategies=None, start_date=None, end_date=None, output_dir='Trades', portfolio_name=None, slippage=0.1, capital=None, margin_multiplier=1.0, no_json=False, no_excel=False, no_charts=False, cpu_only=False, merge_output=False, debug=False)
2025-05-09 21:44:25,346 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Text-to-speech notifications not available
2025-05-09 21:44:25,346 - __main__ - INFO - GPU acceleration enabled: False
2025-05-09 21:44:25,346 - __main__ - INFO - HeavyDB integration available (Host: **************)
2025-05-09 21:44:25,563 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-09 21:44:25,563 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-09 21:44:25,564 - __main__ - INFO - Using new Excel input format (PortfolioSetting & StrategySetting)
2025-05-09 21:44:25,564 - bt.backtester_stable.BTRUN.config - INFO - Found portfolio file at /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-09 21:44:25,564 - __main__ - INFO - Reading PortfolioSetting & StrategySetting from /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-09 21:44:25,682 - __main__ - DEBUG - Loaded 1 PortfolioSetting rows, 21 StrategySetting rows
2025-05-09 21:44:25,764 - __main__ - DEBUG - Loaded 1 rows from /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx
2025-05-09 21:44:25,764 - __main__ - INFO - Processing portfolio: NIF0DTE
2025-05-09 21:44:25,764 - __main__ - DEBUG - Building portfolio request for portfolio: NIF0DTE
2025-05-09 21:44:25,766 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting single backtest for NIF0DTE
2025-05-09 21:44:25,766 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Using HeavyDB for data access
2025-05-09 21:44:25,766 - bt.backtester_stable.BTRUN.runtime - ERROR - Backtest failed - no valid response received
2025-05-09 21:44:25,766 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Backtest completed in 0.00 seconds
2025-05-09 21:44:25,766 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - ERROR - Portfolio backtest failed: Backtest failed
2025-05-09 21:44:25,766 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Backtest completed with 0 results
2025-05-09 21:44:25,767 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Exiting main function
