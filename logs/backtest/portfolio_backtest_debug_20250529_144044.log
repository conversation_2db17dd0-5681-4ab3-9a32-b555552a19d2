2025-05-29 14:40:44,114 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-29 14:40:44,114 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-29 14:40:44,114 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/../models', '/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages', '/srv/samba/shared']
2025-05-29 14:40:44,115 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-29 14:40:44,115 - __main__ - DEBUG - GPU enabled: False
2025-05-29 14:40:44,118 - bt.common.gpu_worker_pool - INFO - Calculated optimal workers: 8 (CPU: 72, Memory: 251.8GB)
2025-05-29 14:40:44,118 - __main__ - INFO - Initialized GPU worker pool with 8 workers
2025-05-29 14:40:44,118 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-29 14:40:44,118 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-29 14:40:44,118 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-29 14:40:44,124 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-29 14:40:44,125 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-29 14:40:44,125 - backtester_stable.BTRUN.core.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-29 14:40:44,125 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-29 14:40:44,126 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-29 14:40:44,126 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-29 14:40:44,126 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-29 14:40:44,126 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-29 14:40:44,126 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-29 14:40:44,126 - __main__ - INFO - Using portfolio Excel from CLI argument: bt/backtester_stable/BTRUN/input_sheets/input_portfolio_2024_4months.xlsx
2025-05-29 14:40:44,126 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: bt/backtester_stable/BTRUN/input_sheets
2025-05-29 14:40:44,127 - __main__ - INFO - Parsing main portfolio Excel: bt/backtester_stable/BTRUN/input_sheets/input_portfolio_2024_4months.xlsx using modern parser. INPUT_FILE_FOLDER is currently: bt/backtester_stable/BTRUN/input_sheets
2025-05-29 14:40:44,260 - __main__ - INFO - Running 1 backtests in parallel with 8 workers
2025-05-29 14:40:44,261 - __main__ - ERROR - Error in main: Worker pool not initialized
Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py", line 591, in main
    results = worker_pool.submit_batch(_row_run_backtest, tasks)
  File "/srv/samba/shared/bt/common/gpu_worker_pool.py", line 284, in submit_batch
    raise RuntimeError("Worker pool not initialized")
RuntimeError: Worker pool not initialized
