2025-05-21 15:23:28,504 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-21 15:23:28,505 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-21 15:23:28,505 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages']
2025-05-21 15:23:28,505 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-21 15:23:28,505 - __main__ - DEBUG - GPU enabled: False
2025-05-21 15:23:28,507 - __main__ - INFO - No config provided – defaulting to Excel input format
2025-05-21 15:23:28,507 - __main__ - INFO - GPU acceleration enabled: False
2025-05-21 15:23:28,507 - __main__ - INFO - HeavyDB integration available (Host: 127.0.0.1)
2025-05-21 15:23:28,726 - backtester_stable.BTRUN.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-21 15:23:28,726 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-21 15:23:28,726 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-21 15:23:28,726 - backtester_stable.BTRUN.utils - INFO - Running necessary functions before starting BT...
2025-05-21 15:23:28,726 - backtester_stable.BTRUN.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/bt/backtester_stable/BTRUN/input/fixtures/LOTSIZE.csv
2025-05-21 15:23:28,726 - backtester_stable.BTRUN.utils - INFO - Loading lot sizes from: /srv/samba/shared/bt/backtester_stable/BTRUN/input/fixtures/LOTSIZE.csv
2025-05-21 15:23:28,732 - backtester_stable.BTRUN.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-21 15:23:28,732 - backtester_stable.BTRUN.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-21 15:23:28,732 - backtester_stable.BTRUN.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-21 15:23:28,732 - backtester_stable.BTRUN.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-21 15:23:28,732 - backtester_stable.BTRUN.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-21 15:23:28,732 - backtester_stable.BTRUN.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-21 15:23:28,733 - backtester_stable.BTRUN.utils - INFO - Pre-BT functions complete.
2025-05-21 15:23:28,733 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-21 15:23:28,733 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-21 15:23:28,733 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_verify_20250521_152325.xlsx
2025-05-21 15:23:28,733 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-21 15:23:28,733 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_verify_20250521_152325.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-21 15:23:28,858 - __main__ - INFO - Successfully parsed 1 portfolio(s) using modern parser.
2025-05-21 15:23:28,858 - __main__ - INFO - Queueing portfolio: NIF0DTE (from modern_portfolio_parser)
2025-05-21 15:23:28,859 - __main__ - INFO - Executing 1 portfolios sequentially (multiprocessing temporarily disabled for debugging)
2025-05-21 15:23:28,859 - __main__ - INFO - Processing payload 1/1: NIF0DTE
2025-05-21 15:23:28,859 - backtester_stable.BTRUN.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'NIF0DTE'
2025-05-21 15:23:29,074 - backtester_stable.BTRUN.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-21 15:23:29,107 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.033s, returned 563 rows. Query: SELECT SUBSTRING('SELECT DISTINCT trade_date FROM nifty_option_chain', 1, 200) ...
2025-05-21 15:23:29,148 - backtester_stable.BTRUN.heavydb_data_access - INFO - Cached 563 distinct trade dates
2025-05-21 15:23:29,148 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] portfolio_model_dict keys: ['portfolio_name', 'start_date', 'end_date', 'slippage_percent', 'margin_multiplier', 'is_tick_bt', 'strategies', 'extra_params']
2025-05-21 15:23:29,149 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-21 15:23:29,149 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-21 15:23:29,188 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.039s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:23:29,227 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.038s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''12:00:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:23:29,228 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523450CE
ce_open                          102.85
ce_high                          106.65
ce_low                            97.85
ce_close                         100.75
ce_volume                       1015050
ce_oi                            978075
ce_coi                                0
ce_iv                             13.44
ce_delta                           0.48
ce_gamma                         0.0015
ce_theta                         -23.27
ce_vega                            7.95
ce_rho                          81.3115
pe_symbol           NIFTY03APR2523450PE
pe_open                           118.2
pe_high                           122.3
pe_low                            112.2
pe_close                          115.7
pe_volume                        877125
pe_oi                           1618950
pe_coi                                0
pe_iv                             13.74
pe_delta                          -0.52
pe_gamma                         0.0015
pe_theta                          -17.3
pe_vega                            7.95
pe_rho                         -88.3744
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-21 15:23:29,228 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23200.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                           123.6
ce_high                           124.3
ce_low                           119.25
ce_close                          124.2
ce_volume                        326775
ce_oi                           4374450
ce_coi                           193725
ce_iv                             14.99
ce_delta                           0.52
ce_gamma                         0.0013
ce_theta                         -25.57
ce_vega                            7.87
ce_rho                          86.2799
pe_symbol           NIFTY03APR2523200PE
pe_open                           100.2
pe_high                           105.2
pe_low                            99.55
pe_close                         100.75
pe_volume                        526875
pe_oi                           5030025
pe_coi                           101175
pe_iv                             13.55
pe_delta                          -0.48
pe_gamma                         0.0015
pe_theta                         -17.08
pe_vega                            7.87
pe_rho                         -81.3242
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-21 15:23:29,229 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 15:23:29,229 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-21 15:23:29,229 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 15:23:29,229 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140252763157024
2025-05-21 15:23:29,229 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140252763157024, OptionType.PUT ID: 140252763157136
2025-05-21 15:23:29,229 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 15:23:29,229 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-21 15:23:29,229 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-21 15:23:29,231 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 15:23:29,535 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.304s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 15:23:29,538 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-21 15:23:29,538 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-21 15:23:29,593 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 15:23:29,602 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-21 15:23:29,602 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-21 15:23:29,642 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.040s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:23:29,680 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.038s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''12:00:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:23:29,681 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523450CE
ce_open                          102.85
ce_high                          106.65
ce_low                            97.85
ce_close                         100.75
ce_volume                       1015050
ce_oi                            978075
ce_coi                                0
ce_iv                             13.44
ce_delta                           0.48
ce_gamma                         0.0015
ce_theta                         -23.27
ce_vega                            7.95
ce_rho                          81.3115
pe_symbol           NIFTY03APR2523450PE
pe_open                           118.2
pe_high                           122.3
pe_low                            112.2
pe_close                          115.7
pe_volume                        877125
pe_oi                           1618950
pe_coi                                0
pe_iv                             13.74
pe_delta                          -0.52
pe_gamma                         0.0015
pe_theta                          -17.3
pe_vega                            7.95
pe_rho                         -88.3744
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-21 15:23:29,682 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23200.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                           123.6
ce_high                           124.3
ce_low                           119.25
ce_close                          124.2
ce_volume                        326775
ce_oi                           4374450
ce_coi                           193725
ce_iv                             14.99
ce_delta                           0.52
ce_gamma                         0.0013
ce_theta                         -25.57
ce_vega                            7.87
ce_rho                          86.2799
pe_symbol           NIFTY03APR2523200PE
pe_open                           100.2
pe_high                           105.2
pe_low                            99.55
pe_close                         100.75
pe_volume                        526875
pe_oi                           5030025
pe_coi                           101175
pe_iv                             13.55
pe_delta                          -0.48
pe_gamma                         0.0015
pe_theta                         -17.08
pe_vega                            7.87
pe_rho                         -81.3242
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-21 15:23:29,682 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 15:23:29,682 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-21 15:23:29,683 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 15:23:29,683 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140252763157136
2025-05-21 15:23:29,683 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140252763157024, OptionType.PUT ID: 140252763157136
2025-05-21 15:23:29,683 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 15:23:29,683 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-21 15:23:29,683 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-21 15:23:29,684 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 15:23:30,005 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.321s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 15:23:30,007 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-21 15:23:30,008 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-21 15:23:30,062 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 15:23:30,087 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-21 15:23:30,087 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-21 15:23:30,125 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.037s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:23:30,163 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.037s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''12:00:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:23:30,164 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523450CE
ce_open                          102.85
ce_high                          106.65
ce_low                            97.85
ce_close                         100.75
ce_volume                       1015050
ce_oi                            978075
ce_coi                                0
ce_iv                             13.44
ce_delta                           0.48
ce_gamma                         0.0015
ce_theta                         -23.27
ce_vega                            7.95
ce_rho                          81.3115
pe_symbol           NIFTY03APR2523450PE
pe_open                           118.2
pe_high                           122.3
pe_low                            112.2
pe_close                          115.7
pe_volume                        877125
pe_oi                           1618950
pe_coi                                0
pe_iv                             13.74
pe_delta                          -0.52
pe_gamma                         0.0015
pe_theta                          -17.3
pe_vega                            7.95
pe_rho                         -88.3744
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-21 15:23:30,164 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23200.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                           123.6
ce_high                           124.3
ce_low                           119.25
ce_close                          124.2
ce_volume                        326775
ce_oi                           4374450
ce_coi                           193725
ce_iv                             14.99
ce_delta                           0.52
ce_gamma                         0.0013
ce_theta                         -25.57
ce_vega                            7.87
ce_rho                          86.2799
pe_symbol           NIFTY03APR2523200PE
pe_open                           100.2
pe_high                           105.2
pe_low                            99.55
pe_close                         100.75
pe_volume                        526875
pe_oi                           5030025
pe_coi                           101175
pe_iv                             13.55
pe_delta                          -0.48
pe_gamma                         0.0015
pe_theta                         -17.08
pe_vega                            7.87
pe_rho                         -81.3242
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-21 15:23:30,164 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 15:23:30,165 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-21 15:23:30,165 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 15:23:30,165 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140252763157024
2025-05-21 15:23:30,165 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140252763157024, OptionType.PUT ID: 140252763157136
2025-05-21 15:23:30,165 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 15:23:30,165 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-21 15:23:30,165 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-21 15:23:30,167 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 15:23:30,479 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.312s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 15:23:30,480 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-21 15:23:30,481 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-21 15:23:30,535 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 15:23:30,553 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-21 15:23:30,553 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-21 15:23:30,592 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.038s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:23:30,630 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.038s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''12:00:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:23:30,631 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523450CE
ce_open                          102.85
ce_high                          106.65
ce_low                            97.85
ce_close                         100.75
ce_volume                       1015050
ce_oi                            978075
ce_coi                                0
ce_iv                             13.44
ce_delta                           0.48
ce_gamma                         0.0015
ce_theta                         -23.27
ce_vega                            7.95
ce_rho                          81.3115
pe_symbol           NIFTY03APR2523450PE
pe_open                           118.2
pe_high                           122.3
pe_low                            112.2
pe_close                          115.7
pe_volume                        877125
pe_oi                           1618950
pe_coi                                0
pe_iv                             13.74
pe_delta                          -0.52
pe_gamma                         0.0015
pe_theta                          -17.3
pe_vega                            7.95
pe_rho                         -88.3744
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-21 15:23:30,631 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23200.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                           123.6
ce_high                           124.3
ce_low                           119.25
ce_close                          124.2
ce_volume                        326775
ce_oi                           4374450
ce_coi                           193725
ce_iv                             14.99
ce_delta                           0.52
ce_gamma                         0.0013
ce_theta                         -25.57
ce_vega                            7.87
ce_rho                          86.2799
pe_symbol           NIFTY03APR2523200PE
pe_open                           100.2
pe_high                           105.2
pe_low                            99.55
pe_close                         100.75
pe_volume                        526875
pe_oi                           5030025
pe_coi                           101175
pe_iv                             13.55
pe_delta                          -0.48
pe_gamma                         0.0015
pe_theta                         -17.08
pe_vega                            7.87
pe_rho                         -81.3242
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-21 15:23:30,631 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 15:23:30,632 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-21 15:23:30,632 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 15:23:30,632 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140252763157136
2025-05-21 15:23:30,632 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140252763157024, OptionType.PUT ID: 140252763157136
2025-05-21 15:23:30,632 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 15:23:30,632 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-21 15:23:30,632 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-21 15:23:30,633 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 15:23:30,951 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.317s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 15:23:30,953 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-21 15:23:30,954 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-21 15:23:31,038 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 15:23:31,050 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-21 15:23:31,050 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23420.45, 'atm_strike': 23450.0, 'strike': 23450.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523450CE', 'ce_open': 102.85, 'ce_high': 106.65, 'ce_low': 97.85, 'ce_close': 100.75, 'ce_volume': 1015050, 'ce_oi': 978075, 'ce_coi': 0, 'ce_iv': 13.44, 'ce_delta': 0.48, 'ce_gamma': 0.0015, 'ce_theta': -23.27, 'ce_vega': 7.95, 'ce_rho': 81.3115, 'pe_symbol': 'NIFTY03APR2523450PE', 'pe_open': 118.2, 'pe_high': 122.3, 'pe_low': 112.2, 'pe_close': 115.7, 'pe_volume': 877125, 'pe_oi': 1618950, 'pe_coi': 0, 'pe_iv': 13.74, 'pe_delta': -0.52, 'pe_gamma': 0.0015, 'pe_theta': -17.3, 'pe_vega': 7.95, 'pe_rho': -88.3744, 'future_open': 23534.5, 'future_high': 23543.95, 'future_low': 23527.0, 'future_close': 23536.75, 'future_volume': 86475, 'future_oi': 12605100, 'future_coi': 0}
2025-05-21 15:23:31,050 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23195.3, 'atm_strike': 23200.0, 'strike': 23200.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523200CE', 'ce_open': 123.6, 'ce_high': 124.3, 'ce_low': 119.25, 'ce_close': 124.2, 'ce_volume': 326775, 'ce_oi': 4374450, 'ce_coi': 193725, 'ce_iv': 14.99, 'ce_delta': 0.52, 'ce_gamma': 0.0013, 'ce_theta': -25.57, 'ce_vega': 7.87, 'ce_rho': 86.2799, 'pe_symbol': 'NIFTY03APR2523200PE', 'pe_open': 100.2, 'pe_high': 105.2, 'pe_low': 99.55, 'pe_close': 100.75, 'pe_volume': 526875, 'pe_oi': 5030025, 'pe_coi': 101175, 'pe_iv': 13.55, 'pe_delta': -0.48, 'pe_gamma': 0.0015, 'pe_theta': -17.08, 'pe_vega': 7.87, 'pe_rho': -81.3242, 'future_open': 23347.0, 'future_high': 23348.85, 'future_low': 23339.0, 'future_close': 23348.1, 'future_volume': 19050, 'future_oi': 13928550, 'future_coi': -28275, 'close': 2.15, 'datetime': Timestamp('2025-04-01 09:17:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-21 15:23:31,050 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-21 15:23:31,051 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -747.5000000000001, PnL (Slippage): -747.5000000000001, Net PnL: -747.5000000000001
2025-05-21 15:23:31,051 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-21 15:23:31,051 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2025-04-01', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2025-04-01', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23450, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 115.7, 'exit_price': 100.75, 'points': -14.950000000000003, 'pointsAfterSlippage': -14.950000000000003, 'pnl': -747.5000000000001, 'pnlAfterSlippage': -747.5000000000001, 'expenses': 0.0, 'netPnlAfterExpenses': -747.5000000000001, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23420.45, 'index_exit_price': 23195.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-01 09:16:00', 'exit_datetime': '2025-04-01 12:00:00'}
2025-05-21 15:23:31,051 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-21 15:23:31,051 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-21 15:23:31,079 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.027s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:23:31,107 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.028s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''12:00:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:23:31,108 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
trade_date                   2025-04-02
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23204.8
atm_strike                      23200.0
strike                          23200.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                          107.95
ce_high                           113.5
ce_low                           105.15
ce_close                          110.5
ce_volume                       2666850
ce_oi                           5862000
ce_coi                                0
ce_iv                             16.53
ce_delta                           0.53
ce_gamma                         0.0015
ce_theta                         -34.48
ce_vega                             6.2
ce_rho                          54.4841
pe_symbol           NIFTY03APR2523200PE
pe_open                            86.9
pe_high                            94.9
pe_low                            85.55
pe_close                          86.15
pe_volume                       3014850
pe_oi                           5689575
pe_coi                                0
pe_iv                             15.06
pe_delta                          -0.47
pe_gamma                         0.0017
pe_theta                         -25.36
pe_vega                             6.2
pe_rho                         -49.7483
future_open                     23339.0
future_high                     23344.7
future_low                     23331.25
future_close                   23344.15
future_volume                     43725
future_oi                      12808425
future_coi                            0
2025-05-21 15:23:31,109 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2025-04-02
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23261.45
atm_strike                      23250.0
strike                          23250.0
dte                                   1
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523250CE
ce_open                            83.1
ce_high                            87.9
ce_low                             83.1
ce_close                           87.0
ce_volume                        546750
ce_oi                           6733800
ce_coi                                0
ce_iv                             12.13
ce_delta                           0.55
ce_gamma                         0.0021
ce_theta                         -26.28
ce_vega                            6.18
ce_rho                           57.017
pe_symbol           NIFTY03APR2523250PE
pe_open                           70.65
pe_high                           70.65
pe_low                             65.7
pe_close                           66.8
pe_volume                        766500
pe_oi                           7853325
pe_coi                                0
pe_iv                             12.41
pe_delta                          -0.45
pe_gamma                          0.002
pe_theta                         -20.43
pe_vega                            6.19
pe_rho                         -47.8132
future_open                    23386.95
future_high                     23394.9
future_low                     23383.35
future_close                    23390.0
future_volume                      2475
future_oi                      12651900
future_coi                            0
2025-05-21 15:23:31,109 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 15:23:31,109 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-21 15:23:31,110 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 15:23:31,110 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140252763157024
2025-05-21 15:23:31,110 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140252763157024, OptionType.PUT ID: 140252763157136
2025-05-21 15:23:31,110 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 15:23:31,110 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-21 15:23:31,110 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-21 15:23:31,112 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-02' AND trade_date <= '2025-04-02'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 15:23:31,425 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.313s, returned 18845 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 15:23:31,427 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18845 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-21 15:23:31,428 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-21 15:23:31,508 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 15:23:31,520 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-21 15:23:31,520 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-21 15:23:31,560 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.040s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:23:31,602 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.042s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''12:00:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:23:31,603 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
trade_date                   2025-04-02
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23204.8
atm_strike                      23200.0
strike                          23200.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                          107.95
ce_high                           113.5
ce_low                           105.15
ce_close                          110.5
ce_volume                       2666850
ce_oi                           5862000
ce_coi                                0
ce_iv                             16.53
ce_delta                           0.53
ce_gamma                         0.0015
ce_theta                         -34.48
ce_vega                             6.2
ce_rho                          54.4841
pe_symbol           NIFTY03APR2523200PE
pe_open                            86.9
pe_high                            94.9
pe_low                            85.55
pe_close                          86.15
pe_volume                       3014850
pe_oi                           5689575
pe_coi                                0
pe_iv                             15.06
pe_delta                          -0.47
pe_gamma                         0.0017
pe_theta                         -25.36
pe_vega                             6.2
pe_rho                         -49.7483
future_open                     23339.0
future_high                     23344.7
future_low                     23331.25
future_close                   23344.15
future_volume                     43725
future_oi                      12808425
future_coi                            0
2025-05-21 15:23:31,604 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2025-04-02
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23261.45
atm_strike                      23250.0
strike                          23250.0
dte                                   1
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523250CE
ce_open                            83.1
ce_high                            87.9
ce_low                             83.1
ce_close                           87.0
ce_volume                        546750
ce_oi                           6733800
ce_coi                                0
ce_iv                             12.13
ce_delta                           0.55
ce_gamma                         0.0021
ce_theta                         -26.28
ce_vega                            6.18
ce_rho                           57.017
pe_symbol           NIFTY03APR2523250PE
pe_open                           70.65
pe_high                           70.65
pe_low                             65.7
pe_close                           66.8
pe_volume                        766500
pe_oi                           7853325
pe_coi                                0
pe_iv                             12.41
pe_delta                          -0.45
pe_gamma                          0.002
pe_theta                         -20.43
pe_vega                            6.19
pe_rho                         -47.8132
future_open                    23386.95
future_high                     23394.9
future_low                     23383.35
future_close                    23390.0
future_volume                      2475
future_oi                      12651900
future_coi                            0
2025-05-21 15:23:31,604 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 15:23:31,605 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-21 15:23:31,605 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 15:23:31,605 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140252763157136
2025-05-21 15:23:31,605 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140252763157024, OptionType.PUT ID: 140252763157136
2025-05-21 15:23:31,605 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 15:23:31,605 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-21 15:23:31,606 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-21 15:23:31,607 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-02' AND trade_date <= '2025-04-02'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 15:23:31,951 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.343s, returned 18845 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 15:23:31,953 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18845 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-21 15:23:31,954 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-21 15:23:32,033 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 15:23:32,069 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-21 15:23:32,069 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-21 15:23:32,098 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.028s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:23:32,125 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.027s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''12:00:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:23:32,127 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
trade_date                   2025-04-02
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23204.8
atm_strike                      23200.0
strike                          23200.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                          107.95
ce_high                           113.5
ce_low                           105.15
ce_close                          110.5
ce_volume                       2666850
ce_oi                           5862000
ce_coi                                0
ce_iv                             16.53
ce_delta                           0.53
ce_gamma                         0.0015
ce_theta                         -34.48
ce_vega                             6.2
ce_rho                          54.4841
pe_symbol           NIFTY03APR2523200PE
pe_open                            86.9
pe_high                            94.9
pe_low                            85.55
pe_close                          86.15
pe_volume                       3014850
pe_oi                           5689575
pe_coi                                0
pe_iv                             15.06
pe_delta                          -0.47
pe_gamma                         0.0017
pe_theta                         -25.36
pe_vega                             6.2
pe_rho                         -49.7483
future_open                     23339.0
future_high                     23344.7
future_low                     23331.25
future_close                   23344.15
future_volume                     43725
future_oi                      12808425
future_coi                            0
2025-05-21 15:23:32,128 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2025-04-02
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23261.45
atm_strike                      23250.0
strike                          23250.0
dte                                   1
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523250CE
ce_open                            83.1
ce_high                            87.9
ce_low                             83.1
ce_close                           87.0
ce_volume                        546750
ce_oi                           6733800
ce_coi                                0
ce_iv                             12.13
ce_delta                           0.55
ce_gamma                         0.0021
ce_theta                         -26.28
ce_vega                            6.18
ce_rho                           57.017
pe_symbol           NIFTY03APR2523250PE
pe_open                           70.65
pe_high                           70.65
pe_low                             65.7
pe_close                           66.8
pe_volume                        766500
pe_oi                           7853325
pe_coi                                0
pe_iv                             12.41
pe_delta                          -0.45
pe_gamma                          0.002
pe_theta                         -20.43
pe_vega                            6.19
pe_rho                         -47.8132
future_open                    23386.95
future_high                     23394.9
future_low                     23383.35
future_close                    23390.0
future_volume                      2475
future_oi                      12651900
future_coi                            0
2025-05-21 15:23:32,128 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 15:23:32,128 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-21 15:23:32,128 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 15:23:32,128 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140252763157024
2025-05-21 15:23:32,128 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140252763157024, OptionType.PUT ID: 140252763157136
2025-05-21 15:23:32,129 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 15:23:32,129 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-21 15:23:32,129 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-21 15:23:32,130 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-02' AND trade_date <= '2025-04-02'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 15:23:32,440 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.310s, returned 18845 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 15:23:32,443 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18845 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-21 15:23:32,443 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-21 15:23:32,531 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 15:23:32,557 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-21 15:23:32,557 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-21 15:23:32,597 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.039s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:23:32,626 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.029s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''12:00:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:23:32,628 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
trade_date                   2025-04-02
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23204.8
atm_strike                      23200.0
strike                          23200.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                          107.95
ce_high                           113.5
ce_low                           105.15
ce_close                          110.5
ce_volume                       2666850
ce_oi                           5862000
ce_coi                                0
ce_iv                             16.53
ce_delta                           0.53
ce_gamma                         0.0015
ce_theta                         -34.48
ce_vega                             6.2
ce_rho                          54.4841
pe_symbol           NIFTY03APR2523200PE
pe_open                            86.9
pe_high                            94.9
pe_low                            85.55
pe_close                          86.15
pe_volume                       3014850
pe_oi                           5689575
pe_coi                                0
pe_iv                             15.06
pe_delta                          -0.47
pe_gamma                         0.0017
pe_theta                         -25.36
pe_vega                             6.2
pe_rho                         -49.7483
future_open                     23339.0
future_high                     23344.7
future_low                     23331.25
future_close                   23344.15
future_volume                     43725
future_oi                      12808425
future_coi                            0
2025-05-21 15:23:32,628 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2025-04-02
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23261.45
atm_strike                      23250.0
strike                          23250.0
dte                                   1
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523250CE
ce_open                            83.1
ce_high                            87.9
ce_low                             83.1
ce_close                           87.0
ce_volume                        546750
ce_oi                           6733800
ce_coi                                0
ce_iv                             12.13
ce_delta                           0.55
ce_gamma                         0.0021
ce_theta                         -26.28
ce_vega                            6.18
ce_rho                           57.017
pe_symbol           NIFTY03APR2523250PE
pe_open                           70.65
pe_high                           70.65
pe_low                             65.7
pe_close                           66.8
pe_volume                        766500
pe_oi                           7853325
pe_coi                                0
pe_iv                             12.41
pe_delta                          -0.45
pe_gamma                          0.002
pe_theta                         -20.43
pe_vega                            6.19
pe_rho                         -47.8132
future_open                    23386.95
future_high                     23394.9
future_low                     23383.35
future_close                    23390.0
future_volume                      2475
future_oi                      12651900
future_coi                            0
2025-05-21 15:23:32,629 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 15:23:32,629 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-21 15:23:32,629 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 15:23:32,629 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140252763157136
2025-05-21 15:23:32,629 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140252763157024, OptionType.PUT ID: 140252763157136
2025-05-21 15:23:32,629 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 15:23:32,630 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-21 15:23:32,630 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-21 15:23:32,631 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-02' AND trade_date <= '2025-04-02'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 15:23:32,920 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.289s, returned 18845 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 15:23:32,923 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18845 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-21 15:23:32,923 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-21 15:23:33,003 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 15:23:33,014 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-21 15:23:33,015 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 2), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23204.8, 'atm_strike': 23200.0, 'strike': 23200.0, 'dte': 1, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523200CE', 'ce_open': 107.95, 'ce_high': 113.5, 'ce_low': 105.15, 'ce_close': 110.5, 'ce_volume': 2666850, 'ce_oi': 5862000, 'ce_coi': 0, 'ce_iv': 16.53, 'ce_delta': 0.53, 'ce_gamma': 0.0015, 'ce_theta': -34.48, 'ce_vega': 6.2, 'ce_rho': 54.4841, 'pe_symbol': 'NIFTY03APR2523200PE', 'pe_open': 86.9, 'pe_high': 94.9, 'pe_low': 85.55, 'pe_close': 86.15, 'pe_volume': 3014850, 'pe_oi': 5689575, 'pe_coi': 0, 'pe_iv': 15.06, 'pe_delta': -0.47, 'pe_gamma': 0.0017, 'pe_theta': -25.36, 'pe_vega': 6.2, 'pe_rho': -49.7483, 'future_open': 23339.0, 'future_high': 23344.7, 'future_low': 23331.25, 'future_close': 23344.15, 'future_volume': 43725, 'future_oi': 12808425, 'future_coi': 0}
2025-05-21 15:23:33,015 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 2), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23261.45, 'atm_strike': 23250.0, 'strike': 23250.0, 'dte': 1, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523250CE', 'ce_open': 83.1, 'ce_high': 87.9, 'ce_low': 83.1, 'ce_close': 87.0, 'ce_volume': 546750, 'ce_oi': 6733800, 'ce_coi': 0, 'ce_iv': 12.13, 'ce_delta': 0.55, 'ce_gamma': 0.0021, 'ce_theta': -26.28, 'ce_vega': 6.18, 'ce_rho': 57.017, 'pe_symbol': 'NIFTY03APR2523250PE', 'pe_open': 70.65, 'pe_high': 70.65, 'pe_low': 65.7, 'pe_close': 66.8, 'pe_volume': 766500, 'pe_oi': 7853325, 'pe_coi': 0, 'pe_iv': 12.41, 'pe_delta': -0.45, 'pe_gamma': 0.002, 'pe_theta': -20.43, 'pe_vega': 6.19, 'pe_rho': -47.8132, 'future_open': 23386.95, 'future_high': 23394.9, 'future_low': 23383.35, 'future_close': 23390.0, 'future_volume': 2475, 'future_oi': 12651900, 'future_coi': 0, 'close': 1.14, 'datetime': Timestamp('2025-04-02 09:17:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-21 15:23:33,015 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-21 15:23:33,015 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -967.5000000000005, PnL (Slippage): -967.5000000000005, Net PnL: -967.5000000000005
2025-05-21 15:23:33,015 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-21 15:23:33,016 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2025-04-02', 'entry_time': '09:16:00', 'entry_day': 'Wednesday', 'exit_date': '2025-04-02', 'exit_time': '12:00:00', 'exit_day': 'Wednesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23200, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 86.15, 'exit_price': 66.8, 'points': -19.35000000000001, 'pointsAfterSlippage': -19.35000000000001, 'pnl': -967.5000000000005, 'pnlAfterSlippage': -967.5000000000005, 'expenses': 0.0, 'netPnlAfterExpenses': -967.5000000000005, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23204.8, 'index_exit_price': 23261.45, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-02 09:16:00', 'exit_datetime': '2025-04-02 12:00:00'}
2025-05-21 15:23:33,016 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Number of trade records generated: 2
2025-05-21 15:23:33,016 - backtester_stable.BTRUN.heavydb_trade_processing - INFO - Generated 2 trade records via model-driven path
2025-05-21 15:23:33,016 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Closed reusable HeavyDB connection from get_trades_for_portfolio
2025-05-21 15:23:33,040 - backtester_stable.BTRUN.builders - WARNING - utils.MARGIN_INFO not found or empty - margin requirements will be 0. Attempting to populate.
2025-05-21 15:23:33,041 - backtester_stable.BTRUN.config - INFO - Found fixture 'margin_symbol_info.json' at /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-21 15:23:33,041 - backtester_stable.BTRUN.utils - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json (Broker: angel)
2025-05-21 15:23:33,041 - backtester_stable.BTRUN.utils - INFO - Successfully populated config.MARGIN_INFO for angel.
2025-05-21 15:23:33,041 - backtester_stable.BTRUN.builders - ERROR - utils.MARGIN_INFO still empty after populate attempt. Margin req will be 0.
2025-05-21 15:23:33,043 - backtester_stable.BTRUN.builders - DEBUG - Strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL margin requirement: 0.00
2025-05-21 15:23:33,043 - backtester_stable.BTRUN.builders - INFO - Total portfolio margin requirement: 0.00
2025-05-21 15:23:33,043 - backtester_stable.BTRUN.builders - DEBUG - Converting tick PnL to daywise DataFrame.
2025-05-21 15:23:33,045 - backtester_stable.BTRUN.builders - INFO - Backtest response parsed.
2025-05-21 15:23:33,087 - backtester_stable.BTRUN.runtime - INFO - Generated metrics_df with 50 records for strategies: ['Combined' 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL']
2025-05-21 15:23:33,172 - backtester_stable.BTRUN.runtime - INFO - Saving Excel output to /srv/samba/shared/Trades/verify_sl_tp_exit_20250521_152325.xlsx
2025-05-21 15:23:33,172 - backtester_stable.BTRUN.runtime - DEBUG - [DEBUG save_backtest_results] save_excel=True, metrics_df is empty: False
2025-05-21 15:23:33,172 - backtester_stable.BTRUN.io - INFO - 2025-05-21 15:23:33.172744, Started writing stats to excel file: /srv/samba/shared/Trades/verify_sl_tp_exit_20250521_152325.xlsx
2025-05-21 15:23:33,172 - backtester_stable.BTRUN.gpu_helpers - DEBUG - Temporarily switching to CPU-only mode.
2025-05-21 15:23:33,238 - backtester_stable.BTRUN.io - INFO - 2025-05-21 15:23:33.238540, Excel file prepared successfully, Time taken: 0.07s
2025-05-21 15:23:33,238 - backtester_stable.BTRUN.gpu_helpers - DEBUG - Restored GPU_ENABLED to: False
2025-05-21 15:23:33,238 - backtester_stable.BTRUN.runtime - INFO - Saving JSON output to /srv/samba/shared/Trades/verify_sl_tp_exit_20250521_152325.json
2025-05-21 15:23:33,264 - backtester_stable.BTRUN.io - INFO - --- JSON DUMP DEBUG START ---
2025-05-21 15:23:33,264 - backtester_stable.BTRUN.io - INFO - --- JSON DUMP DEBUG END ---
2025-05-21 15:23:33,265 - backtester_stable.BTRUN.io - INFO - Successfully wrote JSON output to /srv/samba/shared/Trades/verify_sl_tp_exit_20250521_152325.json
2025-05-21 15:23:33,266 - backtester_stable.BTRUN.io - WARNING - pyttsx3 not installed - skipping text-to-speech
