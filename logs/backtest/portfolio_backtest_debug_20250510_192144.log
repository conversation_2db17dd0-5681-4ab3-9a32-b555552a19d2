2025-05-10 19:21:44,361 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-10 19:21:44,361 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-10 19:21:44,361 - __main__ - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-10 19:21:44,362 - __main__ - DEBUG - HeavyDB host: **************, DB: heavydb
2025-05-10 19:21:44,362 - __main__ - DEBUG - GPU enabled: True
2025-05-10 19:21:44,362 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting BTRunPortfolio_GPU.py - Debugging enabled
2025-05-10 19:21:44,362 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Current working directory: /srv/samba/shared
2025-05-10 19:21:44,362 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-10 19:21:44,362 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Importing BTRUN modules...
2025-05-10 19:21:44,362 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Config module imported from: /srv/samba/shared/bt/backtester_stable/BTRUN/config.py
2025-05-10 19:21:44,362 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - HeavyDB settings - Host: **************, Database: heavydb
2025-05-10 19:21:44,362 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - GPU enabled: True
2025-05-10 19:21:44,363 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Successfully imported heavydb_helpers module
2025-05-10 19:21:44,364 - __main__ - INFO - GPU acceleration enabled: True
2025-05-10 19:21:44,433 - __main__ - INFO - GPU Memory: 35419MB free / 40384MB total
2025-05-10 19:21:44,433 - __main__ - INFO - HeavyDB integration available (Host: **************)
2025-05-10 19:21:44,648 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-10 19:21:44,648 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-10 19:21:44,648 - __main__ - INFO - Runtime flags – workers: 2, retry_cpu: True
2025-05-10 19:21:44,648 - __main__ - INFO - Using new Excel input format (PortfolioSetting & StrategySetting)
2025-05-10 19:21:44,648 - bt.backtester_stable.BTRUN.config - INFO - Found portfolio file at /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-10 19:21:44,648 - __main__ - INFO - Reading PortfolioSetting & StrategySetting from /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-10 19:21:44,747 - __main__ - DEBUG - Loaded 1 PortfolioSetting rows, 21 StrategySetting rows
2025-05-10 19:21:44,825 - __main__ - DEBUG - Loaded 1 rows from /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx
2025-05-10 19:21:44,825 - __main__ - INFO - Queueing portfolio: NIF0DTE
2025-05-10 19:21:44,826 - __main__ - DEBUG - Building portfolio request for portfolio: NIF0DTE
2025-05-10 19:21:44,827 - __main__ - INFO - Executing 1 portfolios with 2 workers
