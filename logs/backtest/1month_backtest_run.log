================================================================================
1-MONTH GPU OPTIMIZED BACKTEST
================================================================================
Start time: 2025-05-29 21:44:41

System has 72 CPU cores
✅ GPU modules detected - GPU optimization enabled

Command to execute:
python3 /srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py --portfolio-excel /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_2024_1month_test.xlsx --output-path /srv/samba/shared/1month_gpu_backtest_20250529_214441.xlsx --workers auto --cpu-workers 72 --batch-days 7 --merge-output --no-charts --use-gpu-optimization --gpu-workers 4 --gpu-threshold 1000

--------------------------------------------------------------------------------
Starting backtest...
--------------------------------------------------------------------------------

CONFIG.PY: Attempting to load config.py...
Partially restored config.py is being imported - Phase 10.
USING PARTIALLY RESTORED DEBUG CONFIG.PY - get_table_name active!
USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
CONFIG.PY: Finished loading config.py.
2025-05-29 21:44:45,990 - root - ERROR - Failed to import BTRUN config module
Warning: Could not import required modules: No module named 'bt.backtester_stable.models'
Failed to auto-detect optimal workers, defaulting to 1
2025-05-30 03:14:45,995 - __main__ - INFO - Running in sequential (single-worker) mode.
2025-05-30 03:14:45,996 - __main__ - INFO - GPU optimization is not available (missing dependencies or import error). Running sequentially.
2025-05-30 03:14:45,996 - __main__ - INFO - GPU optimization disabled or unavailable. Running sequentially.
2025-05-30 03:14:45,996 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-30 03:14:45,996 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-30 03:14:45,996 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-30 03:14:46,001 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-30 03:14:46,001 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-30 03:14:46,002 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-30 03:14:46,002 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-30 03:14:46,002 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-30 03:14:46,002 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-30 03:14:46,002 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-30 03:14:46,002 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-30 03:14:46,002 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_2024_1month_test.xlsx
2025-05-30 03:14:46,002 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-30 03:14:46,002 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_2024_1month_test.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-30 03:14:46,132 - __main__ - INFO - Processing portfolio 'NIF0DTE' sequentially.
2025-05-30 03:14:46,132 - __main__ - INFO - Processing batch: NIF0DTE_fulldaterange (2024-04-01 to 2024-04-30) for original portfolio 'NIF0DTE'
[DEBUG] run_full_backtest bt_params keys: ['portfolio_model', 'portfolio_name', 'start_date', 'end_date']
2025-05-30 03:14:46,132 - backtester_stable.BTRUN.core.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'NIF0DTE'
2025-05-30 03:14:46,347 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 03:14:46,380 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.032s, returned 563 rows. Query: SELECT SUBSTRING('SELECT DISTINCT trade_date FROM nifty_option_chain', 1, 200) ...
2025-05-30 03:14:46,422 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - Cached 563 distinct trade dates
2025-05-30 03:14:46,423 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DTE filter found in strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL: 0
2025-05-30 03:14:46,709 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.286s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-01'' 
                    AND expiry_date = ''2024-04-01''
                    LIMIT 1', 1, 200) ...
2025-05-30 03:14:46,709 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-01 - Not an expiry day (DTE=0 filter active)
2025-05-30 03:14:46,729 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-02'' 
                    AND expiry_date = ''2024-04-02''
                    LIMIT 1', 1, 200) ...
2025-05-30 03:14:46,729 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-02 - Not an expiry day (DTE=0 filter active)
2025-05-30 03:14:46,754 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.025s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-03'' 
                    AND expiry_date = ''2024-04-03''
                    LIMIT 1', 1, 200) ...
2025-05-30 03:14:46,754 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-03 - Not an expiry day (DTE=0 filter active)
2025-05-30 03:14:46,773 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-04'' 
                    AND expiry_date = ''2024-04-04''
                    LIMIT 1', 1, 200) ...
2025-05-30 03:14:46,773 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Processing date 2024-04-04 - Is an expiry day (DTE=0 filter active)
2025-05-30 03:14:46,774 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 1 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-30 03:14:46,833 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.059s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-04'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-30 03:14:46,869 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.035s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-04'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-30 03:14:46,869 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Query results: entry_df size=1, exit_df size=1
2025-05-30 03:14:46,872 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-30 03:14:47,094 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 03:14:47,472 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.378s, returned 29452 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-30 03:14:47,561 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 1 of 3
2025-05-30 03:14:47,561 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-30 03:14:47,561 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 2 of 3
2025-05-30 03:14:47,561 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-30 03:14:47,562 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 3 of 3
2025-05-30 03:14:47,562 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-30 03:14:47,562 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 1
2025-05-30 03:14:47,562 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 2 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-30 03:14:47,620 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.057s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-04'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-30 03:14:47,676 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.056s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-04'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-30 03:14:47,676 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Query results: entry_df size=1, exit_df size=1
2025-05-30 03:14:47,677 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-30 03:14:47,900 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 03:14:48,252 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.353s, returned 29452 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-30 03:14:48,339 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 1 of 3
2025-05-30 03:14:48,339 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-30 03:14:48,340 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 2 of 3
2025-05-30 03:14:48,340 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-30 03:14:48,340 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 3 of 3
2025-05-30 03:14:48,340 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-30 03:14:48,340 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 2
2025-05-30 03:14:48,340 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 3 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-30 03:14:48,376 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.035s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-04'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike + (2.0 * 50)) AND oc.ce_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-30 03:14:48,409 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.032s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-04'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike + (2.0 * 50)) AND oc.ce_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-30 03:14:48,409 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Query results: entry_df size=1, exit_df size=1
2025-05-30 03:14:48,410 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-30 03:14:48,632 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 03:14:48,978 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.346s, returned 29452 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-30 03:14:49,107 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 1 of 3
2025-05-30 03:14:49,107 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-30 03:14:49,107 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 2 of 3
2025-05-30 03:14:49,107 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-30 03:14:49,107 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 3 of 3
2025-05-30 03:14:49,108 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-30 03:14:49,108 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 3
2025-05-30 03:14:49,108 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 4 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-30 03:14:49,145 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.036s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-04'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike - (2.0 * 50)) AND oc.pe_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-30 03:14:49,179 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.033s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-04'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike - (2.0 * 50)) AND oc.pe_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-30 03:14:49,179 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Query results: entry_df size=1, exit_df size=1
2025-05-30 03:14:49,181 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-30 03:14:49,394 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 03:14:49,746 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.351s, returned 29452 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-30 03:14:49,882 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 1 of 3
2025-05-30 03:14:49,882 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-30 03:14:49,882 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 2 of 3
2025-05-30 03:14:49,882 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-30 03:14:49,882 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 3 of 3
2025-05-30 03:14:49,882 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-30 03:14:49,883 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 4
2025-05-30 03:14:49,907 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.024s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-05'' 
                    AND expiry_date = ''2024-04-05''
                    LIMIT 1', 1, 200) ...
2025-05-30 03:14:49,907 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-05 - Not an expiry day (DTE=0 filter active)
2025-05-30 03:14:49,928 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.021s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-08'' 
                    AND expiry_date = ''2024-04-08''
                    LIMIT 1', 1, 200) ...
2025-05-30 03:14:49,928 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-08 - Not an expiry day (DTE=0 filter active)
2025-05-30 03:14:49,949 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-09'' 
                    AND expiry_date = ''2024-04-09''
                    LIMIT 1', 1, 200) ...
2025-05-30 03:14:49,949 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-09 - Not an expiry day (DTE=0 filter active)
2025-05-30 03:14:49,971 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.022s, returned 1 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-10'' 
                    AND expiry_date = ''2024-04-10''
                    LIMIT 1', 1, 200) ...
2025-05-30 03:14:49,971 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Processing date 2024-04-10 - Is an expiry day (DTE=0 filter active)
2025-05-30 03:14:49,972 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 1 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-30 03:14:50,007 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.034s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-10'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-30 03:14:50,040 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.033s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-10'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-30 03:14:50,040 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Query results: entry_df size=1, exit_df size=1
2025-05-30 03:14:50,043 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-30 03:14:50,261 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 03:14:50,577 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.315s, returned 25020 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-30 03:14:50,687 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 1 of 3
2025-05-30 03:14:50,687 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-30 03:14:50,687 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 2 of 3
2025-05-30 03:14:50,687 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-30 03:14:50,687 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 3 of 3
2025-05-30 03:14:50,687 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-30 03:14:50,688 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 1
2025-05-30 03:14:50,688 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 2 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-30 03:14:50,724 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.035s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-10'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-30 03:14:50,754 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.030s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-10'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-30 03:14:50,754 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Query results: entry_df size=1, exit_df size=1
2025-05-30 03:14:50,756 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-30 03:14:50,971 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 03:14:51,283 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.311s, returned 25020 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-30 03:14:51,394 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 1 of 3
2025-05-30 03:14:51,394 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-30 03:14:51,394 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 2 of 3
2025-05-30 03:14:51,394 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-30 03:14:51,394 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 3 of 3
2025-05-30 03:14:51,395 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-30 03:14:51,395 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 2
2025-05-30 03:14:51,395 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 3 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-30 03:14:51,456 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.060s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-10'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike + (2.0 * 50)) AND oc.ce_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-30 03:14:51,488 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.032s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-10'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike + (2.0 * 50)) AND oc.ce_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-30 03:14:51,488 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Query results: entry_df size=1, exit_df size=1
2025-05-30 03:14:51,490 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-30 03:14:51,707 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 03:14:52,037 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.330s, returned 25020 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-30 03:14:52,118 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 1 of 3
2025-05-30 03:14:52,119 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-30 03:14:52,119 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 2 of 3
2025-05-30 03:14:52,119 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-30 03:14:52,119 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 3 of 3
2025-05-30 03:14:52,119 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-30 03:14:52,120 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 3
2025-05-30 03:14:52,120 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 4 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-30 03:14:52,158 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.037s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-10'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike - (2.0 * 50)) AND oc.pe_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-30 03:14:52,219 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.061s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-10'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike - (2.0 * 50)) AND oc.pe_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-30 03:14:52,219 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Query results: entry_df size=1, exit_df size=1
2025-05-30 03:14:52,222 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-30 03:14:52,458 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 03:14:52,754 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.295s, returned 25020 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-30 03:14:52,829 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 1 of 3
2025-05-30 03:14:52,829 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-30 03:14:52,829 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 2 of 3
2025-05-30 03:14:52,829 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-30 03:14:52,830 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 3 of 3
2025-05-30 03:14:52,830 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-30 03:14:52,830 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 4
2025-05-30 03:14:52,869 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.039s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-12'' 
                    AND expiry_date = ''2024-04-12''
                    LIMIT 1', 1, 200) ...
2025-05-30 03:14:52,870 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-12 - Not an expiry day (DTE=0 filter active)
2025-05-30 03:14:52,890 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.021s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-15'' 
                    AND expiry_date = ''2024-04-15''
                    LIMIT 1', 1, 200) ...
2025-05-30 03:14:52,891 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-15 - Not an expiry day (DTE=0 filter active)
2025-05-30 03:14:52,910 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-16'' 
                    AND expiry_date = ''2024-04-16''
                    LIMIT 1', 1, 200) ...
2025-05-30 03:14:52,910 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-16 - Not an expiry day (DTE=0 filter active)
2025-05-30 03:14:52,928 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-18'' 
                    AND expiry_date = ''2024-04-18''
                    LIMIT 1', 1, 200) ...
2025-05-30 03:14:52,928 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Processing date 2024-04-18 - Is an expiry day (DTE=0 filter active)
2025-05-30 03:14:52,928 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 1 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-30 03:14:52,983 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.055s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-18'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-30 03:14:53,019 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.036s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-18'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-30 03:14:53,020 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Query results: entry_df size=1, exit_df size=1
2025-05-30 03:14:53,021 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-30 03:14:53,246 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 03:14:53,587 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.340s, returned 27518 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-30 03:14:53,670 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 1 of 3
2025-05-30 03:14:53,671 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-30 03:14:53,671 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 2 of 3
2025-05-30 03:14:53,671 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-30 03:14:53,671 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 3 of 3
2025-05-30 03:14:53,671 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-30 03:14:53,671 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 1
2025-05-30 03:14:53,671 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 2 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-30 03:14:53,731 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.059s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-18'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-30 03:14:53,761 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.030s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-18'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-30 03:14:53,762 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Query results: entry_df size=1, exit_df size=1
2025-05-30 03:14:53,763 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-30 03:14:53,990 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 03:14:54,328 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.338s, returned 27518 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-30 03:14:54,408 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 1 of 3
2025-05-30 03:14:54,408 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-30 03:14:54,408 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 2 of 3
2025-05-30 03:14:54,408 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-30 03:14:54,408 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 3 of 3
2025-05-30 03:14:54,408 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-30 03:14:54,409 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 2
2025-05-30 03:14:54,409 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 3 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-30 03:14:54,451 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.042s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-18'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike + (2.0 * 50)) AND oc.ce_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-30 03:14:54,490 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.039s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-18'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike + (2.0 * 50)) AND oc.ce_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-30 03:14:54,490 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Query results: entry_df size=1, exit_df size=1
2025-05-30 03:14:54,492 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-30 03:14:54,716 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 03:14:55,131 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.415s, returned 27518 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-30 03:14:55,217 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 1 of 3
2025-05-30 03:14:55,217 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-30 03:14:55,217 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 2 of 3
2025-05-30 03:14:55,217 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-30 03:14:55,217 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 3 of 3
2025-05-30 03:14:55,217 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-30 03:14:55,218 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 3
2025-05-30 03:14:55,218 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 4 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-30 03:14:55,256 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.038s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-18'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike - (2.0 * 50)) AND oc.pe_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-30 03:14:55,311 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.054s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-18'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike - (2.0 * 50)) AND oc.pe_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-30 03:14:55,311 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Query results: entry_df size=1, exit_df size=1
2025-05-30 03:14:55,312 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-30 03:14:55,532 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 03:14:55,881 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.348s, returned 27518 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-30 03:14:55,961 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 1 of 3
2025-05-30 03:14:55,962 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-30 03:14:55,962 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 2 of 3
2025-05-30 03:14:55,962 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-30 03:14:55,962 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 3 of 3
2025-05-30 03:14:55,962 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-30 03:14:55,962 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 4
2025-05-30 03:14:55,997 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.035s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-19'' 
                    AND expiry_date = ''2024-04-19''
                    LIMIT 1', 1, 200) ...
2025-05-30 03:14:55,998 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-19 - Not an expiry day (DTE=0 filter active)
2025-05-30 03:14:56,022 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.024s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-22'' 
                    AND expiry_date = ''2024-04-22''
                    LIMIT 1', 1, 200) ...
2025-05-30 03:14:56,022 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-22 - Not an expiry day (DTE=0 filter active)
2025-05-30 03:14:56,063 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.041s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-23'' 
                    AND expiry_date = ''2024-04-23''
                    LIMIT 1', 1, 200) ...
2025-05-30 03:14:56,063 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-23 - Not an expiry day (DTE=0 filter active)
2025-05-30 03:14:56,086 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.023s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-24'' 
                    AND expiry_date = ''2024-04-24''
                    LIMIT 1', 1, 200) ...
2025-05-30 03:14:56,086 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-24 - Not an expiry day (DTE=0 filter active)
2025-05-30 03:14:56,121 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.035s, returned 1 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-25'' 
                    AND expiry_date = ''2024-04-25''
                    LIMIT 1', 1, 200) ...
2025-05-30 03:14:56,121 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Processing date 2024-04-25 - Is an expiry day (DTE=0 filter active)
2025-05-30 03:14:56,121 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 1 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-30 03:14:56,173 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.050s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-25'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-30 03:14:56,226 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.052s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-25'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-30 03:14:56,226 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Query results: entry_df size=1, exit_df size=1
2025-05-30 03:14:56,228 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-30 03:14:56,493 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 03:14:56,902 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.409s, returned 29966 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-30 03:14:56,995 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 1 of 3
2025-05-30 03:14:56,995 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-30 03:14:56,995 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 2 of 3
2025-05-30 03:14:56,995 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-30 03:14:56,995 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 3 of 3
2025-05-30 03:14:56,996 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-30 03:14:56,996 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 1
2025-05-30 03:14:56,996 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 2 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-30 03:14:57,051 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.054s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-25'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-30 03:14:57,102 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.051s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-25'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-30 03:14:57,102 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Query results: entry_df size=1, exit_df size=1
2025-05-30 03:14:57,104 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-30 03:14:57,336 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 03:14:57,708 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.371s, returned 29966 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-30 03:14:57,814 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 1 of 3
2025-05-30 03:14:57,815 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-30 03:14:57,815 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 2 of 3
2025-05-30 03:14:57,815 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-30 03:14:57,815 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 3 of 3
2025-05-30 03:14:57,815 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-30 03:14:57,816 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 2
2025-05-30 03:14:57,816 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 3 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-30 03:14:57,871 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.055s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-25'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike + (2.0 * 50)) AND oc.ce_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-30 03:14:57,909 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.038s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-25'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike + (2.0 * 50)) AND oc.ce_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-30 03:14:57,909 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Query results: entry_df size=1, exit_df size=1
2025-05-30 03:14:57,911 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-30 03:14:58,137 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 03:14:58,512 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.374s, returned 29966 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-30 03:14:58,600 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 1 of 3
2025-05-30 03:14:58,600 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-30 03:14:58,600 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 2 of 3
2025-05-30 03:14:58,600 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-30 03:14:58,600 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 3 of 3
2025-05-30 03:14:58,600 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-30 03:14:58,601 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 3
2025-05-30 03:14:58,601 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 4 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-30 03:14:58,654 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.052s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-25'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike - (2.0 * 50)) AND oc.pe_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-30 03:14:58,709 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.055s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-04-25'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike - (2.0 * 50)) AND oc.pe_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-30 03:14:58,709 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Query results: entry_df size=1, exit_df size=1
2025-05-30 03:14:58,710 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-30 03:14:58,932 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 03:14:59,306 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.374s, returned 29966 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-30 03:14:59,394 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 1 of 3
2025-05-30 03:14:59,394 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-30 03:14:59,394 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 2 of 3
2025-05-30 03:14:59,395 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-30 03:14:59,395 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 3 of 3
2025-05-30 03:14:59,395 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-30 03:14:59,395 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 4
2025-05-30 03:14:59,432 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.036s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-26'' 
                    AND expiry_date = ''2024-04-26''
                    LIMIT 1', 1, 200) ...
2025-05-30 03:14:59,432 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-26 - Not an expiry day (DTE=0 filter active)
2025-05-30 03:14:59,460 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.029s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-29'' 
                    AND expiry_date = ''2024-04-29''
                    LIMIT 1', 1, 200) ...
2025-05-30 03:14:59,461 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-29 - Not an expiry day (DTE=0 filter active)
2025-05-30 03:14:59,495 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.035s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2024-04-30'' 
                    AND expiry_date = ''2024-04-30''
                    LIMIT 1', 1, 200) ...
2025-05-30 03:14:59,495 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2024-04-30 - Not an expiry day (DTE=0 filter active)
2025-05-30 03:14:59,496 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Generated 16 trade records via model-driven path
2025-05-30 03:14:59,496 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Calculating tick-by-tick P&L using batched queries for performance optimization
2025-05-30 03:14:59,496 - backtester_stable.BTRUN.strategies.tick_pnl_batch - INFO - Starting batched tick P&L calculation for 16 trades
2025-05-30 03:14:59,496 - backtester_stable.BTRUN.strategies.tick_pnl_batch - INFO - Processing date 240404 with 4 trades
2025-05-30 03:14:59,552 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.056s, returned 1125 rows. Query: SELECT SUBSTRING('SELECT 
        EXTRACT(HOUR FROM trade_time) * 10000 + 
        EXTRACT(MINUTE FROM trade_time) * 100 + 
        EXTRACT(SECOND FROM trade_time) AS trade_time_int,
        strike,
        expiry_date', 1, 200) ...
2025-05-30 03:14:59,618 - backtester_stable.BTRUN.strategies.tick_pnl_batch - INFO - Loaded 1125 price points for 375 unique timestamps
2025-05-30 03:14:59,620 - backtester_stable.BTRUN.strategies.tick_pnl_batch - INFO - Calculated tick P&L for date 240404: 375 timestamps
2025-05-30 03:14:59,620 - backtester_stable.BTRUN.strategies.tick_pnl_batch - INFO - Processing date 240410 with 4 trades
2025-05-30 03:14:59,676 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.056s, returned 1125 rows. Query: SELECT SUBSTRING('SELECT 
        EXTRACT(HOUR FROM trade_time) * 10000 + 
        EXTRACT(MINUTE FROM trade_time) * 100 + 
        EXTRACT(SECOND FROM trade_time) AS trade_time_int,
        strike,
        expiry_date', 1, 200) ...
2025-05-30 03:14:59,743 - backtester_stable.BTRUN.strategies.tick_pnl_batch - INFO - Loaded 1125 price points for 375 unique timestamps
2025-05-30 03:14:59,746 - backtester_stable.BTRUN.strategies.tick_pnl_batch - INFO - Calculated tick P&L for date 240410: 375 timestamps
2025-05-30 03:14:59,746 - backtester_stable.BTRUN.strategies.tick_pnl_batch - INFO - Processing date 240418 with 4 trades
2025-05-30 03:14:59,801 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.055s, returned 1125 rows. Query: SELECT SUBSTRING('SELECT 
        EXTRACT(HOUR FROM trade_time) * 10000 + 
        EXTRACT(MINUTE FROM trade_time) * 100 + 
        EXTRACT(SECOND FROM trade_time) AS trade_time_int,
        strike,
        expiry_date', 1, 200) ...
2025-05-30 03:14:59,867 - backtester_stable.BTRUN.strategies.tick_pnl_batch - INFO - Loaded 1125 price points for 375 unique timestamps
2025-05-30 03:14:59,869 - backtester_stable.BTRUN.strategies.tick_pnl_batch - INFO - Calculated tick P&L for date 240418: 375 timestamps
2025-05-30 03:14:59,869 - backtester_stable.BTRUN.strategies.tick_pnl_batch - INFO - Processing date 240425 with 4 trades
2025-05-30 03:14:59,921 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.051s, returned 1125 rows. Query: SELECT SUBSTRING('SELECT 
        EXTRACT(HOUR FROM trade_time) * 10000 + 
        EXTRACT(MINUTE FROM trade_time) * 100 + 
        EXTRACT(SECOND FROM trade_time) AS trade_time_int,
        strike,
        expiry_date', 1, 200) ...
2025-05-30 03:14:59,984 - backtester_stable.BTRUN.strategies.tick_pnl_batch - INFO - Loaded 1125 price points for 375 unique timestamps
2025-05-30 03:14:59,988 - backtester_stable.BTRUN.strategies.tick_pnl_batch - INFO - Calculated tick P&L for date 240425: 375 timestamps
2025-05-30 03:14:59,988 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Successfully calculated tick P&L data: 4 dates, 1500 total timestamps
2025-05-30 03:15:00,007 - backtester_stable.BTRUN.builders - WARNING - utils.MARGIN_INFO not found or empty - margin requirements will be 0. Attempting to populate.
2025-05-30 03:15:00,007 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'margin_symbol_info.json' at /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-30 03:15:00,007 - backtester_stable.BTRUN.core.utils - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json (Broker: angel)
2025-05-30 03:15:00,008 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.MARGIN_INFO for angel.
2025-05-30 03:15:00,008 - backtester_stable.BTRUN.builders - ERROR - utils.MARGIN_INFO still empty after populate attempt. Margin req will be 0.
2025-05-30 03:15:00,011 - backtester_stable.BTRUN.builders - INFO - Total portfolio margin requirement: 0.00
2025-05-30 03:15:00,013 - backtester_stable.BTRUN.builders - INFO - Backtest response parsed.
2025-05-30 03:15:00,056 - backtester_stable.BTRUN.core.runtime - INFO - Generated metrics_df with 50 records for strategies: ['Combined' 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL']
/srv/samba/shared/bt/backtester_stable/BTRUN/core/stats.py:197: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd.rename(columns={"entryDate": "Date", "bookedPnL": "PNL"}, inplace=True)
/srv/samba/shared/bt/backtester_stable/BTRUN/core/stats.py:198: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['Date'] = pd.to_datetime(temp_df_pd['Date'])
/srv/samba/shared/bt/backtester_stable/BTRUN/core/stats.py:199: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['year'] = temp_df_pd['Date'].dt.year
/srv/samba/shared/bt/backtester_stable/BTRUN/core/stats.py:200: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['day_name'] = temp_df_pd['Date'].dt.strftime("%A")
/srv/samba/shared/bt/backtester_stable/BTRUN/core/stats.py:235: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd.rename(columns={"entryDate": "Date", "bookedPnL": "PNL"}, inplace=True)
/srv/samba/shared/bt/backtester_stable/BTRUN/core/stats.py:236: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['Date'] = pd.to_datetime(temp_df_pd['Date'])
/srv/samba/shared/bt/backtester_stable/BTRUN/core/stats.py:237: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['year'] = temp_df_pd['Date'].dt.year
/srv/samba/shared/bt/backtester_stable/BTRUN/core/stats.py:238: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['month_name'] = temp_df_pd['Date'].dt.strftime("%B") # Full month name
/srv/samba/shared/bt/backtester_stable/BTRUN/core/stats.py:197: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd.rename(columns={"entryDate": "Date", "bookedPnL": "PNL"}, inplace=True)
/srv/samba/shared/bt/backtester_stable/BTRUN/core/stats.py:198: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['Date'] = pd.to_datetime(temp_df_pd['Date'])
/srv/samba/shared/bt/backtester_stable/BTRUN/core/stats.py:199: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['year'] = temp_df_pd['Date'].dt.year
/srv/samba/shared/bt/backtester_stable/BTRUN/core/stats.py:200: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['day_name'] = temp_df_pd['Date'].dt.strftime("%A")
/srv/samba/shared/bt/backtester_stable/BTRUN/core/stats.py:235: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd.rename(columns={"entryDate": "Date", "bookedPnL": "PNL"}, inplace=True)
/srv/samba/shared/bt/backtester_stable/BTRUN/core/stats.py:236: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['Date'] = pd.to_datetime(temp_df_pd['Date'])
/srv/samba/shared/bt/backtester_stable/BTRUN/core/stats.py:237: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['year'] = temp_df_pd['Date'].dt.year
/srv/samba/shared/bt/backtester_stable/BTRUN/core/stats.py:238: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['month_name'] = temp_df_pd['Date'].dt.strftime("%B") # Full month name
2025-05-30 03:15:00,120 - __main__ - INFO - Worker completed batch: NIF0DTE_fulldaterange for portfolio 'NIF0DTE' in 13.99s
2025-05-30 03:15:00,120 - __main__ - INFO - Combining 1 batch(es) for portfolio: NIF0DTE
/srv/samba/shared/bt/backtester_stable/BTRUN/core/stats.py:235: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd.rename(columns={"entryDate": "Date", "bookedPnL": "PNL"}, inplace=True)
/srv/samba/shared/bt/backtester_stable/BTRUN/core/stats.py:236: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['Date'] = pd.to_datetime(temp_df_pd['Date'])
/srv/samba/shared/bt/backtester_stable/BTRUN/core/stats.py:237: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['year'] = temp_df_pd['Date'].dt.year
/srv/samba/shared/bt/backtester_stable/BTRUN/core/stats.py:238: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  temp_df_pd['month_name'] = temp_df_pd['Date'].dt.strftime("%B") # Full month name
2025-05-30 03:15:00,147 - __main__ - INFO - Attempting to save combined backtest results for 1 portfolio(s) to /srv/samba/shared/1month_gpu_backtest_20250529_214441.xlsx
2025-05-30 03:15:00,147 - backtester_stable.BTRUN.core.io - INFO - Writing results to /srv/samba/shared/1month_gpu_backtest_20250529_214441.xlsx
2025-05-30 03:15:00,148 - backtester_stable.BTRUN.core.io - INFO - 2025-05-29 21:45:00.148384, Started writing stats to excel file: /srv/samba/shared/1month_gpu_backtest_20250529_214441.xlsx
2025-05-30 03:15:00,166 - backtester_stable.BTRUN.core.io - INFO - Generated PortfolioParameter sheet from PortfolioSetting
2025-05-30 03:15:00,167 - backtester_stable.BTRUN.core.io - INFO - Added Metrics sheet
2025-05-30 03:15:00,192 - backtester_stable.BTRUN.core.io - INFO - Added PORTFOLIO Trans sheet
2025-05-30 03:15:00,197 - backtester_stable.BTRUN.core.io - INFO - Added PORTFOLIO Results sheet
2025-05-30 03:15:00,197 - backtester_stable.BTRUN.core.io - INFO - 2025-05-29 21:45:00.197885, Excel file prepared successfully, Time taken: 0.05s
2025-05-30 03:15:00,217 - backtester_stable.BTRUN.core.io - INFO - Successfully wrote results to /srv/samba/shared/1month_gpu_backtest_20250529_214441.xlsx
2025-05-30 03:15:00,217 - __main__ - INFO - Successfully saved backtest results to /srv/samba/shared/1month_gpu_backtest_20250529_214441.xlsx
2025-05-30 03:15:00,217 - __main__ - INFO - Backtest results saved to /srv/samba/shared/1month_gpu_backtest_20250529_214441.xlsx

✅ Backtest completed successfully!

================================================================================
BACKTEST SUMMARY
================================================================================
Start time: 2025-05-29 21:44:43
End time: 2025-05-29 21:45:00
Duration: 0:00:17.012793
Output file: /srv/samba/shared/1month_gpu_backtest_20250529_214441.xlsx
File size: 0.01 MB

Sheets in output: ['PortfolioParameter', 'Metrics', 'PORTFOLIO Trans', 'PORTFOLIO Results']

✅ All TBS column fixes are active:
   - DTE filtering
   - StrikeSelectionTime
   - Weekday filtering
   - ATM offset logic
   - Query batching
