2025-05-26 11:57:27,162 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-26 11:57:27,162 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-26 11:57:27,163 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/../models', '/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages', '/srv/samba/shared']
2025-05-26 11:57:27,163 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-26 11:57:27,163 - __main__ - DEBUG - GPU enabled: False
2025-05-26 11:57:27,165 - __main__ - INFO - GPU acceleration enabled: False
2025-05-26 11:57:27,165 - __main__ - INFO - HeavyDB integration available (Host: 127.0.0.1)
2025-05-26 11:57:27,385 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-26 11:57:27,385 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-26 11:57:27,385 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-26 11:57:27,385 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-26 11:57:27,386 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-26 11:57:27,386 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-26 11:57:27,392 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-26 11:57:27,392 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-26 11:57:27,393 - backtester_stable.BTRUN.core.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-26 11:57:27,393 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-26 11:57:27,393 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-26 11:57:27,393 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-26 11:57:27,393 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-26 11:57:27,393 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-26 11:57:27,393 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-26 11:57:27,393 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-26 11:57:27,393 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-26 11:57:27,393 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-26 11:57:27,517 - __main__ - INFO - Successfully parsed 1 portfolio(s) using modern parser.
2025-05-26 11:57:27,517 - __main__ - INFO - Queueing portfolio: NIF0DTE (from modern_portfolio_parser)
2025-05-26 11:57:27,517 - __main__ - INFO - Executing 1 portfolios sequentially (multiprocessing temporarily disabled for debugging)
2025-05-26 11:57:27,517 - __main__ - INFO - Processing payload 1/1: NIF0DTE
2025-05-26 11:57:27,517 - backtester_stable.BTRUN.core.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'NIF0DTE'
2025-05-26 11:57:27,737 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-26 11:57:27,767 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.029s, returned 563 rows. Query: SELECT SUBSTRING('SELECT DISTINCT trade_date FROM nifty_option_chain', 1, 200) ...
2025-05-26 11:57:27,809 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - Cached 563 distinct trade dates
2025-05-26 11:57:27,810 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] portfolio_model_dict keys: ['portfolio_name', 'start_date', 'end_date', 'slippage_percent', 'margin_multiplier', 'is_tick_bt', 'strategies', 'extra_params']
2025-05-26 11:57:27,810 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 1 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 11:57:27,810 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-26 11:57:27,810 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-26 11:57:27,849 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.038s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-26 11:57:27,888 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.039s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-26 11:57:27,888 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Query results: entry_df size=1, exit_df size=1
2025-05-26 11:57:27,889 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Got entry and exit data, proceeding...
2025-05-26 11:57:27,889 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
                        trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523450CE
ce_open                          102.85
ce_high                          106.65
ce_low                            97.85
ce_close                         100.75
ce_volume                       1015050
ce_oi                            978075
ce_coi                                0
ce_iv                             13.44
ce_delta                           0.48
ce_gamma                         0.0015
ce_theta                         -23.27
ce_vega                            7.95
ce_rho                          81.3115
pe_symbol           NIFTY03APR2523450PE
pe_open                           118.2
pe_high                           122.3
pe_low                            112.2
pe_close                          115.7
pe_volume                        877125
pe_oi                           1618950
pe_coi                                0
pe_iv                             13.74
pe_delta                          -0.52
pe_gamma                         0.0015
pe_theta                          -17.3
pe_vega                            7.95
pe_rho                         -88.3744
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-26 11:57:27,890 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23200.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                           123.6
ce_high                           124.3
ce_low                           119.25
ce_close                          124.2
ce_volume                        326775
ce_oi                           4374450
ce_coi                           193725
ce_iv                             14.99
ce_delta                           0.52
ce_gamma                         0.0013
ce_theta                         -25.57
ce_vega                            7.87
ce_rho                          86.2799
pe_symbol           NIFTY03APR2523200PE
pe_open                           100.2
pe_high                           105.2
pe_low                            99.55
pe_close                         100.75
pe_volume                        526875
pe_oi                           5030025
pe_coi                           101175
pe_iv                             13.55
pe_delta                          -0.48
pe_gamma                         0.0015
pe_theta                         -17.08
pe_vega                            7.87
pe_rho                         -81.3242
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-26 11:57:27,890 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-26 11:57:27,891 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-26 11:57:27,891 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-26 11:57:27,891 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Entering risk rules block
2025-05-26 11:57:27,891 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-26 11:57:27,891 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140168341769120
2025-05-26 11:57:27,891 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140168341769120, OptionType.PUT ID: 140168341769232
2025-05-26 11:57:27,891 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-26 11:57:27,891 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-26 11:57:27,891 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-26 11:57:27,893 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-26 11:57:28,195 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.302s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-26 11:57:28,197 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-26 11:57:28,198 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-26 11:57:28,256 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-26 11:57:28,260 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 1 - Tick data for specific strike 23450.0 is EMPTY after filtering windowed data.
2025-05-26 11:57:28,260 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Starting risk rules loop, has 3 rules
2025-05-26 11:57:28,260 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 1 of 3
2025-05-26 11:57:28,260 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-26 11:57:28,264 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - evaluate_risk_rule returned: triggered=True
2025-05-26 11:57:28,265 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Breaking out of risk rules loop due to triggered rule
2025-05-26 11:57:28,266 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-26 11:57:28,266 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Reached trade building section (outside all conditionals)
2025-05-26 11:57:28,266 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 1
2025-05-26 11:57:28,266 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 1, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 11:57:28,266 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23420.45, 'atm_strike': 23450.0, 'strike': 23450.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523450CE', 'ce_open': 102.85, 'ce_high': 106.65, 'ce_low': 97.85, 'ce_close': 100.75, 'ce_volume': 1015050, 'ce_oi': 978075, 'ce_coi': 0, 'ce_iv': 13.44, 'ce_delta': 0.48, 'ce_gamma': 0.0015, 'ce_theta': -23.27, 'ce_vega': 7.95, 'ce_rho': 81.3115, 'pe_symbol': 'NIFTY03APR2523450PE', 'pe_open': 118.2, 'pe_high': 122.3, 'pe_low': 112.2, 'pe_close': 115.7, 'pe_volume': 877125, 'pe_oi': 1618950, 'pe_coi': 0, 'pe_iv': 13.74, 'pe_delta': -0.52, 'pe_gamma': 0.0015, 'pe_theta': -17.3, 'pe_vega': 7.95, 'pe_rho': -88.3744, 'future_open': 23534.5, 'future_high': 23543.95, 'future_low': 23527.0, 'future_close': 23536.75, 'future_volume': 86475, 'future_oi': 12605100, 'future_coi': 0}
2025-05-26 11:57:28,266 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23195.3, 'atm_strike': 23200.0, 'strike': 23200.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523200CE', 'ce_open': 123.6, 'ce_high': 124.3, 'ce_low': 119.25, 'ce_close': 124.2, 'ce_volume': 326775, 'ce_oi': 4374450, 'ce_coi': 193725, 'ce_iv': 14.99, 'ce_delta': 0.52, 'ce_gamma': 0.0013, 'ce_theta': -25.57, 'ce_vega': 7.87, 'ce_rho': 86.2799, 'pe_symbol': 'NIFTY03APR2523200PE', 'pe_open': 100.2, 'pe_high': 105.2, 'pe_low': 99.55, 'pe_close': 100.75, 'pe_volume': 526875, 'pe_oi': 5030025, 'pe_coi': 101175, 'pe_iv': 13.55, 'pe_delta': -0.48, 'pe_gamma': 0.0015, 'pe_theta': -17.08, 'pe_vega': 7.87, 'pe_rho': -81.3242, 'future_open': 23347.0, 'future_high': 23348.85, 'future_low': 23339.0, 'future_close': 23348.1, 'future_volume': 19050, 'future_oi': 13928550, 'future_coi': -28275, 'close': 1450.2, 'datetime': Timestamp('2025-04-01 12:00:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-26 11:57:28,266 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=CALL, Transaction=SELL, Lots=1
2025-05-26 11:57:28,266 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-26 11:57:28,267 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-26 11:57:28,267 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-26 11:57:28,267 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -1172.5000000000002, PnL (Slippage): -1172.5000000000002, Net PnL: -1172.5000000000002
2025-05-26 11:57:28,267 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-26 11:57:28,267 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '1', 'entry_date': '2025-04-01', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2025-04-01', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23450, 'instrument_type': 'CE', 'side': 'SELL', 'filled_quantity': 50.0, 'entry_price': 100.75, 'exit_price': 124.2, 'points': -23.450000000000003, 'pointsAfterSlippage': -23.450000000000003, 'pnl': -1172.5000000000002, 'pnlAfterSlippage': -1172.5000000000002, 'expenses': 0.0, 'netPnlAfterExpenses': -1172.5000000000002, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23420.45, 'index_exit_price': 23195.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-01 09:16:00', 'exit_datetime': '2025-04-01 12:00:00'}
2025-05-26 11:57:28,267 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 1, about to append. Total records before: 0
2025-05-26 11:57:28,267 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 1. Total records now: 1
2025-05-26 11:57:28,267 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 2 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 11:57:28,267 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-26 11:57:28,267 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-26 11:57:28,292 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.024s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-26 11:57:28,315 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.023s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-26 11:57:28,315 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Query results: entry_df size=1, exit_df size=1
2025-05-26 11:57:28,316 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Got entry and exit data, proceeding...
2025-05-26 11:57:28,317 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
                        trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523450CE
ce_open                          102.85
ce_high                          106.65
ce_low                            97.85
ce_close                         100.75
ce_volume                       1015050
ce_oi                            978075
ce_coi                                0
ce_iv                             13.44
ce_delta                           0.48
ce_gamma                         0.0015
ce_theta                         -23.27
ce_vega                            7.95
ce_rho                          81.3115
pe_symbol           NIFTY03APR2523450PE
pe_open                           118.2
pe_high                           122.3
pe_low                            112.2
pe_close                          115.7
pe_volume                        877125
pe_oi                           1618950
pe_coi                                0
pe_iv                             13.74
pe_delta                          -0.52
pe_gamma                         0.0015
pe_theta                          -17.3
pe_vega                            7.95
pe_rho                         -88.3744
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-26 11:57:28,317 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23200.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                           123.6
ce_high                           124.3
ce_low                           119.25
ce_close                          124.2
ce_volume                        326775
ce_oi                           4374450
ce_coi                           193725
ce_iv                             14.99
ce_delta                           0.52
ce_gamma                         0.0013
ce_theta                         -25.57
ce_vega                            7.87
ce_rho                          86.2799
pe_symbol           NIFTY03APR2523200PE
pe_open                           100.2
pe_high                           105.2
pe_low                            99.55
pe_close                         100.75
pe_volume                        526875
pe_oi                           5030025
pe_coi                           101175
pe_iv                             13.55
pe_delta                          -0.48
pe_gamma                         0.0015
pe_theta                         -17.08
pe_vega                            7.87
pe_rho                         -81.3242
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-26 11:57:28,317 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-26 11:57:28,317 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-26 11:57:28,317 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-26 11:57:28,318 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Entering risk rules block
2025-05-26 11:57:28,318 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-26 11:57:28,318 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140168341769232
2025-05-26 11:57:28,319 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140168341769120, OptionType.PUT ID: 140168341769232
2025-05-26 11:57:28,319 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-26 11:57:28,319 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-26 11:57:28,319 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-26 11:57:28,320 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-26 11:57:28,643 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.322s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-26 11:57:28,644 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-26 11:57:28,645 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-26 11:57:28,702 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-26 11:57:28,705 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 2 - Tick data for specific strike 23450.0 is EMPTY after filtering windowed data.
2025-05-26 11:57:28,705 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Starting risk rules loop, has 3 rules
2025-05-26 11:57:28,705 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 1 of 3
2025-05-26 11:57:28,705 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-26 11:57:28,715 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-26 11:57:28,716 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 1
2025-05-26 11:57:28,716 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 2 of 3
2025-05-26 11:57:28,716 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type TAKEPROFIT
2025-05-26 11:57:28,726 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-26 11:57:28,726 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 2
2025-05-26 11:57:28,726 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 3 of 3
2025-05-26 11:57:28,726 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type TRAIL
2025-05-26 11:57:28,729 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-26 11:57:28,729 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 3
2025-05-26 11:57:28,729 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-26 11:57:28,729 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Reached trade building section (outside all conditionals)
2025-05-26 11:57:28,729 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 2
2025-05-26 11:57:28,729 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 2, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 11:57:28,729 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23420.45, 'atm_strike': 23450.0, 'strike': 23450.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523450CE', 'ce_open': 102.85, 'ce_high': 106.65, 'ce_low': 97.85, 'ce_close': 100.75, 'ce_volume': 1015050, 'ce_oi': 978075, 'ce_coi': 0, 'ce_iv': 13.44, 'ce_delta': 0.48, 'ce_gamma': 0.0015, 'ce_theta': -23.27, 'ce_vega': 7.95, 'ce_rho': 81.3115, 'pe_symbol': 'NIFTY03APR2523450PE', 'pe_open': 118.2, 'pe_high': 122.3, 'pe_low': 112.2, 'pe_close': 115.7, 'pe_volume': 877125, 'pe_oi': 1618950, 'pe_coi': 0, 'pe_iv': 13.74, 'pe_delta': -0.52, 'pe_gamma': 0.0015, 'pe_theta': -17.3, 'pe_vega': 7.95, 'pe_rho': -88.3744, 'future_open': 23534.5, 'future_high': 23543.95, 'future_low': 23527.0, 'future_close': 23536.75, 'future_volume': 86475, 'future_oi': 12605100, 'future_coi': 0}
2025-05-26 11:57:28,729 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(12, 0), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23195.3, 'atm_strike': 23200.0, 'strike': 23200.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523200CE', 'ce_open': 123.6, 'ce_high': 124.3, 'ce_low': 119.25, 'ce_close': 124.2, 'ce_volume': 326775, 'ce_oi': 4374450, 'ce_coi': 193725, 'ce_iv': 14.99, 'ce_delta': 0.52, 'ce_gamma': 0.0013, 'ce_theta': -25.57, 'ce_vega': 7.87, 'ce_rho': 86.2799, 'pe_symbol': 'NIFTY03APR2523200PE', 'pe_open': 100.2, 'pe_high': 105.2, 'pe_low': 99.55, 'pe_close': 100.75, 'pe_volume': 526875, 'pe_oi': 5030025, 'pe_coi': 101175, 'pe_iv': 13.55, 'pe_delta': -0.48, 'pe_gamma': 0.0015, 'pe_theta': -17.08, 'pe_vega': 7.87, 'pe_rho': -81.3242, 'future_open': 23347.0, 'future_high': 23348.85, 'future_low': 23339.0, 'future_close': 23348.1, 'future_volume': 19050, 'future_oi': 13928550, 'future_coi': -28275}
2025-05-26 11:57:28,729 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=SELL, Lots=1
2025-05-26 11:57:28,729 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-26 11:57:28,729 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-26 11:57:28,729 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-26 11:57:28,730 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: 747.5000000000001, PnL (Slippage): 747.5000000000001, Net PnL: 747.5000000000001
2025-05-26 11:57:28,730 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-26 11:57:28,730 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '2', 'entry_date': '2025-04-01', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2025-04-01', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23450, 'instrument_type': 'PE', 'side': 'SELL', 'filled_quantity': 50.0, 'entry_price': 115.7, 'exit_price': 100.75, 'points': 14.950000000000003, 'pointsAfterSlippage': 14.950000000000003, 'pnl': 747.5000000000001, 'pnlAfterSlippage': 747.5000000000001, 'expenses': 0.0, 'netPnlAfterExpenses': 747.5000000000001, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23420.45, 'index_exit_price': 23195.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-01 09:16:00', 'exit_datetime': '2025-04-01 12:00:00'}
2025-05-26 11:57:28,730 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 2, about to append. Total records before: 1
2025-05-26 11:57:28,730 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 2. Total records now: 2
2025-05-26 11:57:28,730 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 3 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 11:57:28,730 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-26 11:57:28,730 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-26 11:57:28,764 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.034s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-26 11:57:28,804 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.040s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-26 11:57:28,805 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Query results: entry_df size=1, exit_df size=1
2025-05-26 11:57:28,805 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Got entry and exit data, proceeding...
2025-05-26 11:57:28,806 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
                        trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23550.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY03APR2523550CE
ce_open                           61.15
ce_high                           61.75
ce_low                             55.0
ce_close                          56.45
ce_volume                       1147575
ce_oi                           2744250
ce_coi                                0
ce_iv                             12.93
ce_delta                           0.34
ce_gamma                         0.0014
ce_theta                         -19.86
ce_vega                            7.25
ce_rho                          56.1949
pe_symbol           NIFTY03APR2523550PE
pe_open                           174.0
pe_high                           180.1
pe_low                            167.8
pe_close                          175.5
pe_volume                        326550
pe_oi                           1975725
pe_coi                                0
pe_iv                             13.83
pe_delta                          -0.66
pe_gamma                         0.0013
pe_theta                         -14.93
pe_vega                            7.34
pe_rho                        -112.5433
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-26 11:57:28,807 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23300.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY03APR2523300CE
ce_open                           75.59
ce_high                           76.15
ce_low                            73.15
ce_close                          76.15
ce_volume                        250950
ce_oi                           7928250
ce_coi                           109950
ce_iv                             14.54
ce_delta                           0.37
ce_gamma                         0.0013
ce_theta                         -23.12
ce_vega                            7.53
ce_rho                          63.7039
pe_symbol           NIFTY03APR2523300PE
pe_open                           152.9
pe_high                          158.35
pe_low                           151.65
pe_close                         153.44
pe_volume                        252000
pe_oi                           4303500
pe_coi                           -10725
pe_iv                             13.14
pe_delta                          -0.63
pe_gamma                         0.0015
pe_theta                         -14.47
pe_vega                            7.45
pe_rho                        -107.0027
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-26 11:57:28,807 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-26 11:57:28,807 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-26 11:57:28,807 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-26 11:57:28,807 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Entering risk rules block
2025-05-26 11:57:28,807 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-26 11:57:28,807 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140168341769120
2025-05-26 11:57:28,808 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140168341769120, OptionType.PUT ID: 140168341769232
2025-05-26 11:57:28,808 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-26 11:57:28,808 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-26 11:57:28,808 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-26 11:57:28,809 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-26 11:57:29,092 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.283s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-26 11:57:29,094 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-26 11:57:29,094 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-26 11:57:29,152 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-26 11:57:29,156 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 3 - Tick data for specific strike 23550.0 is EMPTY after filtering windowed data.
2025-05-26 11:57:29,156 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Starting risk rules loop, has 3 rules
2025-05-26 11:57:29,156 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 1 of 3
2025-05-26 11:57:29,156 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-26 11:57:29,166 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - evaluate_risk_rule returned: triggered=False
2025-05-26 11:57:29,167 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Finished processing risk rule 1
2025-05-26 11:57:29,167 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 2 of 3
2025-05-26 11:57:29,167 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - About to call evaluate_risk_rule for rule type TAKEPROFIT
2025-05-26 11:57:29,170 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - evaluate_risk_rule returned: triggered=True
2025-05-26 11:57:29,172 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Breaking out of risk rules loop due to triggered rule
2025-05-26 11:57:29,172 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-26 11:57:29,172 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Reached trade building section (outside all conditionals)
2025-05-26 11:57:29,172 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 3
2025-05-26 11:57:29,172 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 3, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 11:57:29,172 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23420.45, 'atm_strike': 23450.0, 'strike': 23550.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'OTM2', 'put_strike_type': 'ITM2', 'ce_symbol': 'NIFTY03APR2523550CE', 'ce_open': 61.15, 'ce_high': 61.75, 'ce_low': 55.0, 'ce_close': 56.45, 'ce_volume': 1147575, 'ce_oi': 2744250, 'ce_coi': 0, 'ce_iv': 12.93, 'ce_delta': 0.34, 'ce_gamma': 0.0014, 'ce_theta': -19.86, 'ce_vega': 7.25, 'ce_rho': 56.1949, 'pe_symbol': 'NIFTY03APR2523550PE', 'pe_open': 174.0, 'pe_high': 180.1, 'pe_low': 167.8, 'pe_close': 175.5, 'pe_volume': 326550, 'pe_oi': 1975725, 'pe_coi': 0, 'pe_iv': 13.83, 'pe_delta': -0.66, 'pe_gamma': 0.0013, 'pe_theta': -14.93, 'pe_vega': 7.34, 'pe_rho': -112.5433, 'future_open': 23534.5, 'future_high': 23543.95, 'future_low': 23527.0, 'future_close': 23536.75, 'future_volume': 86475, 'future_oi': 12605100, 'future_coi': 0}
2025-05-26 11:57:29,172 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23195.3, 'atm_strike': 23200.0, 'strike': 23300.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'OTM2', 'put_strike_type': 'ITM2', 'ce_symbol': 'NIFTY03APR2523300CE', 'ce_open': 75.59, 'ce_high': 76.15, 'ce_low': 73.15, 'ce_close': 76.15, 'ce_volume': 250950, 'ce_oi': 7928250, 'ce_coi': 109950, 'ce_iv': 14.54, 'ce_delta': 0.37, 'ce_gamma': 0.0013, 'ce_theta': -23.12, 'ce_vega': 7.53, 'ce_rho': 63.7039, 'pe_symbol': 'NIFTY03APR2523300PE', 'pe_open': 152.9, 'pe_high': 158.35, 'pe_low': 151.65, 'pe_close': 153.44, 'pe_volume': 252000, 'pe_oi': 4303500, 'pe_coi': -10725, 'pe_iv': 13.14, 'pe_delta': -0.63, 'pe_gamma': 0.0015, 'pe_theta': -14.47, 'pe_vega': 7.45, 'pe_rho': -107.0027, 'future_open': 23347.0, 'future_high': 23348.85, 'future_low': 23339.0, 'future_close': 23348.1, 'future_volume': 19050, 'future_oi': 13928550, 'future_coi': -28275, 'close': 1450.2, 'datetime': Timestamp('2025-04-01 12:00:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-26 11:57:29,172 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=CALL, Transaction=BUY, Lots=1
2025-05-26 11:57:29,173 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-26 11:57:29,173 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-26 11:57:29,173 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-26 11:57:29,173 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: 985.0000000000001, PnL (Slippage): 985.0000000000001, Net PnL: 985.0000000000001
2025-05-26 11:57:29,173 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-26 11:57:29,173 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '3', 'entry_date': '2025-04-01', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2025-04-01', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23550, 'instrument_type': 'CE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 56.45, 'exit_price': 76.15, 'points': 19.700000000000003, 'pointsAfterSlippage': 19.700000000000003, 'pnl': 985.0000000000001, 'pnlAfterSlippage': 985.0000000000001, 'expenses': 0.0, 'netPnlAfterExpenses': 985.0000000000001, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23420.45, 'index_exit_price': 23195.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-01 09:16:00', 'exit_datetime': '2025-04-01 12:00:00'}
2025-05-26 11:57:29,173 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 3, about to append. Total records before: 2
2025-05-26 11:57:29,173 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 3. Total records now: 3
2025-05-26 11:57:29,173 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 4 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 11:57:29,173 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-26 11:57:29,173 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-26 11:57:29,202 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.029s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-26 11:57:29,242 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.040s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-26 11:57:29,242 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Query results: entry_df size=1, exit_df size=1
2025-05-26 11:57:29,243 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Got entry and exit data, proceeding...
2025-05-26 11:57:29,243 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
                        trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23350.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY03APR2523350CE
ce_open                           164.4
ce_high                           166.5
ce_low                            156.3
ce_close                          158.0
ce_volume                        408675
ce_oi                            381750
ce_coi                                0
ce_iv                             13.71
ce_delta                           0.62
ce_gamma                         0.0014
ce_theta                         -23.53
ce_vega                            7.54
ce_rho                         105.3755
pe_symbol           NIFTY03APR2523350PE
pe_open                            78.3
pe_high                           80.95
pe_low                            71.84
pe_close                          76.59
pe_volume                       1021875
pe_oi                           1656000
pe_coi                                0
pe_iv                             14.49
pe_delta                          -0.38
pe_gamma                         0.0013
pe_theta                         -18.33
pe_vega                            7.58
pe_rho                         -64.7877
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-26 11:57:29,244 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23100.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY03APR2523100CE
ce_open                           186.9
ce_high                           188.0
ce_low                            181.8
ce_close                          187.2
ce_volume                         77775
ce_oi                            957825
ce_coi                            12900
ce_iv                             15.68
ce_delta                           0.66
ce_gamma                         0.0012
ce_theta                         -25.85
ce_vega                            7.36
ce_rho                         106.8743
pe_symbol           NIFTY03APR2523100PE
pe_open                            63.7
pe_high                            67.2
pe_low                            63.05
pe_close                          64.15
pe_volume                        269475
pe_oi                           3243600
pe_coi                            78900
pe_iv                             14.17
pe_delta                          -0.34
pe_gamma                         0.0013
pe_theta                         -17.23
pe_vega                            7.25
pe_rho                         -57.8964
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-26 11:57:29,244 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-26 11:57:29,244 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-26 11:57:29,244 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-26 11:57:29,244 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Entering risk rules block
2025-05-26 11:57:29,245 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-26 11:57:29,245 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140168341769232
2025-05-26 11:57:29,245 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140168341769120, OptionType.PUT ID: 140168341769232
2025-05-26 11:57:29,245 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-26 11:57:29,245 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-26 11:57:29,245 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-26 11:57:29,246 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-26 11:57:29,551 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.305s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-26 11:57:29,553 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-26 11:57:29,554 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-26 11:57:29,608 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-26 11:57:29,611 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 4 - Tick data for specific strike 23350.0 is EMPTY after filtering windowed data.
2025-05-26 11:57:29,611 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Starting risk rules loop, has 3 rules
2025-05-26 11:57:29,611 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 1 of 3
2025-05-26 11:57:29,611 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-26 11:57:29,615 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - evaluate_risk_rule returned: triggered=True
2025-05-26 11:57:29,616 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Breaking out of risk rules loop due to triggered rule
2025-05-26 11:57:29,616 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-26 11:57:29,617 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Reached trade building section (outside all conditionals)
2025-05-26 11:57:29,617 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 4
2025-05-26 11:57:29,617 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 11:57:29,617 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23420.45, 'atm_strike': 23450.0, 'strike': 23350.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY03APR2523350CE', 'ce_open': 164.4, 'ce_high': 166.5, 'ce_low': 156.3, 'ce_close': 158.0, 'ce_volume': 408675, 'ce_oi': 381750, 'ce_coi': 0, 'ce_iv': 13.71, 'ce_delta': 0.62, 'ce_gamma': 0.0014, 'ce_theta': -23.53, 'ce_vega': 7.54, 'ce_rho': 105.3755, 'pe_symbol': 'NIFTY03APR2523350PE', 'pe_open': 78.3, 'pe_high': 80.95, 'pe_low': 71.84, 'pe_close': 76.59, 'pe_volume': 1021875, 'pe_oi': 1656000, 'pe_coi': 0, 'pe_iv': 14.49, 'pe_delta': -0.38, 'pe_gamma': 0.0013, 'pe_theta': -18.33, 'pe_vega': 7.58, 'pe_rho': -64.7877, 'future_open': 23534.5, 'future_high': 23543.95, 'future_low': 23527.0, 'future_close': 23536.75, 'future_volume': 86475, 'future_oi': 12605100, 'future_coi': 0}
2025-05-26 11:57:29,617 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23195.3, 'atm_strike': 23200.0, 'strike': 23100.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY03APR2523100CE', 'ce_open': 186.9, 'ce_high': 188.0, 'ce_low': 181.8, 'ce_close': 187.2, 'ce_volume': 77775, 'ce_oi': 957825, 'ce_coi': 12900, 'ce_iv': 15.68, 'ce_delta': 0.66, 'ce_gamma': 0.0012, 'ce_theta': -25.85, 'ce_vega': 7.36, 'ce_rho': 106.8743, 'pe_symbol': 'NIFTY03APR2523100PE', 'pe_open': 63.7, 'pe_high': 67.2, 'pe_low': 63.05, 'pe_close': 64.15, 'pe_volume': 269475, 'pe_oi': 3243600, 'pe_coi': 78900, 'pe_iv': 14.17, 'pe_delta': -0.34, 'pe_gamma': 0.0013, 'pe_theta': -17.23, 'pe_vega': 7.25, 'pe_rho': -57.8964, 'future_open': 23347.0, 'future_high': 23348.85, 'future_low': 23339.0, 'future_close': 23348.1, 'future_volume': 19050, 'future_oi': 13928550, 'future_coi': -28275, 'close': 2.15, 'datetime': Timestamp('2025-04-01 12:00:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-26 11:57:29,617 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-26 11:57:29,617 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-26 11:57:29,617 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-26 11:57:29,617 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-26 11:57:29,617 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -621.9999999999999, PnL (Slippage): -621.9999999999999, Net PnL: -621.9999999999999
2025-05-26 11:57:29,618 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-26 11:57:29,618 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2025-04-01', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2025-04-01', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23350, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 76.59, 'exit_price': 64.15, 'points': -12.439999999999998, 'pointsAfterSlippage': -12.439999999999998, 'pnl': -621.9999999999999, 'pnlAfterSlippage': -621.9999999999999, 'expenses': 0.0, 'netPnlAfterExpenses': -621.9999999999999, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23420.45, 'index_exit_price': 23195.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-01 09:16:00', 'exit_datetime': '2025-04-01 12:00:00'}
2025-05-26 11:57:29,618 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 4, about to append. Total records before: 3
2025-05-26 11:57:29,618 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 4. Total records now: 4
2025-05-26 11:57:29,618 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Number of trade records generated: 4
2025-05-26 11:57:29,618 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Generated 4 trade records via model-driven path
2025-05-26 11:57:29,618 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Calculating tick-by-tick P&L for Max Profit/Loss tracking
2025-05-26 11:57:29,642 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.023s, returned 375 rows. Query: SELECT SUBSTRING('SELECT DISTINCT EXTRACT(HOUR FROM trade_time) * 10000 + 
                                       EXTRACT(MINUTE FROM trade_time) * 100 + 
                                       EXTRACT(SECOND FROM trad', 1, 200) ...
2025-05-26 11:57:29,659 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:29,673 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:29,690 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:29,707 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:29,725 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:29,742 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:29,760 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:29,778 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:29,796 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:29,814 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:29,832 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:29,849 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:29,867 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:29,885 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:29,903 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:29,921 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:29,939 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:29,957 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:29,975 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:29,992 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,010 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,028 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,046 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,064 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,082 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,100 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,117 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,135 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,153 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,171 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,189 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,206 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,224 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,242 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,259 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,277 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,295 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,312 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,331 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,348 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,366 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,384 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,401 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,419 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,437 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,455 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,473 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,490 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,508 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,526 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,544 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,562 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,580 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,598 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,616 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,634 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,652 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,669 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,687 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,705 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,723 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,743 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,762 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,782 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,801 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,821 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,841 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,860 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,880 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,900 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,919 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,939 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,959 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,979 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:30,998 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,018 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,038 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,058 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,077 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,097 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,117 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,136 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,156 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,176 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,195 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,215 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,235 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,255 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,274 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,294 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,314 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,334 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,354 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,373 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,393 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,413 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,433 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,453 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,473 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,493 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,512 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,533 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,552 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,572 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,592 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,611 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,631 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,650 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,670 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,690 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,709 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,729 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,749 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,768 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,788 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,807 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,827 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,847 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,867 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,887 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,907 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,926 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,946 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,966 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:31,986 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,005 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,025 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,045 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,064 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,084 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,104 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,123 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,143 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,164 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.021s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,218 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.053s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,237 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,257 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,276 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,296 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,315 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,335 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,355 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,375 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,394 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,414 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,434 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,454 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,473 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,493 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,513 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,532 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,552 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,572 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,591 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,611 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,631 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,651 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,671 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,690 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,710 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,730 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,749 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,769 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,789 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,809 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,829 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,848 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,868 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,889 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,909 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,928 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,948 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,967 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:32,987 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,006 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,025 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,045 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,064 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,084 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,103 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,123 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,143 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,162 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,181 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,201 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,220 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,240 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,259 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,279 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,299 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,318 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,337 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,357 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,377 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,396 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,416 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,436 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,455 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,475 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,494 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,514 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,533 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,552 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,572 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,591 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,611 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,630 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,650 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,669 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,689 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,709 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,728 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,748 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,767 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,787 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,806 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,826 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,845 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,865 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,884 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,904 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,923 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,943 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,963 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:33,982 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,002 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,022 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,041 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,061 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,080 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,100 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,120 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,140 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,159 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,179 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,198 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,218 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,238 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,258 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,279 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.021s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,301 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.021s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,320 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,340 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,360 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,380 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,399 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,418 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,438 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,457 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,476 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,496 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,515 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,535 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,555 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,574 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,594 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,613 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,633 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,652 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,672 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,691 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,711 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,730 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,750 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,770 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,789 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,809 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,828 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,848 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,867 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,887 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,907 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,926 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,946 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,966 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:34,985 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,005 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,024 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,044 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,064 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,083 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,103 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,122 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,142 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,162 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,181 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,201 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,222 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,241 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,261 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,280 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,299 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,319 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,337 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,357 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,375 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,399 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.023s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,418 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,437 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,456 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,475 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,494 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,513 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,533 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,552 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,571 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,590 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,609 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,628 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,648 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,668 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,687 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,705 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,724 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,744 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,763 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,782 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,801 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,820 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,837 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,856 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,871 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,890 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,909 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,928 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,947 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,966 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:35,985 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,004 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,023 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,042 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,061 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,081 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,100 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,118 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,138 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,157 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,176 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,194 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,213 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,233 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,252 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,271 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,290 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,309 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,328 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,348 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,367 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,386 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,404 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,424 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,442 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,462 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,480 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,499 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,518 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,537 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,556 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,575 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,594 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,613 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,632 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,651 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,670 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,689 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,715 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.026s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,735 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,755 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,774 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,794 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,813 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,831 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,850 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,869 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,888 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,907 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,926 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,946 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,966 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:36,985 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,005 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,024 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,044 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,063 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,083 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,102 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,122 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,141 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,161 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,218 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.056s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,237 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,256 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,276 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,295 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,315 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,334 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,354 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,373 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,393 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,413 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,432 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,452 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,472 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,492 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,511 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,531 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,550 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,569 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,589 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,608 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,628 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,647 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,667 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,686 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,706 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,725 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,745 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,764 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,784 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,803 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,823 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,839 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,859 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,878 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,897 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,917 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,936 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,956 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,975 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:37,995 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,014 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,034 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,053 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,073 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,092 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,111 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,131 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,150 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,170 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,189 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,208 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,228 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,247 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,267 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,287 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,306 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,325 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,345 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,365 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,384 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,404 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,424 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,444 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,464 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,483 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,503 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,523 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,542 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,561 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,581 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,601 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,620 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,640 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,659 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,679 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,698 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,717 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,737 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,757 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,776 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,796 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,816 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,835 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,855 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,875 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,895 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,916 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.021s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,936 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,956 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,975 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:38,995 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,015 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,034 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,054 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,073 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,093 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,113 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,132 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,152 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,172 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,191 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,211 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,231 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,250 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,270 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,289 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,309 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,328 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,348 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,368 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,388 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,408 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,430 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.021s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,451 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.021s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,472 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,492 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,510 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,529 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,549 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,568 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,588 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,608 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,628 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,648 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,667 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,687 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,707 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,726 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,745 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,765 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,785 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,805 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,824 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,846 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.021s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,865 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,885 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,904 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,924 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,943 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,963 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:39,982 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,002 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,021 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,041 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,060 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,080 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,100 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,119 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,139 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,158 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,178 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,198 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,217 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,237 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,257 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,276 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,296 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,315 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,334 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,354 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,373 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,393 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,412 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,432 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,451 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,471 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,491 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,510 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,532 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.021s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,551 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,571 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,590 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,610 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,630 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,650 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,669 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,689 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,709 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,728 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,748 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,768 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,787 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,807 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,826 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,842 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,861 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,881 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,900 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,920 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,939 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,959 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,979 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:40,999 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,018 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,038 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,058 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,079 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.021s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,100 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,119 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,139 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,159 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,178 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,198 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,217 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,237 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,256 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,276 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,296 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,315 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,335 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,354 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,374 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,393 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,413 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,433 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,452 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,472 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,491 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,511 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,530 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,550 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,569 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,589 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,609 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,628 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,648 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,667 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,687 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,707 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,726 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,746 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,766 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,785 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,805 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,825 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,845 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,864 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,884 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,904 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,923 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,943 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,962 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:41,982 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,001 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,021 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,040 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,060 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,079 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,099 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,119 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,139 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,158 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,214 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.056s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,234 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,254 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,273 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,292 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,312 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,331 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,351 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,370 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,390 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,409 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,429 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,448 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,468 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,488 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,508 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,528 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,549 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:57:42,571 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Calculated tick P&L for date 250401: 375 timestamps
2025-05-26 11:57:42,572 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Closed reusable HeavyDB connection from get_trades_for_portfolio
2025-05-26 11:57:42,607 - backtester_stable.BTRUN.builders - WARNING - utils.MARGIN_INFO not found or empty - margin requirements will be 0. Attempting to populate.
2025-05-26 11:57:42,607 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'margin_symbol_info.json' at /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-26 11:57:42,607 - backtester_stable.BTRUN.core.utils - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json (Broker: angel)
2025-05-26 11:57:42,608 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.MARGIN_INFO for angel.
2025-05-26 11:57:42,608 - backtester_stable.BTRUN.builders - ERROR - utils.MARGIN_INFO still empty after populate attempt. Margin req will be 0.
2025-05-26 11:57:42,614 - backtester_stable.BTRUN.builders - DEBUG - Strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL margin requirement: 0.00
2025-05-26 11:57:42,614 - backtester_stable.BTRUN.builders - INFO - Total portfolio margin requirement: 0.00
2025-05-26 11:57:42,614 - backtester_stable.BTRUN.builders - DEBUG - Converting tick PnL to daywise DataFrame.
2025-05-26 11:57:42,616 - backtester_stable.BTRUN.builders - INFO - Backtest response parsed.
2025-05-26 11:57:42,660 - backtester_stable.BTRUN.core.runtime - INFO - Generated metrics_df with 50 records for strategies: ['Combined' 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL']
2025-05-26 11:57:42,737 - backtester_stable.BTRUN.core.runtime - INFO - Saving Excel output to output/tbs_apr3_heavydb.xlsx
2025-05-26 11:57:42,737 - backtester_stable.BTRUN.core.runtime - DEBUG - [DEBUG save_backtest_results] save_excel=True, metrics_df is empty: False
2025-05-26 11:57:42,737 - backtester_stable.BTRUN.core.io - INFO - 2025-05-26 11:57:42.737447, Started writing stats to excel file: output/tbs_apr3_heavydb.xlsx
2025-05-26 11:57:42,737 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Temporarily switching to CPU-only mode.
2025-05-26 11:57:42,780 - backtester_stable.BTRUN.core.io - INFO - Added PortfolioParameter sheet in legacy 2x2 format
2025-05-26 11:57:42,812 - backtester_stable.BTRUN.core.io - INFO - Added GeneralParameter sheet from input files
2025-05-26 11:57:42,824 - backtester_stable.BTRUN.core.io - INFO - Added LegParameter sheet from input files
2025-05-26 11:57:42,825 - backtester_stable.BTRUN.core.io - INFO - Filtered metrics to show only 'Combined' entries: 25 rows
2025-05-26 11:57:42,878 - backtester_stable.BTRUN.core.io - INFO - Added PORTFOLIO Results sheet in legacy format
2025-05-26 11:57:42,938 - backtester_stable.BTRUN.core.io - INFO - 2025-05-26 11:57:42.938876, Excel file prepared successfully, Time taken: 0.2s
2025-05-26 11:57:42,939 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Restored GPU_ENABLED to: False
2025-05-26 11:57:42,939 - backtester_stable.BTRUN.core.runtime - INFO - Saving JSON output to output/tbs_apr3_heavydb.json
2025-05-26 11:57:42,963 - backtester_stable.BTRUN.core.io - INFO - --- JSON DUMP DEBUG START ---
2025-05-26 11:57:42,964 - backtester_stable.BTRUN.core.io - INFO - --- JSON DUMP DEBUG END ---
2025-05-26 11:57:42,964 - backtester_stable.BTRUN.core.io - INFO - Successfully wrote JSON output to output/tbs_apr3_heavydb.json
2025-05-26 11:57:42,970 - backtester_stable.BTRUN.core.io - ERROR - Text-to-speech failed: This means you probably do not have eSpeak or eSpeak-ng installed!
