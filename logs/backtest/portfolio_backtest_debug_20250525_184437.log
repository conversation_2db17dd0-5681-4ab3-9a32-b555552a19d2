2025-05-25 18:44:37,617 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-25 18:44:37,617 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-25 18:44:37,618 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/../models', '/srv/samba/shared', '/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages', '/srv/samba/shared']
2025-05-25 18:44:37,618 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-25 18:44:37,618 - __main__ - DEBUG - GPU enabled: False
2025-05-25 18:44:37,620 - __main__ - INFO - GPU acceleration enabled: False
2025-05-25 18:44:37,620 - __main__ - INFO - HeavyDB integration available (Host: 127.0.0.1)
2025-05-25 18:44:37,672 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-25 18:44:37,672 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-25 18:44:37,672 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/../models', '/srv/samba/shared', '/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages', '/srv/samba/shared']
2025-05-25 18:44:37,672 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-25 18:44:37,672 - __main__ - DEBUG - GPU enabled: False
2025-05-25 18:44:37,674 - __main__ - INFO - GPU acceleration enabled: False
2025-05-25 18:44:37,674 - __main__ - INFO - HeavyDB integration available (Host: 127.0.0.1)
2025-05-25 18:44:37,847 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-25 18:44:37,847 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-25 18:44:37,847 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-25 18:44:37,848 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-25 18:44:37,848 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-25 18:44:37,848 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-25 18:44:37,855 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-25 18:44:37,856 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-25 18:44:37,856 - backtester_stable.BTRUN.core.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-25 18:44:37,856 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-25 18:44:37,857 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-25 18:44:37,857 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-25 18:44:37,857 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-25 18:44:37,857 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-25 18:44:37,857 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-25 18:44:37,857 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-25 18:44:37,857 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-25 18:44:37,857 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-25 18:44:37,881 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-25 18:44:37,881 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-25 18:44:37,881 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/../models', '/srv/samba/shared', '/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages', '/srv/samba/shared']
2025-05-25 18:44:37,881 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-25 18:44:37,881 - __main__ - DEBUG - GPU enabled: False
2025-05-25 18:44:37,883 - __main__ - INFO - GPU acceleration enabled: False
2025-05-25 18:44:37,883 - __main__ - INFO - HeavyDB integration available (Host: 127.0.0.1)
2025-05-25 18:44:37,896 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-25 18:44:37,897 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-25 18:44:37,897 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-25 18:44:37,897 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-25 18:44:37,897 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-25 18:44:37,897 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-25 18:44:37,897 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/../models', '/srv/samba/shared', '/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages', '/srv/samba/shared']
2025-05-25 18:44:37,897 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-25 18:44:37,897 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-25 18:44:37,897 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-25 18:44:37,897 - __main__ - DEBUG - GPU enabled: False
2025-05-25 18:44:37,899 - __main__ - INFO - GPU acceleration enabled: False
2025-05-25 18:44:37,899 - __main__ - INFO - HeavyDB integration available (Host: 127.0.0.1)
2025-05-25 18:44:37,903 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-25 18:44:37,904 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-25 18:44:37,904 - backtester_stable.BTRUN.core.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-25 18:44:37,904 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-25 18:44:37,906 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-25 18:44:37,906 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-25 18:44:37,906 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-25 18:44:37,906 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-25 18:44:37,906 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-25 18:44:37,906 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-25 18:44:37,906 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-25 18:44:37,906 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-25 18:44:37,994 - __main__ - INFO - Successfully parsed 1 portfolio(s) using modern parser.
2025-05-25 18:44:37,994 - __main__ - INFO - Queueing portfolio: NIF0DTE (from modern_portfolio_parser)
2025-05-25 18:44:37,994 - __main__ - INFO - Executing 1 portfolios sequentially (multiprocessing temporarily disabled for debugging)
2025-05-25 18:44:37,995 - __main__ - INFO - Processing payload 1/1: NIF0DTE
2025-05-25 18:44:37,995 - backtester_stable.BTRUN.core.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'NIF0DTE'
2025-05-25 18:44:38,042 - __main__ - INFO - Successfully parsed 1 portfolio(s) using modern parser.
2025-05-25 18:44:38,042 - __main__ - INFO - Queueing portfolio: NIF0DTE (from modern_portfolio_parser)
2025-05-25 18:44:38,042 - __main__ - INFO - Executing 1 portfolios sequentially (multiprocessing temporarily disabled for debugging)
2025-05-25 18:44:38,043 - __main__ - INFO - Processing payload 1/1: NIF0DTE
2025-05-25 18:44:38,043 - backtester_stable.BTRUN.core.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'NIF0DTE'
2025-05-25 18:44:38,128 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-25 18:44:38,128 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-25 18:44:38,128 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-25 18:44:38,128 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-25 18:44:38,128 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-25 18:44:38,128 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-25 18:44:38,134 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-25 18:44:38,135 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-25 18:44:38,135 - backtester_stable.BTRUN.core.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-25 18:44:38,135 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-25 18:44:38,136 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-25 18:44:38,136 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-25 18:44:38,137 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-25 18:44:38,137 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-25 18:44:38,137 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-25 18:44:38,137 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-25 18:44:38,137 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-25 18:44:38,137 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-25 18:44:38,144 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-25 18:44:38,144 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-25 18:44:38,144 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-25 18:44:38,144 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-25 18:44:38,145 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-25 18:44:38,145 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-25 18:44:38,151 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-25 18:44:38,151 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-25 18:44:38,151 - backtester_stable.BTRUN.core.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-25 18:44:38,151 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-25 18:44:38,153 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-25 18:44:38,153 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-25 18:44:38,153 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-25 18:44:38,153 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-25 18:44:38,153 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-25 18:44:38,153 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-25 18:44:38,153 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-25 18:44:38,153 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-25 18:44:38,244 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-25 18:44:38,266 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.021s, returned 563 rows. Query: SELECT SUBSTRING('SELECT DISTINCT trade_date FROM nifty_option_chain', 1, 200) ...
2025-05-25 18:44:38,278 - __main__ - INFO - Successfully parsed 1 portfolio(s) using modern parser.
2025-05-25 18:44:38,278 - __main__ - INFO - Queueing portfolio: NIF0DTE (from modern_portfolio_parser)
2025-05-25 18:44:38,278 - __main__ - INFO - Executing 1 portfolios sequentially (multiprocessing temporarily disabled for debugging)
2025-05-25 18:44:38,278 - __main__ - INFO - Processing payload 1/1: NIF0DTE
2025-05-25 18:44:38,278 - backtester_stable.BTRUN.core.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'NIF0DTE'
2025-05-25 18:44:38,289 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-25 18:44:38,297 - __main__ - INFO - Successfully parsed 1 portfolio(s) using modern parser.
2025-05-25 18:44:38,297 - __main__ - INFO - Queueing portfolio: NIF0DTE (from modern_portfolio_parser)
2025-05-25 18:44:38,297 - __main__ - INFO - Executing 1 portfolios sequentially (multiprocessing temporarily disabled for debugging)
2025-05-25 18:44:38,297 - __main__ - INFO - Processing payload 1/1: NIF0DTE
2025-05-25 18:44:38,297 - backtester_stable.BTRUN.core.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'NIF0DTE'
2025-05-25 18:44:38,307 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 563 rows. Query: SELECT SUBSTRING('SELECT DISTINCT trade_date FROM nifty_option_chain', 1, 200) ...
2025-05-25 18:44:38,313 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - Cached 563 distinct trade dates
2025-05-25 18:44:38,313 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] portfolio_model_dict keys: ['portfolio_name', 'start_date', 'end_date', 'slippage_percent', 'margin_multiplier', 'is_tick_bt', 'strategies', 'extra_params']
2025-05-25 18:44:38,314 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 1 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:38,314 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 18:44:38,314 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 18:44:38,350 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.035s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 18:44:38,351 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - Cached 563 distinct trade dates
2025-05-25 18:44:38,351 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] portfolio_model_dict keys: ['portfolio_name', 'start_date', 'end_date', 'slippage_percent', 'margin_multiplier', 'is_tick_bt', 'strategies', 'extra_params']
2025-05-25 18:44:38,352 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 1 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:38,352 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 18:44:38,352 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 18:44:38,380 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.030s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 18:44:38,380 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Query results: entry_df size=1, exit_df size=1
2025-05-25 18:44:38,381 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Got entry and exit data, proceeding...
2025-05-25 18:44:38,382 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
                        trade_date                   2024-12-31
trade_time                     09:16:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                           23559.15
atm_strike                      23550.0
strike                          23550.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY02JAN2523550CE
ce_open                          135.05
ce_high                          139.69
ce_low                            133.4
ce_close                         139.69
ce_volume                        547275
ce_oi                            240150
ce_coi                                0
ce_iv                             15.78
ce_delta                           0.54
ce_gamma                         0.0013
ce_theta                         -27.19
ce_vega                            7.97
ce_rho                          90.4087
pe_symbol           NIFTY02JAN2523550PE
pe_open                          120.05
pe_high                          122.65
pe_low                            115.9
pe_close                          116.0
pe_volume                        813750
pe_oi                            969150
pe_coi                                0
pe_iv                             16.09
pe_delta                          -0.46
pe_gamma                         0.0012
pe_theta                         -21.22
pe_vega                            7.97
pe_rho                         -80.1808
future_open                     23694.4
future_high                     23701.8
future_low                      23685.0
future_close                    23701.8
future_volume                     78275
future_oi                      12385600
future_coi                            0
2025-05-25 18:44:38,383 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2024-12-31
trade_time                     12:00:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                            23595.8
atm_strike                      23650.0
strike                          23650.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY02JAN2523650CE
ce_open                           128.5
ce_high                           133.3
ce_low                            126.8
ce_close                         128.25
ce_volume                        190575
ce_oi                           2261775
ce_coi                            -1050
ce_iv                             18.21
ce_delta                           0.46
ce_gamma                         0.0011
ce_theta                         -30.43
ce_vega                            7.98
ce_rho                          78.1655
pe_symbol           NIFTY02JAN2523650PE
pe_open                           153.0
pe_high                           155.1
pe_low                            148.1
pe_close                          153.1
pe_volume                        145650
pe_oi                           1755600
pe_coi                             5700
pe_iv                             16.68
pe_delta                          -0.54
pe_gamma                         0.0012
pe_theta                          -21.6
pe_vega                            7.97
pe_rho                          -93.616
future_open                    23742.95
future_high                    23756.15
future_low                      23740.5
future_close                   23741.75
future_volume                      6000
future_oi                      12877100
future_coi                         2550
2025-05-25 18:44:38,383 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 18:44:38,383 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 18:44:38,383 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-25 18:44:38,383 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Entering risk rules block
2025-05-25 18:44:38,383 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 18:44:38,383 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140417784366016
2025-05-25 18:44:38,383 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140417784366016, OptionType.PUT ID: 140417784366128
2025-05-25 18:44:38,383 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 18:44:38,384 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 18:44:38,384 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 18:44:38,385 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2024-12-31' AND trade_date <= '2024-12-31'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 18:44:38,390 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.037s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 18:44:38,424 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.034s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 18:44:38,424 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Query results: entry_df size=1, exit_df size=1
2025-05-25 18:44:38,425 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Got entry and exit data, proceeding...
2025-05-25 18:44:38,426 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
                        trade_date                   2024-12-31
trade_time                     09:16:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                           23559.15
atm_strike                      23550.0
strike                          23550.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY02JAN2523550CE
ce_open                          135.05
ce_high                          139.69
ce_low                            133.4
ce_close                         139.69
ce_volume                        547275
ce_oi                            240150
ce_coi                                0
ce_iv                             15.78
ce_delta                           0.54
ce_gamma                         0.0013
ce_theta                         -27.19
ce_vega                            7.97
ce_rho                          90.4087
pe_symbol           NIFTY02JAN2523550PE
pe_open                          120.05
pe_high                          122.65
pe_low                            115.9
pe_close                          116.0
pe_volume                        813750
pe_oi                            969150
pe_coi                                0
pe_iv                             16.09
pe_delta                          -0.46
pe_gamma                         0.0012
pe_theta                         -21.22
pe_vega                            7.97
pe_rho                         -80.1808
future_open                     23694.4
future_high                     23701.8
future_low                      23685.0
future_close                    23701.8
future_volume                     78275
future_oi                      12385600
future_coi                            0
2025-05-25 18:44:38,426 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2024-12-31
trade_time                     12:00:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                            23595.8
atm_strike                      23650.0
strike                          23650.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY02JAN2523650CE
ce_open                           128.5
ce_high                           133.3
ce_low                            126.8
ce_close                         128.25
ce_volume                        190575
ce_oi                           2261775
ce_coi                            -1050
ce_iv                             18.21
ce_delta                           0.46
ce_gamma                         0.0011
ce_theta                         -30.43
ce_vega                            7.98
ce_rho                          78.1655
pe_symbol           NIFTY02JAN2523650PE
pe_open                           153.0
pe_high                           155.1
pe_low                            148.1
pe_close                          153.1
pe_volume                        145650
pe_oi                           1755600
pe_coi                             5700
pe_iv                             16.68
pe_delta                          -0.54
pe_gamma                         0.0012
pe_theta                          -21.6
pe_vega                            7.97
pe_rho                          -93.616
future_open                    23742.95
future_high                    23756.15
future_low                      23740.5
future_close                   23741.75
future_volume                      6000
future_oi                      12877100
future_coi                         2550
2025-05-25 18:44:38,427 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 18:44:38,427 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 18:44:38,427 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-25 18:44:38,427 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Entering risk rules block
2025-05-25 18:44:38,427 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 18:44:38,427 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140219477509056
2025-05-25 18:44:38,427 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140219477509056, OptionType.PUT ID: 140219477509168
2025-05-25 18:44:38,427 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 18:44:38,428 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 18:44:38,428 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 18:44:38,429 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2024-12-31' AND trade_date <= '2024-12-31'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 18:44:38,513 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-25 18:44:38,531 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-25 18:44:38,542 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.028s, returned 563 rows. Query: SELECT SUBSTRING('SELECT DISTINCT trade_date FROM nifty_option_chain', 1, 200) ...
2025-05-25 18:44:38,564 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.032s, returned 563 rows. Query: SELECT SUBSTRING('SELECT DISTINCT trade_date FROM nifty_option_chain', 1, 200) ...
2025-05-25 18:44:38,585 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - Cached 563 distinct trade dates
2025-05-25 18:44:38,586 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] portfolio_model_dict keys: ['portfolio_name', 'start_date', 'end_date', 'slippage_percent', 'margin_multiplier', 'is_tick_bt', 'strategies', 'extra_params']
2025-05-25 18:44:38,586 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 1 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:38,587 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 18:44:38,587 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 18:44:38,610 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - Cached 563 distinct trade dates
2025-05-25 18:44:38,611 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] portfolio_model_dict keys: ['portfolio_name', 'start_date', 'end_date', 'slippage_percent', 'margin_multiplier', 'is_tick_bt', 'strategies', 'extra_params']
2025-05-25 18:44:38,611 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 1 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:38,612 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 18:44:38,612 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 18:44:38,624 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.037s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 18:44:38,643 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.031s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 18:44:38,652 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.027s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 18:44:38,652 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Query results: entry_df size=1, exit_df size=1
2025-05-25 18:44:38,653 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Got entry and exit data, proceeding...
2025-05-25 18:44:38,653 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
                        trade_date                   2024-12-31
trade_time                     09:16:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                           23559.15
atm_strike                      23550.0
strike                          23550.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY02JAN2523550CE
ce_open                          135.05
ce_high                          139.69
ce_low                            133.4
ce_close                         139.69
ce_volume                        547275
ce_oi                            240150
ce_coi                                0
ce_iv                             15.78
ce_delta                           0.54
ce_gamma                         0.0013
ce_theta                         -27.19
ce_vega                            7.97
ce_rho                          90.4087
pe_symbol           NIFTY02JAN2523550PE
pe_open                          120.05
pe_high                          122.65
pe_low                            115.9
pe_close                          116.0
pe_volume                        813750
pe_oi                            969150
pe_coi                                0
pe_iv                             16.09
pe_delta                          -0.46
pe_gamma                         0.0012
pe_theta                         -21.22
pe_vega                            7.97
pe_rho                         -80.1808
future_open                     23694.4
future_high                     23701.8
future_low                      23685.0
future_close                    23701.8
future_volume                     78275
future_oi                      12385600
future_coi                            0
2025-05-25 18:44:38,654 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2024-12-31
trade_time                     12:00:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                            23595.8
atm_strike                      23650.0
strike                          23650.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY02JAN2523650CE
ce_open                           128.5
ce_high                           133.3
ce_low                            126.8
ce_close                         128.25
ce_volume                        190575
ce_oi                           2261775
ce_coi                            -1050
ce_iv                             18.21
ce_delta                           0.46
ce_gamma                         0.0011
ce_theta                         -30.43
ce_vega                            7.98
ce_rho                          78.1655
pe_symbol           NIFTY02JAN2523650PE
pe_open                           153.0
pe_high                           155.1
pe_low                            148.1
pe_close                          153.1
pe_volume                        145650
pe_oi                           1755600
pe_coi                             5700
pe_iv                             16.68
pe_delta                          -0.54
pe_gamma                         0.0012
pe_theta                          -21.6
pe_vega                            7.97
pe_rho                          -93.616
future_open                    23742.95
future_high                    23756.15
future_low                      23740.5
future_close                   23741.75
future_volume                      6000
future_oi                      12877100
future_coi                         2550
2025-05-25 18:44:38,654 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 18:44:38,654 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 18:44:38,655 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-25 18:44:38,655 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Entering risk rules block
2025-05-25 18:44:38,655 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 18:44:38,655 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 139631160345536
2025-05-25 18:44:38,655 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139631160345536, OptionType.PUT ID: 139631160345648
2025-05-25 18:44:38,655 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 18:44:38,655 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 18:44:38,655 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 18:44:38,657 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2024-12-31' AND trade_date <= '2024-12-31'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 18:44:38,682 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.039s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 18:44:38,682 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Query results: entry_df size=1, exit_df size=1
2025-05-25 18:44:38,683 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Got entry and exit data, proceeding...
2025-05-25 18:44:38,684 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
                        trade_date                   2024-12-31
trade_time                     09:16:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                           23559.15
atm_strike                      23550.0
strike                          23550.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY02JAN2523550CE
ce_open                          135.05
ce_high                          139.69
ce_low                            133.4
ce_close                         139.69
ce_volume                        547275
ce_oi                            240150
ce_coi                                0
ce_iv                             15.78
ce_delta                           0.54
ce_gamma                         0.0013
ce_theta                         -27.19
ce_vega                            7.97
ce_rho                          90.4087
pe_symbol           NIFTY02JAN2523550PE
pe_open                          120.05
pe_high                          122.65
pe_low                            115.9
pe_close                          116.0
pe_volume                        813750
pe_oi                            969150
pe_coi                                0
pe_iv                             16.09
pe_delta                          -0.46
pe_gamma                         0.0012
pe_theta                         -21.22
pe_vega                            7.97
pe_rho                         -80.1808
future_open                     23694.4
future_high                     23701.8
future_low                      23685.0
future_close                    23701.8
future_volume                     78275
future_oi                      12385600
future_coi                            0
2025-05-25 18:44:38,684 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2024-12-31
trade_time                     12:00:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                            23595.8
atm_strike                      23650.0
strike                          23650.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY02JAN2523650CE
ce_open                           128.5
ce_high                           133.3
ce_low                            126.8
ce_close                         128.25
ce_volume                        190575
ce_oi                           2261775
ce_coi                            -1050
ce_iv                             18.21
ce_delta                           0.46
ce_gamma                         0.0011
ce_theta                         -30.43
ce_vega                            7.98
ce_rho                          78.1655
pe_symbol           NIFTY02JAN2523650PE
pe_open                           153.0
pe_high                           155.1
pe_low                            148.1
pe_close                          153.1
pe_volume                        145650
pe_oi                           1755600
pe_coi                             5700
pe_iv                             16.68
pe_delta                          -0.54
pe_gamma                         0.0012
pe_theta                          -21.6
pe_vega                            7.97
pe_rho                          -93.616
future_open                    23742.95
future_high                    23756.15
future_low                      23740.5
future_close                   23741.75
future_volume                      6000
future_oi                      12877100
future_coi                         2550
2025-05-25 18:44:38,685 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 18:44:38,685 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 18:44:38,685 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-25 18:44:38,685 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Entering risk rules block
2025-05-25 18:44:38,685 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 18:44:38,685 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140095661654976
2025-05-25 18:44:38,685 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140095661654976, OptionType.PUT ID: 140095661655088
2025-05-25 18:44:38,685 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 18:44:38,686 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 18:44:38,686 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 18:44:38,688 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2024-12-31' AND trade_date <= '2024-12-31'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 18:44:38,745 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.316s, returned 25446 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 18:44:38,748 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 25446 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 18:44:38,749 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 18:44:38,753 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.367s, returned 25446 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 18:44:38,756 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 25446 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 18:44:38,756 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 18:44:38,826 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 18:44:38,830 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 1 - Tick data for specific strike 23550.0 is EMPTY after filtering windowed data.
2025-05-25 18:44:38,830 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Starting risk rules loop, has 3 rules
2025-05-25 18:44:38,830 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 1 of 3
2025-05-25 18:44:38,831 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-25 18:44:38,834 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - evaluate_risk_rule returned: triggered=True
2025-05-25 18:44:38,836 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 18:44:38,836 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Breaking out of risk rules loop due to triggered rule
2025-05-25 18:44:38,836 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-25 18:44:38,837 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Reached trade building section (outside all conditionals)
2025-05-25 18:44:38,837 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 1
2025-05-25 18:44:38,837 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 1, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:38,837 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23559.15, 'atm_strike': 23550.0, 'strike': 23550.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY02JAN2523550CE', 'ce_open': 135.05, 'ce_high': 139.69, 'ce_low': 133.4, 'ce_close': 139.69, 'ce_volume': 547275, 'ce_oi': 240150, 'ce_coi': 0, 'ce_iv': 15.78, 'ce_delta': 0.54, 'ce_gamma': 0.0013, 'ce_theta': -27.19, 'ce_vega': 7.97, 'ce_rho': 90.4087, 'pe_symbol': 'NIFTY02JAN2523550PE', 'pe_open': 120.05, 'pe_high': 122.65, 'pe_low': 115.9, 'pe_close': 116.0, 'pe_volume': 813750, 'pe_oi': 969150, 'pe_coi': 0, 'pe_iv': 16.09, 'pe_delta': -0.46, 'pe_gamma': 0.0012, 'pe_theta': -21.22, 'pe_vega': 7.97, 'pe_rho': -80.1808, 'future_open': 23694.4, 'future_high': 23701.8, 'future_low': 23685.0, 'future_close': 23701.8, 'future_volume': 78275, 'future_oi': 12385600, 'future_coi': 0}
2025-05-25 18:44:38,837 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23595.8, 'atm_strike': 23650.0, 'strike': 23650.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY02JAN2523650CE', 'ce_open': 128.5, 'ce_high': 133.3, 'ce_low': 126.8, 'ce_close': 128.25, 'ce_volume': 190575, 'ce_oi': 2261775, 'ce_coi': -1050, 'ce_iv': 18.21, 'ce_delta': 0.46, 'ce_gamma': 0.0011, 'ce_theta': -30.43, 'ce_vega': 7.98, 'ce_rho': 78.1655, 'pe_symbol': 'NIFTY02JAN2523650PE', 'pe_open': 153.0, 'pe_high': 155.1, 'pe_low': 148.1, 'pe_close': 153.1, 'pe_volume': 145650, 'pe_oi': 1755600, 'pe_coi': 5700, 'pe_iv': 16.68, 'pe_delta': -0.54, 'pe_gamma': 0.0012, 'pe_theta': -21.6, 'pe_vega': 7.97, 'pe_rho': -93.616, 'future_open': 23742.95, 'future_high': 23756.15, 'future_low': 23740.5, 'future_close': 23741.75, 'future_volume': 6000, 'future_oi': 12877100, 'future_coi': 2550, 'close': 2076.0, 'datetime': Timestamp('2024-12-31 12:00:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-25 18:44:38,837 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=CALL, Transaction=SELL, Lots=1
2025-05-25 18:44:38,837 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 18:44:38,837 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 18:44:38,838 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 18:44:38,838 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: 571.9999999999999, PnL (Slippage): 571.9999999999999, Net PnL: 571.9999999999999
2025-05-25 18:44:38,838 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 18:44:38,838 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '1', 'entry_date': '2024-12-31', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2024-12-31', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-01-02', 'strike': 23550, 'instrument_type': 'CE', 'side': 'SELL', 'filled_quantity': 50.0, 'entry_price': 139.69, 'exit_price': 128.25, 'points': 11.439999999999998, 'pointsAfterSlippage': 11.439999999999998, 'pnl': 571.9999999999999, 'pnlAfterSlippage': 571.9999999999999, 'expenses': 0.0, 'netPnlAfterExpenses': 571.9999999999999, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23559.15, 'index_exit_price': 23595.8, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2024-12-31 09:16:00', 'exit_datetime': '2024-12-31 12:00:00'}
2025-05-25 18:44:38,838 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 1, about to append. Total records before: 0
2025-05-25 18:44:38,838 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 1. Total records now: 1
2025-05-25 18:44:38,838 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 2 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:38,838 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 18:44:38,838 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 18:44:38,841 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 1 - Tick data for specific strike 23550.0 is EMPTY after filtering windowed data.
2025-05-25 18:44:38,841 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Starting risk rules loop, has 3 rules
2025-05-25 18:44:38,841 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 1 of 3
2025-05-25 18:44:38,841 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-25 18:44:38,846 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - evaluate_risk_rule returned: triggered=True
2025-05-25 18:44:38,848 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Breaking out of risk rules loop due to triggered rule
2025-05-25 18:44:38,848 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-25 18:44:38,848 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Reached trade building section (outside all conditionals)
2025-05-25 18:44:38,848 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 1
2025-05-25 18:44:38,848 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 1, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:38,849 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23559.15, 'atm_strike': 23550.0, 'strike': 23550.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY02JAN2523550CE', 'ce_open': 135.05, 'ce_high': 139.69, 'ce_low': 133.4, 'ce_close': 139.69, 'ce_volume': 547275, 'ce_oi': 240150, 'ce_coi': 0, 'ce_iv': 15.78, 'ce_delta': 0.54, 'ce_gamma': 0.0013, 'ce_theta': -27.19, 'ce_vega': 7.97, 'ce_rho': 90.4087, 'pe_symbol': 'NIFTY02JAN2523550PE', 'pe_open': 120.05, 'pe_high': 122.65, 'pe_low': 115.9, 'pe_close': 116.0, 'pe_volume': 813750, 'pe_oi': 969150, 'pe_coi': 0, 'pe_iv': 16.09, 'pe_delta': -0.46, 'pe_gamma': 0.0012, 'pe_theta': -21.22, 'pe_vega': 7.97, 'pe_rho': -80.1808, 'future_open': 23694.4, 'future_high': 23701.8, 'future_low': 23685.0, 'future_close': 23701.8, 'future_volume': 78275, 'future_oi': 12385600, 'future_coi': 0}
2025-05-25 18:44:38,849 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23595.8, 'atm_strike': 23650.0, 'strike': 23650.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY02JAN2523650CE', 'ce_open': 128.5, 'ce_high': 133.3, 'ce_low': 126.8, 'ce_close': 128.25, 'ce_volume': 190575, 'ce_oi': 2261775, 'ce_coi': -1050, 'ce_iv': 18.21, 'ce_delta': 0.46, 'ce_gamma': 0.0011, 'ce_theta': -30.43, 'ce_vega': 7.98, 'ce_rho': 78.1655, 'pe_symbol': 'NIFTY02JAN2523650PE', 'pe_open': 153.0, 'pe_high': 155.1, 'pe_low': 148.1, 'pe_close': 153.1, 'pe_volume': 145650, 'pe_oi': 1755600, 'pe_coi': 5700, 'pe_iv': 16.68, 'pe_delta': -0.54, 'pe_gamma': 0.0012, 'pe_theta': -21.6, 'pe_vega': 7.97, 'pe_rho': -93.616, 'future_open': 23742.95, 'future_high': 23756.15, 'future_low': 23740.5, 'future_close': 23741.75, 'future_volume': 6000, 'future_oi': 12877100, 'future_coi': 2550, 'close': 2076.0, 'datetime': Timestamp('2024-12-31 12:00:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-25 18:44:38,849 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=CALL, Transaction=SELL, Lots=1
2025-05-25 18:44:38,849 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 18:44:38,849 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 18:44:38,849 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 18:44:38,849 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: 571.9999999999999, PnL (Slippage): 571.9999999999999, Net PnL: 571.9999999999999
2025-05-25 18:44:38,849 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 18:44:38,849 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '1', 'entry_date': '2024-12-31', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2024-12-31', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-01-02', 'strike': 23550, 'instrument_type': 'CE', 'side': 'SELL', 'filled_quantity': 50.0, 'entry_price': 139.69, 'exit_price': 128.25, 'points': 11.439999999999998, 'pointsAfterSlippage': 11.439999999999998, 'pnl': 571.9999999999999, 'pnlAfterSlippage': 571.9999999999999, 'expenses': 0.0, 'netPnlAfterExpenses': 571.9999999999999, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23559.15, 'index_exit_price': 23595.8, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2024-12-31 09:16:00', 'exit_datetime': '2024-12-31 12:00:00'}
2025-05-25 18:44:38,849 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 1, about to append. Total records before: 0
2025-05-25 18:44:38,850 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 1. Total records now: 1
2025-05-25 18:44:38,850 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 2 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:38,850 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 18:44:38,850 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 18:44:38,864 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.025s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 18:44:38,889 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.024s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 18:44:38,889 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Query results: entry_df size=1, exit_df size=1
2025-05-25 18:44:38,889 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Got entry and exit data, proceeding...
2025-05-25 18:44:38,890 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
                        trade_date                   2024-12-31
trade_time                     09:16:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                           23559.15
atm_strike                      23550.0
strike                          23550.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY02JAN2523550CE
ce_open                          135.05
ce_high                          139.69
ce_low                            133.4
ce_close                         139.69
ce_volume                        547275
ce_oi                            240150
ce_coi                                0
ce_iv                             15.78
ce_delta                           0.54
ce_gamma                         0.0013
ce_theta                         -27.19
ce_vega                            7.97
ce_rho                          90.4087
pe_symbol           NIFTY02JAN2523550PE
pe_open                          120.05
pe_high                          122.65
pe_low                            115.9
pe_close                          116.0
pe_volume                        813750
pe_oi                            969150
pe_coi                                0
pe_iv                             16.09
pe_delta                          -0.46
pe_gamma                         0.0012
pe_theta                         -21.22
pe_vega                            7.97
pe_rho                         -80.1808
future_open                     23694.4
future_high                     23701.8
future_low                      23685.0
future_close                    23701.8
future_volume                     78275
future_oi                      12385600
future_coi                            0
2025-05-25 18:44:38,891 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2024-12-31
trade_time                     12:00:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                            23595.8
atm_strike                      23650.0
strike                          23650.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY02JAN2523650CE
ce_open                           128.5
ce_high                           133.3
ce_low                            126.8
ce_close                         128.25
ce_volume                        190575
ce_oi                           2261775
ce_coi                            -1050
ce_iv                             18.21
ce_delta                           0.46
ce_gamma                         0.0011
ce_theta                         -30.43
ce_vega                            7.98
ce_rho                          78.1655
pe_symbol           NIFTY02JAN2523650PE
pe_open                           153.0
pe_high                           155.1
pe_low                            148.1
pe_close                          153.1
pe_volume                        145650
pe_oi                           1755600
pe_coi                             5700
pe_iv                             16.68
pe_delta                          -0.54
pe_gamma                         0.0012
pe_theta                          -21.6
pe_vega                            7.97
pe_rho                          -93.616
future_open                    23742.95
future_high                    23756.15
future_low                      23740.5
future_close                   23741.75
future_volume                      6000
future_oi                      12877100
future_coi                         2550
2025-05-25 18:44:38,891 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 18:44:38,891 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 18:44:38,891 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-25 18:44:38,891 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Entering risk rules block
2025-05-25 18:44:38,893 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 18:44:38,893 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140219477509168
2025-05-25 18:44:38,893 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140219477509056, OptionType.PUT ID: 140219477509168
2025-05-25 18:44:38,893 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 18:44:38,893 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 18:44:38,893 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 18:44:38,894 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.044s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 18:44:38,896 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2024-12-31' AND trade_date <= '2024-12-31'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 18:44:38,919 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.024s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 18:44:38,919 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Query results: entry_df size=1, exit_df size=1
2025-05-25 18:44:38,919 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Got entry and exit data, proceeding...
2025-05-25 18:44:38,920 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
                        trade_date                   2024-12-31
trade_time                     09:16:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                           23559.15
atm_strike                      23550.0
strike                          23550.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY02JAN2523550CE
ce_open                          135.05
ce_high                          139.69
ce_low                            133.4
ce_close                         139.69
ce_volume                        547275
ce_oi                            240150
ce_coi                                0
ce_iv                             15.78
ce_delta                           0.54
ce_gamma                         0.0013
ce_theta                         -27.19
ce_vega                            7.97
ce_rho                          90.4087
pe_symbol           NIFTY02JAN2523550PE
pe_open                          120.05
pe_high                          122.65
pe_low                            115.9
pe_close                          116.0
pe_volume                        813750
pe_oi                            969150
pe_coi                                0
pe_iv                             16.09
pe_delta                          -0.46
pe_gamma                         0.0012
pe_theta                         -21.22
pe_vega                            7.97
pe_rho                         -80.1808
future_open                     23694.4
future_high                     23701.8
future_low                      23685.0
future_close                    23701.8
future_volume                     78275
future_oi                      12385600
future_coi                            0
2025-05-25 18:44:38,921 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2024-12-31
trade_time                     12:00:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                            23595.8
atm_strike                      23650.0
strike                          23650.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY02JAN2523650CE
ce_open                           128.5
ce_high                           133.3
ce_low                            126.8
ce_close                         128.25
ce_volume                        190575
ce_oi                           2261775
ce_coi                            -1050
ce_iv                             18.21
ce_delta                           0.46
ce_gamma                         0.0011
ce_theta                         -30.43
ce_vega                            7.98
ce_rho                          78.1655
pe_symbol           NIFTY02JAN2523650PE
pe_open                           153.0
pe_high                           155.1
pe_low                            148.1
pe_close                          153.1
pe_volume                        145650
pe_oi                           1755600
pe_coi                             5700
pe_iv                             16.68
pe_delta                          -0.54
pe_gamma                         0.0012
pe_theta                          -21.6
pe_vega                            7.97
pe_rho                          -93.616
future_open                    23742.95
future_high                    23756.15
future_low                      23740.5
future_close                   23741.75
future_volume                      6000
future_oi                      12877100
future_coi                         2550
2025-05-25 18:44:38,921 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 18:44:38,921 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 18:44:38,921 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-25 18:44:38,921 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Entering risk rules block
2025-05-25 18:44:38,922 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 18:44:38,922 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140417784366128
2025-05-25 18:44:38,922 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140417784366016, OptionType.PUT ID: 140417784366128
2025-05-25 18:44:38,922 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 18:44:38,922 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 18:44:38,922 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 18:44:38,924 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2024-12-31' AND trade_date <= '2024-12-31'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 18:44:38,966 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.309s, returned 25446 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 18:44:38,969 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 25446 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 18:44:38,970 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 18:44:39,049 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 18:44:39,054 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 1 - Tick data for specific strike 23550.0 is EMPTY after filtering windowed data.
2025-05-25 18:44:39,054 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Starting risk rules loop, has 3 rules
2025-05-25 18:44:39,054 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 1 of 3
2025-05-25 18:44:39,055 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-25 18:44:39,059 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - evaluate_risk_rule returned: triggered=True
2025-05-25 18:44:39,060 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Breaking out of risk rules loop due to triggered rule
2025-05-25 18:44:39,061 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-25 18:44:39,061 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Reached trade building section (outside all conditionals)
2025-05-25 18:44:39,061 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 1
2025-05-25 18:44:39,061 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 1, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:39,061 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23559.15, 'atm_strike': 23550.0, 'strike': 23550.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY02JAN2523550CE', 'ce_open': 135.05, 'ce_high': 139.69, 'ce_low': 133.4, 'ce_close': 139.69, 'ce_volume': 547275, 'ce_oi': 240150, 'ce_coi': 0, 'ce_iv': 15.78, 'ce_delta': 0.54, 'ce_gamma': 0.0013, 'ce_theta': -27.19, 'ce_vega': 7.97, 'ce_rho': 90.4087, 'pe_symbol': 'NIFTY02JAN2523550PE', 'pe_open': 120.05, 'pe_high': 122.65, 'pe_low': 115.9, 'pe_close': 116.0, 'pe_volume': 813750, 'pe_oi': 969150, 'pe_coi': 0, 'pe_iv': 16.09, 'pe_delta': -0.46, 'pe_gamma': 0.0012, 'pe_theta': -21.22, 'pe_vega': 7.97, 'pe_rho': -80.1808, 'future_open': 23694.4, 'future_high': 23701.8, 'future_low': 23685.0, 'future_close': 23701.8, 'future_volume': 78275, 'future_oi': 12385600, 'future_coi': 0}
2025-05-25 18:44:39,061 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23595.8, 'atm_strike': 23650.0, 'strike': 23650.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY02JAN2523650CE', 'ce_open': 128.5, 'ce_high': 133.3, 'ce_low': 126.8, 'ce_close': 128.25, 'ce_volume': 190575, 'ce_oi': 2261775, 'ce_coi': -1050, 'ce_iv': 18.21, 'ce_delta': 0.46, 'ce_gamma': 0.0011, 'ce_theta': -30.43, 'ce_vega': 7.98, 'ce_rho': 78.1655, 'pe_symbol': 'NIFTY02JAN2523650PE', 'pe_open': 153.0, 'pe_high': 155.1, 'pe_low': 148.1, 'pe_close': 153.1, 'pe_volume': 145650, 'pe_oi': 1755600, 'pe_coi': 5700, 'pe_iv': 16.68, 'pe_delta': -0.54, 'pe_gamma': 0.0012, 'pe_theta': -21.6, 'pe_vega': 7.97, 'pe_rho': -93.616, 'future_open': 23742.95, 'future_high': 23756.15, 'future_low': 23740.5, 'future_close': 23741.75, 'future_volume': 6000, 'future_oi': 12877100, 'future_coi': 2550, 'close': 2076.0, 'datetime': Timestamp('2024-12-31 12:00:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-25 18:44:39,061 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=CALL, Transaction=SELL, Lots=1
2025-05-25 18:44:39,061 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 18:44:39,061 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 18:44:39,062 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 18:44:39,062 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: 571.9999999999999, PnL (Slippage): 571.9999999999999, Net PnL: 571.9999999999999
2025-05-25 18:44:39,062 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 18:44:39,062 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '1', 'entry_date': '2024-12-31', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2024-12-31', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-01-02', 'strike': 23550, 'instrument_type': 'CE', 'side': 'SELL', 'filled_quantity': 50.0, 'entry_price': 139.69, 'exit_price': 128.25, 'points': 11.439999999999998, 'pointsAfterSlippage': 11.439999999999998, 'pnl': 571.9999999999999, 'pnlAfterSlippage': 571.9999999999999, 'expenses': 0.0, 'netPnlAfterExpenses': 571.9999999999999, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23559.15, 'index_exit_price': 23595.8, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2024-12-31 09:16:00', 'exit_datetime': '2024-12-31 12:00:00'}
2025-05-25 18:44:39,062 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 1, about to append. Total records before: 0
2025-05-25 18:44:39,062 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 1. Total records now: 1
2025-05-25 18:44:39,062 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 2 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:39,062 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 18:44:39,062 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 18:44:39,080 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.392s, returned 25446 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 18:44:39,083 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 25446 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 18:44:39,084 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 18:44:39,088 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.025s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 18:44:39,112 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.024s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 18:44:39,112 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Query results: entry_df size=1, exit_df size=1
2025-05-25 18:44:39,113 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Got entry and exit data, proceeding...
2025-05-25 18:44:39,114 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
                        trade_date                   2024-12-31
trade_time                     09:16:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                           23559.15
atm_strike                      23550.0
strike                          23550.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY02JAN2523550CE
ce_open                          135.05
ce_high                          139.69
ce_low                            133.4
ce_close                         139.69
ce_volume                        547275
ce_oi                            240150
ce_coi                                0
ce_iv                             15.78
ce_delta                           0.54
ce_gamma                         0.0013
ce_theta                         -27.19
ce_vega                            7.97
ce_rho                          90.4087
pe_symbol           NIFTY02JAN2523550PE
pe_open                          120.05
pe_high                          122.65
pe_low                            115.9
pe_close                          116.0
pe_volume                        813750
pe_oi                            969150
pe_coi                                0
pe_iv                             16.09
pe_delta                          -0.46
pe_gamma                         0.0012
pe_theta                         -21.22
pe_vega                            7.97
pe_rho                         -80.1808
future_open                     23694.4
future_high                     23701.8
future_low                      23685.0
future_close                    23701.8
future_volume                     78275
future_oi                      12385600
future_coi                            0
2025-05-25 18:44:39,115 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2024-12-31
trade_time                     12:00:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                            23595.8
atm_strike                      23650.0
strike                          23650.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY02JAN2523650CE
ce_open                           128.5
ce_high                           133.3
ce_low                            126.8
ce_close                         128.25
ce_volume                        190575
ce_oi                           2261775
ce_coi                            -1050
ce_iv                             18.21
ce_delta                           0.46
ce_gamma                         0.0011
ce_theta                         -30.43
ce_vega                            7.98
ce_rho                          78.1655
pe_symbol           NIFTY02JAN2523650PE
pe_open                           153.0
pe_high                           155.1
pe_low                            148.1
pe_close                          153.1
pe_volume                        145650
pe_oi                           1755600
pe_coi                             5700
pe_iv                             16.68
pe_delta                          -0.54
pe_gamma                         0.0012
pe_theta                          -21.6
pe_vega                            7.97
pe_rho                          -93.616
future_open                    23742.95
future_high                    23756.15
future_low                      23740.5
future_close                   23741.75
future_volume                      6000
future_oi                      12877100
future_coi                         2550
2025-05-25 18:44:39,116 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 18:44:39,116 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 18:44:39,116 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-25 18:44:39,116 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Entering risk rules block
2025-05-25 18:44:39,116 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 18:44:39,116 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 139631160345648
2025-05-25 18:44:39,116 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139631160345536, OptionType.PUT ID: 139631160345648
2025-05-25 18:44:39,116 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 18:44:39,117 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 18:44:39,117 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 18:44:39,118 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2024-12-31' AND trade_date <= '2024-12-31'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 18:44:39,163 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 18:44:39,167 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 1 - Tick data for specific strike 23550.0 is EMPTY after filtering windowed data.
2025-05-25 18:44:39,167 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Starting risk rules loop, has 3 rules
2025-05-25 18:44:39,167 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 1 of 3
2025-05-25 18:44:39,167 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-25 18:44:39,171 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - evaluate_risk_rule returned: triggered=True
2025-05-25 18:44:39,173 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Breaking out of risk rules loop due to triggered rule
2025-05-25 18:44:39,173 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-25 18:44:39,174 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Reached trade building section (outside all conditionals)
2025-05-25 18:44:39,174 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 1
2025-05-25 18:44:39,174 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 1, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:39,174 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23559.15, 'atm_strike': 23550.0, 'strike': 23550.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY02JAN2523550CE', 'ce_open': 135.05, 'ce_high': 139.69, 'ce_low': 133.4, 'ce_close': 139.69, 'ce_volume': 547275, 'ce_oi': 240150, 'ce_coi': 0, 'ce_iv': 15.78, 'ce_delta': 0.54, 'ce_gamma': 0.0013, 'ce_theta': -27.19, 'ce_vega': 7.97, 'ce_rho': 90.4087, 'pe_symbol': 'NIFTY02JAN2523550PE', 'pe_open': 120.05, 'pe_high': 122.65, 'pe_low': 115.9, 'pe_close': 116.0, 'pe_volume': 813750, 'pe_oi': 969150, 'pe_coi': 0, 'pe_iv': 16.09, 'pe_delta': -0.46, 'pe_gamma': 0.0012, 'pe_theta': -21.22, 'pe_vega': 7.97, 'pe_rho': -80.1808, 'future_open': 23694.4, 'future_high': 23701.8, 'future_low': 23685.0, 'future_close': 23701.8, 'future_volume': 78275, 'future_oi': 12385600, 'future_coi': 0}
2025-05-25 18:44:39,174 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23595.8, 'atm_strike': 23650.0, 'strike': 23650.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY02JAN2523650CE', 'ce_open': 128.5, 'ce_high': 133.3, 'ce_low': 126.8, 'ce_close': 128.25, 'ce_volume': 190575, 'ce_oi': 2261775, 'ce_coi': -1050, 'ce_iv': 18.21, 'ce_delta': 0.46, 'ce_gamma': 0.0011, 'ce_theta': -30.43, 'ce_vega': 7.98, 'ce_rho': 78.1655, 'pe_symbol': 'NIFTY02JAN2523650PE', 'pe_open': 153.0, 'pe_high': 155.1, 'pe_low': 148.1, 'pe_close': 153.1, 'pe_volume': 145650, 'pe_oi': 1755600, 'pe_coi': 5700, 'pe_iv': 16.68, 'pe_delta': -0.54, 'pe_gamma': 0.0012, 'pe_theta': -21.6, 'pe_vega': 7.97, 'pe_rho': -93.616, 'future_open': 23742.95, 'future_high': 23756.15, 'future_low': 23740.5, 'future_close': 23741.75, 'future_volume': 6000, 'future_oi': 12877100, 'future_coi': 2550, 'close': 2076.0, 'datetime': Timestamp('2024-12-31 12:00:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-25 18:44:39,174 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=CALL, Transaction=SELL, Lots=1
2025-05-25 18:44:39,174 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 18:44:39,174 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 18:44:39,175 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 18:44:39,175 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: 571.9999999999999, PnL (Slippage): 571.9999999999999, Net PnL: 571.9999999999999
2025-05-25 18:44:39,175 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 18:44:39,175 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '1', 'entry_date': '2024-12-31', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2024-12-31', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-01-02', 'strike': 23550, 'instrument_type': 'CE', 'side': 'SELL', 'filled_quantity': 50.0, 'entry_price': 139.69, 'exit_price': 128.25, 'points': 11.439999999999998, 'pointsAfterSlippage': 11.439999999999998, 'pnl': 571.9999999999999, 'pnlAfterSlippage': 571.9999999999999, 'expenses': 0.0, 'netPnlAfterExpenses': 571.9999999999999, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23559.15, 'index_exit_price': 23595.8, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2024-12-31 09:16:00', 'exit_datetime': '2024-12-31 12:00:00'}
2025-05-25 18:44:39,175 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 1, about to append. Total records before: 0
2025-05-25 18:44:39,175 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 1. Total records now: 1
2025-05-25 18:44:39,175 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 2 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:39,175 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 18:44:39,175 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 18:44:39,204 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.028s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-25 18:44:39,221 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.297s, returned 25446 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 18:44:39,224 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 25446 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 18:44:39,225 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 18:44:39,230 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.334s, returned 25446 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 18:44:39,233 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 25446 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 18:44:39,234 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 18:44:39,240 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.036s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-25 18:44:39,240 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Query results: entry_df size=1, exit_df size=1
2025-05-25 18:44:39,241 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Got entry and exit data, proceeding...
2025-05-25 18:44:39,242 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
                        trade_date                   2024-12-31
trade_time                     09:16:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                           23559.15
atm_strike                      23550.0
strike                          23550.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY02JAN2523550CE
ce_open                          135.05
ce_high                          139.69
ce_low                            133.4
ce_close                         139.69
ce_volume                        547275
ce_oi                            240150
ce_coi                                0
ce_iv                             15.78
ce_delta                           0.54
ce_gamma                         0.0013
ce_theta                         -27.19
ce_vega                            7.97
ce_rho                          90.4087
pe_symbol           NIFTY02JAN2523550PE
pe_open                          120.05
pe_high                          122.65
pe_low                            115.9
pe_close                          116.0
pe_volume                        813750
pe_oi                            969150
pe_coi                                0
pe_iv                             16.09
pe_delta                          -0.46
pe_gamma                         0.0012
pe_theta                         -21.22
pe_vega                            7.97
pe_rho                         -80.1808
future_open                     23694.4
future_high                     23701.8
future_low                      23685.0
future_close                    23701.8
future_volume                     78275
future_oi                      12385600
future_coi                            0
2025-05-25 18:44:39,242 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2024-12-31
trade_time                     12:00:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                            23595.8
atm_strike                      23650.0
strike                          23650.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY02JAN2523650CE
ce_open                           128.5
ce_high                           133.3
ce_low                            126.8
ce_close                         128.25
ce_volume                        190575
ce_oi                           2261775
ce_coi                            -1050
ce_iv                             18.21
ce_delta                           0.46
ce_gamma                         0.0011
ce_theta                         -30.43
ce_vega                            7.98
ce_rho                          78.1655
pe_symbol           NIFTY02JAN2523650PE
pe_open                           153.0
pe_high                           155.1
pe_low                            148.1
pe_close                          153.1
pe_volume                        145650
pe_oi                           1755600
pe_coi                             5700
pe_iv                             16.68
pe_delta                          -0.54
pe_gamma                         0.0012
pe_theta                          -21.6
pe_vega                            7.97
pe_rho                          -93.616
future_open                    23742.95
future_high                    23756.15
future_low                      23740.5
future_close                   23741.75
future_volume                      6000
future_oi                      12877100
future_coi                         2550
2025-05-25 18:44:39,243 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 18:44:39,243 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 18:44:39,243 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-25 18:44:39,243 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Entering risk rules block
2025-05-25 18:44:39,243 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 18:44:39,243 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140095661655088
2025-05-25 18:44:39,243 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140095661654976, OptionType.PUT ID: 140095661655088
2025-05-25 18:44:39,244 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 18:44:39,244 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 18:44:39,244 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 18:44:39,245 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2024-12-31' AND trade_date <= '2024-12-31'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 18:44:39,301 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 18:44:39,305 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 2 - Tick data for specific strike 23550.0 is EMPTY after filtering windowed data.
2025-05-25 18:44:39,306 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Starting risk rules loop, has 3 rules
2025-05-25 18:44:39,306 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 1 of 3
2025-05-25 18:44:39,306 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-25 18:44:39,308 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 18:44:39,312 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 2 - Tick data for specific strike 23550.0 is EMPTY after filtering windowed data.
2025-05-25 18:44:39,312 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Starting risk rules loop, has 3 rules
2025-05-25 18:44:39,312 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 1 of 3
2025-05-25 18:44:39,313 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-25 18:44:39,318 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-25 18:44:39,318 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 1
2025-05-25 18:44:39,318 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 2 of 3
2025-05-25 18:44:39,318 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type TAKEPROFIT
2025-05-25 18:44:39,324 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-25 18:44:39,324 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 1
2025-05-25 18:44:39,324 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 2 of 3
2025-05-25 18:44:39,324 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type TAKEPROFIT
2025-05-25 18:44:39,330 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-25 18:44:39,330 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 2
2025-05-25 18:44:39,330 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 3 of 3
2025-05-25 18:44:39,330 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type TRAIL
2025-05-25 18:44:39,333 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-25 18:44:39,333 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 3
2025-05-25 18:44:39,334 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-25 18:44:39,334 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Reached trade building section (outside all conditionals)
2025-05-25 18:44:39,334 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 2
2025-05-25 18:44:39,334 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 2, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:39,334 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23559.15, 'atm_strike': 23550.0, 'strike': 23550.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY02JAN2523550CE', 'ce_open': 135.05, 'ce_high': 139.69, 'ce_low': 133.4, 'ce_close': 139.69, 'ce_volume': 547275, 'ce_oi': 240150, 'ce_coi': 0, 'ce_iv': 15.78, 'ce_delta': 0.54, 'ce_gamma': 0.0013, 'ce_theta': -27.19, 'ce_vega': 7.97, 'ce_rho': 90.4087, 'pe_symbol': 'NIFTY02JAN2523550PE', 'pe_open': 120.05, 'pe_high': 122.65, 'pe_low': 115.9, 'pe_close': 116.0, 'pe_volume': 813750, 'pe_oi': 969150, 'pe_coi': 0, 'pe_iv': 16.09, 'pe_delta': -0.46, 'pe_gamma': 0.0012, 'pe_theta': -21.22, 'pe_vega': 7.97, 'pe_rho': -80.1808, 'future_open': 23694.4, 'future_high': 23701.8, 'future_low': 23685.0, 'future_close': 23701.8, 'future_volume': 78275, 'future_oi': 12385600, 'future_coi': 0}
2025-05-25 18:44:39,334 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': datetime.time(12, 0), 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23595.8, 'atm_strike': 23650.0, 'strike': 23650.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY02JAN2523650CE', 'ce_open': 128.5, 'ce_high': 133.3, 'ce_low': 126.8, 'ce_close': 128.25, 'ce_volume': 190575, 'ce_oi': 2261775, 'ce_coi': -1050, 'ce_iv': 18.21, 'ce_delta': 0.46, 'ce_gamma': 0.0011, 'ce_theta': -30.43, 'ce_vega': 7.98, 'ce_rho': 78.1655, 'pe_symbol': 'NIFTY02JAN2523650PE', 'pe_open': 153.0, 'pe_high': 155.1, 'pe_low': 148.1, 'pe_close': 153.1, 'pe_volume': 145650, 'pe_oi': 1755600, 'pe_coi': 5700, 'pe_iv': 16.68, 'pe_delta': -0.54, 'pe_gamma': 0.0012, 'pe_theta': -21.6, 'pe_vega': 7.97, 'pe_rho': -93.616, 'future_open': 23742.95, 'future_high': 23756.15, 'future_low': 23740.5, 'future_close': 23741.75, 'future_volume': 6000, 'future_oi': 12877100, 'future_coi': 2550}
2025-05-25 18:44:39,334 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=SELL, Lots=1
2025-05-25 18:44:39,334 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 18:44:39,334 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 18:44:39,334 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 18:44:39,335 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -1854.9999999999998, PnL (Slippage): -1854.9999999999998, Net PnL: -1854.9999999999998
2025-05-25 18:44:39,335 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 18:44:39,335 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '2', 'entry_date': '2024-12-31', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2024-12-31', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-01-02', 'strike': 23550, 'instrument_type': 'PE', 'side': 'SELL', 'filled_quantity': 50.0, 'entry_price': 116.0, 'exit_price': 153.1, 'points': -37.099999999999994, 'pointsAfterSlippage': -37.099999999999994, 'pnl': -1854.9999999999998, 'pnlAfterSlippage': -1854.9999999999998, 'expenses': 0.0, 'netPnlAfterExpenses': -1854.9999999999998, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23559.15, 'index_exit_price': 23595.8, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2024-12-31 09:16:00', 'exit_datetime': '2024-12-31 12:00:00'}
2025-05-25 18:44:39,335 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 2, about to append. Total records before: 1
2025-05-25 18:44:39,335 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 2. Total records now: 2
2025-05-25 18:44:39,335 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 3 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:39,335 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 18:44:39,335 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 18:44:39,335 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-25 18:44:39,336 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 2
2025-05-25 18:44:39,336 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 3 of 3
2025-05-25 18:44:39,336 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type TRAIL
2025-05-25 18:44:39,339 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-25 18:44:39,339 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 3
2025-05-25 18:44:39,339 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-25 18:44:39,339 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Reached trade building section (outside all conditionals)
2025-05-25 18:44:39,339 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 2
2025-05-25 18:44:39,339 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 2, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:39,339 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23559.15, 'atm_strike': 23550.0, 'strike': 23550.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY02JAN2523550CE', 'ce_open': 135.05, 'ce_high': 139.69, 'ce_low': 133.4, 'ce_close': 139.69, 'ce_volume': 547275, 'ce_oi': 240150, 'ce_coi': 0, 'ce_iv': 15.78, 'ce_delta': 0.54, 'ce_gamma': 0.0013, 'ce_theta': -27.19, 'ce_vega': 7.97, 'ce_rho': 90.4087, 'pe_symbol': 'NIFTY02JAN2523550PE', 'pe_open': 120.05, 'pe_high': 122.65, 'pe_low': 115.9, 'pe_close': 116.0, 'pe_volume': 813750, 'pe_oi': 969150, 'pe_coi': 0, 'pe_iv': 16.09, 'pe_delta': -0.46, 'pe_gamma': 0.0012, 'pe_theta': -21.22, 'pe_vega': 7.97, 'pe_rho': -80.1808, 'future_open': 23694.4, 'future_high': 23701.8, 'future_low': 23685.0, 'future_close': 23701.8, 'future_volume': 78275, 'future_oi': 12385600, 'future_coi': 0}
2025-05-25 18:44:39,339 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': datetime.time(12, 0), 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23595.8, 'atm_strike': 23650.0, 'strike': 23650.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY02JAN2523650CE', 'ce_open': 128.5, 'ce_high': 133.3, 'ce_low': 126.8, 'ce_close': 128.25, 'ce_volume': 190575, 'ce_oi': 2261775, 'ce_coi': -1050, 'ce_iv': 18.21, 'ce_delta': 0.46, 'ce_gamma': 0.0011, 'ce_theta': -30.43, 'ce_vega': 7.98, 'ce_rho': 78.1655, 'pe_symbol': 'NIFTY02JAN2523650PE', 'pe_open': 153.0, 'pe_high': 155.1, 'pe_low': 148.1, 'pe_close': 153.1, 'pe_volume': 145650, 'pe_oi': 1755600, 'pe_coi': 5700, 'pe_iv': 16.68, 'pe_delta': -0.54, 'pe_gamma': 0.0012, 'pe_theta': -21.6, 'pe_vega': 7.97, 'pe_rho': -93.616, 'future_open': 23742.95, 'future_high': 23756.15, 'future_low': 23740.5, 'future_close': 23741.75, 'future_volume': 6000, 'future_oi': 12877100, 'future_coi': 2550}
2025-05-25 18:44:39,340 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=SELL, Lots=1
2025-05-25 18:44:39,340 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 18:44:39,340 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 18:44:39,340 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 18:44:39,340 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -1854.9999999999998, PnL (Slippage): -1854.9999999999998, Net PnL: -1854.9999999999998
2025-05-25 18:44:39,340 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 18:44:39,340 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '2', 'entry_date': '2024-12-31', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2024-12-31', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-01-02', 'strike': 23550, 'instrument_type': 'PE', 'side': 'SELL', 'filled_quantity': 50.0, 'entry_price': 116.0, 'exit_price': 153.1, 'points': -37.099999999999994, 'pointsAfterSlippage': -37.099999999999994, 'pnl': -1854.9999999999998, 'pnlAfterSlippage': -1854.9999999999998, 'expenses': 0.0, 'netPnlAfterExpenses': -1854.9999999999998, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23559.15, 'index_exit_price': 23595.8, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2024-12-31 09:16:00', 'exit_datetime': '2024-12-31 12:00:00'}
2025-05-25 18:44:39,340 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 2, about to append. Total records before: 1
2025-05-25 18:44:39,340 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 2. Total records now: 2
2025-05-25 18:44:39,340 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 3 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:39,340 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 18:44:39,340 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 18:44:39,361 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.025s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 18:44:39,383 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.042s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 18:44:39,386 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.025s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 18:44:39,386 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Query results: entry_df size=1, exit_df size=1
2025-05-25 18:44:39,386 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Got entry and exit data, proceeding...
2025-05-25 18:44:39,387 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
                        trade_date                   2024-12-31
trade_time                     09:16:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                           23559.15
atm_strike                      23550.0
strike                          23650.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY02JAN2523650CE
ce_open                           89.65
ce_high                           93.35
ce_low                             88.8
ce_close                          93.35
ce_volume                        362775
ce_oi                            837675
ce_coi                                0
ce_iv                             15.83
ce_delta                           0.41
ce_gamma                         0.0012
ce_theta                         -25.97
ce_vega                             7.8
ce_rho                           69.451
pe_symbol           NIFTY02JAN2523650PE
pe_open                           174.7
pe_high                           177.6
pe_low                           169.35
pe_close                         169.35
pe_volume                        419700
pe_oi                           1428975
pe_coi                                0
pe_iv                             16.12
pe_delta                          -0.59
pe_gamma                         0.0012
pe_theta                         -19.96
pe_vega                            7.81
pe_rho                        -101.4727
future_open                     23694.4
future_high                     23701.8
future_low                      23685.0
future_close                    23701.8
future_volume                     78275
future_oi                      12385600
future_coi                            0
2025-05-25 18:44:39,388 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2024-12-31
trade_time                     12:00:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                            23595.8
atm_strike                      23650.0
strike                          23750.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY02JAN2523750CE
ce_open                            85.1
ce_high                           88.45
ce_low                            83.95
ce_close                           85.1
ce_volume                        156975
ce_oi                           2209800
ce_coi                           -20775
ce_iv                             17.83
ce_delta                           0.34
ce_gamma                          0.001
ce_theta                         -27.46
ce_vega                            7.47
ce_rho                          59.9379
pe_symbol           NIFTY02JAN2523750PE
pe_open                           209.9
pe_high                          211.95
pe_low                           203.25
pe_close                          209.3
pe_volume                         25125
pe_oi                           1043100
pe_coi                             2925
pe_iv                             16.11
pe_delta                          -0.66
pe_gamma                         0.0011
pe_theta                         -18.06
pe_vega                            7.35
pe_rho                        -114.5129
future_open                    23742.95
future_high                    23756.15
future_low                      23740.5
future_close                   23741.75
future_volume                      6000
future_oi                      12877100
future_coi                         2550
2025-05-25 18:44:39,388 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 18:44:39,388 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 18:44:39,388 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-25 18:44:39,388 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Entering risk rules block
2025-05-25 18:44:39,389 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 18:44:39,389 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140417784366016
2025-05-25 18:44:39,389 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140417784366016, OptionType.PUT ID: 140417784366128
2025-05-25 18:44:39,389 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 18:44:39,389 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 18:44:39,389 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 18:44:39,391 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2024-12-31' AND trade_date <= '2024-12-31'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 18:44:39,407 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.025s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 18:44:39,408 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Query results: entry_df size=1, exit_df size=1
2025-05-25 18:44:39,408 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Got entry and exit data, proceeding...
2025-05-25 18:44:39,409 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
                        trade_date                   2024-12-31
trade_time                     09:16:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                           23559.15
atm_strike                      23550.0
strike                          23650.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY02JAN2523650CE
ce_open                           89.65
ce_high                           93.35
ce_low                             88.8
ce_close                          93.35
ce_volume                        362775
ce_oi                            837675
ce_coi                                0
ce_iv                             15.83
ce_delta                           0.41
ce_gamma                         0.0012
ce_theta                         -25.97
ce_vega                             7.8
ce_rho                           69.451
pe_symbol           NIFTY02JAN2523650PE
pe_open                           174.7
pe_high                           177.6
pe_low                           169.35
pe_close                         169.35
pe_volume                        419700
pe_oi                           1428975
pe_coi                                0
pe_iv                             16.12
pe_delta                          -0.59
pe_gamma                         0.0012
pe_theta                         -19.96
pe_vega                            7.81
pe_rho                        -101.4727
future_open                     23694.4
future_high                     23701.8
future_low                      23685.0
future_close                    23701.8
future_volume                     78275
future_oi                      12385600
future_coi                            0
2025-05-25 18:44:39,410 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2024-12-31
trade_time                     12:00:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                            23595.8
atm_strike                      23650.0
strike                          23750.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY02JAN2523750CE
ce_open                            85.1
ce_high                           88.45
ce_low                            83.95
ce_close                           85.1
ce_volume                        156975
ce_oi                           2209800
ce_coi                           -20775
ce_iv                             17.83
ce_delta                           0.34
ce_gamma                          0.001
ce_theta                         -27.46
ce_vega                            7.47
ce_rho                          59.9379
pe_symbol           NIFTY02JAN2523750PE
pe_open                           209.9
pe_high                          211.95
pe_low                           203.25
pe_close                          209.3
pe_volume                         25125
pe_oi                           1043100
pe_coi                             2925
pe_iv                             16.11
pe_delta                          -0.66
pe_gamma                         0.0011
pe_theta                         -18.06
pe_vega                            7.35
pe_rho                        -114.5129
future_open                    23742.95
future_high                    23756.15
future_low                      23740.5
future_close                   23741.75
future_volume                      6000
future_oi                      12877100
future_coi                         2550
2025-05-25 18:44:39,410 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 18:44:39,410 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 18:44:39,410 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-25 18:44:39,410 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Entering risk rules block
2025-05-25 18:44:39,410 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 18:44:39,410 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140219477509056
2025-05-25 18:44:39,411 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140219477509056, OptionType.PUT ID: 140219477509168
2025-05-25 18:44:39,411 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 18:44:39,411 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 18:44:39,411 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 18:44:39,412 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2024-12-31' AND trade_date <= '2024-12-31'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 18:44:39,438 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.320s, returned 25446 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 18:44:39,440 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 25446 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 18:44:39,441 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 18:44:39,512 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 18:44:39,516 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 2 - Tick data for specific strike 23550.0 is EMPTY after filtering windowed data.
2025-05-25 18:44:39,516 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Starting risk rules loop, has 3 rules
2025-05-25 18:44:39,516 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 1 of 3
2025-05-25 18:44:39,516 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-25 18:44:39,527 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-25 18:44:39,527 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 1
2025-05-25 18:44:39,527 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 2 of 3
2025-05-25 18:44:39,528 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type TAKEPROFIT
2025-05-25 18:44:39,538 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-25 18:44:39,538 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 2
2025-05-25 18:44:39,538 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 3 of 3
2025-05-25 18:44:39,539 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type TRAIL
2025-05-25 18:44:39,541 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-25 18:44:39,542 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 3
2025-05-25 18:44:39,542 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-25 18:44:39,542 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Reached trade building section (outside all conditionals)
2025-05-25 18:44:39,542 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 2
2025-05-25 18:44:39,542 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 2, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:39,542 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23559.15, 'atm_strike': 23550.0, 'strike': 23550.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY02JAN2523550CE', 'ce_open': 135.05, 'ce_high': 139.69, 'ce_low': 133.4, 'ce_close': 139.69, 'ce_volume': 547275, 'ce_oi': 240150, 'ce_coi': 0, 'ce_iv': 15.78, 'ce_delta': 0.54, 'ce_gamma': 0.0013, 'ce_theta': -27.19, 'ce_vega': 7.97, 'ce_rho': 90.4087, 'pe_symbol': 'NIFTY02JAN2523550PE', 'pe_open': 120.05, 'pe_high': 122.65, 'pe_low': 115.9, 'pe_close': 116.0, 'pe_volume': 813750, 'pe_oi': 969150, 'pe_coi': 0, 'pe_iv': 16.09, 'pe_delta': -0.46, 'pe_gamma': 0.0012, 'pe_theta': -21.22, 'pe_vega': 7.97, 'pe_rho': -80.1808, 'future_open': 23694.4, 'future_high': 23701.8, 'future_low': 23685.0, 'future_close': 23701.8, 'future_volume': 78275, 'future_oi': 12385600, 'future_coi': 0}
2025-05-25 18:44:39,542 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': datetime.time(12, 0), 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23595.8, 'atm_strike': 23650.0, 'strike': 23650.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY02JAN2523650CE', 'ce_open': 128.5, 'ce_high': 133.3, 'ce_low': 126.8, 'ce_close': 128.25, 'ce_volume': 190575, 'ce_oi': 2261775, 'ce_coi': -1050, 'ce_iv': 18.21, 'ce_delta': 0.46, 'ce_gamma': 0.0011, 'ce_theta': -30.43, 'ce_vega': 7.98, 'ce_rho': 78.1655, 'pe_symbol': 'NIFTY02JAN2523650PE', 'pe_open': 153.0, 'pe_high': 155.1, 'pe_low': 148.1, 'pe_close': 153.1, 'pe_volume': 145650, 'pe_oi': 1755600, 'pe_coi': 5700, 'pe_iv': 16.68, 'pe_delta': -0.54, 'pe_gamma': 0.0012, 'pe_theta': -21.6, 'pe_vega': 7.97, 'pe_rho': -93.616, 'future_open': 23742.95, 'future_high': 23756.15, 'future_low': 23740.5, 'future_close': 23741.75, 'future_volume': 6000, 'future_oi': 12877100, 'future_coi': 2550}
2025-05-25 18:44:39,542 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=SELL, Lots=1
2025-05-25 18:44:39,542 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 18:44:39,543 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 18:44:39,543 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 18:44:39,543 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -1854.9999999999998, PnL (Slippage): -1854.9999999999998, Net PnL: -1854.9999999999998
2025-05-25 18:44:39,543 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 18:44:39,543 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '2', 'entry_date': '2024-12-31', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2024-12-31', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-01-02', 'strike': 23550, 'instrument_type': 'PE', 'side': 'SELL', 'filled_quantity': 50.0, 'entry_price': 116.0, 'exit_price': 153.1, 'points': -37.099999999999994, 'pointsAfterSlippage': -37.099999999999994, 'pnl': -1854.9999999999998, 'pnlAfterSlippage': -1854.9999999999998, 'expenses': 0.0, 'netPnlAfterExpenses': -1854.9999999999998, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23559.15, 'index_exit_price': 23595.8, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2024-12-31 09:16:00', 'exit_datetime': '2024-12-31 12:00:00'}
2025-05-25 18:44:39,543 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 2, about to append. Total records before: 1
2025-05-25 18:44:39,544 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 2. Total records now: 2
2025-05-25 18:44:39,544 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 3 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:39,544 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 18:44:39,544 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 18:44:39,586 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.041s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 18:44:39,613 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.027s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 18:44:39,613 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Query results: entry_df size=1, exit_df size=1
2025-05-25 18:44:39,614 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Got entry and exit data, proceeding...
2025-05-25 18:44:39,615 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
                        trade_date                   2024-12-31
trade_time                     09:16:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                           23559.15
atm_strike                      23550.0
strike                          23650.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY02JAN2523650CE
ce_open                           89.65
ce_high                           93.35
ce_low                             88.8
ce_close                          93.35
ce_volume                        362775
ce_oi                            837675
ce_coi                                0
ce_iv                             15.83
ce_delta                           0.41
ce_gamma                         0.0012
ce_theta                         -25.97
ce_vega                             7.8
ce_rho                           69.451
pe_symbol           NIFTY02JAN2523650PE
pe_open                           174.7
pe_high                           177.6
pe_low                           169.35
pe_close                         169.35
pe_volume                        419700
pe_oi                           1428975
pe_coi                                0
pe_iv                             16.12
pe_delta                          -0.59
pe_gamma                         0.0012
pe_theta                         -19.96
pe_vega                            7.81
pe_rho                        -101.4727
future_open                     23694.4
future_high                     23701.8
future_low                      23685.0
future_close                    23701.8
future_volume                     78275
future_oi                      12385600
future_coi                            0
2025-05-25 18:44:39,615 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2024-12-31
trade_time                     12:00:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                            23595.8
atm_strike                      23650.0
strike                          23750.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY02JAN2523750CE
ce_open                            85.1
ce_high                           88.45
ce_low                            83.95
ce_close                           85.1
ce_volume                        156975
ce_oi                           2209800
ce_coi                           -20775
ce_iv                             17.83
ce_delta                           0.34
ce_gamma                          0.001
ce_theta                         -27.46
ce_vega                            7.47
ce_rho                          59.9379
pe_symbol           NIFTY02JAN2523750PE
pe_open                           209.9
pe_high                          211.95
pe_low                           203.25
pe_close                          209.3
pe_volume                         25125
pe_oi                           1043100
pe_coi                             2925
pe_iv                             16.11
pe_delta                          -0.66
pe_gamma                         0.0011
pe_theta                         -18.06
pe_vega                            7.35
pe_rho                        -114.5129
future_open                    23742.95
future_high                    23756.15
future_low                      23740.5
future_close                   23741.75
future_volume                      6000
future_oi                      12877100
future_coi                         2550
2025-05-25 18:44:39,616 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 18:44:39,616 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 18:44:39,616 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-25 18:44:39,616 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Entering risk rules block
2025-05-25 18:44:39,616 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 18:44:39,616 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 139631160345536
2025-05-25 18:44:39,616 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139631160345536, OptionType.PUT ID: 139631160345648
2025-05-25 18:44:39,616 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 18:44:39,617 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 18:44:39,617 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 18:44:39,618 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2024-12-31' AND trade_date <= '2024-12-31'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 18:44:39,638 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.393s, returned 25446 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 18:44:39,641 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 25446 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 18:44:39,642 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 18:44:39,719 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 18:44:39,724 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 2 - Tick data for specific strike 23550.0 is EMPTY after filtering windowed data.
2025-05-25 18:44:39,724 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Starting risk rules loop, has 3 rules
2025-05-25 18:44:39,724 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 1 of 3
2025-05-25 18:44:39,724 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-25 18:44:39,735 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.323s, returned 25446 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 18:44:39,736 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-25 18:44:39,736 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 1
2025-05-25 18:44:39,736 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 2 of 3
2025-05-25 18:44:39,736 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type TAKEPROFIT
2025-05-25 18:44:39,738 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 25446 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 18:44:39,739 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 18:44:39,747 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-25 18:44:39,748 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 2
2025-05-25 18:44:39,748 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 3 of 3
2025-05-25 18:44:39,748 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type TRAIL
2025-05-25 18:44:39,751 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-25 18:44:39,751 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 3
2025-05-25 18:44:39,751 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-25 18:44:39,751 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Reached trade building section (outside all conditionals)
2025-05-25 18:44:39,751 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 2
2025-05-25 18:44:39,752 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 2, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:39,752 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23559.15, 'atm_strike': 23550.0, 'strike': 23550.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY02JAN2523550CE', 'ce_open': 135.05, 'ce_high': 139.69, 'ce_low': 133.4, 'ce_close': 139.69, 'ce_volume': 547275, 'ce_oi': 240150, 'ce_coi': 0, 'ce_iv': 15.78, 'ce_delta': 0.54, 'ce_gamma': 0.0013, 'ce_theta': -27.19, 'ce_vega': 7.97, 'ce_rho': 90.4087, 'pe_symbol': 'NIFTY02JAN2523550PE', 'pe_open': 120.05, 'pe_high': 122.65, 'pe_low': 115.9, 'pe_close': 116.0, 'pe_volume': 813750, 'pe_oi': 969150, 'pe_coi': 0, 'pe_iv': 16.09, 'pe_delta': -0.46, 'pe_gamma': 0.0012, 'pe_theta': -21.22, 'pe_vega': 7.97, 'pe_rho': -80.1808, 'future_open': 23694.4, 'future_high': 23701.8, 'future_low': 23685.0, 'future_close': 23701.8, 'future_volume': 78275, 'future_oi': 12385600, 'future_coi': 0}
2025-05-25 18:44:39,752 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': datetime.time(12, 0), 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23595.8, 'atm_strike': 23650.0, 'strike': 23650.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY02JAN2523650CE', 'ce_open': 128.5, 'ce_high': 133.3, 'ce_low': 126.8, 'ce_close': 128.25, 'ce_volume': 190575, 'ce_oi': 2261775, 'ce_coi': -1050, 'ce_iv': 18.21, 'ce_delta': 0.46, 'ce_gamma': 0.0011, 'ce_theta': -30.43, 'ce_vega': 7.98, 'ce_rho': 78.1655, 'pe_symbol': 'NIFTY02JAN2523650PE', 'pe_open': 153.0, 'pe_high': 155.1, 'pe_low': 148.1, 'pe_close': 153.1, 'pe_volume': 145650, 'pe_oi': 1755600, 'pe_coi': 5700, 'pe_iv': 16.68, 'pe_delta': -0.54, 'pe_gamma': 0.0012, 'pe_theta': -21.6, 'pe_vega': 7.97, 'pe_rho': -93.616, 'future_open': 23742.95, 'future_high': 23756.15, 'future_low': 23740.5, 'future_close': 23741.75, 'future_volume': 6000, 'future_oi': 12877100, 'future_coi': 2550}
2025-05-25 18:44:39,752 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=SELL, Lots=1
2025-05-25 18:44:39,752 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 18:44:39,752 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 18:44:39,752 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 18:44:39,752 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -1854.9999999999998, PnL (Slippage): -1854.9999999999998, Net PnL: -1854.9999999999998
2025-05-25 18:44:39,753 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 18:44:39,753 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '2', 'entry_date': '2024-12-31', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2024-12-31', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-01-02', 'strike': 23550, 'instrument_type': 'PE', 'side': 'SELL', 'filled_quantity': 50.0, 'entry_price': 116.0, 'exit_price': 153.1, 'points': -37.099999999999994, 'pointsAfterSlippage': -37.099999999999994, 'pnl': -1854.9999999999998, 'pnlAfterSlippage': -1854.9999999999998, 'expenses': 0.0, 'netPnlAfterExpenses': -1854.9999999999998, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23559.15, 'index_exit_price': 23595.8, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2024-12-31 09:16:00', 'exit_datetime': '2024-12-31 12:00:00'}
2025-05-25 18:44:39,753 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 2, about to append. Total records before: 1
2025-05-25 18:44:39,753 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 2. Total records now: 2
2025-05-25 18:44:39,753 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 3 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:39,753 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 18:44:39,753 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 18:44:39,783 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.029s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 18:44:39,819 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 18:44:39,822 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.039s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 18:44:39,822 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Query results: entry_df size=1, exit_df size=1
2025-05-25 18:44:39,823 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Got entry and exit data, proceeding...
2025-05-25 18:44:39,824 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
                        trade_date                   2024-12-31
trade_time                     09:16:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                           23559.15
atm_strike                      23550.0
strike                          23650.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY02JAN2523650CE
ce_open                           89.65
ce_high                           93.35
ce_low                             88.8
ce_close                          93.35
ce_volume                        362775
ce_oi                            837675
ce_coi                                0
ce_iv                             15.83
ce_delta                           0.41
ce_gamma                         0.0012
ce_theta                         -25.97
ce_vega                             7.8
ce_rho                           69.451
pe_symbol           NIFTY02JAN2523650PE
pe_open                           174.7
pe_high                           177.6
pe_low                           169.35
pe_close                         169.35
pe_volume                        419700
pe_oi                           1428975
pe_coi                                0
pe_iv                             16.12
pe_delta                          -0.59
pe_gamma                         0.0012
pe_theta                         -19.96
pe_vega                            7.81
pe_rho                        -101.4727
future_open                     23694.4
future_high                     23701.8
future_low                      23685.0
future_close                    23701.8
future_volume                     78275
future_oi                      12385600
future_coi                            0
2025-05-25 18:44:39,824 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 3 - Tick data for specific strike 23650.0 is EMPTY after filtering windowed data.
2025-05-25 18:44:39,824 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Starting risk rules loop, has 3 rules
2025-05-25 18:44:39,824 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 1 of 3
2025-05-25 18:44:39,824 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-25 18:44:39,825 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2024-12-31
trade_time                     12:00:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                            23595.8
atm_strike                      23650.0
strike                          23750.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY02JAN2523750CE
ce_open                            85.1
ce_high                           88.45
ce_low                            83.95
ce_close                           85.1
ce_volume                        156975
ce_oi                           2209800
ce_coi                           -20775
ce_iv                             17.83
ce_delta                           0.34
ce_gamma                          0.001
ce_theta                         -27.46
ce_vega                            7.47
ce_rho                          59.9379
pe_symbol           NIFTY02JAN2523750PE
pe_open                           209.9
pe_high                          211.95
pe_low                           203.25
pe_close                          209.3
pe_volume                         25125
pe_oi                           1043100
pe_coi                             2925
pe_iv                             16.11
pe_delta                          -0.66
pe_gamma                         0.0011
pe_theta                         -18.06
pe_vega                            7.35
pe_rho                        -114.5129
future_open                    23742.95
future_high                    23756.15
future_low                      23740.5
future_close                   23741.75
future_volume                      6000
future_oi                      12877100
future_coi                         2550
2025-05-25 18:44:39,825 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 18:44:39,825 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 18:44:39,825 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-25 18:44:39,825 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Entering risk rules block
2025-05-25 18:44:39,825 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 18:44:39,825 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140095661654976
2025-05-25 18:44:39,826 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140095661654976, OptionType.PUT ID: 140095661655088
2025-05-25 18:44:39,826 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 18:44:39,826 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-25 18:44:39,826 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-25 18:44:39,827 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2024-12-31' AND trade_date <= '2024-12-31'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 18:44:39,832 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.441s, returned 25446 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 18:44:39,836 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 25446 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 18:44:39,836 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - evaluate_risk_rule returned: triggered=False
2025-05-25 18:44:39,836 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Finished processing risk rule 1
2025-05-25 18:44:39,836 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 2 of 3
2025-05-25 18:44:39,836 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - About to call evaluate_risk_rule for rule type TAKEPROFIT
2025-05-25 18:44:39,839 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 18:44:39,840 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - evaluate_risk_rule returned: triggered=True
2025-05-25 18:44:39,842 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Breaking out of risk rules loop due to triggered rule
2025-05-25 18:44:39,842 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-25 18:44:39,842 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Reached trade building section (outside all conditionals)
2025-05-25 18:44:39,842 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 3
2025-05-25 18:44:39,842 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 3, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:39,843 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23559.15, 'atm_strike': 23550.0, 'strike': 23650.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'OTM2', 'put_strike_type': 'ITM2', 'ce_symbol': 'NIFTY02JAN2523650CE', 'ce_open': 89.65, 'ce_high': 93.35, 'ce_low': 88.8, 'ce_close': 93.35, 'ce_volume': 362775, 'ce_oi': 837675, 'ce_coi': 0, 'ce_iv': 15.83, 'ce_delta': 0.41, 'ce_gamma': 0.0012, 'ce_theta': -25.97, 'ce_vega': 7.8, 'ce_rho': 69.451, 'pe_symbol': 'NIFTY02JAN2523650PE', 'pe_open': 174.7, 'pe_high': 177.6, 'pe_low': 169.35, 'pe_close': 169.35, 'pe_volume': 419700, 'pe_oi': 1428975, 'pe_coi': 0, 'pe_iv': 16.12, 'pe_delta': -0.59, 'pe_gamma': 0.0012, 'pe_theta': -19.96, 'pe_vega': 7.81, 'pe_rho': -101.4727, 'future_open': 23694.4, 'future_high': 23701.8, 'future_low': 23685.0, 'future_close': 23701.8, 'future_volume': 78275, 'future_oi': 12385600, 'future_coi': 0}
2025-05-25 18:44:39,843 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23595.8, 'atm_strike': 23650.0, 'strike': 23750.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'OTM2', 'put_strike_type': 'ITM2', 'ce_symbol': 'NIFTY02JAN2523750CE', 'ce_open': 85.1, 'ce_high': 88.45, 'ce_low': 83.95, 'ce_close': 85.1, 'ce_volume': 156975, 'ce_oi': 2209800, 'ce_coi': -20775, 'ce_iv': 17.83, 'ce_delta': 0.34, 'ce_gamma': 0.001, 'ce_theta': -27.46, 'ce_vega': 7.47, 'ce_rho': 59.9379, 'pe_symbol': 'NIFTY02JAN2523750PE', 'pe_open': 209.9, 'pe_high': 211.95, 'pe_low': 203.25, 'pe_close': 209.3, 'pe_volume': 25125, 'pe_oi': 1043100, 'pe_coi': 2925, 'pe_iv': 16.11, 'pe_delta': -0.66, 'pe_gamma': 0.0011, 'pe_theta': -18.06, 'pe_vega': 7.35, 'pe_rho': -114.5129, 'future_open': 23742.95, 'future_high': 23756.15, 'future_low': 23740.5, 'future_close': 23741.75, 'future_volume': 6000, 'future_oi': 12877100, 'future_coi': 2550, 'close': 2076.0, 'datetime': Timestamp('2024-12-31 12:00:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-25 18:44:39,843 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=CALL, Transaction=BUY, Lots=1
2025-05-25 18:44:39,843 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 18:44:39,843 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 18:44:39,843 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 18:44:39,843 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -412.5, PnL (Slippage): -412.5, Net PnL: -412.5
2025-05-25 18:44:39,843 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 18:44:39,843 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '3', 'entry_date': '2024-12-31', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2024-12-31', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-01-02', 'strike': 23650, 'instrument_type': 'CE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 93.35, 'exit_price': 85.1, 'points': -8.25, 'pointsAfterSlippage': -8.25, 'pnl': -412.5, 'pnlAfterSlippage': -412.5, 'expenses': 0.0, 'netPnlAfterExpenses': -412.5, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23559.15, 'index_exit_price': 23595.8, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2024-12-31 09:16:00', 'exit_datetime': '2024-12-31 12:00:00'}
2025-05-25 18:44:39,843 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 3, about to append. Total records before: 2
2025-05-25 18:44:39,843 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 3. Total records now: 3
2025-05-25 18:44:39,844 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 4 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:39,844 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 18:44:39,844 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 18:44:39,873 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.029s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 18:44:39,901 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.028s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 18:44:39,901 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Query results: entry_df size=1, exit_df size=1
2025-05-25 18:44:39,902 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Got entry and exit data, proceeding...
2025-05-25 18:44:39,902 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
                        trade_date                   2024-12-31
trade_time                     09:16:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                           23559.15
atm_strike                      23550.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY02JAN2523450CE
ce_open                           192.2
ce_high                           198.3
ce_low                            191.0
ce_close                          198.3
ce_volume                         74925
ce_oi                             66750
ce_coi                                0
ce_iv                              15.7
ce_delta                           0.66
ce_gamma                         0.0012
ce_theta                         -26.05
ce_vega                            7.36
ce_rho                         110.9442
pe_symbol           NIFTY02JAN2523450PE
pe_open                           77.09
pe_high                           79.75
pe_low                             74.7
pe_close                           74.7
pe_volume                        399600
pe_oi                           1544025
pe_coi                                0
pe_iv                             16.04
pe_delta                          -0.34
pe_gamma                         0.0011
pe_theta                         -20.16
pe_vega                            7.38
pe_rho                         -59.3506
future_open                     23694.4
future_high                     23701.8
future_low                      23685.0
future_close                    23701.8
future_volume                     78275
future_oi                      12385600
future_coi                            0
2025-05-25 18:44:39,903 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2024-12-31
trade_time                     12:00:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                            23595.8
atm_strike                      23650.0
strike                          23550.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY02JAN2523550CE
ce_open                           185.0
ce_high                           188.9
ce_low                           181.45
ce_close                         182.95
ce_volume                        187800
ce_oi                           1872075
ce_coi                           -10275
ce_iv                             18.68
ce_delta                           0.58
ce_gamma                          0.001
ce_theta                         -31.49
ce_vega                            7.89
ce_rho                          96.0607
pe_symbol           NIFTY02JAN2523550PE
pe_open                           106.2
pe_high                           109.4
pe_low                            103.5
pe_close                          107.8
pe_volume                        282900
pe_oi                           3496350
pe_coi                           -31500
pe_iv                             17.11
pe_delta                          -0.42
pe_gamma                         0.0011
pe_theta                         -22.69
pe_vega                            7.87
pe_rho                         -73.3389
future_open                    23742.95
future_high                    23756.15
future_low                      23740.5
future_close                   23741.75
future_volume                      6000
future_oi                      12877100
future_coi                         2550
2025-05-25 18:44:39,903 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 18:44:39,903 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 18:44:39,903 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-25 18:44:39,903 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Entering risk rules block
2025-05-25 18:44:39,904 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 18:44:39,904 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140219477509168
2025-05-25 18:44:39,904 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140219477509056, OptionType.PUT ID: 140219477509168
2025-05-25 18:44:39,904 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 18:44:39,904 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 18:44:39,904 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 18:44:39,905 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2024-12-31' AND trade_date <= '2024-12-31'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 18:44:39,950 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 18:44:39,956 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 3 - Tick data for specific strike 23650.0 is EMPTY after filtering windowed data.
2025-05-25 18:44:39,956 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Starting risk rules loop, has 3 rules
2025-05-25 18:44:39,956 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 1 of 3
2025-05-25 18:44:39,956 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-25 18:44:39,972 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - evaluate_risk_rule returned: triggered=False
2025-05-25 18:44:39,972 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Finished processing risk rule 1
2025-05-25 18:44:39,972 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 2 of 3
2025-05-25 18:44:39,972 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - About to call evaluate_risk_rule for rule type TAKEPROFIT
2025-05-25 18:44:39,976 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - evaluate_risk_rule returned: triggered=True
2025-05-25 18:44:39,979 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Breaking out of risk rules loop due to triggered rule
2025-05-25 18:44:39,979 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-25 18:44:39,979 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Reached trade building section (outside all conditionals)
2025-05-25 18:44:39,979 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 3
2025-05-25 18:44:39,979 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 3, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:39,980 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23559.15, 'atm_strike': 23550.0, 'strike': 23650.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'OTM2', 'put_strike_type': 'ITM2', 'ce_symbol': 'NIFTY02JAN2523650CE', 'ce_open': 89.65, 'ce_high': 93.35, 'ce_low': 88.8, 'ce_close': 93.35, 'ce_volume': 362775, 'ce_oi': 837675, 'ce_coi': 0, 'ce_iv': 15.83, 'ce_delta': 0.41, 'ce_gamma': 0.0012, 'ce_theta': -25.97, 'ce_vega': 7.8, 'ce_rho': 69.451, 'pe_symbol': 'NIFTY02JAN2523650PE', 'pe_open': 174.7, 'pe_high': 177.6, 'pe_low': 169.35, 'pe_close': 169.35, 'pe_volume': 419700, 'pe_oi': 1428975, 'pe_coi': 0, 'pe_iv': 16.12, 'pe_delta': -0.59, 'pe_gamma': 0.0012, 'pe_theta': -19.96, 'pe_vega': 7.81, 'pe_rho': -101.4727, 'future_open': 23694.4, 'future_high': 23701.8, 'future_low': 23685.0, 'future_close': 23701.8, 'future_volume': 78275, 'future_oi': 12385600, 'future_coi': 0}
2025-05-25 18:44:39,980 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23595.8, 'atm_strike': 23650.0, 'strike': 23750.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'OTM2', 'put_strike_type': 'ITM2', 'ce_symbol': 'NIFTY02JAN2523750CE', 'ce_open': 85.1, 'ce_high': 88.45, 'ce_low': 83.95, 'ce_close': 85.1, 'ce_volume': 156975, 'ce_oi': 2209800, 'ce_coi': -20775, 'ce_iv': 17.83, 'ce_delta': 0.34, 'ce_gamma': 0.001, 'ce_theta': -27.46, 'ce_vega': 7.47, 'ce_rho': 59.9379, 'pe_symbol': 'NIFTY02JAN2523750PE', 'pe_open': 209.9, 'pe_high': 211.95, 'pe_low': 203.25, 'pe_close': 209.3, 'pe_volume': 25125, 'pe_oi': 1043100, 'pe_coi': 2925, 'pe_iv': 16.11, 'pe_delta': -0.66, 'pe_gamma': 0.0011, 'pe_theta': -18.06, 'pe_vega': 7.35, 'pe_rho': -114.5129, 'future_open': 23742.95, 'future_high': 23756.15, 'future_low': 23740.5, 'future_close': 23741.75, 'future_volume': 6000, 'future_oi': 12877100, 'future_coi': 2550, 'close': 2076.0, 'datetime': Timestamp('2024-12-31 12:00:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-25 18:44:39,980 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=CALL, Transaction=BUY, Lots=1
2025-05-25 18:44:39,980 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 18:44:39,980 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 18:44:39,980 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 18:44:39,980 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -412.5, PnL (Slippage): -412.5, Net PnL: -412.5
2025-05-25 18:44:39,981 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 18:44:39,981 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '3', 'entry_date': '2024-12-31', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2024-12-31', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-01-02', 'strike': 23650, 'instrument_type': 'CE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 93.35, 'exit_price': 85.1, 'points': -8.25, 'pointsAfterSlippage': -8.25, 'pnl': -412.5, 'pnlAfterSlippage': -412.5, 'expenses': 0.0, 'netPnlAfterExpenses': -412.5, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23559.15, 'index_exit_price': 23595.8, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2024-12-31 09:16:00', 'exit_datetime': '2024-12-31 12:00:00'}
2025-05-25 18:44:39,981 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 3, about to append. Total records before: 2
2025-05-25 18:44:39,981 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 3. Total records now: 3
2025-05-25 18:44:39,981 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 4 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:39,981 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 18:44:39,981 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 18:44:40,015 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.397s, returned 25446 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 18:44:40,018 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 25446 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 18:44:40,019 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 18:44:40,019 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.037s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 18:44:40,047 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.028s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 18:44:40,048 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Query results: entry_df size=1, exit_df size=1
2025-05-25 18:44:40,048 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Got entry and exit data, proceeding...
2025-05-25 18:44:40,049 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
                        trade_date                   2024-12-31
trade_time                     09:16:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                           23559.15
atm_strike                      23550.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY02JAN2523450CE
ce_open                           192.2
ce_high                           198.3
ce_low                            191.0
ce_close                          198.3
ce_volume                         74925
ce_oi                             66750
ce_coi                                0
ce_iv                              15.7
ce_delta                           0.66
ce_gamma                         0.0012
ce_theta                         -26.05
ce_vega                            7.36
ce_rho                         110.9442
pe_symbol           NIFTY02JAN2523450PE
pe_open                           77.09
pe_high                           79.75
pe_low                             74.7
pe_close                           74.7
pe_volume                        399600
pe_oi                           1544025
pe_coi                                0
pe_iv                             16.04
pe_delta                          -0.34
pe_gamma                         0.0011
pe_theta                         -20.16
pe_vega                            7.38
pe_rho                         -59.3506
future_open                     23694.4
future_high                     23701.8
future_low                      23685.0
future_close                    23701.8
future_volume                     78275
future_oi                      12385600
future_coi                            0
2025-05-25 18:44:40,050 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2024-12-31
trade_time                     12:00:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                            23595.8
atm_strike                      23650.0
strike                          23550.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY02JAN2523550CE
ce_open                           185.0
ce_high                           188.9
ce_low                           181.45
ce_close                         182.95
ce_volume                        187800
ce_oi                           1872075
ce_coi                           -10275
ce_iv                             18.68
ce_delta                           0.58
ce_gamma                          0.001
ce_theta                         -31.49
ce_vega                            7.89
ce_rho                          96.0607
pe_symbol           NIFTY02JAN2523550PE
pe_open                           106.2
pe_high                           109.4
pe_low                            103.5
pe_close                          107.8
pe_volume                        282900
pe_oi                           3496350
pe_coi                           -31500
pe_iv                             17.11
pe_delta                          -0.42
pe_gamma                         0.0011
pe_theta                         -22.69
pe_vega                            7.87
pe_rho                         -73.3389
future_open                    23742.95
future_high                    23756.15
future_low                      23740.5
future_close                   23741.75
future_volume                      6000
future_oi                      12877100
future_coi                         2550
2025-05-25 18:44:40,050 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 18:44:40,050 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 18:44:40,050 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-25 18:44:40,051 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Entering risk rules block
2025-05-25 18:44:40,052 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 18:44:40,052 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140417784366128
2025-05-25 18:44:40,052 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140417784366016, OptionType.PUT ID: 140417784366128
2025-05-25 18:44:40,052 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 18:44:40,053 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 18:44:40,053 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 18:44:40,054 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2024-12-31' AND trade_date <= '2024-12-31'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 18:44:40,093 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 18:44:40,098 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 3 - Tick data for specific strike 23650.0 is EMPTY after filtering windowed data.
2025-05-25 18:44:40,098 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Starting risk rules loop, has 3 rules
2025-05-25 18:44:40,098 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 1 of 3
2025-05-25 18:44:40,098 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-25 18:44:40,110 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - evaluate_risk_rule returned: triggered=False
2025-05-25 18:44:40,110 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Finished processing risk rule 1
2025-05-25 18:44:40,110 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 2 of 3
2025-05-25 18:44:40,110 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - About to call evaluate_risk_rule for rule type TAKEPROFIT
2025-05-25 18:44:40,114 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - evaluate_risk_rule returned: triggered=True
2025-05-25 18:44:40,116 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Breaking out of risk rules loop due to triggered rule
2025-05-25 18:44:40,116 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-25 18:44:40,116 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Reached trade building section (outside all conditionals)
2025-05-25 18:44:40,116 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 3
2025-05-25 18:44:40,116 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 3, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:40,116 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23559.15, 'atm_strike': 23550.0, 'strike': 23650.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'OTM2', 'put_strike_type': 'ITM2', 'ce_symbol': 'NIFTY02JAN2523650CE', 'ce_open': 89.65, 'ce_high': 93.35, 'ce_low': 88.8, 'ce_close': 93.35, 'ce_volume': 362775, 'ce_oi': 837675, 'ce_coi': 0, 'ce_iv': 15.83, 'ce_delta': 0.41, 'ce_gamma': 0.0012, 'ce_theta': -25.97, 'ce_vega': 7.8, 'ce_rho': 69.451, 'pe_symbol': 'NIFTY02JAN2523650PE', 'pe_open': 174.7, 'pe_high': 177.6, 'pe_low': 169.35, 'pe_close': 169.35, 'pe_volume': 419700, 'pe_oi': 1428975, 'pe_coi': 0, 'pe_iv': 16.12, 'pe_delta': -0.59, 'pe_gamma': 0.0012, 'pe_theta': -19.96, 'pe_vega': 7.81, 'pe_rho': -101.4727, 'future_open': 23694.4, 'future_high': 23701.8, 'future_low': 23685.0, 'future_close': 23701.8, 'future_volume': 78275, 'future_oi': 12385600, 'future_coi': 0}
2025-05-25 18:44:40,116 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23595.8, 'atm_strike': 23650.0, 'strike': 23750.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'OTM2', 'put_strike_type': 'ITM2', 'ce_symbol': 'NIFTY02JAN2523750CE', 'ce_open': 85.1, 'ce_high': 88.45, 'ce_low': 83.95, 'ce_close': 85.1, 'ce_volume': 156975, 'ce_oi': 2209800, 'ce_coi': -20775, 'ce_iv': 17.83, 'ce_delta': 0.34, 'ce_gamma': 0.001, 'ce_theta': -27.46, 'ce_vega': 7.47, 'ce_rho': 59.9379, 'pe_symbol': 'NIFTY02JAN2523750PE', 'pe_open': 209.9, 'pe_high': 211.95, 'pe_low': 203.25, 'pe_close': 209.3, 'pe_volume': 25125, 'pe_oi': 1043100, 'pe_coi': 2925, 'pe_iv': 16.11, 'pe_delta': -0.66, 'pe_gamma': 0.0011, 'pe_theta': -18.06, 'pe_vega': 7.35, 'pe_rho': -114.5129, 'future_open': 23742.95, 'future_high': 23756.15, 'future_low': 23740.5, 'future_close': 23741.75, 'future_volume': 6000, 'future_oi': 12877100, 'future_coi': 2550, 'close': 2076.0, 'datetime': Timestamp('2024-12-31 12:00:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-25 18:44:40,116 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=CALL, Transaction=BUY, Lots=1
2025-05-25 18:44:40,117 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 18:44:40,117 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 18:44:40,117 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 18:44:40,117 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -412.5, PnL (Slippage): -412.5, Net PnL: -412.5
2025-05-25 18:44:40,117 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 18:44:40,117 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '3', 'entry_date': '2024-12-31', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2024-12-31', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-01-02', 'strike': 23650, 'instrument_type': 'CE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 93.35, 'exit_price': 85.1, 'points': -8.25, 'pointsAfterSlippage': -8.25, 'pnl': -412.5, 'pnlAfterSlippage': -412.5, 'expenses': 0.0, 'netPnlAfterExpenses': -412.5, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23559.15, 'index_exit_price': 23595.8, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2024-12-31 09:16:00', 'exit_datetime': '2024-12-31 12:00:00'}
2025-05-25 18:44:40,117 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 3, about to append. Total records before: 2
2025-05-25 18:44:40,117 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 3. Total records now: 3
2025-05-25 18:44:40,117 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 4 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:40,118 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 18:44:40,118 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 18:44:40,145 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.027s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 18:44:40,174 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.029s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 18:44:40,174 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Query results: entry_df size=1, exit_df size=1
2025-05-25 18:44:40,175 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Got entry and exit data, proceeding...
2025-05-25 18:44:40,176 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
                        trade_date                   2024-12-31
trade_time                     09:16:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                           23559.15
atm_strike                      23550.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY02JAN2523450CE
ce_open                           192.2
ce_high                           198.3
ce_low                            191.0
ce_close                          198.3
ce_volume                         74925
ce_oi                             66750
ce_coi                                0
ce_iv                              15.7
ce_delta                           0.66
ce_gamma                         0.0012
ce_theta                         -26.05
ce_vega                            7.36
ce_rho                         110.9442
pe_symbol           NIFTY02JAN2523450PE
pe_open                           77.09
pe_high                           79.75
pe_low                             74.7
pe_close                           74.7
pe_volume                        399600
pe_oi                           1544025
pe_coi                                0
pe_iv                             16.04
pe_delta                          -0.34
pe_gamma                         0.0011
pe_theta                         -20.16
pe_vega                            7.38
pe_rho                         -59.3506
future_open                     23694.4
future_high                     23701.8
future_low                      23685.0
future_close                    23701.8
future_volume                     78275
future_oi                      12385600
future_coi                            0
2025-05-25 18:44:40,176 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2024-12-31
trade_time                     12:00:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                            23595.8
atm_strike                      23650.0
strike                          23550.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY02JAN2523550CE
ce_open                           185.0
ce_high                           188.9
ce_low                           181.45
ce_close                         182.95
ce_volume                        187800
ce_oi                           1872075
ce_coi                           -10275
ce_iv                             18.68
ce_delta                           0.58
ce_gamma                          0.001
ce_theta                         -31.49
ce_vega                            7.89
ce_rho                          96.0607
pe_symbol           NIFTY02JAN2523550PE
pe_open                           106.2
pe_high                           109.4
pe_low                            103.5
pe_close                          107.8
pe_volume                        282900
pe_oi                           3496350
pe_coi                           -31500
pe_iv                             17.11
pe_delta                          -0.42
pe_gamma                         0.0011
pe_theta                         -22.69
pe_vega                            7.87
pe_rho                         -73.3389
future_open                    23742.95
future_high                    23756.15
future_low                      23740.5
future_close                   23741.75
future_volume                      6000
future_oi                      12877100
future_coi                         2550
2025-05-25 18:44:40,177 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 18:44:40,177 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 18:44:40,177 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-25 18:44:40,177 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Entering risk rules block
2025-05-25 18:44:40,177 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 18:44:40,177 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 139631160345648
2025-05-25 18:44:40,177 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139631160345536, OptionType.PUT ID: 139631160345648
2025-05-25 18:44:40,177 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 18:44:40,178 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 18:44:40,178 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 18:44:40,179 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2024-12-31' AND trade_date <= '2024-12-31'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 18:44:40,202 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.375s, returned 25446 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 18:44:40,205 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 25446 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-25 18:44:40,206 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-25 18:44:40,284 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 18:44:40,289 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 3 - Tick data for specific strike 23650.0 is EMPTY after filtering windowed data.
2025-05-25 18:44:40,289 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Starting risk rules loop, has 3 rules
2025-05-25 18:44:40,289 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 1 of 3
2025-05-25 18:44:40,289 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-25 18:44:40,300 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - evaluate_risk_rule returned: triggered=False
2025-05-25 18:44:40,300 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Finished processing risk rule 1
2025-05-25 18:44:40,301 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 2 of 3
2025-05-25 18:44:40,301 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - About to call evaluate_risk_rule for rule type TAKEPROFIT
2025-05-25 18:44:40,304 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - evaluate_risk_rule returned: triggered=True
2025-05-25 18:44:40,305 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.399s, returned 25446 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 18:44:40,306 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Breaking out of risk rules loop due to triggered rule
2025-05-25 18:44:40,306 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-25 18:44:40,306 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Reached trade building section (outside all conditionals)
2025-05-25 18:44:40,307 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 3
2025-05-25 18:44:40,307 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 3, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:40,307 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23559.15, 'atm_strike': 23550.0, 'strike': 23650.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'OTM2', 'put_strike_type': 'ITM2', 'ce_symbol': 'NIFTY02JAN2523650CE', 'ce_open': 89.65, 'ce_high': 93.35, 'ce_low': 88.8, 'ce_close': 93.35, 'ce_volume': 362775, 'ce_oi': 837675, 'ce_coi': 0, 'ce_iv': 15.83, 'ce_delta': 0.41, 'ce_gamma': 0.0012, 'ce_theta': -25.97, 'ce_vega': 7.8, 'ce_rho': 69.451, 'pe_symbol': 'NIFTY02JAN2523650PE', 'pe_open': 174.7, 'pe_high': 177.6, 'pe_low': 169.35, 'pe_close': 169.35, 'pe_volume': 419700, 'pe_oi': 1428975, 'pe_coi': 0, 'pe_iv': 16.12, 'pe_delta': -0.59, 'pe_gamma': 0.0012, 'pe_theta': -19.96, 'pe_vega': 7.81, 'pe_rho': -101.4727, 'future_open': 23694.4, 'future_high': 23701.8, 'future_low': 23685.0, 'future_close': 23701.8, 'future_volume': 78275, 'future_oi': 12385600, 'future_coi': 0}
2025-05-25 18:44:40,307 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23595.8, 'atm_strike': 23650.0, 'strike': 23750.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'OTM2', 'put_strike_type': 'ITM2', 'ce_symbol': 'NIFTY02JAN2523750CE', 'ce_open': 85.1, 'ce_high': 88.45, 'ce_low': 83.95, 'ce_close': 85.1, 'ce_volume': 156975, 'ce_oi': 2209800, 'ce_coi': -20775, 'ce_iv': 17.83, 'ce_delta': 0.34, 'ce_gamma': 0.001, 'ce_theta': -27.46, 'ce_vega': 7.47, 'ce_rho': 59.9379, 'pe_symbol': 'NIFTY02JAN2523750PE', 'pe_open': 209.9, 'pe_high': 211.95, 'pe_low': 203.25, 'pe_close': 209.3, 'pe_volume': 25125, 'pe_oi': 1043100, 'pe_coi': 2925, 'pe_iv': 16.11, 'pe_delta': -0.66, 'pe_gamma': 0.0011, 'pe_theta': -18.06, 'pe_vega': 7.35, 'pe_rho': -114.5129, 'future_open': 23742.95, 'future_high': 23756.15, 'future_low': 23740.5, 'future_close': 23741.75, 'future_volume': 6000, 'future_oi': 12877100, 'future_coi': 2550, 'close': 2076.0, 'datetime': Timestamp('2024-12-31 12:00:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-25 18:44:40,307 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=CALL, Transaction=BUY, Lots=1
2025-05-25 18:44:40,307 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 18:44:40,307 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 18:44:40,307 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 18:44:40,307 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -412.5, PnL (Slippage): -412.5, Net PnL: -412.5
2025-05-25 18:44:40,308 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 18:44:40,307 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 25446 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 18:44:40,308 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '3', 'entry_date': '2024-12-31', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2024-12-31', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-01-02', 'strike': 23650, 'instrument_type': 'CE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 93.35, 'exit_price': 85.1, 'points': -8.25, 'pointsAfterSlippage': -8.25, 'pnl': -412.5, 'pnlAfterSlippage': -412.5, 'expenses': 0.0, 'netPnlAfterExpenses': -412.5, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23559.15, 'index_exit_price': 23595.8, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2024-12-31 09:16:00', 'exit_datetime': '2024-12-31 12:00:00'}
2025-05-25 18:44:40,308 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 3, about to append. Total records before: 2
2025-05-25 18:44:40,308 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 3. Total records now: 3
2025-05-25 18:44:40,308 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 4 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:40,308 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-25 18:44:40,308 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-12-31' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-25 18:44:40,309 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 18:44:40,340 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.031s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-25 18:44:40,370 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.030s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-12-31'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-25 18:44:40,370 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Query results: entry_df size=1, exit_df size=1
2025-05-25 18:44:40,371 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Got entry and exit data, proceeding...
2025-05-25 18:44:40,372 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
                        trade_date                   2024-12-31
trade_time                     09:16:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                           23559.15
atm_strike                      23550.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY02JAN2523450CE
ce_open                           192.2
ce_high                           198.3
ce_low                            191.0
ce_close                          198.3
ce_volume                         74925
ce_oi                             66750
ce_coi                                0
ce_iv                              15.7
ce_delta                           0.66
ce_gamma                         0.0012
ce_theta                         -26.05
ce_vega                            7.36
ce_rho                         110.9442
pe_symbol           NIFTY02JAN2523450PE
pe_open                           77.09
pe_high                           79.75
pe_low                             74.7
pe_close                           74.7
pe_volume                        399600
pe_oi                           1544025
pe_coi                                0
pe_iv                             16.04
pe_delta                          -0.34
pe_gamma                         0.0011
pe_theta                         -20.16
pe_vega                            7.38
pe_rho                         -59.3506
future_open                     23694.4
future_high                     23701.8
future_low                      23685.0
future_close                    23701.8
future_volume                     78275
future_oi                      12385600
future_coi                            0
2025-05-25 18:44:40,372 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2024-12-31
trade_time                     12:00:00
expiry_date                  2025-01-02
index_name                        NIFTY
spot                            23595.8
atm_strike                      23650.0
strike                          23550.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY02JAN2523550CE
ce_open                           185.0
ce_high                           188.9
ce_low                           181.45
ce_close                         182.95
ce_volume                        187800
ce_oi                           1872075
ce_coi                           -10275
ce_iv                             18.68
ce_delta                           0.58
ce_gamma                          0.001
ce_theta                         -31.49
ce_vega                            7.89
ce_rho                          96.0607
pe_symbol           NIFTY02JAN2523550PE
pe_open                           106.2
pe_high                           109.4
pe_low                            103.5
pe_close                          107.8
pe_volume                        282900
pe_oi                           3496350
pe_coi                           -31500
pe_iv                             17.11
pe_delta                          -0.42
pe_gamma                         0.0011
pe_theta                         -22.69
pe_vega                            7.87
pe_rho                         -73.3389
future_open                    23742.95
future_high                    23756.15
future_low                      23740.5
future_close                   23741.75
future_volume                      6000
future_oi                      12877100
future_coi                         2550
2025-05-25 18:44:40,373 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-25 18:44:40,373 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-25 18:44:40,373 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-25 18:44:40,373 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Entering risk rules block
2025-05-25 18:44:40,373 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-25 18:44:40,373 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140095661655088
2025-05-25 18:44:40,374 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140095661654976, OptionType.PUT ID: 140095661655088
2025-05-25 18:44:40,374 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-25 18:44:40,374 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-25 18:44:40,374 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-25 18:44:40,375 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2024-12-31' AND trade_date <= '2024-12-31'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-25 18:44:40,386 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 18:44:40,391 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 4 - Tick data for specific strike 23450.0 is EMPTY after filtering windowed data.
2025-05-25 18:44:40,391 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Starting risk rules loop, has 3 rules
2025-05-25 18:44:40,391 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 1 of 3
2025-05-25 18:44:40,391 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-25 18:44:40,395 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - evaluate_risk_rule returned: triggered=True
2025-05-25 18:44:40,397 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Breaking out of risk rules loop due to triggered rule
2025-05-25 18:44:40,397 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-25 18:44:40,397 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Reached trade building section (outside all conditionals)
2025-05-25 18:44:40,397 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 4
2025-05-25 18:44:40,397 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:40,397 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23559.15, 'atm_strike': 23550.0, 'strike': 23450.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY02JAN2523450CE', 'ce_open': 192.2, 'ce_high': 198.3, 'ce_low': 191.0, 'ce_close': 198.3, 'ce_volume': 74925, 'ce_oi': 66750, 'ce_coi': 0, 'ce_iv': 15.7, 'ce_delta': 0.66, 'ce_gamma': 0.0012, 'ce_theta': -26.05, 'ce_vega': 7.36, 'ce_rho': 110.9442, 'pe_symbol': 'NIFTY02JAN2523450PE', 'pe_open': 77.09, 'pe_high': 79.75, 'pe_low': 74.7, 'pe_close': 74.7, 'pe_volume': 399600, 'pe_oi': 1544025, 'pe_coi': 0, 'pe_iv': 16.04, 'pe_delta': -0.34, 'pe_gamma': 0.0011, 'pe_theta': -20.16, 'pe_vega': 7.38, 'pe_rho': -59.3506, 'future_open': 23694.4, 'future_high': 23701.8, 'future_low': 23685.0, 'future_close': 23701.8, 'future_volume': 78275, 'future_oi': 12385600, 'future_coi': 0}
2025-05-25 18:44:40,398 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23595.8, 'atm_strike': 23650.0, 'strike': 23550.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY02JAN2523550CE', 'ce_open': 185.0, 'ce_high': 188.9, 'ce_low': 181.45, 'ce_close': 182.95, 'ce_volume': 187800, 'ce_oi': 1872075, 'ce_coi': -10275, 'ce_iv': 18.68, 'ce_delta': 0.58, 'ce_gamma': 0.001, 'ce_theta': -31.49, 'ce_vega': 7.89, 'ce_rho': 96.0607, 'pe_symbol': 'NIFTY02JAN2523550PE', 'pe_open': 106.2, 'pe_high': 109.4, 'pe_low': 103.5, 'pe_close': 107.8, 'pe_volume': 282900, 'pe_oi': 3496350, 'pe_coi': -31500, 'pe_iv': 17.11, 'pe_delta': -0.42, 'pe_gamma': 0.0011, 'pe_theta': -22.69, 'pe_vega': 7.87, 'pe_rho': -73.3389, 'future_open': 23742.95, 'future_high': 23756.15, 'future_low': 23740.5, 'future_close': 23741.75, 'future_volume': 6000, 'future_oi': 12877100, 'future_coi': 2550, 'close': 13.75, 'datetime': Timestamp('2024-12-31 12:00:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-25 18:44:40,398 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-25 18:44:40,398 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 18:44:40,398 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 18:44:40,398 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 18:44:40,398 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: 1654.9999999999998, PnL (Slippage): 1654.9999999999998, Net PnL: 1654.9999999999998
2025-05-25 18:44:40,398 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 18:44:40,398 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2024-12-31', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2024-12-31', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-01-02', 'strike': 23450, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 74.7, 'exit_price': 107.8, 'points': 33.099999999999994, 'pointsAfterSlippage': 33.099999999999994, 'pnl': 1654.9999999999998, 'pnlAfterSlippage': 1654.9999999999998, 'expenses': 0.0, 'netPnlAfterExpenses': 1654.9999999999998, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23559.15, 'index_exit_price': 23595.8, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2024-12-31 09:16:00', 'exit_datetime': '2024-12-31 12:00:00'}
2025-05-25 18:44:40,399 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 4, about to append. Total records before: 3
2025-05-25 18:44:40,399 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 4. Total records now: 4
2025-05-25 18:44:40,399 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Number of trade records generated: 4
2025-05-25 18:44:40,399 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Generated 4 trade records via model-driven path
2025-05-25 18:44:40,399 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Closed reusable HeavyDB connection from get_trades_for_portfolio
2025-05-25 18:44:40,417 - backtester_stable.BTRUN.builders - WARNING - utils.MARGIN_INFO not found or empty - margin requirements will be 0. Attempting to populate.
2025-05-25 18:44:40,417 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'margin_symbol_info.json' at /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-25 18:44:40,417 - backtester_stable.BTRUN.core.utils - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json (Broker: angel)
2025-05-25 18:44:40,417 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.MARGIN_INFO for angel.
2025-05-25 18:44:40,417 - backtester_stable.BTRUN.builders - ERROR - utils.MARGIN_INFO still empty after populate attempt. Margin req will be 0.
2025-05-25 18:44:40,421 - backtester_stable.BTRUN.builders - DEBUG - Strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL margin requirement: 0.00
2025-05-25 18:44:40,421 - backtester_stable.BTRUN.builders - INFO - Total portfolio margin requirement: 0.00
2025-05-25 18:44:40,421 - backtester_stable.BTRUN.builders - DEBUG - Converting tick PnL to daywise DataFrame.
2025-05-25 18:44:40,422 - backtester_stable.BTRUN.builders - INFO - Backtest response parsed.
2025-05-25 18:44:40,448 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.394s, returned 25446 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 18:44:40,451 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 25446 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 18:44:40,452 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 18:44:40,455 - backtester_stable.BTRUN.core.runtime - INFO - Generated metrics_df with 50 records for strategies: ['Combined' 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL']
2025-05-25 18:44:40,517 - backtester_stable.BTRUN.core.runtime - INFO - Saving Excel output to ./test_20250410.xlsx
2025-05-25 18:44:40,517 - backtester_stable.BTRUN.core.runtime - DEBUG - [DEBUG save_backtest_results] save_excel=True, metrics_df is empty: False
2025-05-25 18:44:40,517 - backtester_stable.BTRUN.core.io - INFO - 2025-05-25 18:44:40.517212, Started writing stats to excel file: ./test_20250410.xlsx
2025-05-25 18:44:40,517 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Temporarily switching to CPU-only mode.
2025-05-25 18:44:40,527 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 18:44:40,531 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 4 - Tick data for specific strike 23450.0 is EMPTY after filtering windowed data.
2025-05-25 18:44:40,531 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Starting risk rules loop, has 3 rules
2025-05-25 18:44:40,531 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 1 of 3
2025-05-25 18:44:40,532 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-25 18:44:40,535 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - evaluate_risk_rule returned: triggered=True
2025-05-25 18:44:40,537 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Breaking out of risk rules loop due to triggered rule
2025-05-25 18:44:40,537 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-25 18:44:40,537 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Reached trade building section (outside all conditionals)
2025-05-25 18:44:40,537 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 4
2025-05-25 18:44:40,537 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:40,538 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23559.15, 'atm_strike': 23550.0, 'strike': 23450.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY02JAN2523450CE', 'ce_open': 192.2, 'ce_high': 198.3, 'ce_low': 191.0, 'ce_close': 198.3, 'ce_volume': 74925, 'ce_oi': 66750, 'ce_coi': 0, 'ce_iv': 15.7, 'ce_delta': 0.66, 'ce_gamma': 0.0012, 'ce_theta': -26.05, 'ce_vega': 7.36, 'ce_rho': 110.9442, 'pe_symbol': 'NIFTY02JAN2523450PE', 'pe_open': 77.09, 'pe_high': 79.75, 'pe_low': 74.7, 'pe_close': 74.7, 'pe_volume': 399600, 'pe_oi': 1544025, 'pe_coi': 0, 'pe_iv': 16.04, 'pe_delta': -0.34, 'pe_gamma': 0.0011, 'pe_theta': -20.16, 'pe_vega': 7.38, 'pe_rho': -59.3506, 'future_open': 23694.4, 'future_high': 23701.8, 'future_low': 23685.0, 'future_close': 23701.8, 'future_volume': 78275, 'future_oi': 12385600, 'future_coi': 0}
2025-05-25 18:44:40,538 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23595.8, 'atm_strike': 23650.0, 'strike': 23550.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY02JAN2523550CE', 'ce_open': 185.0, 'ce_high': 188.9, 'ce_low': 181.45, 'ce_close': 182.95, 'ce_volume': 187800, 'ce_oi': 1872075, 'ce_coi': -10275, 'ce_iv': 18.68, 'ce_delta': 0.58, 'ce_gamma': 0.001, 'ce_theta': -31.49, 'ce_vega': 7.89, 'ce_rho': 96.0607, 'pe_symbol': 'NIFTY02JAN2523550PE', 'pe_open': 106.2, 'pe_high': 109.4, 'pe_low': 103.5, 'pe_close': 107.8, 'pe_volume': 282900, 'pe_oi': 3496350, 'pe_coi': -31500, 'pe_iv': 17.11, 'pe_delta': -0.42, 'pe_gamma': 0.0011, 'pe_theta': -22.69, 'pe_vega': 7.87, 'pe_rho': -73.3389, 'future_open': 23742.95, 'future_high': 23756.15, 'future_low': 23740.5, 'future_close': 23741.75, 'future_volume': 6000, 'future_oi': 12877100, 'future_coi': 2550, 'close': 13.75, 'datetime': Timestamp('2024-12-31 12:00:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-25 18:44:40,538 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-25 18:44:40,538 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 18:44:40,538 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 18:44:40,538 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 18:44:40,538 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: 1654.9999999999998, PnL (Slippage): 1654.9999999999998, Net PnL: 1654.9999999999998
2025-05-25 18:44:40,538 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 18:44:40,539 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2024-12-31', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2024-12-31', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-01-02', 'strike': 23450, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 74.7, 'exit_price': 107.8, 'points': 33.099999999999994, 'pointsAfterSlippage': 33.099999999999994, 'pnl': 1654.9999999999998, 'pnlAfterSlippage': 1654.9999999999998, 'expenses': 0.0, 'netPnlAfterExpenses': 1654.9999999999998, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23559.15, 'index_exit_price': 23595.8, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2024-12-31 09:16:00', 'exit_datetime': '2024-12-31 12:00:00'}
2025-05-25 18:44:40,539 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 4, about to append. Total records before: 3
2025-05-25 18:44:40,539 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 4. Total records now: 4
2025-05-25 18:44:40,539 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Number of trade records generated: 4
2025-05-25 18:44:40,539 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Generated 4 trade records via model-driven path
2025-05-25 18:44:40,539 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Closed reusable HeavyDB connection from get_trades_for_portfolio
2025-05-25 18:44:40,557 - backtester_stable.BTRUN.builders - WARNING - utils.MARGIN_INFO not found or empty - margin requirements will be 0. Attempting to populate.
2025-05-25 18:44:40,557 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'margin_symbol_info.json' at /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-25 18:44:40,557 - backtester_stable.BTRUN.core.utils - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json (Broker: angel)
2025-05-25 18:44:40,557 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.MARGIN_INFO for angel.
2025-05-25 18:44:40,558 - backtester_stable.BTRUN.builders - ERROR - utils.MARGIN_INFO still empty after populate attempt. Margin req will be 0.
2025-05-25 18:44:40,561 - backtester_stable.BTRUN.builders - DEBUG - Strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL margin requirement: 0.00
2025-05-25 18:44:40,561 - backtester_stable.BTRUN.builders - INFO - Total portfolio margin requirement: 0.00
2025-05-25 18:44:40,561 - backtester_stable.BTRUN.builders - DEBUG - Converting tick PnL to daywise DataFrame.
2025-05-25 18:44:40,562 - backtester_stable.BTRUN.builders - INFO - Backtest response parsed.
2025-05-25 18:44:40,571 - backtester_stable.BTRUN.core.io - INFO - 2025-05-25 18:44:40.571118, Excel file prepared successfully, Time taken: 0.05s
2025-05-25 18:44:40,571 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Restored GPU_ENABLED to: False
2025-05-25 18:44:40,571 - backtester_stable.BTRUN.core.runtime - INFO - Saving JSON output to ./test_20250410.json
2025-05-25 18:44:40,571 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.392s, returned 25446 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 18:44:40,574 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 25446 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 18:44:40,575 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 18:44:40,590 - backtester_stable.BTRUN.core.io - INFO - --- JSON DUMP DEBUG START ---
2025-05-25 18:44:40,590 - backtester_stable.BTRUN.core.io - INFO - --- JSON DUMP DEBUG END ---
2025-05-25 18:44:40,591 - backtester_stable.BTRUN.core.io - INFO - Successfully wrote JSON output to ./test_20250410.json
2025-05-25 18:44:40,596 - backtester_stable.BTRUN.core.io - ERROR - Text-to-speech failed: This means you probably do not have eSpeak or eSpeak-ng installed!
2025-05-25 18:44:40,599 - backtester_stable.BTRUN.core.runtime - INFO - Generated metrics_df with 50 records for strategies: ['Combined' 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL']
2025-05-25 18:44:40,665 - backtester_stable.BTRUN.core.runtime - INFO - Saving Excel output to ./test_20250409.xlsx
2025-05-25 18:44:40,665 - backtester_stable.BTRUN.core.runtime - DEBUG - [DEBUG save_backtest_results] save_excel=True, metrics_df is empty: False
2025-05-25 18:44:40,665 - backtester_stable.BTRUN.core.io - INFO - 2025-05-25 18:44:40.665452, Started writing stats to excel file: ./test_20250409.xlsx
2025-05-25 18:44:40,665 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Temporarily switching to CPU-only mode.
2025-05-25 18:44:40,679 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 18:44:40,684 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 4 - Tick data for specific strike 23450.0 is EMPTY after filtering windowed data.
2025-05-25 18:44:40,684 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Starting risk rules loop, has 3 rules
2025-05-25 18:44:40,684 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 1 of 3
2025-05-25 18:44:40,684 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-25 18:44:40,689 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - evaluate_risk_rule returned: triggered=True
2025-05-25 18:44:40,691 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Breaking out of risk rules loop due to triggered rule
2025-05-25 18:44:40,691 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-25 18:44:40,691 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Reached trade building section (outside all conditionals)
2025-05-25 18:44:40,691 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 4
2025-05-25 18:44:40,691 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:40,692 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23559.15, 'atm_strike': 23550.0, 'strike': 23450.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY02JAN2523450CE', 'ce_open': 192.2, 'ce_high': 198.3, 'ce_low': 191.0, 'ce_close': 198.3, 'ce_volume': 74925, 'ce_oi': 66750, 'ce_coi': 0, 'ce_iv': 15.7, 'ce_delta': 0.66, 'ce_gamma': 0.0012, 'ce_theta': -26.05, 'ce_vega': 7.36, 'ce_rho': 110.9442, 'pe_symbol': 'NIFTY02JAN2523450PE', 'pe_open': 77.09, 'pe_high': 79.75, 'pe_low': 74.7, 'pe_close': 74.7, 'pe_volume': 399600, 'pe_oi': 1544025, 'pe_coi': 0, 'pe_iv': 16.04, 'pe_delta': -0.34, 'pe_gamma': 0.0011, 'pe_theta': -20.16, 'pe_vega': 7.38, 'pe_rho': -59.3506, 'future_open': 23694.4, 'future_high': 23701.8, 'future_low': 23685.0, 'future_close': 23701.8, 'future_volume': 78275, 'future_oi': 12385600, 'future_coi': 0}
2025-05-25 18:44:40,692 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23595.8, 'atm_strike': 23650.0, 'strike': 23550.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY02JAN2523550CE', 'ce_open': 185.0, 'ce_high': 188.9, 'ce_low': 181.45, 'ce_close': 182.95, 'ce_volume': 187800, 'ce_oi': 1872075, 'ce_coi': -10275, 'ce_iv': 18.68, 'ce_delta': 0.58, 'ce_gamma': 0.001, 'ce_theta': -31.49, 'ce_vega': 7.89, 'ce_rho': 96.0607, 'pe_symbol': 'NIFTY02JAN2523550PE', 'pe_open': 106.2, 'pe_high': 109.4, 'pe_low': 103.5, 'pe_close': 107.8, 'pe_volume': 282900, 'pe_oi': 3496350, 'pe_coi': -31500, 'pe_iv': 17.11, 'pe_delta': -0.42, 'pe_gamma': 0.0011, 'pe_theta': -22.69, 'pe_vega': 7.87, 'pe_rho': -73.3389, 'future_open': 23742.95, 'future_high': 23756.15, 'future_low': 23740.5, 'future_close': 23741.75, 'future_volume': 6000, 'future_oi': 12877100, 'future_coi': 2550, 'close': 13.75, 'datetime': Timestamp('2024-12-31 12:00:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-25 18:44:40,692 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-25 18:44:40,692 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 18:44:40,692 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 18:44:40,692 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 18:44:40,693 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: 1654.9999999999998, PnL (Slippage): 1654.9999999999998, Net PnL: 1654.9999999999998
2025-05-25 18:44:40,693 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 18:44:40,693 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2024-12-31', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2024-12-31', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-01-02', 'strike': 23450, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 74.7, 'exit_price': 107.8, 'points': 33.099999999999994, 'pointsAfterSlippage': 33.099999999999994, 'pnl': 1654.9999999999998, 'pnlAfterSlippage': 1654.9999999999998, 'expenses': 0.0, 'netPnlAfterExpenses': 1654.9999999999998, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23559.15, 'index_exit_price': 23595.8, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2024-12-31 09:16:00', 'exit_datetime': '2024-12-31 12:00:00'}
2025-05-25 18:44:40,693 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 4, about to append. Total records before: 3
2025-05-25 18:44:40,693 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 4. Total records now: 4
2025-05-25 18:44:40,693 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Number of trade records generated: 4
2025-05-25 18:44:40,693 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Generated 4 trade records via model-driven path
2025-05-25 18:44:40,694 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Closed reusable HeavyDB connection from get_trades_for_portfolio
2025-05-25 18:44:40,715 - backtester_stable.BTRUN.builders - WARNING - utils.MARGIN_INFO not found or empty - margin requirements will be 0. Attempting to populate.
2025-05-25 18:44:40,716 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'margin_symbol_info.json' at /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-25 18:44:40,716 - backtester_stable.BTRUN.core.utils - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json (Broker: angel)
2025-05-25 18:44:40,716 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.MARGIN_INFO for angel.
2025-05-25 18:44:40,716 - backtester_stable.BTRUN.builders - ERROR - utils.MARGIN_INFO still empty after populate attempt. Margin req will be 0.
2025-05-25 18:44:40,721 - backtester_stable.BTRUN.builders - DEBUG - Strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL margin requirement: 0.00
2025-05-25 18:44:40,721 - backtester_stable.BTRUN.builders - INFO - Total portfolio margin requirement: 0.00
2025-05-25 18:44:40,721 - backtester_stable.BTRUN.builders - DEBUG - Converting tick PnL to daywise DataFrame.
2025-05-25 18:44:40,722 - backtester_stable.BTRUN.core.io - INFO - 2025-05-25 18:44:40.722347, Excel file prepared successfully, Time taken: 0.06s
2025-05-25 18:44:40,722 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Restored GPU_ENABLED to: False
2025-05-25 18:44:40,722 - backtester_stable.BTRUN.core.runtime - INFO - Saving JSON output to ./test_20250409.json
2025-05-25 18:44:40,722 - backtester_stable.BTRUN.builders - INFO - Backtest response parsed.
2025-05-25 18:44:40,743 - backtester_stable.BTRUN.core.io - INFO - --- JSON DUMP DEBUG START ---
2025-05-25 18:44:40,744 - backtester_stable.BTRUN.core.io - INFO - --- JSON DUMP DEBUG END ---
2025-05-25 18:44:40,745 - backtester_stable.BTRUN.core.io - INFO - Successfully wrote JSON output to ./test_20250409.json
2025-05-25 18:44:40,751 - backtester_stable.BTRUN.core.io - ERROR - Text-to-speech failed: This means you probably do not have eSpeak or eSpeak-ng installed!
2025-05-25 18:44:40,767 - backtester_stable.BTRUN.core.runtime - INFO - Generated metrics_df with 50 records for strategies: ['Combined' 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL']
2025-05-25 18:44:40,778 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.402s, returned 25446 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-25 18:44:40,781 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 25446 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-25 18:44:40,782 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-25 18:44:40,828 - backtester_stable.BTRUN.core.runtime - INFO - Saving Excel output to ./test_20250408.xlsx
2025-05-25 18:44:40,828 - backtester_stable.BTRUN.core.runtime - DEBUG - [DEBUG save_backtest_results] save_excel=True, metrics_df is empty: False
2025-05-25 18:44:40,828 - backtester_stable.BTRUN.core.io - INFO - 2025-05-25 18:44:40.828959, Started writing stats to excel file: ./test_20250408.xlsx
2025-05-25 18:44:40,829 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Temporarily switching to CPU-only mode.
2025-05-25 18:44:40,878 - backtester_stable.BTRUN.core.io - INFO - 2025-05-25 18:44:40.878793, Excel file prepared successfully, Time taken: 0.05s
2025-05-25 18:44:40,878 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Restored GPU_ENABLED to: False
2025-05-25 18:44:40,878 - backtester_stable.BTRUN.core.runtime - INFO - Saving JSON output to ./test_20250408.json
2025-05-25 18:44:40,896 - backtester_stable.BTRUN.core.io - INFO - --- JSON DUMP DEBUG START ---
2025-05-25 18:44:40,896 - backtester_stable.BTRUN.core.io - INFO - --- JSON DUMP DEBUG END ---
2025-05-25 18:44:40,897 - backtester_stable.BTRUN.core.io - INFO - Successfully wrote JSON output to ./test_20250408.json
2025-05-25 18:44:40,903 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-25 18:44:40,904 - backtester_stable.BTRUN.core.io - ERROR - Text-to-speech failed: This means you probably do not have eSpeak or eSpeak-ng installed!
2025-05-25 18:44:40,912 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 4 - Tick data for specific strike 23450.0 is EMPTY after filtering windowed data.
2025-05-25 18:44:40,912 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Starting risk rules loop, has 3 rules
2025-05-25 18:44:40,913 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 1 of 3
2025-05-25 18:44:40,913 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-25 18:44:40,917 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - evaluate_risk_rule returned: triggered=True
2025-05-25 18:44:40,919 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Breaking out of risk rules loop due to triggered rule
2025-05-25 18:44:40,919 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-25 18:44:40,920 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Reached trade building section (outside all conditionals)
2025-05-25 18:44:40,920 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 4
2025-05-25 18:44:40,920 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-25 18:44:40,920 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23559.15, 'atm_strike': 23550.0, 'strike': 23450.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY02JAN2523450CE', 'ce_open': 192.2, 'ce_high': 198.3, 'ce_low': 191.0, 'ce_close': 198.3, 'ce_volume': 74925, 'ce_oi': 66750, 'ce_coi': 0, 'ce_iv': 15.7, 'ce_delta': 0.66, 'ce_gamma': 0.0012, 'ce_theta': -26.05, 'ce_vega': 7.36, 'ce_rho': 110.9442, 'pe_symbol': 'NIFTY02JAN2523450PE', 'pe_open': 77.09, 'pe_high': 79.75, 'pe_low': 74.7, 'pe_close': 74.7, 'pe_volume': 399600, 'pe_oi': 1544025, 'pe_coi': 0, 'pe_iv': 16.04, 'pe_delta': -0.34, 'pe_gamma': 0.0011, 'pe_theta': -20.16, 'pe_vega': 7.38, 'pe_rho': -59.3506, 'future_open': 23694.4, 'future_high': 23701.8, 'future_low': 23685.0, 'future_close': 23701.8, 'future_volume': 78275, 'future_oi': 12385600, 'future_coi': 0}
2025-05-25 18:44:40,920 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2024, 12, 31), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 1, 2), 'index_name': 'NIFTY', 'spot': 23595.8, 'atm_strike': 23650.0, 'strike': 23550.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY02JAN2523550CE', 'ce_open': 185.0, 'ce_high': 188.9, 'ce_low': 181.45, 'ce_close': 182.95, 'ce_volume': 187800, 'ce_oi': 1872075, 'ce_coi': -10275, 'ce_iv': 18.68, 'ce_delta': 0.58, 'ce_gamma': 0.001, 'ce_theta': -31.49, 'ce_vega': 7.89, 'ce_rho': 96.0607, 'pe_symbol': 'NIFTY02JAN2523550PE', 'pe_open': 106.2, 'pe_high': 109.4, 'pe_low': 103.5, 'pe_close': 107.8, 'pe_volume': 282900, 'pe_oi': 3496350, 'pe_coi': -31500, 'pe_iv': 17.11, 'pe_delta': -0.42, 'pe_gamma': 0.0011, 'pe_theta': -22.69, 'pe_vega': 7.87, 'pe_rho': -73.3389, 'future_open': 23742.95, 'future_high': 23756.15, 'future_low': 23740.5, 'future_close': 23741.75, 'future_volume': 6000, 'future_oi': 12877100, 'future_coi': 2550, 'close': 13.75, 'datetime': Timestamp('2024-12-31 12:00:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-25 18:44:40,920 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-25 18:44:40,920 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-25 18:44:40,920 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-25 18:44:40,921 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-25 18:44:40,921 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: 1654.9999999999998, PnL (Slippage): 1654.9999999999998, Net PnL: 1654.9999999999998
2025-05-25 18:44:40,921 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-25 18:44:40,921 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2024-12-31', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2024-12-31', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-01-02', 'strike': 23450, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 74.7, 'exit_price': 107.8, 'points': 33.099999999999994, 'pointsAfterSlippage': 33.099999999999994, 'pnl': 1654.9999999999998, 'pnlAfterSlippage': 1654.9999999999998, 'expenses': 0.0, 'netPnlAfterExpenses': 1654.9999999999998, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23559.15, 'index_exit_price': 23595.8, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2024-12-31 09:16:00', 'exit_datetime': '2024-12-31 12:00:00'}
2025-05-25 18:44:40,921 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 4, about to append. Total records before: 3
2025-05-25 18:44:40,921 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 4. Total records now: 4
2025-05-25 18:44:40,921 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Number of trade records generated: 4
2025-05-25 18:44:40,921 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Generated 4 trade records via model-driven path
2025-05-25 18:44:40,922 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Closed reusable HeavyDB connection from get_trades_for_portfolio
2025-05-25 18:44:40,939 - backtester_stable.BTRUN.builders - WARNING - utils.MARGIN_INFO not found or empty - margin requirements will be 0. Attempting to populate.
2025-05-25 18:44:40,940 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'margin_symbol_info.json' at /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-25 18:44:40,940 - backtester_stable.BTRUN.core.utils - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json (Broker: angel)
2025-05-25 18:44:40,940 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.MARGIN_INFO for angel.
2025-05-25 18:44:40,940 - backtester_stable.BTRUN.builders - ERROR - utils.MARGIN_INFO still empty after populate attempt. Margin req will be 0.
2025-05-25 18:44:40,944 - backtester_stable.BTRUN.builders - DEBUG - Strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL margin requirement: 0.00
2025-05-25 18:44:40,944 - backtester_stable.BTRUN.builders - INFO - Total portfolio margin requirement: 0.00
2025-05-25 18:44:40,944 - backtester_stable.BTRUN.builders - DEBUG - Converting tick PnL to daywise DataFrame.
2025-05-25 18:44:40,945 - backtester_stable.BTRUN.builders - INFO - Backtest response parsed.
2025-05-25 18:44:40,981 - backtester_stable.BTRUN.core.runtime - INFO - Generated metrics_df with 50 records for strategies: ['Combined' 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL']
2025-05-25 18:44:41,046 - backtester_stable.BTRUN.core.runtime - INFO - Saving Excel output to ./test_20250407.xlsx
2025-05-25 18:44:41,046 - backtester_stable.BTRUN.core.runtime - DEBUG - [DEBUG save_backtest_results] save_excel=True, metrics_df is empty: False
2025-05-25 18:44:41,046 - backtester_stable.BTRUN.core.io - INFO - 2025-05-25 18:44:41.046545, Started writing stats to excel file: ./test_20250407.xlsx
2025-05-25 18:44:41,046 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Temporarily switching to CPU-only mode.
2025-05-25 18:44:41,106 - backtester_stable.BTRUN.core.io - INFO - 2025-05-25 18:44:41.106001, Excel file prepared successfully, Time taken: 0.06s
2025-05-25 18:44:41,106 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Restored GPU_ENABLED to: False
2025-05-25 18:44:41,106 - backtester_stable.BTRUN.core.runtime - INFO - Saving JSON output to ./test_20250407.json
2025-05-25 18:44:41,127 - backtester_stable.BTRUN.core.io - INFO - --- JSON DUMP DEBUG START ---
2025-05-25 18:44:41,127 - backtester_stable.BTRUN.core.io - INFO - --- JSON DUMP DEBUG END ---
2025-05-25 18:44:41,128 - backtester_stable.BTRUN.core.io - INFO - Successfully wrote JSON output to ./test_20250407.json
2025-05-25 18:44:41,134 - backtester_stable.BTRUN.core.io - ERROR - Text-to-speech failed: This means you probably do not have eSpeak or eSpeak-ng installed!
