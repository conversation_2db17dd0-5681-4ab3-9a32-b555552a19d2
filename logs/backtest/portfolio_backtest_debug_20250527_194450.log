2025-05-27 19:44:50,234 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-27 19:44:50,234 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-27 19:44:50,235 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/../models', '/srv/samba/shared', '/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages', '/srv/samba/shared']
2025-05-27 19:44:50,235 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-27 19:44:50,235 - __main__ - DEBUG - GPU enabled: False
2025-05-27 19:44:50,236 - __main__ - INFO - GPU acceleration enabled: False
2025-05-27 19:44:50,236 - __main__ - INFO - HeavyDB integration available (Host: 127.0.0.1)
2025-05-27 19:44:50,484 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-27 19:44:50,484 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-27 19:44:50,484 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-27 19:44:50,484 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-27 19:44:50,485 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-27 19:44:50,485 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-27 19:44:50,491 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-27 19:44:50,492 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-27 19:44:50,492 - backtester_stable.BTRUN.core.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-27 19:44:50,492 - backtester_stable.BTRUN.core.data_fetchers - INFO - Margin symbol info cache is outdated. Fetching fresh data.
2025-05-27 19:44:50,492 - backtester_stable.BTRUN.core.data_fetchers - INFO - Fetching margin symbol information from Angel One...
2025-05-27 19:44:50,492 - backtester_stable.BTRUN.core.data_fetchers - INFO - Fetching Angel One contracts from: https://margin-calc-arom-prod.angelbroking.com/exchange/BFO/product/OPTION/contract
2025-05-27 19:44:50,494 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): margin-calc-arom-prod.angelbroking.com:443
2025-05-27 19:44:51,764 - urllib3.connectionpool - DEBUG - https://margin-calc-arom-prod.angelbroking.com:443 "GET /exchange/BFO/product/OPTION/contract HTTP/1.1" 200 None
2025-05-27 19:44:51,769 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully fetched 663 contracts for exchange BFO from Angel One.
2025-05-27 19:44:51,773 - backtester_stable.BTRUN.core.data_fetchers - INFO - Fetching Angel One contracts from: https://margin-calc-arom-prod.angelbroking.com/exchange/NFO/product/OPTION/contract
2025-05-27 19:44:51,774 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): margin-calc-arom-prod.angelbroking.com:443
2025-05-27 19:44:53,013 - urllib3.connectionpool - DEBUG - https://margin-calc-arom-prod.angelbroking.com:443 "GET /exchange/NFO/product/OPTION/contract HTTP/1.1" 200 None
2025-05-27 19:44:53,018 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully fetched 671 contracts for exchange NFO from Angel One.
2025-05-27 19:44:53,020 - backtester_stable.BTRUN.core.data_fetchers - INFO - Fetching Angel One contracts from: https://margin-calc-arom-prod.angelbroking.com/exchange/MCX/product/OPTION/contract
2025-05-27 19:44:53,022 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): margin-calc-arom-prod.angelbroking.com:443
2025-05-27 19:44:53,840 - urllib3.connectionpool - DEBUG - https://margin-calc-arom-prod.angelbroking.com:443 "GET /exchange/MCX/product/OPTION/contract HTTP/1.1" 200 362
2025-05-27 19:44:53,843 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully fetched 40 contracts for exchange MCX from Angel One.
2025-05-27 19:44:53,845 - backtester_stable.BTRUN.core.data_fetchers - INFO - Processing a total of 92 contracts from Angel One.
2025-05-27 19:44:53,924 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully processed margin info for 16 underlyings from Angel One.
2025-05-27 19:44:53,926 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully fetched and cached margin symbol info to bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-27 19:44:53,926 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-27 19:44:53,926 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-27 19:44:53,926 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-27 19:44:53,926 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-27 19:44:53,926 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/test_data/tbs_test_1day.xlsx
2025-05-27 19:44:53,926 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/test_data
2025-05-27 19:44:53,926 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/test_data/tbs_test_1day.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/test_data
2025-05-27 19:44:54,053 - __main__ - ERROR - Error parsing portfolio with modern_portfolio_parser: Worksheet named 'PortfolioSetting' not found
Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py", line 461, in _row_run_backtest
    parsed_portfolio_models: Dict[str, PortfolioModel] = modern_portfolio_parser.parse_portfolio_excel(main_portfolio_excel_path)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/excel_parser/portfolio_parser.py", line 48, in parse_portfolio_excel
    portfolio_df = pd.read_excel(path, sheet_name="PortfolioSetting")
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/util/_decorators.py", line 211, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/util/_decorators.py", line 331, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/io/excel/_base.py", line 490, in read_excel
    data = io.parse(
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/io/excel/_base.py", line 1734, in parse
    return self._reader.parse(
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/io/excel/_base.py", line 760, in parse
    sheet = self.get_sheet_by_name(asheetname)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/io/excel/_openpyxl.py", line 577, in get_sheet_by_name
    self.raise_if_bad_sheet_by_name(name)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/io/excel/_base.py", line 602, in raise_if_bad_sheet_by_name
    raise ValueError(f"Worksheet named '{name}' not found")
ValueError: Worksheet named 'PortfolioSetting' not found
