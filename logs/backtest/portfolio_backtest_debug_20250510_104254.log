2025-05-10 10:42:54,436 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-10 10:42:54,437 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-10 10:42:54,437 - __main__ - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-10 10:42:54,437 - __main__ - DEBUG - HeavyDB host: **************, DB: heavydb
2025-05-10 10:42:54,437 - __main__ - DEBUG - GPU enabled: True
2025-05-10 10:42:54,438 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting BTRunPortfolio_GPU.py - Debugging enabled
2025-05-10 10:42:54,438 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Current working directory: /srv/samba/shared
2025-05-10 10:42:54,438 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-10 10:42:54,438 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Importing BTRUN modules...
2025-05-10 10:42:54,438 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Config module imported from: /srv/samba/shared/bt/backtester_stable/BTRUN/config.py
2025-05-10 10:42:54,438 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - HeavyDB settings - Host: **************, Database: heavydb
2025-05-10 10:42:54,438 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - GPU enabled: True
2025-05-10 10:42:54,438 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Successfully imported heavydb_helpers module
2025-05-10 10:42:54,438 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Entering main function
2025-05-10 10:42:54,438 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Parsing command line arguments
2025-05-10 10:42:54,440 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Parsed arguments: Namespace(config=None, legacy_excel=True, strategies=None, start_date=None, end_date=None, output_dir='Trades', portfolio_name=None, slippage=0.1, capital=None, margin_multiplier=1.0, no_json=False, no_excel=False, no_charts=False, cpu_only=False, merge_output=False, debug=False)
2025-05-10 10:42:54,440 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Text-to-speech notifications not available
2025-05-10 10:42:54,440 - __main__ - INFO - GPU acceleration enabled: True
2025-05-10 10:42:54,511 - __main__ - INFO - GPU Memory: 35421MB free / 40384MB total
2025-05-10 10:42:54,511 - __main__ - INFO - HeavyDB integration available (Host: **************)
2025-05-10 10:42:54,726 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-10 10:42:54,726 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-10 10:42:54,727 - __main__ - INFO - Using new Excel input format (PortfolioSetting & StrategySetting)
2025-05-10 10:42:54,727 - bt.backtester_stable.BTRUN.config - INFO - Found portfolio file at /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-10 10:42:54,727 - __main__ - INFO - Reading PortfolioSetting & StrategySetting from /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-10 10:42:54,830 - __main__ - DEBUG - Loaded 1 PortfolioSetting rows, 21 StrategySetting rows
2025-05-10 10:42:54,907 - __main__ - DEBUG - Loaded 1 rows from /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx
2025-05-10 10:42:54,908 - __main__ - INFO - Processing portfolio: NIF0DTE
2025-05-10 10:42:54,908 - __main__ - DEBUG - Building portfolio request for portfolio: NIF0DTE
2025-05-10 10:42:54,909 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting single backtest for NIF0DTE
2025-05-10 10:42:54,910 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Using HeavyDB for data access
2025-05-10 10:42:54,910 - bt.backtester_stable.BTRUN.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'NIF0DTE'
2025-05-10 10:42:55,124 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-10 10:42:59,822 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 4.698s, returned 5998 rows
2025-05-10 10:43:00,302 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Generated 11996 synthetic trade rows from option chain
2025-05-10 10:43:00,303 - bt.backtester_stable.BTRUN.builders - INFO - Parsing backtest response...
2025-05-10 10:43:01,306 - bt.backtester_stable.BTRUN.builders - WARNING - Margin calculation in builders.parse_backtest_response is a placeholder.
2025-05-10 10:43:01,306 - bt.backtester_stable.BTRUN.builders - DEBUG - Converting tick PnL to daywise DataFrame.
2025-05-10 10:43:01,308 - bt.backtester_stable.BTRUN.builders - INFO - Backtest response parsed.
2025-05-10 10:43:04,076 - bt.backtester_stable.BTRUN.gpu_helpers - WARNING - Failed to convert to cuDF: Could not convert 'Total' with type str: tried to convert to int64. Falling back to pandas.
2025-05-10 10:43:04,131 - bt.backtester_stable.BTRUN.gpu_helpers - WARNING - Failed to convert to cuDF: Could not convert 'Total' with type str: tried to convert to int64. Falling back to pandas.
2025-05-10 10:43:04,132 - bt.backtester_stable.BTRUN.runtime - INFO - Saving Excel output to Trades/NIF0DTE_10052025_104254.xlsx
2025-05-10 10:43:04,132 - bt.backtester_stable.BTRUN.io - INFO - 2025-05-10 10:43:04.132320, Started writing stats to excel file: Trades/NIF0DTE_10052025_104254.xlsx
2025-05-10 10:43:04,132 - bt.backtester_stable.BTRUN.gpu_helpers - DEBUG - Temporarily switching to CPU-only mode.
2025-05-10 10:43:04,157 - bt.backtester_stable.BTRUN.gpu_helpers - DEBUG - Restored GPU_ENABLED to: True
2025-05-10 10:43:04,157 - bt.backtester_stable.BTRUN.io - ERROR - Error preparing output file: Duplicate column names are not allowed
2025-05-10 10:43:04,158 - bt.backtester_stable.BTRUN.io - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/io.py", line 172, in prepare_output_file
    ordered_df = ordered_df.rename(columns={k: v for k, v in Util.COLUMN_RENAME_MAPPING.items() if k in ordered_df.columns})
  File "/home/<USER>/.local/lib/python3.10/site-packages/nvtx/nvtx.py", line 122, in inner
    result = func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/cudf/core/dataframe.py", line 3512, in rename
    out._data = self._data.rename_levels(mapper=columns, level=level)
  File "/home/<USER>/.local/lib/python3.10/site-packages/cudf/core/column_accessor.py", line 665, in rename_levels
    raise ValueError("Duplicate column names are not allowed")
ValueError: Duplicate column names are not allowed

2025-05-10 10:43:04,159 - bt.backtester_stable.BTRUN.runtime - INFO - Saving JSON output to Trades/NIF0DTE_10052025_104254.json
2025-05-10 10:43:04,304 - numba.core.byteflow - DEBUG - bytecode dump:
>          0	NOP(arg=None, lineno=271)
           2	LOAD_GLOBAL(arg=0, lineno=271)
           4	LOAD_METHOD(arg=1, lineno=271)
           6	LOAD_FAST(arg=0, lineno=271)
           8	CALL_METHOD(arg=1, lineno=271)
          10	POP_JUMP_IF_FALSE(arg=17, lineno=271)
          12	LOAD_GLOBAL(arg=2, lineno=271)
          14	LOAD_FAST(arg=0, lineno=271)
          16	LOAD_CONST(arg=1, lineno=271)
          18	CALL_FUNCTION(arg=2, lineno=271)
          20	POP_JUMP_IF_FALSE(arg=17, lineno=271)
          22	LOAD_FAST(arg=0, lineno=271)
          24	LOAD_METHOD(arg=3, lineno=271)
          26	LOAD_CONST(arg=2, lineno=271)
          28	CALL_METHOD(arg=1, lineno=271)
          30	RETURN_VALUE(arg=None, lineno=271)
>         32	LOAD_FAST(arg=0, lineno=271)
          34	RETURN_VALUE(arg=None, lineno=271)
2025-05-10 10:43:04,304 - numba.core.byteflow - DEBUG - pending: deque([State(pc_initial=0 nstack_initial=0)])
2025-05-10 10:43:04,304 - numba.core.byteflow - DEBUG - stack: []
2025-05-10 10:43:04,304 - numba.core.byteflow - DEBUG - state.pc_initial: State(pc_initial=0 nstack_initial=0)
2025-05-10 10:43:04,304 - numba.core.byteflow - DEBUG - dispatch pc=0, inst=NOP(arg=None, lineno=271)
2025-05-10 10:43:04,304 - numba.core.byteflow - DEBUG - stack []
2025-05-10 10:43:04,304 - numba.core.byteflow - DEBUG - dispatch pc=2, inst=LOAD_GLOBAL(arg=0, lineno=271)
2025-05-10 10:43:04,304 - numba.core.byteflow - DEBUG - stack []
2025-05-10 10:43:04,304 - numba.core.byteflow - DEBUG - dispatch pc=4, inst=LOAD_METHOD(arg=1, lineno=271)
2025-05-10 10:43:04,305 - numba.core.byteflow - DEBUG - stack ['$2load_global.0']
2025-05-10 10:43:04,305 - numba.core.byteflow - DEBUG - dispatch pc=6, inst=LOAD_FAST(arg=0, lineno=271)
2025-05-10 10:43:04,305 - numba.core.byteflow - DEBUG - stack ['$4load_method.1']
2025-05-10 10:43:04,305 - numba.core.byteflow - DEBUG - dispatch pc=8, inst=CALL_METHOD(arg=1, lineno=271)
2025-05-10 10:43:04,305 - numba.core.byteflow - DEBUG - stack ['$4load_method.1', '$x6.2']
2025-05-10 10:43:04,305 - numba.core.byteflow - DEBUG - dispatch pc=10, inst=POP_JUMP_IF_FALSE(arg=17, lineno=271)
2025-05-10 10:43:04,305 - numba.core.byteflow - DEBUG - stack ['$8call_method.3']
2025-05-10 10:43:04,305 - numba.core.byteflow - DEBUG - end state. edges=[Edge(pc=12, stack=(), blockstack=(), npush=0), Edge(pc=32, stack=(), blockstack=(), npush=0)]
2025-05-10 10:43:04,305 - numba.core.byteflow - DEBUG - pending: deque([State(pc_initial=12 nstack_initial=0), State(pc_initial=32 nstack_initial=0)])
2025-05-10 10:43:04,305 - numba.core.byteflow - DEBUG - stack: []
2025-05-10 10:43:04,305 - numba.core.byteflow - DEBUG - state.pc_initial: State(pc_initial=12 nstack_initial=0)
2025-05-10 10:43:04,305 - numba.core.byteflow - DEBUG - dispatch pc=12, inst=LOAD_GLOBAL(arg=2, lineno=271)
2025-05-10 10:43:04,305 - numba.core.byteflow - DEBUG - stack []
2025-05-10 10:43:04,305 - numba.core.byteflow - DEBUG - dispatch pc=14, inst=LOAD_FAST(arg=0, lineno=271)
2025-05-10 10:43:04,305 - numba.core.byteflow - DEBUG - stack ['$12load_global.0']
2025-05-10 10:43:04,305 - numba.core.byteflow - DEBUG - dispatch pc=16, inst=LOAD_CONST(arg=1, lineno=271)
2025-05-10 10:43:04,305 - numba.core.byteflow - DEBUG - stack ['$12load_global.0', '$x14.1']
2025-05-10 10:43:04,306 - numba.core.byteflow - DEBUG - dispatch pc=18, inst=CALL_FUNCTION(arg=2, lineno=271)
2025-05-10 10:43:04,306 - numba.core.byteflow - DEBUG - stack ['$12load_global.0', '$x14.1', '$const16.2']
2025-05-10 10:43:04,306 - numba.core.byteflow - DEBUG - dispatch pc=20, inst=POP_JUMP_IF_FALSE(arg=17, lineno=271)
2025-05-10 10:43:04,306 - numba.core.byteflow - DEBUG - stack ['$18call_function.3']
2025-05-10 10:43:04,306 - numba.core.byteflow - DEBUG - end state. edges=[Edge(pc=22, stack=(), blockstack=(), npush=0), Edge(pc=32, stack=(), blockstack=(), npush=0)]
2025-05-10 10:43:04,306 - numba.core.byteflow - DEBUG - pending: deque([State(pc_initial=32 nstack_initial=0), State(pc_initial=22 nstack_initial=0), State(pc_initial=32 nstack_initial=0)])
2025-05-10 10:43:04,306 - numba.core.byteflow - DEBUG - stack: []
2025-05-10 10:43:04,306 - numba.core.byteflow - DEBUG - state.pc_initial: State(pc_initial=32 nstack_initial=0)
2025-05-10 10:43:04,306 - numba.core.byteflow - DEBUG - dispatch pc=32, inst=LOAD_FAST(arg=0, lineno=271)
2025-05-10 10:43:04,306 - numba.core.byteflow - DEBUG - stack []
2025-05-10 10:43:04,306 - numba.core.byteflow - DEBUG - dispatch pc=34, inst=RETURN_VALUE(arg=None, lineno=271)
2025-05-10 10:43:04,306 - numba.core.byteflow - DEBUG - stack ['$x32.0']
2025-05-10 10:43:04,306 - numba.core.byteflow - DEBUG - end state. edges=[]
2025-05-10 10:43:04,306 - numba.core.byteflow - DEBUG - pending: deque([State(pc_initial=22 nstack_initial=0), State(pc_initial=32 nstack_initial=0)])
2025-05-10 10:43:04,306 - numba.core.byteflow - DEBUG - stack: []
2025-05-10 10:43:04,306 - numba.core.byteflow - DEBUG - state.pc_initial: State(pc_initial=22 nstack_initial=0)
2025-05-10 10:43:04,307 - numba.core.byteflow - DEBUG - dispatch pc=22, inst=LOAD_FAST(arg=0, lineno=271)
2025-05-10 10:43:04,307 - numba.core.byteflow - DEBUG - stack []
2025-05-10 10:43:04,307 - numba.core.byteflow - DEBUG - dispatch pc=24, inst=LOAD_METHOD(arg=3, lineno=271)
2025-05-10 10:43:04,307 - numba.core.byteflow - DEBUG - stack ['$x22.0']
2025-05-10 10:43:04,307 - numba.core.byteflow - DEBUG - dispatch pc=26, inst=LOAD_CONST(arg=2, lineno=271)
2025-05-10 10:43:04,307 - numba.core.byteflow - DEBUG - stack ['$24load_method.1']
2025-05-10 10:43:04,307 - numba.core.byteflow - DEBUG - dispatch pc=28, inst=CALL_METHOD(arg=1, lineno=271)
2025-05-10 10:43:04,307 - numba.core.byteflow - DEBUG - stack ['$24load_method.1', '$const26.2']
2025-05-10 10:43:04,307 - numba.core.byteflow - DEBUG - dispatch pc=30, inst=RETURN_VALUE(arg=None, lineno=271)
2025-05-10 10:43:04,307 - numba.core.byteflow - DEBUG - stack ['$28call_method.3']
2025-05-10 10:43:04,307 - numba.core.byteflow - DEBUG - end state. edges=[]
2025-05-10 10:43:04,307 - numba.core.byteflow - DEBUG - pending: deque([State(pc_initial=32 nstack_initial=0)])
2025-05-10 10:43:04,307 - numba.core.byteflow - DEBUG - -------------------------Prune PHIs-------------------------
2025-05-10 10:43:04,307 - numba.core.byteflow - DEBUG - Used_phis: defaultdict(<class 'set'>,
            {State(pc_initial=0 nstack_initial=0): set(),
             State(pc_initial=12 nstack_initial=0): set(),
             State(pc_initial=22 nstack_initial=0): set(),
             State(pc_initial=32 nstack_initial=0): set()})
2025-05-10 10:43:04,308 - numba.core.byteflow - DEBUG - defmap: {}
2025-05-10 10:43:04,308 - numba.core.byteflow - DEBUG - phismap: defaultdict(<class 'set'>, {})
2025-05-10 10:43:04,308 - numba.core.byteflow - DEBUG - changing phismap: defaultdict(<class 'set'>, {})
2025-05-10 10:43:04,308 - numba.core.byteflow - DEBUG - keep phismap: {}
2025-05-10 10:43:04,308 - numba.core.byteflow - DEBUG - new_out: defaultdict(<class 'dict'>, {})
2025-05-10 10:43:04,308 - numba.core.byteflow - DEBUG - ----------------------DONE Prune PHIs-----------------------
2025-05-10 10:43:04,308 - numba.core.byteflow - DEBUG - block_infos State(pc_initial=0 nstack_initial=0):
AdaptBlockInfo(insts=((0, {}), (2, {'res': '$2load_global.0'}), (4, {'item': '$2load_global.0', 'res': '$4load_method.1'}), (6, {'res': '$x6.2'}), (8, {'func': '$4load_method.1', 'args': ['$x6.2'], 'res': '$8call_method.3'}), (10, {'pred': '$8call_method.3'})), outgoing_phis={}, blockstack=(), active_try_block=None, outgoing_edgepushed={12: (), 32: ()})
2025-05-10 10:43:04,308 - numba.core.byteflow - DEBUG - block_infos State(pc_initial=12 nstack_initial=0):
AdaptBlockInfo(insts=((12, {'res': '$12load_global.0'}), (14, {'res': '$x14.1'}), (16, {'res': '$const16.2'}), (18, {'func': '$12load_global.0', 'args': ['$x14.1', '$const16.2'], 'res': '$18call_function.3'}), (20, {'pred': '$18call_function.3'})), outgoing_phis={}, blockstack=(), active_try_block=None, outgoing_edgepushed={22: (), 32: ()})
2025-05-10 10:43:04,308 - numba.core.byteflow - DEBUG - block_infos State(pc_initial=22 nstack_initial=0):
AdaptBlockInfo(insts=((22, {'res': '$x22.0'}), (24, {'item': '$x22.0', 'res': '$24load_method.1'}), (26, {'res': '$const26.2'}), (28, {'func': '$24load_method.1', 'args': ['$const26.2'], 'res': '$28call_method.3'}), (30, {'retval': '$28call_method.3', 'castval': '$30return_value.4'})), outgoing_phis={}, blockstack=(), active_try_block=None, outgoing_edgepushed={})
2025-05-10 10:43:04,308 - numba.core.byteflow - DEBUG - block_infos State(pc_initial=32 nstack_initial=0):
AdaptBlockInfo(insts=((32, {'res': '$x32.0'}), (34, {'retval': '$x32.0', 'castval': '$34return_value.1'})), outgoing_phis={}, blockstack=(), active_try_block=None, outgoing_edgepushed={})
2025-05-10 10:43:04,309 - numba.core.interpreter - DEBUG - label 0:
    x = arg(0, name=x)                       ['x']
    $2load_global.0 = global(pd: <module 'pandas' from '/home/<USER>/.local/lib/python3.10/site-packages/pandas/__init__.py'>) ['$2load_global.0']
    $4load_method.1 = getattr(value=$2load_global.0, attr=notna) ['$2load_global.0', '$4load_method.1']
    $8call_method.3 = call $4load_method.1(x, func=$4load_method.1, args=[Var(x, io.py:271)], kws=(), vararg=None, varkwarg=None, target=None) ['$4load_method.1', '$8call_method.3', 'x']
    bool10 = global(bool: <class 'bool'>)    ['bool10']
    $10pred = call bool10($8call_method.3, func=bool10, args=(Var($8call_method.3, io.py:271),), kws=(), vararg=None, varkwarg=None, target=None) ['$10pred', '$8call_method.3', 'bool10']
    branch $10pred, 12, 32                   ['$10pred']
label 12:
    $12load_global.0 = global(hasattr: <built-in function hasattr>) ['$12load_global.0']
    $const16.2 = const(str, strftime)        ['$const16.2']
    $18call_function.3 = call $12load_global.0(x, $const16.2, func=$12load_global.0, args=[Var(x, io.py:271), Var($const16.2, io.py:271)], kws=(), vararg=None, varkwarg=None, target=None) ['$12load_global.0', '$18call_function.3', '$const16.2', 'x']
    bool20 = global(bool: <class 'bool'>)    ['bool20']
    $20pred = call bool20($18call_function.3, func=bool20, args=(Var($18call_function.3, io.py:271),), kws=(), vararg=None, varkwarg=None, target=None) ['$18call_function.3', '$20pred', 'bool20']
    branch $20pred, 22, 32                   ['$20pred']
label 22:
    $24load_method.1 = getattr(value=x, attr=strftime) ['$24load_method.1', 'x']
    $const26.2 = const(str, %y%m%d)          ['$const26.2']
    $28call_method.3 = call $24load_method.1($const26.2, func=$24load_method.1, args=[Var($const26.2, io.py:271)], kws=(), vararg=None, varkwarg=None, target=None) ['$24load_method.1', '$28call_method.3', '$const26.2']
    $30return_value.4 = cast(value=$28call_method.3) ['$28call_method.3', '$30return_value.4']
    return $30return_value.4                 ['$30return_value.4']
label 32:
    $34return_value.1 = cast(value=x)        ['$34return_value.1', 'x']
    return $34return_value.1                 ['$34return_value.1']

2025-05-10 10:43:04,317 - numba.core.ssa - DEBUG - ==== SSA block analysis pass on 0
2025-05-10 10:43:04,317 - numba.core.ssa - DEBUG - Running <numba.core.ssa._GatherDefsHandler object at 0x7f2bc848f280>
2025-05-10 10:43:04,318 - numba.core.ssa - DEBUG - on stmt: x = arg(0, name=x)
2025-05-10 10:43:04,318 - numba.core.ssa - DEBUG - on stmt: $2load_global.0 = global(pd: <module 'pandas' from '/home/<USER>/.local/lib/python3.10/site-packages/pandas/__init__.py'>)
2025-05-10 10:43:04,318 - numba.core.ssa - DEBUG - on stmt: $4load_method.1 = getattr(value=$2load_global.0, attr=notna)
2025-05-10 10:43:04,318 - numba.core.ssa - DEBUG - on stmt: $8call_method.3 = call $4load_method.1(x, func=$4load_method.1, args=[Var(x, io.py:271)], kws=(), vararg=None, varkwarg=None, target=None)
2025-05-10 10:43:04,318 - numba.core.ssa - DEBUG - on stmt: bool10 = global(bool: <class 'bool'>)
2025-05-10 10:43:04,318 - numba.core.ssa - DEBUG - on stmt: $10pred = call bool10($8call_method.3, func=bool10, args=(Var($8call_method.3, io.py:271),), kws=(), vararg=None, varkwarg=None, target=None)
2025-05-10 10:43:04,318 - numba.core.ssa - DEBUG - on stmt: branch $10pred, 12, 32
2025-05-10 10:43:04,318 - numba.core.ssa - DEBUG - ==== SSA block analysis pass on 12
2025-05-10 10:43:04,318 - numba.core.ssa - DEBUG - Running <numba.core.ssa._GatherDefsHandler object at 0x7f2bc848f280>
2025-05-10 10:43:04,318 - numba.core.ssa - DEBUG - on stmt: $12load_global.0 = global(hasattr: <built-in function hasattr>)
2025-05-10 10:43:04,319 - numba.core.ssa - DEBUG - on stmt: $const16.2 = const(str, strftime)
2025-05-10 10:43:04,319 - numba.core.ssa - DEBUG - on stmt: $18call_function.3 = call $12load_global.0(x, $const16.2, func=$12load_global.0, args=[Var(x, io.py:271), Var($const16.2, io.py:271)], kws=(), vararg=None, varkwarg=None, target=None)
2025-05-10 10:43:04,319 - numba.core.ssa - DEBUG - on stmt: bool20 = global(bool: <class 'bool'>)
2025-05-10 10:43:04,319 - numba.core.ssa - DEBUG - on stmt: $20pred = call bool20($18call_function.3, func=bool20, args=(Var($18call_function.3, io.py:271),), kws=(), vararg=None, varkwarg=None, target=None)
2025-05-10 10:43:04,319 - numba.core.ssa - DEBUG - on stmt: branch $20pred, 22, 32
2025-05-10 10:43:04,319 - numba.core.ssa - DEBUG - ==== SSA block analysis pass on 22
2025-05-10 10:43:04,319 - numba.core.ssa - DEBUG - Running <numba.core.ssa._GatherDefsHandler object at 0x7f2bc848f280>
2025-05-10 10:43:04,319 - numba.core.ssa - DEBUG - on stmt: $24load_method.1 = getattr(value=x, attr=strftime)
2025-05-10 10:43:04,319 - numba.core.ssa - DEBUG - on stmt: $const26.2 = const(str, %y%m%d)
2025-05-10 10:43:04,319 - numba.core.ssa - DEBUG - on stmt: $28call_method.3 = call $24load_method.1($const26.2, func=$24load_method.1, args=[Var($const26.2, io.py:271)], kws=(), vararg=None, varkwarg=None, target=None)
2025-05-10 10:43:04,319 - numba.core.ssa - DEBUG - on stmt: $30return_value.4 = cast(value=$28call_method.3)
2025-05-10 10:43:04,319 - numba.core.ssa - DEBUG - on stmt: return $30return_value.4
2025-05-10 10:43:04,319 - numba.core.ssa - DEBUG - ==== SSA block analysis pass on 32
2025-05-10 10:43:04,320 - numba.core.ssa - DEBUG - Running <numba.core.ssa._GatherDefsHandler object at 0x7f2bc848f280>
2025-05-10 10:43:04,320 - numba.core.ssa - DEBUG - on stmt: $34return_value.1 = cast(value=x)
2025-05-10 10:43:04,320 - numba.core.ssa - DEBUG - on stmt: return $34return_value.1
2025-05-10 10:43:04,320 - numba.core.ssa - DEBUG - defs defaultdict(<class 'list'>,
            {'$10pred': [<numba.core.ir.Assign object at 0x7f2bc8452500>],
             '$12load_global.0': [<numba.core.ir.Assign object at 0x7f2bc8453ac0>],
             '$18call_function.3': [<numba.core.ir.Assign object at 0x7f2bc8453f40>],
             '$20pred': [<numba.core.ir.Assign object at 0x7f2bc848c1c0>],
             '$24load_method.1': [<numba.core.ir.Assign object at 0x7f2bc848c520>],
             '$28call_method.3': [<numba.core.ir.Assign object at 0x7f2bc848c8b0>],
             '$2load_global.0': [<numba.core.ir.Assign object at 0x7f2bc84536a0>],
             '$30return_value.4': [<numba.core.ir.Assign object at 0x7f2bc848c9a0>],
             '$34return_value.1': [<numba.core.ir.Assign object at 0x7f2bc848cd00>],
             '$4load_method.1': [<numba.core.ir.Assign object at 0x7f2bc84533a0>],
             '$8call_method.3': [<numba.core.ir.Assign object at 0x7f2bc84521d0>],
             '$const16.2': [<numba.core.ir.Assign object at 0x7f2bc8453d60>],
             '$const26.2': [<numba.core.ir.Assign object at 0x7f2bc848c6a0>],
             'bool10': [<numba.core.ir.Assign object at 0x7f2bc8452740>],
             'bool20': [<numba.core.ir.Assign object at 0x7f2bc848c0a0>],
             'x': [<numba.core.ir.Assign object at 0x7f2bc8452b00>]})
2025-05-10 10:43:04,320 - numba.core.ssa - DEBUG - SSA violators set()
2025-05-10 10:43:04,338 - numba.core.typeinfer - DEBUG - captured error
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 155, in propagate
    constraint(typeinfer)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 722, in __call__
    raise UntypedAttributeError(ty, self.attr,
numba.core.errors.UntypedAttributeError: [1m[1mUnknown attribute 'notna' of type Module(<module 'pandas' from '/home/<USER>/.local/lib/python3.10/site-packages/pandas/__init__.py'>)
[1m
File "bt/backtester_stable/BTRUN/io.py", line 271:[0m
[1mdef prepare_output_json(
    <source elided>
                    trans_df[date_col] = trans_df[date_col].apply(
[1m                        lambda x: x.strftime("%y%m%d") if pd.notna(x) and hasattr(x, 'strftime') else x
[0m                        [1m^[0m[0m
[0m
[0m[1mDuring: typing of get attribute at /srv/samba/shared/bt/backtester_stable/BTRUN/io.py (271)[0m
2025-05-10 10:43:04,339 - numba.core.typeinfer - DEBUG - captured error
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 155, in propagate
    constraint(typeinfer)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 576, in __call__
    fnty = typevars[self.func].getone()
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 123, in getone
    raise TypingError("Undecided type {}".format(self))
numba.core.errors.TypingError: [1m[1m[1mUndecided type $4load_method.1 := <undecided>[0m
[0m[1mDuring: resolving caller type: $4load_method.1[0m
[0m[1mDuring: typing of call at /srv/samba/shared/bt/backtester_stable/BTRUN/io.py (271)
[0m
2025-05-10 10:43:04,345 - numba.core.byteflow - DEBUG - bytecode dump:
>          0	NOP(arg=None, lineno=988)
           2	LOAD_GLOBAL(arg=0, lineno=989)
           4	LOAD_FAST(arg=0, lineno=989)
           6	LOAD_FAST(arg=1, lineno=989)
           8	CALL_FUNCTION(arg=2, lineno=989)
          10	RETURN_VALUE(arg=None, lineno=989)
2025-05-10 10:43:04,345 - numba.core.byteflow - DEBUG - pending: deque([State(pc_initial=0 nstack_initial=0)])
2025-05-10 10:43:04,345 - numba.core.byteflow - DEBUG - stack: []
2025-05-10 10:43:04,345 - numba.core.byteflow - DEBUG - state.pc_initial: State(pc_initial=0 nstack_initial=0)
2025-05-10 10:43:04,346 - numba.core.byteflow - DEBUG - dispatch pc=0, inst=NOP(arg=None, lineno=988)
2025-05-10 10:43:04,346 - numba.core.byteflow - DEBUG - stack []
2025-05-10 10:43:04,346 - numba.core.byteflow - DEBUG - dispatch pc=2, inst=LOAD_GLOBAL(arg=0, lineno=989)
2025-05-10 10:43:04,346 - numba.core.byteflow - DEBUG - stack []
2025-05-10 10:43:04,346 - numba.core.byteflow - DEBUG - dispatch pc=4, inst=LOAD_FAST(arg=0, lineno=989)
2025-05-10 10:43:04,346 - numba.core.byteflow - DEBUG - stack ['$2load_global.0']
2025-05-10 10:43:04,346 - numba.core.byteflow - DEBUG - dispatch pc=6, inst=LOAD_FAST(arg=1, lineno=989)
2025-05-10 10:43:04,346 - numba.core.byteflow - DEBUG - stack ['$2load_global.0', '$obj4.1']
2025-05-10 10:43:04,346 - numba.core.byteflow - DEBUG - dispatch pc=8, inst=CALL_FUNCTION(arg=2, lineno=989)
2025-05-10 10:43:04,346 - numba.core.byteflow - DEBUG - stack ['$2load_global.0', '$obj4.1', '$name6.2']
2025-05-10 10:43:04,346 - numba.core.byteflow - DEBUG - dispatch pc=10, inst=RETURN_VALUE(arg=None, lineno=989)
2025-05-10 10:43:04,346 - numba.core.byteflow - DEBUG - stack ['$8call_function.3']
2025-05-10 10:43:04,346 - numba.core.byteflow - DEBUG - end state. edges=[]
2025-05-10 10:43:04,346 - numba.core.byteflow - DEBUG - -------------------------Prune PHIs-------------------------
2025-05-10 10:43:04,346 - numba.core.byteflow - DEBUG - Used_phis: defaultdict(<class 'set'>, {State(pc_initial=0 nstack_initial=0): set()})
2025-05-10 10:43:04,347 - numba.core.byteflow - DEBUG - defmap: {}
2025-05-10 10:43:04,347 - numba.core.byteflow - DEBUG - phismap: defaultdict(<class 'set'>, {})
2025-05-10 10:43:04,347 - numba.core.byteflow - DEBUG - changing phismap: defaultdict(<class 'set'>, {})
2025-05-10 10:43:04,347 - numba.core.byteflow - DEBUG - keep phismap: {}
2025-05-10 10:43:04,347 - numba.core.byteflow - DEBUG - new_out: defaultdict(<class 'dict'>, {})
2025-05-10 10:43:04,347 - numba.core.byteflow - DEBUG - ----------------------DONE Prune PHIs-----------------------
2025-05-10 10:43:04,347 - numba.core.byteflow - DEBUG - block_infos State(pc_initial=0 nstack_initial=0):
AdaptBlockInfo(insts=((0, {}), (2, {'res': '$2load_global.0'}), (4, {'res': '$obj4.1'}), (6, {'res': '$name6.2'}), (8, {'func': '$2load_global.0', 'args': ['$obj4.1', '$name6.2'], 'res': '$8call_function.3'}), (10, {'retval': '$8call_function.3', 'castval': '$10return_value.4'})), outgoing_phis={}, blockstack=(), active_try_block=None, outgoing_edgepushed={})
2025-05-10 10:43:04,347 - numba.core.interpreter - DEBUG - label 0:
    obj = arg(0, name=obj)                   ['obj']
    name = arg(1, name=name)                 ['name']
    $2load_global.0 = global(resolve_hasattr: <intrinsic resolve_hasattr>) ['$2load_global.0']
    $8call_function.3 = call $2load_global.0(obj, name, func=$2load_global.0, args=[Var(obj, builtins.py:988), Var(name, builtins.py:988)], kws=(), vararg=None, varkwarg=None, target=None) ['$2load_global.0', '$8call_function.3', 'name', 'obj']
    $10return_value.4 = cast(value=$8call_function.3) ['$10return_value.4', '$8call_function.3']
    return $10return_value.4                 ['$10return_value.4']

2025-05-10 10:43:04,353 - numba.core.ssa - DEBUG - ==== SSA block analysis pass on 0
2025-05-10 10:43:04,353 - numba.core.ssa - DEBUG - Running <numba.core.ssa._GatherDefsHandler object at 0x7f2bc8233be0>
2025-05-10 10:43:04,353 - numba.core.ssa - DEBUG - on stmt: obj = arg(0, name=obj)
2025-05-10 10:43:04,353 - numba.core.ssa - DEBUG - on stmt: name = arg(1, name=name)
2025-05-10 10:43:04,353 - numba.core.ssa - DEBUG - on stmt: $2load_global.0 = global(resolve_hasattr: <intrinsic resolve_hasattr>)
2025-05-10 10:43:04,353 - numba.core.ssa - DEBUG - on stmt: $8call_function.3 = call $2load_global.0(obj, name, func=$2load_global.0, args=[Var(obj, builtins.py:988), Var(name, builtins.py:988)], kws=(), vararg=None, varkwarg=None, target=None)
2025-05-10 10:43:04,353 - numba.core.ssa - DEBUG - on stmt: $10return_value.4 = cast(value=$8call_function.3)
2025-05-10 10:43:04,353 - numba.core.ssa - DEBUG - on stmt: return $10return_value.4
2025-05-10 10:43:04,353 - numba.core.ssa - DEBUG - defs defaultdict(<class 'list'>,
            {'$10return_value.4': [<numba.core.ir.Assign object at 0x7f2bc8233400>],
             '$2load_global.0': [<numba.core.ir.Assign object at 0x7f2bc8232ec0>],
             '$8call_function.3': [<numba.core.ir.Assign object at 0x7f2bc82332e0>],
             'name': [<numba.core.ir.Assign object at 0x7f2bc8231c00>],
             'obj': [<numba.core.ir.Assign object at 0x7f2bc8232d40>]})
2025-05-10 10:43:04,353 - numba.core.ssa - DEBUG - SSA violators set()
2025-05-10 10:43:04,359 - numba.core.typeinfer - DEBUG - captured error
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 155, in propagate
    constraint(typeinfer)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 578, in __call__
    self.resolve(typeinfer, typevars, fnty)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 601, in resolve
    sig = typeinfer.resolve_call(fnty, pos_args, kw_args)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 1557, in resolve_call
    return self.context.resolve_function_type(fnty, pos_args, kw_args)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typing/context.py", line 213, in resolve_function_type
    raise last_exception
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typing/context.py", line 196, in resolve_function_type
    res = self._resolve_user_function_type(func, args, kws)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typing/context.py", line 248, in _resolve_user_function_type
    return func.get_call_type(self, args, kws)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/types/functions.py", line 331, in get_call_type
    failures.raise_error()
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/types/functions.py", line 227, in raise_error
    raise errors.TypingError(self.format())
numba.core.errors.TypingError: [1m[1m[1mNo implementation of function Function(<intrinsic resolve_hasattr>) found for signature:
 
 >>> resolve_hasattr(Masked(int64), unicode_type)
 
There are 2 candidate implementations:
[1m - Of which 2 did not match due to:
 Intrinsic in function 'resolve_hasattr': File: numba/cpython/builtins.py: Line 960.
   With argument(s): '(Masked(int64), unicode_type)':[0m
[1m  Rejected as the implementation raised a specific error:
    RequireLiteralValue: [1margument 'name' must be a literal string[0m[0m
  raised from /home/<USER>/.local/lib/python3.10/site-packages/numba/cpython/builtins.py:963
[0m
[0m[1mDuring: resolving callee type: Function(<intrinsic resolve_hasattr>)[0m
[0m[1mDuring: typing of call at /home/<USER>/.local/lib/python3.10/site-packages/numba/cpython/builtins.py (989)
[0m
2025-05-10 10:43:04,361 - numba.core.typeinfer - DEBUG - captured error
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 155, in propagate
    constraint(typeinfer)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 578, in __call__
    self.resolve(typeinfer, typevars, fnty)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 601, in resolve
    sig = typeinfer.resolve_call(fnty, pos_args, kw_args)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 1557, in resolve_call
    return self.context.resolve_function_type(fnty, pos_args, kw_args)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typing/context.py", line 213, in resolve_function_type
    raise last_exception
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typing/context.py", line 196, in resolve_function_type
    res = self._resolve_user_function_type(func, args, kws)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typing/context.py", line 248, in _resolve_user_function_type
    return func.get_call_type(self, args, kws)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/types/functions.py", line 331, in get_call_type
    failures.raise_error()
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/types/functions.py", line 227, in raise_error
    raise errors.TypingError(self.format())
numba.core.errors.TypingError: [1m[1m[1mNo implementation of function Function(<intrinsic resolve_hasattr>) found for signature:
 
 >>> resolve_hasattr(Masked(int64), unicode_type)
 
There are 2 candidate implementations:
[1m  - Of which 2 did not match due to:
  Intrinsic in function 'resolve_hasattr': File: numba/cpython/builtins.py: Line 960.
    With argument(s): '(Masked(int64), unicode_type)':[0m
[1m   Rejected as the implementation raised a specific error:
     RequireLiteralValue: [1margument 'name' must be a literal string[0m[0m
  raised from /home/<USER>/.local/lib/python3.10/site-packages/numba/cpython/builtins.py:963
[0m
[0m[1mDuring: resolving callee type: Function(<intrinsic resolve_hasattr>)[0m
[0m[1mDuring: typing of call at /home/<USER>/.local/lib/python3.10/site-packages/numba/cpython/builtins.py (989)
[0m
2025-05-10 10:43:04,363 - numba.core.byteflow - DEBUG - bytecode dump:
>          0	NOP(arg=None, lineno=988)
           2	LOAD_GLOBAL(arg=0, lineno=989)
           4	LOAD_FAST(arg=0, lineno=989)
           6	LOAD_FAST(arg=1, lineno=989)
           8	CALL_FUNCTION(arg=2, lineno=989)
          10	RETURN_VALUE(arg=None, lineno=989)
2025-05-10 10:43:04,363 - numba.core.byteflow - DEBUG - pending: deque([State(pc_initial=0 nstack_initial=0)])
2025-05-10 10:43:04,363 - numba.core.byteflow - DEBUG - stack: []
2025-05-10 10:43:04,363 - numba.core.byteflow - DEBUG - state.pc_initial: State(pc_initial=0 nstack_initial=0)
2025-05-10 10:43:04,363 - numba.core.byteflow - DEBUG - dispatch pc=0, inst=NOP(arg=None, lineno=988)
2025-05-10 10:43:04,363 - numba.core.byteflow - DEBUG - stack []
2025-05-10 10:43:04,363 - numba.core.byteflow - DEBUG - dispatch pc=2, inst=LOAD_GLOBAL(arg=0, lineno=989)
2025-05-10 10:43:04,363 - numba.core.byteflow - DEBUG - stack []
2025-05-10 10:43:04,364 - numba.core.byteflow - DEBUG - dispatch pc=4, inst=LOAD_FAST(arg=0, lineno=989)
2025-05-10 10:43:04,364 - numba.core.byteflow - DEBUG - stack ['$2load_global.0']
2025-05-10 10:43:04,364 - numba.core.byteflow - DEBUG - dispatch pc=6, inst=LOAD_FAST(arg=1, lineno=989)
2025-05-10 10:43:04,364 - numba.core.byteflow - DEBUG - stack ['$2load_global.0', '$obj4.1']
2025-05-10 10:43:04,364 - numba.core.byteflow - DEBUG - dispatch pc=8, inst=CALL_FUNCTION(arg=2, lineno=989)
2025-05-10 10:43:04,364 - numba.core.byteflow - DEBUG - stack ['$2load_global.0', '$obj4.1', '$name6.2']
2025-05-10 10:43:04,364 - numba.core.byteflow - DEBUG - dispatch pc=10, inst=RETURN_VALUE(arg=None, lineno=989)
2025-05-10 10:43:04,364 - numba.core.byteflow - DEBUG - stack ['$8call_function.3']
2025-05-10 10:43:04,364 - numba.core.byteflow - DEBUG - end state. edges=[]
2025-05-10 10:43:04,364 - numba.core.byteflow - DEBUG - -------------------------Prune PHIs-------------------------
2025-05-10 10:43:04,364 - numba.core.byteflow - DEBUG - Used_phis: defaultdict(<class 'set'>, {State(pc_initial=0 nstack_initial=0): set()})
2025-05-10 10:43:04,364 - numba.core.byteflow - DEBUG - defmap: {}
2025-05-10 10:43:04,364 - numba.core.byteflow - DEBUG - phismap: defaultdict(<class 'set'>, {})
2025-05-10 10:43:04,364 - numba.core.byteflow - DEBUG - changing phismap: defaultdict(<class 'set'>, {})
2025-05-10 10:43:04,364 - numba.core.byteflow - DEBUG - keep phismap: {}
2025-05-10 10:43:04,365 - numba.core.byteflow - DEBUG - new_out: defaultdict(<class 'dict'>, {})
2025-05-10 10:43:04,365 - numba.core.byteflow - DEBUG - ----------------------DONE Prune PHIs-----------------------
2025-05-10 10:43:04,365 - numba.core.byteflow - DEBUG - block_infos State(pc_initial=0 nstack_initial=0):
AdaptBlockInfo(insts=((0, {}), (2, {'res': '$2load_global.0'}), (4, {'res': '$obj4.1'}), (6, {'res': '$name6.2'}), (8, {'func': '$2load_global.0', 'args': ['$obj4.1', '$name6.2'], 'res': '$8call_function.3'}), (10, {'retval': '$8call_function.3', 'castval': '$10return_value.4'})), outgoing_phis={}, blockstack=(), active_try_block=None, outgoing_edgepushed={})
2025-05-10 10:43:04,365 - numba.core.interpreter - DEBUG - label 0:
    obj = arg(0, name=obj)                   ['obj']
    name = arg(1, name=name)                 ['name']
    $2load_global.0 = global(resolve_hasattr: <intrinsic resolve_hasattr>) ['$2load_global.0']
    $8call_function.3 = call $2load_global.0(obj, name, func=$2load_global.0, args=[Var(obj, builtins.py:988), Var(name, builtins.py:988)], kws=(), vararg=None, varkwarg=None, target=None) ['$2load_global.0', '$8call_function.3', 'name', 'obj']
    $10return_value.4 = cast(value=$8call_function.3) ['$10return_value.4', '$8call_function.3']
    return $10return_value.4                 ['$10return_value.4']

2025-05-10 10:43:04,370 - numba.core.ssa - DEBUG - ==== SSA block analysis pass on 0
2025-05-10 10:43:04,370 - numba.core.ssa - DEBUG - Running <numba.core.ssa._GatherDefsHandler object at 0x7f2bc8231930>
2025-05-10 10:43:04,370 - numba.core.ssa - DEBUG - on stmt: obj = arg(0, name=obj)
2025-05-10 10:43:04,371 - numba.core.ssa - DEBUG - on stmt: name = arg(1, name=name)
2025-05-10 10:43:04,371 - numba.core.ssa - DEBUG - on stmt: $2load_global.0 = global(resolve_hasattr: <intrinsic resolve_hasattr>)
2025-05-10 10:43:04,371 - numba.core.ssa - DEBUG - on stmt: $8call_function.3 = call $2load_global.0(obj, name, func=$2load_global.0, args=[Var(obj, builtins.py:988), Var(name, builtins.py:988)], kws=(), vararg=None, varkwarg=None, target=None)
2025-05-10 10:43:04,371 - numba.core.ssa - DEBUG - on stmt: $10return_value.4 = cast(value=$8call_function.3)
2025-05-10 10:43:04,371 - numba.core.ssa - DEBUG - on stmt: return $10return_value.4
2025-05-10 10:43:04,371 - numba.core.ssa - DEBUG - defs defaultdict(<class 'list'>,
            {'$10return_value.4': [<numba.core.ir.Assign object at 0x7f2bc82d30d0>],
             '$2load_global.0': [<numba.core.ir.Assign object at 0x7f2bc82d2b90>],
             '$8call_function.3': [<numba.core.ir.Assign object at 0x7f2bc82d2fb0>],
             'name': [<numba.core.ir.Assign object at 0x7f2bc82d2050>],
             'obj': [<numba.core.ir.Assign object at 0x7f2bc82d1bd0>]})
2025-05-10 10:43:04,371 - numba.core.ssa - DEBUG - SSA violators set()
2025-05-10 10:43:04,471 - numba.core.typeinfer - DEBUG - captured error
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 155, in propagate
    constraint(typeinfer)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 722, in __call__
    raise UntypedAttributeError(ty, self.attr,
numba.core.errors.UntypedAttributeError: [1m[1mUnknown attribute 'strftime' of type Masked(int64)
[1m
File "bt/backtester_stable/BTRUN/io.py", line 271:[0m
[1mdef prepare_output_json(
    <source elided>
                    trans_df[date_col] = trans_df[date_col].apply(
[1m                        lambda x: x.strftime("%y%m%d") if pd.notna(x) and hasattr(x, 'strftime') else x
[0m                        [1m^[0m[0m
[0m
[0m[1mDuring: typing of get attribute at /srv/samba/shared/bt/backtester_stable/BTRUN/io.py (271)[0m
2025-05-10 10:43:04,471 - numba.core.typeinfer - DEBUG - captured error
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 155, in propagate
    constraint(typeinfer)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 576, in __call__
    fnty = typevars[self.func].getone()
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 123, in getone
    raise TypingError("Undecided type {}".format(self))
numba.core.errors.TypingError: [1m[1m[1mUndecided type $24load_method.1 := <undecided>[0m
[0m[1mDuring: resolving caller type: $24load_method.1[0m
[0m[1mDuring: typing of call at /srv/samba/shared/bt/backtester_stable/BTRUN/io.py (271)
[0m
2025-05-10 10:43:04,472 - numba.core.typeinfer - DEBUG - captured error
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 155, in propagate
    constraint(typeinfer)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 722, in __call__
    raise UntypedAttributeError(ty, self.attr,
numba.core.errors.UntypedAttributeError: [1m[1mUnknown attribute 'notna' of type Module(<module 'pandas' from '/home/<USER>/.local/lib/python3.10/site-packages/pandas/__init__.py'>)
[1m
File "bt/backtester_stable/BTRUN/io.py", line 271:[0m
[1mdef prepare_output_json(
    <source elided>
                    trans_df[date_col] = trans_df[date_col].apply(
[1m                        lambda x: x.strftime("%y%m%d") if pd.notna(x) and hasattr(x, 'strftime') else x
[0m                        [1m^[0m[0m
[0m
[0m[1mDuring: typing of get attribute at /srv/samba/shared/bt/backtester_stable/BTRUN/io.py (271)[0m
2025-05-10 10:43:04,472 - numba.core.typeinfer - DEBUG - captured error
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 155, in propagate
    constraint(typeinfer)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 576, in __call__
    fnty = typevars[self.func].getone()
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 123, in getone
    raise TypingError("Undecided type {}".format(self))
numba.core.errors.TypingError: [1m[1m[1mUndecided type $4load_method.1 := <undecided>[0m
[0m[1mDuring: resolving caller type: $4load_method.1[0m
[0m[1mDuring: typing of call at /srv/samba/shared/bt/backtester_stable/BTRUN/io.py (271)
[0m
2025-05-10 10:43:04,474 - numba.core.byteflow - DEBUG - bytecode dump:
>          0	NOP(arg=None, lineno=988)
           2	LOAD_GLOBAL(arg=0, lineno=989)
           4	LOAD_FAST(arg=0, lineno=989)
           6	LOAD_FAST(arg=1, lineno=989)
           8	CALL_FUNCTION(arg=2, lineno=989)
          10	RETURN_VALUE(arg=None, lineno=989)
2025-05-10 10:43:04,474 - numba.core.byteflow - DEBUG - pending: deque([State(pc_initial=0 nstack_initial=0)])
2025-05-10 10:43:04,475 - numba.core.byteflow - DEBUG - stack: []
2025-05-10 10:43:04,475 - numba.core.byteflow - DEBUG - state.pc_initial: State(pc_initial=0 nstack_initial=0)
2025-05-10 10:43:04,475 - numba.core.byteflow - DEBUG - dispatch pc=0, inst=NOP(arg=None, lineno=988)
2025-05-10 10:43:04,475 - numba.core.byteflow - DEBUG - stack []
2025-05-10 10:43:04,475 - numba.core.byteflow - DEBUG - dispatch pc=2, inst=LOAD_GLOBAL(arg=0, lineno=989)
2025-05-10 10:43:04,475 - numba.core.byteflow - DEBUG - stack []
2025-05-10 10:43:04,475 - numba.core.byteflow - DEBUG - dispatch pc=4, inst=LOAD_FAST(arg=0, lineno=989)
2025-05-10 10:43:04,475 - numba.core.byteflow - DEBUG - stack ['$2load_global.0']
2025-05-10 10:43:04,475 - numba.core.byteflow - DEBUG - dispatch pc=6, inst=LOAD_FAST(arg=1, lineno=989)
2025-05-10 10:43:04,475 - numba.core.byteflow - DEBUG - stack ['$2load_global.0', '$obj4.1']
2025-05-10 10:43:04,475 - numba.core.byteflow - DEBUG - dispatch pc=8, inst=CALL_FUNCTION(arg=2, lineno=989)
2025-05-10 10:43:04,475 - numba.core.byteflow - DEBUG - stack ['$2load_global.0', '$obj4.1', '$name6.2']
2025-05-10 10:43:04,475 - numba.core.byteflow - DEBUG - dispatch pc=10, inst=RETURN_VALUE(arg=None, lineno=989)
2025-05-10 10:43:04,475 - numba.core.byteflow - DEBUG - stack ['$8call_function.3']
2025-05-10 10:43:04,475 - numba.core.byteflow - DEBUG - end state. edges=[]
2025-05-10 10:43:04,475 - numba.core.byteflow - DEBUG - -------------------------Prune PHIs-------------------------
2025-05-10 10:43:04,476 - numba.core.byteflow - DEBUG - Used_phis: defaultdict(<class 'set'>, {State(pc_initial=0 nstack_initial=0): set()})
2025-05-10 10:43:04,476 - numba.core.byteflow - DEBUG - defmap: {}
2025-05-10 10:43:04,476 - numba.core.byteflow - DEBUG - phismap: defaultdict(<class 'set'>, {})
2025-05-10 10:43:04,476 - numba.core.byteflow - DEBUG - changing phismap: defaultdict(<class 'set'>, {})
2025-05-10 10:43:04,476 - numba.core.byteflow - DEBUG - keep phismap: {}
2025-05-10 10:43:04,476 - numba.core.byteflow - DEBUG - new_out: defaultdict(<class 'dict'>, {})
2025-05-10 10:43:04,476 - numba.core.byteflow - DEBUG - ----------------------DONE Prune PHIs-----------------------
2025-05-10 10:43:04,476 - numba.core.byteflow - DEBUG - block_infos State(pc_initial=0 nstack_initial=0):
AdaptBlockInfo(insts=((0, {}), (2, {'res': '$2load_global.0'}), (4, {'res': '$obj4.1'}), (6, {'res': '$name6.2'}), (8, {'func': '$2load_global.0', 'args': ['$obj4.1', '$name6.2'], 'res': '$8call_function.3'}), (10, {'retval': '$8call_function.3', 'castval': '$10return_value.4'})), outgoing_phis={}, blockstack=(), active_try_block=None, outgoing_edgepushed={})
2025-05-10 10:43:04,477 - numba.core.interpreter - DEBUG - label 0:
    obj = arg(0, name=obj)                   ['obj']
    name = arg(1, name=name)                 ['name']
    $2load_global.0 = global(resolve_hasattr: <intrinsic resolve_hasattr>) ['$2load_global.0']
    $8call_function.3 = call $2load_global.0(obj, name, func=$2load_global.0, args=[Var(obj, builtins.py:988), Var(name, builtins.py:988)], kws=(), vararg=None, varkwarg=None, target=None) ['$2load_global.0', '$8call_function.3', 'name', 'obj']
    $10return_value.4 = cast(value=$8call_function.3) ['$10return_value.4', '$8call_function.3']
    return $10return_value.4                 ['$10return_value.4']

2025-05-10 10:43:04,482 - numba.core.ssa - DEBUG - ==== SSA block analysis pass on 0
2025-05-10 10:43:04,482 - numba.core.ssa - DEBUG - Running <numba.core.ssa._GatherDefsHandler object at 0x7f2bc82d0370>
2025-05-10 10:43:04,482 - numba.core.ssa - DEBUG - on stmt: obj = arg(0, name=obj)
2025-05-10 10:43:04,482 - numba.core.ssa - DEBUG - on stmt: name = arg(1, name=name)
2025-05-10 10:43:04,482 - numba.core.ssa - DEBUG - on stmt: $2load_global.0 = global(resolve_hasattr: <intrinsic resolve_hasattr>)
2025-05-10 10:43:04,482 - numba.core.ssa - DEBUG - on stmt: $8call_function.3 = call $2load_global.0(obj, name, func=$2load_global.0, args=[Var(obj, builtins.py:988), Var(name, builtins.py:988)], kws=(), vararg=None, varkwarg=None, target=None)
2025-05-10 10:43:04,482 - numba.core.ssa - DEBUG - on stmt: $10return_value.4 = cast(value=$8call_function.3)
2025-05-10 10:43:04,482 - numba.core.ssa - DEBUG - on stmt: return $10return_value.4
2025-05-10 10:43:04,482 - numba.core.ssa - DEBUG - defs defaultdict(<class 'list'>,
            {'$10return_value.4': [<numba.core.ir.Assign object at 0x7f2bc8231fc0>],
             '$2load_global.0': [<numba.core.ir.Assign object at 0x7f2bc82d1630>],
             '$8call_function.3': [<numba.core.ir.Assign object at 0x7f2bc8233760>],
             'name': [<numba.core.ir.Assign object at 0x7f2bc82d3fa0>],
             'obj': [<numba.core.ir.Assign object at 0x7f2bc82d1090>]})
2025-05-10 10:43:04,482 - numba.core.ssa - DEBUG - SSA violators set()
2025-05-10 10:43:04,485 - numba.core.typeinfer - DEBUG - captured error
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 155, in propagate
    constraint(typeinfer)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 578, in __call__
    self.resolve(typeinfer, typevars, fnty)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 601, in resolve
    sig = typeinfer.resolve_call(fnty, pos_args, kw_args)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 1557, in resolve_call
    return self.context.resolve_function_type(fnty, pos_args, kw_args)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typing/context.py", line 213, in resolve_function_type
    raise last_exception
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typing/context.py", line 196, in resolve_function_type
    res = self._resolve_user_function_type(func, args, kws)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typing/context.py", line 248, in _resolve_user_function_type
    return func.get_call_type(self, args, kws)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/types/functions.py", line 331, in get_call_type
    failures.raise_error()
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/types/functions.py", line 227, in raise_error
    raise errors.TypingError(self.format())
numba.core.errors.TypingError: [1m[1m[1mNo implementation of function Function(<intrinsic resolve_hasattr>) found for signature:
 
 >>> resolve_hasattr(Masked(int64), unicode_type)
 
There are 2 candidate implementations:
[1m   - Of which 2 did not match due to:
   Intrinsic in function 'resolve_hasattr': File: numba/cpython/builtins.py: Line 960.
     With argument(s): '(Masked(int64), unicode_type)':[0m
[1m    Rejected as the implementation raised a specific error:
      RequireLiteralValue: [1margument 'name' must be a literal string[0m[0m
  raised from /home/<USER>/.local/lib/python3.10/site-packages/numba/cpython/builtins.py:963
[0m
[0m[1mDuring: resolving callee type: Function(<intrinsic resolve_hasattr>)[0m
[0m[1mDuring: typing of call at /home/<USER>/.local/lib/python3.10/site-packages/numba/cpython/builtins.py (989)
[0m
2025-05-10 10:43:04,486 - numba.core.typeinfer - DEBUG - captured error
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 155, in propagate
    constraint(typeinfer)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 578, in __call__
    self.resolve(typeinfer, typevars, fnty)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 601, in resolve
    sig = typeinfer.resolve_call(fnty, pos_args, kw_args)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 1557, in resolve_call
    return self.context.resolve_function_type(fnty, pos_args, kw_args)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typing/context.py", line 213, in resolve_function_type
    raise last_exception
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typing/context.py", line 196, in resolve_function_type
    res = self._resolve_user_function_type(func, args, kws)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typing/context.py", line 248, in _resolve_user_function_type
    return func.get_call_type(self, args, kws)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/types/functions.py", line 331, in get_call_type
    failures.raise_error()
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/types/functions.py", line 227, in raise_error
    raise errors.TypingError(self.format())
numba.core.errors.TypingError: [1m[1m[1mNo implementation of function Function(<intrinsic resolve_hasattr>) found for signature:
 
 >>> resolve_hasattr(Masked(int64), unicode_type)
 
There are 2 candidate implementations:
[1m    - Of which 2 did not match due to:
    Intrinsic in function 'resolve_hasattr': File: numba/cpython/builtins.py: Line 960.
      With argument(s): '(Masked(int64), unicode_type)':[0m
[1m     Rejected as the implementation raised a specific error:
       RequireLiteralValue: [1margument 'name' must be a literal string[0m[0m
  raised from /home/<USER>/.local/lib/python3.10/site-packages/numba/cpython/builtins.py:963
[0m
[0m[1mDuring: resolving callee type: Function(<intrinsic resolve_hasattr>)[0m
[0m[1mDuring: typing of call at /home/<USER>/.local/lib/python3.10/site-packages/numba/cpython/builtins.py (989)
[0m
2025-05-10 10:43:04,488 - numba.core.typeinfer - DEBUG - captured error
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 155, in propagate
    constraint(typeinfer)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 722, in __call__
    raise UntypedAttributeError(ty, self.attr,
numba.core.errors.UntypedAttributeError: [1m[1mUnknown attribute 'strftime' of type Masked(int64)
[1m
File "bt/backtester_stable/BTRUN/io.py", line 271:[0m
[1mdef prepare_output_json(
    <source elided>
                    trans_df[date_col] = trans_df[date_col].apply(
[1m                        lambda x: x.strftime("%y%m%d") if pd.notna(x) and hasattr(x, 'strftime') else x
[0m                        [1m^[0m[0m
[0m
[0m[1mDuring: typing of get attribute at /srv/samba/shared/bt/backtester_stable/BTRUN/io.py (271)[0m
2025-05-10 10:43:04,488 - numba.core.typeinfer - DEBUG - captured error
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 155, in propagate
    constraint(typeinfer)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 576, in __call__
    fnty = typevars[self.func].getone()
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 123, in getone
    raise TypingError("Undecided type {}".format(self))
numba.core.errors.TypingError: [1m[1m[1mUndecided type $24load_method.1 := <undecided>[0m
[0m[1mDuring: resolving caller type: $24load_method.1[0m
[0m[1mDuring: typing of call at /srv/samba/shared/bt/backtester_stable/BTRUN/io.py (271)
[0m
2025-05-10 10:43:04,492 - numba.core.typeinfer - DEBUG - captured error
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 155, in propagate
    constraint(typeinfer)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 722, in __call__
    raise UntypedAttributeError(ty, self.attr,
numba.core.errors.UntypedAttributeError: [1m[1mUnknown attribute 'notna' of type Module(<module 'pandas' from '/home/<USER>/.local/lib/python3.10/site-packages/pandas/__init__.py'>)
[1m
File "bt/backtester_stable/BTRUN/io.py", line 271:[0m
[1mdef prepare_output_json(
    <source elided>
                    trans_df[date_col] = trans_df[date_col].apply(
[1m                        lambda x: x.strftime("%y%m%d") if pd.notna(x) and hasattr(x, 'strftime') else x
[0m                        [1m^[0m[0m
[0m
[0m[1mDuring: typing of get attribute at /srv/samba/shared/bt/backtester_stable/BTRUN/io.py (271)[0m
2025-05-10 10:43:04,492 - numba.core.typeinfer - DEBUG - captured error
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 155, in propagate
    constraint(typeinfer)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 576, in __call__
    fnty = typevars[self.func].getone()
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 123, in getone
    raise TypingError("Undecided type {}".format(self))
numba.core.errors.TypingError: [1m[1m[1mUndecided type $4load_method.1 := <undecided>[0m
[0m[1mDuring: resolving caller type: $4load_method.1[0m
[0m[1mDuring: typing of call at /srv/samba/shared/bt/backtester_stable/BTRUN/io.py (271)
[0m
2025-05-10 10:43:04,493 - numba.core.typeinfer - DEBUG - captured error
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 155, in propagate
    constraint(typeinfer)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 722, in __call__
    raise UntypedAttributeError(ty, self.attr,
numba.core.errors.UntypedAttributeError: [1m[1mUnknown attribute 'notna' of type Module(<module 'pandas' from '/home/<USER>/.local/lib/python3.10/site-packages/pandas/__init__.py'>)
[1m
File "bt/backtester_stable/BTRUN/io.py", line 271:[0m
[1mdef prepare_output_json(
    <source elided>
                    trans_df[date_col] = trans_df[date_col].apply(
[1m                        lambda x: x.strftime("%y%m%d") if pd.notna(x) and hasattr(x, 'strftime') else x
[0m                        [1m^[0m[0m
[0m
[0m[1mDuring: typing of get attribute at /srv/samba/shared/bt/backtester_stable/BTRUN/io.py (271)[0m
2025-05-10 10:43:04,494 - numba.core.typeinfer - DEBUG - captured error
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 155, in propagate
    constraint(typeinfer)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 576, in __call__
    fnty = typevars[self.func].getone()
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 123, in getone
    raise TypingError("Undecided type {}".format(self))
numba.core.errors.TypingError: [1m[1m[1mUndecided type $4load_method.1 := <undecided>[0m
[0m[1mDuring: resolving caller type: $4load_method.1[0m
[0m[1mDuring: typing of call at /srv/samba/shared/bt/backtester_stable/BTRUN/io.py (271)
[0m
2025-05-10 10:43:04,495 - bt.backtester_stable.BTRUN.io - ERROR - Error preparing output JSON: user defined function compilation failed.
2025-05-10 10:43:04,497 - bt.backtester_stable.BTRUN.io - ERROR - Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/cudf/core/indexed_frame.py", line 2359, in _apply
    kernel, retty = _compile_or_get(
  File "/home/<USER>/.local/lib/python3.10/site-packages/nvtx/nvtx.py", line 122, in inner
    result = func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/cudf/core/udf/utils.py", line 268, in _compile_or_get
    kernel, scalar_return_type = kernel_getter(frame, func, args)
  File "/home/<USER>/.local/lib/python3.10/site-packages/cudf/core/udf/scalar_function.py", line 55, in _get_scalar_kernel
    scalar_return_type = _get_udf_return_type(sr_type, func, args)
  File "/home/<USER>/.local/lib/python3.10/site-packages/nvtx/nvtx.py", line 122, in inner
    result = func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/cudf/core/udf/utils.py", line 88, in _get_udf_return_type
    ptx, output_type = cudautils.compile_udf(func, compile_sig)
  File "/home/<USER>/.local/lib/python3.10/site-packages/cudf/utils/cudautils.py", line 126, in compile_udf
    ptx_code, return_type = cuda.compile_ptx_for_current_device(
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/cuda/compiler.py", line 319, in compile_ptx_for_current_device
    return compile_ptx(pyfunc, sig, debug=debug, lineinfo=lineinfo,
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/compiler_lock.py", line 35, in _acquire_compile_lock
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/cuda/compiler.py", line 289, in compile_ptx
    cres = compile_cuda(pyfunc, return_type, args, debug=debug,
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/compiler_lock.py", line 35, in _acquire_compile_lock
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/cuda/compiler.py", line 230, in compile_cuda
    cres = compiler.compile_extra(typingctx=typingctx,
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/compiler.py", line 762, in compile_extra
    return pipeline.compile_extra(func)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/compiler.py", line 460, in compile_extra
    return self._compile_bytecode()
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/compiler.py", line 528, in _compile_bytecode
    return self._compile_core()
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/compiler.py", line 507, in _compile_core
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/compiler.py", line 494, in _compile_core
    pm.run(self.state)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/compiler_machinery.py", line 368, in run
    raise patched_exception
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/compiler_machinery.py", line 356, in run
    self._runPass(idx, pass_inst, state)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/compiler_lock.py", line 35, in _acquire_compile_lock
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/compiler_machinery.py", line 311, in _runPass
    mutated |= check(pss.run_pass, internal_state)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/compiler_machinery.py", line 273, in check
    mangled = func(compiler_state)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typed_passes.py", line 110, in run_pass
    typemap, return_type, calltypes, errs = type_inference_stage(
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typed_passes.py", line 88, in type_inference_stage
    errs = infer.propagate(raise_errors=raise_errors)
  File "/home/<USER>/.local/lib/python3.10/site-packages/numba/core/typeinfer.py", line 1086, in propagate
    raise errors[0]
numba.core.errors.TypingError: Failed in cuda mode pipeline (step: nopython frontend)
[1m[1mUnknown attribute 'notna' of type Module(<module 'pandas' from '/home/<USER>/.local/lib/python3.10/site-packages/pandas/__init__.py'>)
[1m
File "bt/backtester_stable/BTRUN/io.py", line 271:[0m
[1mdef prepare_output_json(
    <source elided>
                    trans_df[date_col] = trans_df[date_col].apply(
[1m                        lambda x: x.strftime("%y%m%d") if pd.notna(x) and hasattr(x, 'strftime') else x
[0m                        [1m^[0m[0m
[0m
[0m[1mDuring: typing of get attribute at /srv/samba/shared/bt/backtester_stable/BTRUN/io.py (271)[0m
[1m
File "bt/backtester_stable/BTRUN/io.py", line 271:[0m
[1mdef prepare_output_json(
    <source elided>
                    trans_df[date_col] = trans_df[date_col].apply(
[1m                        lambda x: x.strftime("%y%m%d") if pd.notna(x) and hasattr(x, 'strftime') else x
[0m                        [1m^[0m[0m


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/io.py", line 270, in prepare_output_json
    trans_df[date_col] = trans_df[date_col].apply(
  File "/home/<USER>/.local/lib/python3.10/site-packages/nvtx/nvtx.py", line 122, in inner
    result = func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/cudf/core/series.py", line 2653, in apply
    result = self._apply(func, _get_scalar_kernel, *args, **kwargs)
  File "/usr/lib/python3.10/contextlib.py", line 79, in inner
    return func(*args, **kwds)
  File "/home/<USER>/.local/lib/python3.10/site-packages/nvtx/nvtx.py", line 122, in inner
    result = func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/cudf/core/indexed_frame.py", line 2363, in _apply
    raise ValueError(
ValueError: user defined function compilation failed.

2025-05-10 10:43:04,498 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Backtest completed in 9.59 seconds
2025-05-10 10:43:04,498 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Portfolio backtest completed successfully with 11996 trades
2025-05-10 10:43:04,498 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Saved json file to Trades/NIF0DTE_10052025_104254.json
2025-05-10 10:43:04,498 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Saved excel file to Trades/NIF0DTE_10052025_104254.xlsx
2025-05-10 10:43:04,498 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Backtest Summary: {
  "Total PnL": 0.0,
  "Win Rate": 0.0,
  "Sharpe Ratio": 0.0,
  "CAGR": 0.0,
  "Max Drawdown": 0.0
}
2025-05-10 10:43:04,499 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Backtest completed with 1 results
2025-05-10 10:43:04,499 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Exiting main function
