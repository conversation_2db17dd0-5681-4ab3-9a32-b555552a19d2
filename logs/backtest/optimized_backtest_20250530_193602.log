2025-05-30 19:36:02,227 - __main__ - INFO - ================================================================================
2025-05-30 19:36:02,227 - __main__ - INFO - OPTIMIZED BACKTEST WITH GOLDEN FORMAT
2025-05-30 19:36:02,227 - __main__ - INFO - ================================================================================
2025-05-30 19:36:02,235 - bt.backtester_stable.BTRUN.core.config - WARNING - Partially restored config.py is being imported - Phase 10.
2025-05-30 19:36:02,236 - bt.backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - get_table_name active!
2025-05-30 19:36:02,236 - bt.backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
2025-05-30 19:36:02,236 - bt.backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
2025-05-30 19:36:03,948 - numba.cuda.cudadrv.driver - INFO - init
2025-05-30 19:36:04,253 - bt.backtester_stable.BTRUN.core.gpu_helpers - INFO - cuDF is available - GPU acceleration enabled
2025-05-30 19:36:04,507 - bt.backtester_stable.BTRUN.strategies.tv_processor - INFO - Using DirectDAL for database access
2025-05-30 19:36:04,529 - root - ERROR - Failed to import BTRUN config module
2025-05-30 19:36:04,535 - __main__ - INFO - Loading portfolio from: input_portfolio_fixed.xlsx
2025-05-30 19:36:04,659 - __main__ - INFO - Portfolio: NIF0DTE
2025-05-30 19:36:04,659 - __main__ - INFO - Strategies: 1
2025-05-30 19:36:04,877 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 19:36:04,877 - __main__ - INFO - Running backtest from 240401 to 240430
2025-05-30 19:36:05,096 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 19:36:05,143 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.046s, returned 563 rows. Query: SELECT SUBSTRING('SELECT DISTINCT trade_date FROM nifty_option_chain', 1, 200) ...
2025-05-30 19:36:05,186 - bt.backtester_stable.BTRUN.core.heavydb_data_access - INFO - Cached 563 distinct trade dates
2025-05-30 19:36:05,187 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DTE filter found in strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL: 0
2025-05-30 19:36:05,224 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.037s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2025-04-01'' 
                    AND expiry_date = ''2025-04-01''
                    LIMIT 1', 1, 200) ...
2025-05-30 19:36:05,224 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2025-04-01 - Not an expiry day (DTE=0 filter active)
2025-05-30 19:36:05,260 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.035s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2025-04-02'' 
                    AND expiry_date = ''2025-04-02''
                    LIMIT 1', 1, 200) ...
2025-05-30 19:36:05,260 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2025-04-02 - Not an expiry day (DTE=0 filter active)
2025-05-30 19:36:05,296 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.036s, returned 1 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2025-04-03'' 
                    AND expiry_date = ''2025-04-03''
                    LIMIT 1', 1, 200) ...
2025-05-30 19:36:05,296 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Processing date 2025-04-03 - Is an expiry day (DTE=0 filter active)
2025-05-30 19:36:05,296 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 1 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-30 19:36:05,354 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.057s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-30 19:36:05,410 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.056s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-30 19:36:05,411 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Query results: entry_df size=1, exit_df size=1
2025-05-30 19:36:05,413 - bt.backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-30 19:36:05,745 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 19:36:06,032 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.287s, returned 18336 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-30 19:36:06,091 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 1 of 3
2025-05-30 19:36:06,091 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-30 19:36:06,091 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 2 of 3
2025-05-30 19:36:06,091 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-30 19:36:06,091 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 3 of 3
2025-05-30 19:36:06,091 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-30 19:36:06,092 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 1
2025-05-30 19:36:06,092 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 2 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-30 19:36:06,148 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.055s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-30 19:36:06,192 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.044s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-30 19:36:06,193 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Query results: entry_df size=1, exit_df size=1
2025-05-30 19:36:06,194 - bt.backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-30 19:36:06,442 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 19:36:06,697 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.254s, returned 18336 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-30 19:36:06,753 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 1 of 3
2025-05-30 19:36:06,753 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-30 19:36:06,753 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 2 of 3
2025-05-30 19:36:06,753 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-30 19:36:06,753 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 3 of 3
2025-05-30 19:36:06,753 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-30 19:36:06,754 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 2
2025-05-30 19:36:06,754 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 3 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-30 19:36:06,811 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.057s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike + (2.0 * 50)) AND oc.ce_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-30 19:36:06,858 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.046s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike + (2.0 * 50)) AND oc.ce_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-30 19:36:06,858 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Query results: entry_df size=1, exit_df size=1
2025-05-30 19:36:06,859 - bt.backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-30 19:36:07,088 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 19:36:07,310 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.222s, returned 18336 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-30 19:36:07,405 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 1 of 3
2025-05-30 19:36:07,405 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-30 19:36:07,405 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 2 of 3
2025-05-30 19:36:07,405 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-30 19:36:07,405 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 3 of 3
2025-05-30 19:36:07,405 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-30 19:36:07,406 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 3
2025-05-30 19:36:07,406 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 4 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-30 19:36:07,460 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.052s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike - (2.0 * 50)) AND oc.pe_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-30 19:36:07,506 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.046s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike - (2.0 * 50)) AND oc.pe_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-30 19:36:07,506 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Query results: entry_df size=1, exit_df size=1
2025-05-30 19:36:07,508 - bt.backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-30 19:36:07,729 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 19:36:07,977 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.248s, returned 18336 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-30 19:36:08,061 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 1 of 3
2025-05-30 19:36:08,061 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-30 19:36:08,061 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 2 of 3
2025-05-30 19:36:08,061 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-30 19:36:08,062 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 3 of 3
2025-05-30 19:36:08,062 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-30 19:36:08,062 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 4
2025-05-30 19:36:08,087 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.025s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2025-04-04'' 
                    AND expiry_date = ''2025-04-04''
                    LIMIT 1', 1, 200) ...
2025-05-30 19:36:08,087 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2025-04-04 - Not an expiry day (DTE=0 filter active)
2025-05-30 19:36:08,116 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.028s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2025-04-07'' 
                    AND expiry_date = ''2025-04-07''
                    LIMIT 1', 1, 200) ...
2025-05-30 19:36:08,116 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2025-04-07 - Not an expiry day (DTE=0 filter active)
2025-05-30 19:36:08,138 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.022s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2025-04-08'' 
                    AND expiry_date = ''2025-04-08''
                    LIMIT 1', 1, 200) ...
2025-05-30 19:36:08,138 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2025-04-08 - Not an expiry day (DTE=0 filter active)
2025-05-30 19:36:08,175 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.037s, returned 1 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2025-04-09'' 
                    AND expiry_date = ''2025-04-09''
                    LIMIT 1', 1, 200) ...
2025-05-30 19:36:08,175 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Processing date 2025-04-09 - Is an expiry day (DTE=0 filter active)
2025-05-30 19:36:08,175 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 1 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-30 19:36:08,209 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.033s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-09'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-30 19:36:08,256 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.047s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-09'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-30 19:36:08,256 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Query results: entry_df size=1, exit_df size=1
2025-05-30 19:36:08,259 - bt.backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-30 19:36:08,479 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 19:36:08,804 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.325s, returned 24122 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-30 19:36:08,912 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 1 of 3
2025-05-30 19:36:08,912 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-30 19:36:08,912 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 2 of 3
2025-05-30 19:36:08,912 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-30 19:36:08,912 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 3 of 3
2025-05-30 19:36:08,912 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-30 19:36:08,913 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 1
2025-05-30 19:36:08,913 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 2 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-30 19:36:08,963 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.049s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-09'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-30 19:36:08,994 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.032s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-09'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-30 19:36:08,995 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Query results: entry_df size=1, exit_df size=1
2025-05-30 19:36:08,996 - bt.backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-30 19:36:09,217 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 19:36:09,570 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.353s, returned 24122 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-30 19:36:09,678 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 1 of 3
2025-05-30 19:36:09,678 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-30 19:36:09,678 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 2 of 3
2025-05-30 19:36:09,678 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-30 19:36:09,678 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 3 of 3
2025-05-30 19:36:09,678 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-30 19:36:09,679 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 2
2025-05-30 19:36:09,679 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 3 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-30 19:36:09,739 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.059s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-09'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike + (2.0 * 50)) AND oc.ce_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-30 19:36:09,797 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.058s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-09'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike + (2.0 * 50)) AND oc.ce_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-30 19:36:09,797 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Query results: entry_df size=1, exit_df size=1
2025-05-30 19:36:09,799 - bt.backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-30 19:36:10,021 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 19:36:10,380 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.359s, returned 24122 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-30 19:36:10,482 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 1 of 3
2025-05-30 19:36:10,482 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-30 19:36:10,482 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 2 of 3
2025-05-30 19:36:10,482 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-30 19:36:10,482 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 3 of 3
2025-05-30 19:36:10,482 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-30 19:36:10,483 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 3
2025-05-30 19:36:10,483 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 4 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-30 19:36:10,527 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.043s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-09'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike - (2.0 * 50)) AND oc.pe_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-30 19:36:10,566 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.039s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-09'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike - (2.0 * 50)) AND oc.pe_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-30 19:36:10,566 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Query results: entry_df size=1, exit_df size=1
2025-05-30 19:36:10,567 - bt.backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-30 19:36:10,825 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 19:36:11,154 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.329s, returned 24122 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-30 19:36:11,226 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 1 of 3
2025-05-30 19:36:11,227 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-30 19:36:11,227 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 2 of 3
2025-05-30 19:36:11,227 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-30 19:36:11,227 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 3 of 3
2025-05-30 19:36:11,227 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-30 19:36:11,227 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 4
2025-05-30 19:36:11,254 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.026s, returned 0 rows. Query: SELECT SUBSTRING('SELECT DISTINCT 1 
                    FROM nifty_option_chain 
                    WHERE trade_date = ''2025-04-11'' 
                    AND expiry_date = ''2025-04-11''
                    LIMIT 1', 1, 200) ...
2025-05-30 19:36:11,254 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Skipping date 2025-04-11 - Not an expiry day (DTE=0 filter active)
2025-05-30 19:36:11,254 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Generated 8 trade records via model-driven path
2025-05-30 19:36:11,255 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Calculating tick-by-tick P&L using batched queries for performance optimization
2025-05-30 19:36:11,255 - bt.backtester_stable.BTRUN.strategies.tick_pnl_batch - INFO - Starting batched tick P&L calculation for 8 trades
2025-05-30 19:36:11,255 - bt.backtester_stable.BTRUN.strategies.tick_pnl_batch - INFO - Processing date 250403 with 4 trades
2025-05-30 19:36:11,309 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.053s, returned 1095 rows. Query: SELECT SUBSTRING('SELECT 
        EXTRACT(HOUR FROM trade_time) * 10000 + 
        EXTRACT(MINUTE FROM trade_time) * 100 + 
        EXTRACT(SECOND FROM trade_time) AS trade_time_int,
        strike,
        expiry_date', 1, 200) ...
2025-05-30 19:36:11,376 - bt.backtester_stable.BTRUN.strategies.tick_pnl_batch - INFO - Loaded 1095 price points for 365 unique timestamps
2025-05-30 19:36:11,378 - bt.backtester_stable.BTRUN.strategies.tick_pnl_batch - INFO - Calculated tick P&L for date 250403: 365 timestamps
2025-05-30 19:36:11,378 - bt.backtester_stable.BTRUN.strategies.tick_pnl_batch - INFO - Processing date 250409 with 4 trades
2025-05-30 19:36:11,434 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.056s, returned 1125 rows. Query: SELECT SUBSTRING('SELECT 
        EXTRACT(HOUR FROM trade_time) * 10000 + 
        EXTRACT(MINUTE FROM trade_time) * 100 + 
        EXTRACT(SECOND FROM trade_time) AS trade_time_int,
        strike,
        expiry_date', 1, 200) ...
2025-05-30 19:36:11,500 - bt.backtester_stable.BTRUN.strategies.tick_pnl_batch - INFO - Loaded 1125 price points for 375 unique timestamps
2025-05-30 19:36:11,504 - bt.backtester_stable.BTRUN.strategies.tick_pnl_batch - INFO - Calculated tick P&L for date 250409: 375 timestamps
2025-05-30 19:36:11,504 - bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Successfully calculated tick P&L data: 2 dates, 740 total timestamps
2025-05-30 19:36:11,505 - __main__ - INFO - Backtest query completed in 6.63s
2025-05-30 19:36:11,505 - __main__ - INFO - Processing backtest results...
2025-05-30 19:36:11,522 - bt.backtester_stable.BTRUN.builders - WARNING - utils.MARGIN_INFO not found or empty - margin requirements will be 0. Attempting to populate.
2025-05-30 19:36:11,522 - bt.backtester_stable.BTRUN.core.config - INFO - Found fixture 'margin_symbol_info.json' at bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-30 19:36:11,522 - bt.backtester_stable.BTRUN.core.utils - INFO - Loading margin symbol info from: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json (Broker: angel)
2025-05-30 19:36:11,523 - bt.backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.MARGIN_INFO for angel.
2025-05-30 19:36:11,523 - bt.backtester_stable.BTRUN.builders - ERROR - utils.MARGIN_INFO still empty after populate attempt. Margin req will be 0.
2025-05-30 19:36:11,523 - bt.backtester_stable.BTRUN.builders - WARNING - config.LOT_SIZE is empty. Attempting to populate via utils.
2025-05-30 19:36:11,523 - bt.backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-30 19:36:11,523 - bt.backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-30 19:36:11,528 - bt.backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-30 19:36:11,532 - bt.backtester_stable.BTRUN.builders - INFO - Total portfolio margin requirement: 0.00
2025-05-30 19:36:11,534 - bt.backtester_stable.BTRUN.builders - INFO - Backtest response parsed.
2025-05-30 19:36:11,571 - bt.backtester_stable.BTRUN.core.runtime - INFO - Generated metrics_df with 50 records for strategies: ['Combined' 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL']
2025-05-30 19:36:11,633 - __main__ - INFO - Result processing completed in 0.13s
2025-05-30 19:36:11,633 - __main__ - INFO - Writing golden format output to: optimized_golden_test.xlsx
2025-05-30 19:36:11,634 - bt.backtester_stable.BTRUN.core.io_golden - INFO - Writing results in golden format to optimized_golden_test.xlsx
2025-05-30 19:36:11,654 - bt.backtester_stable.BTRUN.core.io_golden - INFO - Added PortfolioParameter sheet
2025-05-30 19:36:11,662 - bt.backtester_stable.BTRUN.core.io_golden - INFO - Added GeneralParameter sheet
2025-05-30 19:36:11,671 - bt.backtester_stable.BTRUN.core.io_golden - ERROR - Error writing golden format results: 'LegModel' object has no attribute 'transaction_type'
2025-05-30 19:36:11,677 - bt.backtester_stable.BTRUN.core.io_golden - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/core/io_golden.py", line 376, in write_results_golden_format
    leg_params_df = create_leg_parameters(portfolio_model, portfolio_trans_df)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/core/io_golden.py", line 230, in create_leg_parameters
    'Transaction': leg.transaction_type.value if hasattr(leg.transaction_type, 'value') else str(leg.transaction_type),
  File "/usr/local/lib/python3.10/dist-packages/pydantic/main.py", line 989, in __getattr__
    raise AttributeError(f'{type(self).__name__!r} object has no attribute {item!r}')
AttributeError: 'LegModel' object has no attribute 'transaction_type'. Did you mean: 'transaction'?

2025-05-30 19:36:11,677 - __main__ - ERROR - Backtest failed: 'LegModel' object has no attribute 'transaction_type'
2025-05-30 19:36:11,681 - __main__ - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/run_optimized_backtest_golden.py", line 128, in run_optimized_backtest
    write_results_golden_format(
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/core/io_golden.py", line 376, in write_results_golden_format
    leg_params_df = create_leg_parameters(portfolio_model, portfolio_trans_df)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/core/io_golden.py", line 230, in create_leg_parameters
    'Transaction': leg.transaction_type.value if hasattr(leg.transaction_type, 'value') else str(leg.transaction_type),
  File "/usr/local/lib/python3.10/dist-packages/pydantic/main.py", line 989, in __getattr__
    raise AttributeError(f'{type(self).__name__!r} object has no attribute {item!r}')
AttributeError: 'LegModel' object has no attribute 'transaction_type'. Did you mean: 'transaction'?

