2025-05-12 08:42:28,261 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-12 08:42:28,262 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-12 08:42:28,262 - __main__ - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-12 08:42:28,262 - __main__ - DEBUG - HeavyDB host: **************, DB: heavydb
2025-05-12 08:42:28,262 - __main__ - DEBUG - GPU enabled: True
2025-05-12 08:42:28,264 - __main__ - INFO - No config provided – defaulting to Excel input format
2025-05-12 08:42:28,264 - __main__ - INFO - GPU acceleration enabled: True
2025-05-12 08:42:28,333 - __main__ - INFO - GPU Memory: 35415MB free / 40384MB total
2025-05-12 08:42:28,333 - __main__ - INFO - HeavyDB integration available (Host: **************)
2025-05-12 08:42:28,549 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-12 08:42:28,549 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-12 08:42:28,550 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-12 08:42:28,552 - bt.backtester_stable.BTRUN.Util - INFO - Running necessary functions before starting BT...
2025-05-12 08:42:28,552 - bt.backtester_stable.BTRUN.config - DEBUG - Found fixture at /srv/samba/shared/LOTSIZE.csv
2025-05-12 08:42:28,552 - bt.backtester_stable.BTRUN.Util - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-12 08:42:28,558 - bt.backtester_stable.BTRUN.Util - INFO - Successfully populated LOT_SIZE: 50 entries.
2025-05-12 08:42:28,558 - bt.backtester_stable.BTRUN.config - WARNING - Fixture margin_symbol_info.json not found in any expected location
2025-05-12 08:42:28,558 - bt.backtester_stable.BTRUN.Util - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/margin_symbol_info.json (Broker: angel)
2025-05-12 08:42:28,558 - bt.backtester_stable.BTRUN.Util - INFO - Pre-BT functions complete.
2025-05-12 08:42:28,558 - __main__ - INFO - Using new Excel input format (PortfolioSetting & StrategySetting)
2025-05-12 08:42:28,558 - __main__ - INFO - Reading PortfolioSetting & StrategySetting from /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-12 08:42:28,660 - __main__ - DEBUG - Loaded 1 PortfolioSetting rows, 21 StrategySetting rows
2025-05-12 08:42:28,661 - __main__ - INFO - Queueing portfolio: NIF0DTE
2025-05-12 08:42:28,661 - __main__ - DEBUG - Building portfolio request for portfolio: NIF0DTE
2025-05-12 08:42:28,663 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 08:42:28,663 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 08:42:28,663 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 08:42:28,663 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 08:42:28,663 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 08:42:28,664 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 08:42:28,664 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 08:42:28,664 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 08:42:28,664 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 08:42:28,664 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 08:42:28,664 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 08:42:28,665 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 08:42:28,665 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 08:42:28,665 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 08:42:28,759 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-12 08:42:28,760 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY CALL at 2025-05-12 09:16:00.760037 via method 'ATM'
2025-05-12 08:42:28,760 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-12 08:42:28,760 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY PUT at 2025-05-12 09:16:00.760416 via method 'ATM'
2025-05-12 08:42:28,760 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-12 08:42:28,760 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY CALL at 2025-05-12 09:16:00.760730 via method 'ATM'
2025-05-12 08:42:28,760 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-12 08:42:28,761 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY PUT at 2025-05-12 09:16:00.761031 via method 'ATM'
2025-05-12 08:42:28,761 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 08:42:28,761 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 08:42:28,761 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 08:42:28,761 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 08:42:28,762 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 08:42:28,762 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 08:42:28,762 - __main__ - DEBUG - Built request for NIF0DTE with 1 strategies
2025-05-12 08:42:28,762 - __main__ - DEBUG - First strategy sample: {"id": "", "name": "RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL", "evaluator": "Tbs", "type": "STRATEGYTYPE.INTRADAY", "index": "NSEINDEX.NIFTY", "underlying": "CASH", "entry_time": 33360, "exit_time": 43200, "last_entry_time": 43200, "strike_selection_time": 33360, "multiplier": 1, "is_tick_bt": false, "legs": [{"id": "1", "option_type": "OPTIONTYPE.CALL", "side": "SIDE.SELL", "expiry_type": "EXPIRYTYPE.WEEKLY", "strike_selection": {"type": "BY_ATM_STRIKE", "value": 0.0}, "quantity": 50, "multipl
2025-05-12 08:42:28,762 - __main__ - INFO - Executing 1 portfolios sequentially
2025-05-12 08:42:28,763 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting BTRunPortfolio_GPU.py - Debugging enabled
2025-05-12 08:42:28,763 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Current working directory: /srv/samba/shared
2025-05-12 08:42:28,763 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-12 08:42:28,763 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Importing BTRUN modules...
2025-05-12 08:42:28,763 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Config module imported from: /srv/samba/shared/bt/backtester_stable/BTRUN/config.py
2025-05-12 08:42:28,763 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - HeavyDB settings - Host: **************, Database: heavydb
2025-05-12 08:42:28,763 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - GPU enabled: True
2025-05-12 08:42:28,763 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Successfully imported heavydb_helpers module
2025-05-12 08:42:28,763 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting single backtest for portfolio
2025-05-12 08:42:28,763 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Using HeavyDB for data access
2025-05-12 08:42:28,763 - bt.backtester_stable.BTRUN.runtime - INFO - Fetching trades from local HeavyDB for portfolio ''
2025-05-12 08:42:28,980 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-12 08:42:29,196 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-12 08:42:32,530 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 3.334s, returned 563 rows
2025-05-12 08:42:32,571 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Cached 563 distinct trade dates
2025-05-12 08:42:32,572 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating EntryWindow from dict, this path should ideally not be taken.
2025-05-12 08:42:32,572 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating ExitWindow from dict, this path should ideally not be taken.
2025-05-12 08:42:32,572 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating EntryWindow from dict, this path should ideally not be taken.
2025-05-12 08:42:32,572 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating ExitWindow from dict, this path should ideally not be taken.
2025-05-12 08:42:32,572 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating EntryWindow from dict, this path should ideally not be taken.
2025-05-12 08:42:32,572 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating ExitWindow from dict, this path should ideally not be taken.
2025-05-12 08:42:32,572 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating EntryWindow from dict, this path should ideally not be taken.
2025-05-12 08:42:32,573 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating ExitWindow from dict, this path should ideally not be taken.
2025-05-12 08:42:32,573 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Parsed 4 legs for strategy 'strategy': ['L1', 'L1', 'L1', 'L1']
2025-05-12 08:42:32,573 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Leg L1: risk=[]
2025-05-12 08:42:32,573 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Leg L1: risk=[]
2025-05-12 08:42:32,573 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Leg L1: risk=[]
2025-05-12 08:42:32,573 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Leg L1: risk=[]
2025-05-12 08:42:32,573 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Duplicate leg_ids found in strategy 'strategy': ['L1', 'L1', 'L1', 'L1']
2025-05-12 08:42:32,573 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - _build_union_sql: included leg_ids: ['L1']
2025-05-12 08:42:32,573 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - _build_union_sql: included leg_ids: ['L1']
2025-05-12 08:42:32,573 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Union Entry SQL (2025-04-01):
SELECT *, 'L1' AS leg_id FROM (SELECT * FROM nifty_option_chain en0
WHERE en0.trade_date = DATE '2025-04-01' AND en0.expiry_date = (SELECT MIN(expiry_date) FROM nifty_option_chain WHERE trade_date = date '2025-04-01') AND en0.call_strike_type = 'ATM' AND en0.trade_time >= TIME '09:15:00'
ORDER BY en0.trade_time ASC
LIMIT 1
OPTIONS (JIT_COMPILE=true)) sub0
2025-05-12 08:42:32,573 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Union Exit SQL (2025-04-01):
SELECT *, 'L1' AS leg_id FROM (SELECT * FROM nifty_option_chain ex0
WHERE ex0.trade_date = DATE '2025-04-01' AND ex0.expiry_date = (SELECT MIN(expiry_date) FROM nifty_option_chain WHERE trade_date = date '2025-04-01') AND ex0.call_strike_type = 'ATM' AND ex0.trade_time <= TIME '15:15:00'
ORDER BY ex0.trade_time DESC
LIMIT 1
OPTIONS (JIT_COMPILE=true)) sub0
2025-05-12 08:42:32,580 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "OPTIONS" at line 5, column 1.
Was expecting one of:
    "FETCH" ...
    "OFFSET" ...
    ")" ...
    "," ...
    
2025-05-12 08:42:32,580 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT *, 'L1' AS leg_id FROM (SELECT * FROM nifty_option_chain en0
WHERE en0.trade_date = DATE '2025-04-01' AND en0.expiry_date = (SELECT MIN(expiry_date) FROM nifty_option_chain WHERE trade_date = date '2025-04-01') AND en0.call_strike_type = 'ATM' AND en0.trade_time >= TIME '09:15:00'
ORDER BY en0.trade_time ASC
LIMIT 1
OPTIONS (JIT_COMPILE=true)) sub0
2025-05-12 08:42:32,584 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "OPTIONS" at line 5, column 1.\nWas expecting one of:\n    "FETCH" ...\n    "OFFSET" ...\n    ")" ...\n    "," ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 139, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "OPTIONS" at line 5, column 1.
Was expecting one of:
    "FETCH" ...
    "OFFSET" ...
    ")" ...
    "," ...
    

2025-05-12 08:42:32,587 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "OPTIONS" at line 5, column 1.
Was expecting one of:
    "FETCH" ...
    "OFFSET" ...
    ")" ...
    "," ...
    
2025-05-12 08:42:32,587 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT *, 'L1' AS leg_id FROM (SELECT * FROM nifty_option_chain ex0
WHERE ex0.trade_date = DATE '2025-04-01' AND ex0.expiry_date = (SELECT MIN(expiry_date) FROM nifty_option_chain WHERE trade_date = date '2025-04-01') AND ex0.call_strike_type = 'ATM' AND ex0.trade_time <= TIME '15:15:00'
ORDER BY ex0.trade_time DESC
LIMIT 1
OPTIONS (JIT_COMPILE=true)) sub0
2025-05-12 08:42:32,587 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "OPTIONS" at line 5, column 1.\nWas expecting one of:\n    "FETCH" ...\n    "OFFSET" ...\n    ")" ...\n    "," ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 139, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "OPTIONS" at line 5, column 1.
Was expecting one of:
    "FETCH" ...
    "OFFSET" ...
    ")" ...
    "," ...
    

2025-05-12 08:42:32,588 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating EntryWindow from dict, this path should ideally not be taken.
2025-05-12 08:42:32,588 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating ExitWindow from dict, this path should ideally not be taken.
2025-05-12 08:42:32,588 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating EntryWindow from dict, this path should ideally not be taken.
2025-05-12 08:42:32,588 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating ExitWindow from dict, this path should ideally not be taken.
2025-05-12 08:42:32,588 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating EntryWindow from dict, this path should ideally not be taken.
2025-05-12 08:42:32,589 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating ExitWindow from dict, this path should ideally not be taken.
2025-05-12 08:42:32,589 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating EntryWindow from dict, this path should ideally not be taken.
2025-05-12 08:42:32,589 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating ExitWindow from dict, this path should ideally not be taken.
2025-05-12 08:42:32,589 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Parsed 4 legs for strategy 'strategy': ['L1', 'L1', 'L1', 'L1']
2025-05-12 08:42:32,589 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Leg L1: risk=[]
2025-05-12 08:42:32,589 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Leg L1: risk=[]
2025-05-12 08:42:32,589 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Leg L1: risk=[]
2025-05-12 08:42:32,589 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Leg L1: risk=[]
2025-05-12 08:42:32,589 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Duplicate leg_ids found in strategy 'strategy': ['L1', 'L1', 'L1', 'L1']
2025-05-12 08:42:32,589 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - _build_union_sql: included leg_ids: ['L1']
2025-05-12 08:42:32,589 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - _build_union_sql: included leg_ids: ['L1']
2025-05-12 08:42:32,589 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Union Entry SQL (2025-04-02):
SELECT *, 'L1' AS leg_id FROM (SELECT * FROM nifty_option_chain en0
WHERE en0.trade_date = DATE '2025-04-02' AND en0.expiry_date = (SELECT MIN(expiry_date) FROM nifty_option_chain WHERE trade_date = date '2025-04-02') AND en0.call_strike_type = 'ATM' AND en0.trade_time >= TIME '09:15:00'
ORDER BY en0.trade_time ASC
LIMIT 1
OPTIONS (JIT_COMPILE=true)) sub0
2025-05-12 08:42:32,589 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Union Exit SQL (2025-04-02):
SELECT *, 'L1' AS leg_id FROM (SELECT * FROM nifty_option_chain ex0
WHERE ex0.trade_date = DATE '2025-04-02' AND ex0.expiry_date = (SELECT MIN(expiry_date) FROM nifty_option_chain WHERE trade_date = date '2025-04-02') AND ex0.call_strike_type = 'ATM' AND ex0.trade_time <= TIME '15:15:00'
ORDER BY ex0.trade_time DESC
LIMIT 1
OPTIONS (JIT_COMPILE=true)) sub0
2025-05-12 08:42:32,592 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "OPTIONS" at line 5, column 1.
Was expecting one of:
    "FETCH" ...
    "OFFSET" ...
    ")" ...
    "," ...
    
2025-05-12 08:42:32,592 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT *, 'L1' AS leg_id FROM (SELECT * FROM nifty_option_chain en0
WHERE en0.trade_date = DATE '2025-04-02' AND en0.expiry_date = (SELECT MIN(expiry_date) FROM nifty_option_chain WHERE trade_date = date '2025-04-02') AND en0.call_strike_type = 'ATM' AND en0.trade_time >= TIME '09:15:00'
ORDER BY en0.trade_time ASC
LIMIT 1
OPTIONS (JIT_COMPILE=true)) sub0
2025-05-12 08:42:32,592 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "OPTIONS" at line 5, column 1.\nWas expecting one of:\n    "FETCH" ...\n    "OFFSET" ...\n    ")" ...\n    "," ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 139, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "OPTIONS" at line 5, column 1.
Was expecting one of:
    "FETCH" ...
    "OFFSET" ...
    ")" ...
    "," ...
    

2025-05-12 08:42:32,595 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "OPTIONS" at line 5, column 1.
Was expecting one of:
    "FETCH" ...
    "OFFSET" ...
    ")" ...
    "," ...
    
2025-05-12 08:42:32,595 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT *, 'L1' AS leg_id FROM (SELECT * FROM nifty_option_chain ex0
WHERE ex0.trade_date = DATE '2025-04-02' AND ex0.expiry_date = (SELECT MIN(expiry_date) FROM nifty_option_chain WHERE trade_date = date '2025-04-02') AND ex0.call_strike_type = 'ATM' AND ex0.trade_time <= TIME '15:15:00'
ORDER BY ex0.trade_time DESC
LIMIT 1
OPTIONS (JIT_COMPILE=true)) sub0
2025-05-12 08:42:32,595 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "OPTIONS" at line 5, column 1.\nWas expecting one of:\n    "FETCH" ...\n    "OFFSET" ...\n    ")" ...\n    "," ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 139, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "OPTIONS" at line 5, column 1.
Was expecting one of:
    "FETCH" ...
    "OFFSET" ...
    ")" ...
    "," ...
    

2025-05-12 08:42:32,596 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Generated 0 trade records via HeavyDB snapshot across 2 trading days
2025-05-12 08:42:32,596 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Reusable HeavyDB connection closed in get_trades_for_portfolio.
2025-05-12 08:42:32,596 - bt.backtester_stable.BTRUN.builders - INFO - Parsing backtest response...
2025-05-12 08:42:32,596 - bt.backtester_stable.BTRUN.builders - WARNING - No trades data in backtest response or invalid format.
2025-05-12 08:42:32,597 - bt.backtester_stable.BTRUN.runtime - WARNING - No trades found in backtest response
2025-05-12 08:42:32,597 - bt.backtester_stable.BTRUN.runtime - INFO - Saving Excel output to backtest_output/NIF0DTE_12052025_084228.xlsx
2025-05-12 08:42:32,597 - bt.backtester_stable.BTRUN.runtime - WARNING - Empty metrics DataFrame - not saving Excel output
2025-05-12 08:42:32,598 - bt.backtester_stable.BTRUN.runtime - INFO - Saving JSON output to backtest_output/NIF0DTE_12052025_084228.json
2025-05-12 08:42:32,598 - bt.backtester_stable.BTRUN.runtime - WARNING - Empty metrics DataFrame - not saving JSON output
2025-05-12 08:42:32,598 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Backtest completed in 3.83 seconds
2025-05-12 08:42:32,598 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Portfolio backtest completed successfully with 0 trades
2025-05-12 08:42:32,598 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Saved json file to backtest_output/NIF0DTE_12052025_084228.json
2025-05-12 08:42:32,598 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Saved excel file to backtest_output/NIF0DTE_12052025_084228.xlsx
