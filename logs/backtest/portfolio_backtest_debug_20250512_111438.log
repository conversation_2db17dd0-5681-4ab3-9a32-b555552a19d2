2025-05-12 11:14:38,679 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-12 11:14:38,679 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-12 11:14:38,680 - __main__ - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-12 11:14:38,680 - __main__ - DEBUG - HeavyDB host: **************, DB: heavydb
2025-05-12 11:14:38,680 - __main__ - DEBUG - GPU enabled: True
2025-05-12 11:14:38,681 - __main__ - INFO - GPU acceleration enabled: True
2025-05-12 11:14:38,750 - __main__ - INFO - GPU Memory: 35415MB free / 40384MB total
2025-05-12 11:14:38,750 - __main__ - INFO - HeavyDB integration available (Host: **************)
2025-05-12 11:14:38,964 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-12 11:14:38,964 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-12 11:14:38,964 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-12 11:14:38,967 - bt.backtester_stable.BTRUN.Util - INFO - Running necessary functions before starting BT...
2025-05-12 11:14:38,967 - bt.backtester_stable.BTRUN.config - DEBUG - Found fixture at /srv/samba/shared/LOTSIZE.csv
2025-05-12 11:14:38,967 - bt.backtester_stable.BTRUN.Util - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-12 11:14:38,972 - bt.backtester_stable.BTRUN.Util - INFO - Successfully populated LOT_SIZE: 50 entries.
2025-05-12 11:14:38,972 - bt.backtester_stable.BTRUN.config - WARNING - Fixture margin_symbol_info.json not found in any expected location
2025-05-12 11:14:38,972 - bt.backtester_stable.BTRUN.Util - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/margin_symbol_info.json (Broker: angel)
2025-05-12 11:14:38,972 - bt.backtester_stable.BTRUN.Util - INFO - Pre-BT functions complete.
2025-05-12 11:14:38,972 - __main__ - INFO - Using new Excel input format (PortfolioSetting & StrategySetting)
2025-05-12 11:14:38,972 - __main__ - INFO - Reading PortfolioSetting & StrategySetting from /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-12 11:14:39,069 - __main__ - DEBUG - Loaded 1 PortfolioSetting rows, 21 StrategySetting rows
2025-05-12 11:14:39,069 - __main__ - INFO - Queueing portfolio: NIF0DTE
2025-05-12 11:14:39,069 - __main__ - DEBUG - Building portfolio request for portfolio: NIF0DTE
2025-05-12 11:14:39,071 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:14:39,071 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:14:39,071 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:14:39,071 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:14:39,071 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:14:39,071 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:14:39,072 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:14:39,072 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:14:39,072 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:14:39,072 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:14:39,072 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:14:39,072 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:14:39,072 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:14:39,073 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:14:39,165 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-12 11:14:39,165 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY CALL at 2025-05-12 09:16:00.165513 via method 'ATM'
2025-05-12 11:14:39,165 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-12 11:14:39,165 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY PUT at 2025-05-12 09:16:00.165850 via method 'ATM'
2025-05-12 11:14:39,166 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-12 11:14:39,166 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY CALL at 2025-05-12 09:16:00.166126 via method 'ATM'
2025-05-12 11:14:39,166 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-12 11:14:39,166 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY PUT at 2025-05-12 09:16:00.166405 via method 'ATM'
2025-05-12 11:14:39,166 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:14:39,166 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:14:39,166 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:14:39,167 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:14:39,167 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:14:39,167 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:14:39,174 - bt.backtester_stable.BTRUN.util_adapter - ERROR - Adapter failed for leg 1 in strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL: EntryWindow.__init__() got an unexpected keyword argument 'start'
Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/util_adapter.py", line 150, in util_json_to_portfolio_model
    ew = EntryWindow(start=entry_time_str)
TypeError: EntryWindow.__init__() got an unexpected keyword argument 'start'
2025-05-12 11:14:39,174 - bt.backtester_stable.BTRUN.util_adapter - ERROR - Adapter failed for leg 2 in strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL: EntryWindow.__init__() got an unexpected keyword argument 'start'
Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/util_adapter.py", line 150, in util_json_to_portfolio_model
    ew = EntryWindow(start=entry_time_str)
TypeError: EntryWindow.__init__() got an unexpected keyword argument 'start'
2025-05-12 11:14:39,175 - bt.backtester_stable.BTRUN.util_adapter - ERROR - Adapter failed for leg 3 in strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL: EntryWindow.__init__() got an unexpected keyword argument 'start'
Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/util_adapter.py", line 150, in util_json_to_portfolio_model
    ew = EntryWindow(start=entry_time_str)
TypeError: EntryWindow.__init__() got an unexpected keyword argument 'start'
2025-05-12 11:14:39,175 - bt.backtester_stable.BTRUN.util_adapter - ERROR - Adapter failed for leg 4 in strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL: EntryWindow.__init__() got an unexpected keyword argument 'start'
Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/util_adapter.py", line 150, in util_json_to_portfolio_model
    ew = EntryWindow(start=entry_time_str)
TypeError: EntryWindow.__init__() got an unexpected keyword argument 'start'
2025-05-12 11:14:39,175 - bt.backtester_stable.BTRUN.util_adapter - WARNING - Strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL has no legs after adapter translation – skipping
2025-05-12 11:14:39,175 - __main__ - ERROR - util_adapter translation failed: No valid strategies parsed by util_adapter
Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py", line 242, in _row_build_request
    pf_model_dict = util_json_to_portfolio_model(
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/util_adapter.py", line 188, in util_json_to_portfolio_model
    raise ValueError("No valid strategies parsed by util_adapter")
ValueError: No valid strategies parsed by util_adapter
2025-05-12 11:14:39,176 - __main__ - DEBUG - Built request for NIF0DTE with 1 strategies
2025-05-12 11:14:39,176 - __main__ - DEBUG - First strategy sample: {"id": "", "name": "RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL", "evaluator": "Tbs", "type": "STRATEGYTYPE.INTRADAY", "index": "NSEINDEX.NIFTY", "underlying": "CASH", "entry_time": 33360, "exit_time": 43200, "last_entry_time": 43200, "strike_selection_time": 33360, "multiplier": 1, "is_tick_bt": false, "legs": [{"id": "1", "option_type": "OPTIONTYPE.CALL", "side": "SIDE.SELL", "expiry_type": "EXPIRYTYPE.WEEKLY", "strike_selection": {"type": "BY_ATM_STRIKE", "value": 0.0}, "quantity": 50, "multipl
2025-05-12 11:14:39,176 - __main__ - INFO - Executing 1 portfolios sequentially
2025-05-12 11:14:39,176 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting BTRunPortfolio_GPU.py - Debugging enabled
2025-05-12 11:14:39,176 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Current working directory: /srv/samba/shared
2025-05-12 11:14:39,177 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-12 11:14:39,177 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Importing BTRUN modules...
2025-05-12 11:14:39,177 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Config module imported from: /srv/samba/shared/bt/backtester_stable/BTRUN/config.py
2025-05-12 11:14:39,177 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - HeavyDB settings - Host: **************, Database: heavydb
2025-05-12 11:14:39,177 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - GPU enabled: True
2025-05-12 11:14:39,177 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Successfully imported heavydb_helpers module
2025-05-12 11:14:39,177 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting single backtest for portfolio
2025-05-12 11:14:39,177 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Using HeavyDB for data access
2025-05-12 11:14:39,177 - bt.backtester_stable.BTRUN.runtime - INFO - Fetching trades from local HeavyDB for portfolio ''
2025-05-12 11:14:39,391 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-12 11:14:39,607 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-12 11:14:42,884 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 3.276s, returned 563 rows
2025-05-12 11:14:42,925 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Cached 563 distinct trade dates
2025-05-12 11:14:42,925 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating EntryWindow from dict, this path should ideally not be taken.
2025-05-12 11:14:42,926 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating ExitWindow from dict, this path should ideally not be taken.
2025-05-12 11:14:42,926 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating EntryWindow from dict, this path should ideally not be taken.
2025-05-12 11:14:42,926 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating ExitWindow from dict, this path should ideally not be taken.
2025-05-12 11:14:42,926 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating EntryWindow from dict, this path should ideally not be taken.
2025-05-12 11:14:42,926 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating ExitWindow from dict, this path should ideally not be taken.
2025-05-12 11:14:42,926 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating EntryWindow from dict, this path should ideally not be taken.
2025-05-12 11:14:42,926 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating ExitWindow from dict, this path should ideally not be taken.
2025-05-12 11:14:42,926 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Parsed 4 legs for strategy 'strategy': ['L1', 'L2', 'L3', 'L4']
2025-05-12 11:14:42,926 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Leg L1: risk=[]
2025-05-12 11:14:42,926 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Leg L2: risk=[]
2025-05-12 11:14:42,926 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Leg L3: risk=[]
2025-05-12 11:14:42,926 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Leg L4: risk=[]
2025-05-12 11:14:42,926 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - _build_union_sql: included leg_ids: ['L4', 'L2', 'L1', 'L3']
2025-05-12 11:14:42,926 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - _build_union_sql: included leg_ids: ['L4', 'L2', 'L1', 'L3']
2025-05-12 11:14:42,927 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Union Entry SQL (2025-04-01):
SELECT *, 'L1' AS leg_id FROM (SELECT * FROM nifty_option_chain en0
WHERE en0.trade_date = DATE '2025-04-01' AND en0.expiry_date = (SELECT MIN(expiry_date) FROM nifty_option_chain WHERE trade_date = date '2025-04-01') AND en0.call_strike_type = 'ATM' AND en0.trade_time >= TIME '09:16:00'
ORDER BY en0.trade_time ASC
LIMIT 1) sub0 UNION ALL 
SELECT *, 'L2' AS leg_id FROM (SELECT * FROM nifty_option_chain en1
WHERE en1.trade_date = DATE '2025-04-01' AND en1.expiry_date = (SELECT MIN(expiry_date) FROM nifty_option_chain WHERE trade_date = date '2025-04-01') AND en1.call_strike_type = 'ATM' AND en1.trade_time >= TIME '09:16:00'
ORDER BY en1.trade_time ASC
LIMIT 1) sub1 UNION ALL 
SELECT *, 'L3' AS leg_id FROM (SELECT * FROM nifty_option_chain en2
WHERE en2.trade_date = DATE '2025-04-01' AND en2.expiry_date = (SELECT MIN(expiry_date) FROM nifty_option_chain WHERE trade_date = date '2025-04-01') AND en2.call_strike_type = 'ATM' AND en2.trade_time >= TIME '09:16:00'
ORDER BY en2.trade_time ASC
LIMIT 1) sub2 UNION ALL 
SELECT *, 'L4' AS leg_id FROM (SELECT * FROM nifty_option_chain en3
WHERE en3.trade_date = DATE '2025-04-01' AND en3.expiry_date = (SELECT MIN(expiry_date) FROM nifty_option_chain WHERE trade_date = date '2025-04-01') AND en3.call_strike_type = 'ATM' AND en3.trade_time >= TIME '09:16:00'
ORDER BY en3.trade_time ASC
LIMIT 1) sub3
2025-05-12 11:14:42,927 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Union Exit SQL (2025-04-01):
SELECT *, 'L1' AS leg_id FROM (SELECT * FROM nifty_option_chain ex0
WHERE ex0.trade_date = DATE '2025-04-01' AND ex0.expiry_date = (SELECT MIN(expiry_date) FROM nifty_option_chain WHERE trade_date = date '2025-04-01') AND ex0.call_strike_type = 'ATM' AND ex0.trade_time <= TIME '12:00:00'
ORDER BY ex0.trade_time DESC
LIMIT 1) sub0 UNION ALL 
SELECT *, 'L2' AS leg_id FROM (SELECT * FROM nifty_option_chain ex1
WHERE ex1.trade_date = DATE '2025-04-01' AND ex1.expiry_date = (SELECT MIN(expiry_date) FROM nifty_option_chain WHERE trade_date = date '2025-04-01') AND ex1.call_strike_type = 'ATM' AND ex1.trade_time <= TIME '12:00:00'
ORDER BY ex1.trade_time DESC
LIMIT 1) sub1 UNION ALL 
SELECT *, 'L3' AS leg_id FROM (SELECT * FROM nifty_option_chain ex2
WHERE ex2.trade_date = DATE '2025-04-01' AND ex2.expiry_date = (SELECT MIN(expiry_date) FROM nifty_option_chain WHERE trade_date = date '2025-04-01') AND ex2.call_strike_type = 'ATM' AND ex2.trade_time <= TIME '12:00:00'
ORDER BY ex2.trade_time DESC
LIMIT 1) sub2 UNION ALL 
SELECT *, 'L4' AS leg_id FROM (SELECT * FROM nifty_option_chain ex3
WHERE ex3.trade_date = DATE '2025-04-01' AND ex3.expiry_date = (SELECT MIN(expiry_date) FROM nifty_option_chain WHERE trade_date = date '2025-04-01') AND ex3.call_strike_type = 'ATM' AND ex3.trade_time <= TIME '12:00:00'
ORDER BY ex3.trade_time DESC
LIMIT 1) sub3
2025-05-12 11:15:48,741 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 65.814s, returned 4 rows
2025-05-12 11:16:55,430 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 66.689s, returned 4 rows
2025-05-12 11:16:55,432 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating EntryWindow from dict, this path should ideally not be taken.
2025-05-12 11:16:55,432 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating ExitWindow from dict, this path should ideally not be taken.
2025-05-12 11:16:55,433 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating EntryWindow from dict, this path should ideally not be taken.
2025-05-12 11:16:55,433 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating ExitWindow from dict, this path should ideally not be taken.
2025-05-12 11:16:55,433 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating EntryWindow from dict, this path should ideally not be taken.
2025-05-12 11:16:55,433 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating ExitWindow from dict, this path should ideally not be taken.
2025-05-12 11:16:55,433 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating EntryWindow from dict, this path should ideally not be taken.
2025-05-12 11:16:55,433 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - Re-creating ExitWindow from dict, this path should ideally not be taken.
2025-05-12 11:16:55,433 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Parsed 4 legs for strategy 'strategy': ['L1', 'L2', 'L3', 'L4']
2025-05-12 11:16:55,433 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Leg L1: risk=[]
2025-05-12 11:16:55,433 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Leg L2: risk=[]
2025-05-12 11:16:55,433 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Leg L3: risk=[]
2025-05-12 11:16:55,433 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Leg L4: risk=[]
2025-05-12 11:16:55,434 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - _build_union_sql: included leg_ids: ['L4', 'L2', 'L1', 'L3']
2025-05-12 11:16:55,434 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - _build_union_sql: included leg_ids: ['L4', 'L2', 'L1', 'L3']
2025-05-12 11:16:55,434 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Union Entry SQL (2025-04-02):
SELECT *, 'L1' AS leg_id FROM (SELECT * FROM nifty_option_chain en0
WHERE en0.trade_date = DATE '2025-04-02' AND en0.expiry_date = (SELECT MIN(expiry_date) FROM nifty_option_chain WHERE trade_date = date '2025-04-02') AND en0.call_strike_type = 'ATM' AND en0.trade_time >= TIME '09:16:00'
ORDER BY en0.trade_time ASC
LIMIT 1) sub0 UNION ALL 
SELECT *, 'L2' AS leg_id FROM (SELECT * FROM nifty_option_chain en1
WHERE en1.trade_date = DATE '2025-04-02' AND en1.expiry_date = (SELECT MIN(expiry_date) FROM nifty_option_chain WHERE trade_date = date '2025-04-02') AND en1.call_strike_type = 'ATM' AND en1.trade_time >= TIME '09:16:00'
ORDER BY en1.trade_time ASC
LIMIT 1) sub1 UNION ALL 
SELECT *, 'L3' AS leg_id FROM (SELECT * FROM nifty_option_chain en2
WHERE en2.trade_date = DATE '2025-04-02' AND en2.expiry_date = (SELECT MIN(expiry_date) FROM nifty_option_chain WHERE trade_date = date '2025-04-02') AND en2.call_strike_type = 'ATM' AND en2.trade_time >= TIME '09:16:00'
ORDER BY en2.trade_time ASC
LIMIT 1) sub2 UNION ALL 
SELECT *, 'L4' AS leg_id FROM (SELECT * FROM nifty_option_chain en3
WHERE en3.trade_date = DATE '2025-04-02' AND en3.expiry_date = (SELECT MIN(expiry_date) FROM nifty_option_chain WHERE trade_date = date '2025-04-02') AND en3.call_strike_type = 'ATM' AND en3.trade_time >= TIME '09:16:00'
ORDER BY en3.trade_time ASC
LIMIT 1) sub3
2025-05-12 11:16:55,434 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Union Exit SQL (2025-04-02):
SELECT *, 'L1' AS leg_id FROM (SELECT * FROM nifty_option_chain ex0
WHERE ex0.trade_date = DATE '2025-04-02' AND ex0.expiry_date = (SELECT MIN(expiry_date) FROM nifty_option_chain WHERE trade_date = date '2025-04-02') AND ex0.call_strike_type = 'ATM' AND ex0.trade_time <= TIME '12:00:00'
ORDER BY ex0.trade_time DESC
LIMIT 1) sub0 UNION ALL 
SELECT *, 'L2' AS leg_id FROM (SELECT * FROM nifty_option_chain ex1
WHERE ex1.trade_date = DATE '2025-04-02' AND ex1.expiry_date = (SELECT MIN(expiry_date) FROM nifty_option_chain WHERE trade_date = date '2025-04-02') AND ex1.call_strike_type = 'ATM' AND ex1.trade_time <= TIME '12:00:00'
ORDER BY ex1.trade_time DESC
LIMIT 1) sub1 UNION ALL 
SELECT *, 'L3' AS leg_id FROM (SELECT * FROM nifty_option_chain ex2
WHERE ex2.trade_date = DATE '2025-04-02' AND ex2.expiry_date = (SELECT MIN(expiry_date) FROM nifty_option_chain WHERE trade_date = date '2025-04-02') AND ex2.call_strike_type = 'ATM' AND ex2.trade_time <= TIME '12:00:00'
ORDER BY ex2.trade_time DESC
LIMIT 1) sub2 UNION ALL 
SELECT *, 'L4' AS leg_id FROM (SELECT * FROM nifty_option_chain ex3
WHERE ex3.trade_date = DATE '2025-04-02' AND ex3.expiry_date = (SELECT MIN(expiry_date) FROM nifty_option_chain WHERE trade_date = date '2025-04-02') AND ex3.call_strike_type = 'ATM' AND ex3.trade_time <= TIME '12:00:00'
ORDER BY ex3.trade_time DESC
LIMIT 1) sub3
2025-05-12 11:18:00,428 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 64.994s, returned 4 rows
2025-05-12 11:19:05,534 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 65.106s, returned 4 rows
2025-05-12 11:19:05,537 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Generated 8 trade records via HeavyDB snapshot across 2 trading days
2025-05-12 11:19:05,538 - bt.backtester_stable.BTRUN.heavydb_helpers - DEBUG - Reusable HeavyDB connection closed in get_trades_for_portfolio.
2025-05-12 11:19:05,538 - bt.backtester_stable.BTRUN.builders - INFO - Parsing backtest response...
2025-05-12 11:19:06,592 - bt.backtester_stable.BTRUN.builders - WARNING - Margin calculation in builders.parse_backtest_response is a placeholder.
2025-05-12 11:19:06,592 - bt.backtester_stable.BTRUN.builders - DEBUG - Converting tick PnL to daywise DataFrame.
2025-05-12 11:19:06,594 - bt.backtester_stable.BTRUN.builders - INFO - Backtest response parsed.
2025-05-12 11:19:06,632 - bt.backtester_stable.BTRUN.gpu_helpers - WARNING - Failed to convert to cuDF: Could not convert 'Total' with type str: tried to convert to int64. Falling back to pandas.
2025-05-12 11:19:06,645 - bt.backtester_stable.BTRUN.gpu_helpers - WARNING - Failed to convert to cuDF: Could not convert 'Total' with type str: tried to convert to int64. Falling back to pandas.
2025-05-12 11:19:06,646 - bt.backtester_stable.BTRUN.runtime - INFO - Saving Excel output to backtest_output_adapter4/NIF0DTE_12052025_111439.xlsx
2025-05-12 11:19:06,646 - bt.backtester_stable.BTRUN.io - INFO - 2025-05-12 11:19:06.646334, Started writing stats to excel file: backtest_output_adapter4/NIF0DTE_12052025_111439.xlsx
2025-05-12 11:19:06,646 - bt.backtester_stable.BTRUN.gpu_helpers - DEBUG - Temporarily switching to CPU-only mode.
2025-05-12 11:19:06,667 - bt.backtester_stable.BTRUN.io - WARNING - Skipping rename of column 'exit_price' to 'Exit at' as it would duplicate an existing column name.
2025-05-12 11:19:06,692 - bt.backtester_stable.BTRUN.gpu_helpers - DEBUG - Restored GPU_ENABLED to: True
2025-05-12 11:19:06,692 - bt.backtester_stable.BTRUN.io - INFO - 2025-05-12 11:19:06.692176, Excel file prepared successfully, Time taken: 0.05s
2025-05-12 11:19:06,692 - bt.backtester_stable.BTRUN.runtime - INFO - Saving JSON output to backtest_output_adapter4/NIF0DTE_12052025_111439.json
2025-05-12 11:19:06,720 - bt.backtester_stable.BTRUN.io - INFO - Successfully wrote JSON output to backtest_output_adapter4/NIF0DTE_12052025_111439.json
2025-05-12 11:19:06,721 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Backtest completed in 267.54 seconds
2025-05-12 11:19:06,721 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Portfolio backtest completed successfully with 8 trades
2025-05-12 11:19:06,721 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Saved json file to backtest_output_adapter4/NIF0DTE_12052025_111439.json
2025-05-12 11:19:06,721 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Saved excel file to backtest_output_adapter4/NIF0DTE_12052025_111439.xlsx
2025-05-12 11:19:06,721 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Backtest Summary: {
  "Total PnL": -22600.0,
  "Win Rate": 0.25,
  "Sharpe Ratio": 0.0979,
  "CAGR": -100.0,
  "Max Drawdown": -415475.0
}
