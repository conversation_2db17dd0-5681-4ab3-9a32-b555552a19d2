2025-05-26 12:01:06,573 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-26 12:01:06,574 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-26 12:01:06,574 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/../models', '/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages', '/srv/samba/shared']
2025-05-26 12:01:06,574 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-26 12:01:06,574 - __main__ - DEBUG - GPU enabled: False
2025-05-26 12:01:06,576 - __main__ - INFO - GPU acceleration enabled: False
2025-05-26 12:01:06,576 - __main__ - INFO - HeavyDB integration available (Host: 127.0.0.1)
2025-05-26 12:01:06,800 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-26 12:01:06,800 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-26 12:01:06,800 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-26 12:01:06,800 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-26 12:01:06,800 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-26 12:01:06,801 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-26 12:01:06,807 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-26 12:01:06,807 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-26 12:01:06,807 - backtester_stable.BTRUN.core.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-26 12:01:06,807 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-26 12:01:06,808 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-26 12:01:06,808 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-26 12:01:06,808 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-26 12:01:06,808 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-26 12:01:06,808 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-26 12:01:06,808 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-26 12:01:06,808 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-26 12:01:06,808 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-26 12:01:06,930 - __main__ - INFO - Successfully parsed 1 portfolio(s) using modern parser.
2025-05-26 12:01:06,931 - __main__ - INFO - Queueing portfolio: NIF0DTE (from modern_portfolio_parser)
2025-05-26 12:01:06,931 - __main__ - INFO - Executing 1 portfolios sequentially (multiprocessing temporarily disabled for debugging)
2025-05-26 12:01:06,931 - __main__ - INFO - Processing payload 1/1: NIF0DTE
2025-05-26 12:01:06,931 - backtester_stable.BTRUN.core.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'NIF0DTE'
2025-05-26 12:01:07,147 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-26 12:01:07,175 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.028s, returned 563 rows. Query: SELECT SUBSTRING('SELECT DISTINCT trade_date FROM nifty_option_chain', 1, 200) ...
2025-05-26 12:01:07,221 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - Cached 563 distinct trade dates
2025-05-26 12:01:07,222 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] portfolio_model_dict keys: ['portfolio_name', 'start_date', 'end_date', 'slippage_percent', 'margin_multiplier', 'is_tick_bt', 'strategies', 'extra_params']
2025-05-26 12:01:07,222 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 1 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 12:01:07,222 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-26 12:01:07,222 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-26 12:01:07,259 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.036s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-26 12:01:07,294 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.035s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-26 12:01:07,294 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Query results: entry_df size=1, exit_df size=1
2025-05-26 12:01:07,295 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Got entry and exit data, proceeding...
2025-05-26 12:01:07,295 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
                        trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523450CE
ce_open                          102.85
ce_high                          106.65
ce_low                            97.85
ce_close                         100.75
ce_volume                       1015050
ce_oi                            978075
ce_coi                                0
ce_iv                             13.44
ce_delta                           0.48
ce_gamma                         0.0015
ce_theta                         -23.27
ce_vega                            7.95
ce_rho                          81.3115
pe_symbol           NIFTY03APR2523450PE
pe_open                           118.2
pe_high                           122.3
pe_low                            112.2
pe_close                          115.7
pe_volume                        877125
pe_oi                           1618950
pe_coi                                0
pe_iv                             13.74
pe_delta                          -0.52
pe_gamma                         0.0015
pe_theta                          -17.3
pe_vega                            7.95
pe_rho                         -88.3744
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-26 12:01:07,296 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23200.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                           123.6
ce_high                           124.3
ce_low                           119.25
ce_close                          124.2
ce_volume                        326775
ce_oi                           4374450
ce_coi                           193725
ce_iv                             14.99
ce_delta                           0.52
ce_gamma                         0.0013
ce_theta                         -25.57
ce_vega                            7.87
ce_rho                          86.2799
pe_symbol           NIFTY03APR2523200PE
pe_open                           100.2
pe_high                           105.2
pe_low                            99.55
pe_close                         100.75
pe_volume                        526875
pe_oi                           5030025
pe_coi                           101175
pe_iv                             13.55
pe_delta                          -0.48
pe_gamma                         0.0015
pe_theta                         -17.08
pe_vega                            7.87
pe_rho                         -81.3242
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-26 12:01:07,296 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-26 12:01:07,296 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-26 12:01:07,296 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-26 12:01:07,296 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Entering risk rules block
2025-05-26 12:01:07,297 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-26 12:01:07,297 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140143174831008
2025-05-26 12:01:07,297 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140143174831008, OptionType.PUT ID: 140143174831120
2025-05-26 12:01:07,297 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-26 12:01:07,297 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-26 12:01:07,297 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-26 12:01:07,299 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-26 12:01:07,603 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.304s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-26 12:01:07,604 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-26 12:01:07,605 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-26 12:01:07,666 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-26 12:01:07,669 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 1 - Tick data for specific strike 23450.0 is EMPTY after filtering windowed data.
2025-05-26 12:01:07,669 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Starting risk rules loop, has 3 rules
2025-05-26 12:01:07,669 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 1 of 3
2025-05-26 12:01:07,670 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-26 12:01:07,673 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - evaluate_risk_rule returned: triggered=True
2025-05-26 12:01:07,675 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Breaking out of risk rules loop due to triggered rule
2025-05-26 12:01:07,676 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-26 12:01:07,676 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Reached trade building section (outside all conditionals)
2025-05-26 12:01:07,676 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 1
2025-05-26 12:01:07,676 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 1, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 12:01:07,676 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23420.45, 'atm_strike': 23450.0, 'strike': 23450.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523450CE', 'ce_open': 102.85, 'ce_high': 106.65, 'ce_low': 97.85, 'ce_close': 100.75, 'ce_volume': 1015050, 'ce_oi': 978075, 'ce_coi': 0, 'ce_iv': 13.44, 'ce_delta': 0.48, 'ce_gamma': 0.0015, 'ce_theta': -23.27, 'ce_vega': 7.95, 'ce_rho': 81.3115, 'pe_symbol': 'NIFTY03APR2523450PE', 'pe_open': 118.2, 'pe_high': 122.3, 'pe_low': 112.2, 'pe_close': 115.7, 'pe_volume': 877125, 'pe_oi': 1618950, 'pe_coi': 0, 'pe_iv': 13.74, 'pe_delta': -0.52, 'pe_gamma': 0.0015, 'pe_theta': -17.3, 'pe_vega': 7.95, 'pe_rho': -88.3744, 'future_open': 23534.5, 'future_high': 23543.95, 'future_low': 23527.0, 'future_close': 23536.75, 'future_volume': 86475, 'future_oi': 12605100, 'future_coi': 0}
2025-05-26 12:01:07,676 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23195.3, 'atm_strike': 23200.0, 'strike': 23200.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523200CE', 'ce_open': 123.6, 'ce_high': 124.3, 'ce_low': 119.25, 'ce_close': 124.2, 'ce_volume': 326775, 'ce_oi': 4374450, 'ce_coi': 193725, 'ce_iv': 14.99, 'ce_delta': 0.52, 'ce_gamma': 0.0013, 'ce_theta': -25.57, 'ce_vega': 7.87, 'ce_rho': 86.2799, 'pe_symbol': 'NIFTY03APR2523200PE', 'pe_open': 100.2, 'pe_high': 105.2, 'pe_low': 99.55, 'pe_close': 100.75, 'pe_volume': 526875, 'pe_oi': 5030025, 'pe_coi': 101175, 'pe_iv': 13.55, 'pe_delta': -0.48, 'pe_gamma': 0.0015, 'pe_theta': -17.08, 'pe_vega': 7.87, 'pe_rho': -81.3242, 'future_open': 23347.0, 'future_high': 23348.85, 'future_low': 23339.0, 'future_close': 23348.1, 'future_volume': 19050, 'future_oi': 13928550, 'future_coi': -28275, 'close': 1450.2, 'datetime': Timestamp('2025-04-01 12:00:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-26 12:01:07,676 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=CALL, Transaction=SELL, Lots=1
2025-05-26 12:01:07,676 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-26 12:01:07,676 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-26 12:01:07,677 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-26 12:01:07,677 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -1172.5000000000002, PnL (Slippage): -1172.5000000000002, Net PnL: -1172.5000000000002
2025-05-26 12:01:07,677 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-26 12:01:07,677 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '1', 'entry_date': '2025-04-01', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2025-04-01', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23450, 'instrument_type': 'CE', 'side': 'SELL', 'filled_quantity': 50.0, 'entry_price': 100.75, 'exit_price': 124.2, 'points': -23.450000000000003, 'pointsAfterSlippage': -23.450000000000003, 'pnl': -1172.5000000000002, 'pnlAfterSlippage': -1172.5000000000002, 'expenses': 0.0, 'netPnlAfterExpenses': -1172.5000000000002, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23420.45, 'index_exit_price': 23195.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-01 09:16:00', 'exit_datetime': '2025-04-01 12:00:00'}
2025-05-26 12:01:07,677 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 1, about to append. Total records before: 0
2025-05-26 12:01:07,677 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 1. Total records now: 1
2025-05-26 12:01:07,677 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 2 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 12:01:07,677 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-26 12:01:07,677 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-26 12:01:07,713 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.035s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-26 12:01:07,749 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.036s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-26 12:01:07,749 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Query results: entry_df size=1, exit_df size=1
2025-05-26 12:01:07,749 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Got entry and exit data, proceeding...
2025-05-26 12:01:07,750 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
                        trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523450CE
ce_open                          102.85
ce_high                          106.65
ce_low                            97.85
ce_close                         100.75
ce_volume                       1015050
ce_oi                            978075
ce_coi                                0
ce_iv                             13.44
ce_delta                           0.48
ce_gamma                         0.0015
ce_theta                         -23.27
ce_vega                            7.95
ce_rho                          81.3115
pe_symbol           NIFTY03APR2523450PE
pe_open                           118.2
pe_high                           122.3
pe_low                            112.2
pe_close                          115.7
pe_volume                        877125
pe_oi                           1618950
pe_coi                                0
pe_iv                             13.74
pe_delta                          -0.52
pe_gamma                         0.0015
pe_theta                          -17.3
pe_vega                            7.95
pe_rho                         -88.3744
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-26 12:01:07,751 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23200.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                           123.6
ce_high                           124.3
ce_low                           119.25
ce_close                          124.2
ce_volume                        326775
ce_oi                           4374450
ce_coi                           193725
ce_iv                             14.99
ce_delta                           0.52
ce_gamma                         0.0013
ce_theta                         -25.57
ce_vega                            7.87
ce_rho                          86.2799
pe_symbol           NIFTY03APR2523200PE
pe_open                           100.2
pe_high                           105.2
pe_low                            99.55
pe_close                         100.75
pe_volume                        526875
pe_oi                           5030025
pe_coi                           101175
pe_iv                             13.55
pe_delta                          -0.48
pe_gamma                         0.0015
pe_theta                         -17.08
pe_vega                            7.87
pe_rho                         -81.3242
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-26 12:01:07,751 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-26 12:01:07,751 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-26 12:01:07,751 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-26 12:01:07,751 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Entering risk rules block
2025-05-26 12:01:07,752 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-26 12:01:07,753 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140143174831120
2025-05-26 12:01:07,753 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140143174831008, OptionType.PUT ID: 140143174831120
2025-05-26 12:01:07,753 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-26 12:01:07,753 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-26 12:01:07,753 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-26 12:01:07,754 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-26 12:01:08,038 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.283s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-26 12:01:08,039 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-26 12:01:08,040 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-26 12:01:08,094 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-26 12:01:08,098 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 2 - Tick data for specific strike 23450.0 is EMPTY after filtering windowed data.
2025-05-26 12:01:08,098 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Starting risk rules loop, has 3 rules
2025-05-26 12:01:08,098 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 1 of 3
2025-05-26 12:01:08,098 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-26 12:01:08,109 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-26 12:01:08,109 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 1
2025-05-26 12:01:08,109 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 2 of 3
2025-05-26 12:01:08,109 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type TAKEPROFIT
2025-05-26 12:01:08,119 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-26 12:01:08,119 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 2
2025-05-26 12:01:08,119 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 3 of 3
2025-05-26 12:01:08,120 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type TRAIL
2025-05-26 12:01:08,122 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-26 12:01:08,122 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 3
2025-05-26 12:01:08,122 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-26 12:01:08,122 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Reached trade building section (outside all conditionals)
2025-05-26 12:01:08,123 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 2
2025-05-26 12:01:08,123 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 2, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 12:01:08,123 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23420.45, 'atm_strike': 23450.0, 'strike': 23450.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523450CE', 'ce_open': 102.85, 'ce_high': 106.65, 'ce_low': 97.85, 'ce_close': 100.75, 'ce_volume': 1015050, 'ce_oi': 978075, 'ce_coi': 0, 'ce_iv': 13.44, 'ce_delta': 0.48, 'ce_gamma': 0.0015, 'ce_theta': -23.27, 'ce_vega': 7.95, 'ce_rho': 81.3115, 'pe_symbol': 'NIFTY03APR2523450PE', 'pe_open': 118.2, 'pe_high': 122.3, 'pe_low': 112.2, 'pe_close': 115.7, 'pe_volume': 877125, 'pe_oi': 1618950, 'pe_coi': 0, 'pe_iv': 13.74, 'pe_delta': -0.52, 'pe_gamma': 0.0015, 'pe_theta': -17.3, 'pe_vega': 7.95, 'pe_rho': -88.3744, 'future_open': 23534.5, 'future_high': 23543.95, 'future_low': 23527.0, 'future_close': 23536.75, 'future_volume': 86475, 'future_oi': 12605100, 'future_coi': 0}
2025-05-26 12:01:08,123 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(12, 0), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23195.3, 'atm_strike': 23200.0, 'strike': 23200.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523200CE', 'ce_open': 123.6, 'ce_high': 124.3, 'ce_low': 119.25, 'ce_close': 124.2, 'ce_volume': 326775, 'ce_oi': 4374450, 'ce_coi': 193725, 'ce_iv': 14.99, 'ce_delta': 0.52, 'ce_gamma': 0.0013, 'ce_theta': -25.57, 'ce_vega': 7.87, 'ce_rho': 86.2799, 'pe_symbol': 'NIFTY03APR2523200PE', 'pe_open': 100.2, 'pe_high': 105.2, 'pe_low': 99.55, 'pe_close': 100.75, 'pe_volume': 526875, 'pe_oi': 5030025, 'pe_coi': 101175, 'pe_iv': 13.55, 'pe_delta': -0.48, 'pe_gamma': 0.0015, 'pe_theta': -17.08, 'pe_vega': 7.87, 'pe_rho': -81.3242, 'future_open': 23347.0, 'future_high': 23348.85, 'future_low': 23339.0, 'future_close': 23348.1, 'future_volume': 19050, 'future_oi': 13928550, 'future_coi': -28275}
2025-05-26 12:01:08,123 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=SELL, Lots=1
2025-05-26 12:01:08,123 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-26 12:01:08,123 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-26 12:01:08,123 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-26 12:01:08,123 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: 747.5000000000001, PnL (Slippage): 747.5000000000001, Net PnL: 747.5000000000001
2025-05-26 12:01:08,123 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-26 12:01:08,124 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '2', 'entry_date': '2025-04-01', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2025-04-01', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23450, 'instrument_type': 'PE', 'side': 'SELL', 'filled_quantity': 50.0, 'entry_price': 115.7, 'exit_price': 100.75, 'points': 14.950000000000003, 'pointsAfterSlippage': 14.950000000000003, 'pnl': 747.5000000000001, 'pnlAfterSlippage': 747.5000000000001, 'expenses': 0.0, 'netPnlAfterExpenses': 747.5000000000001, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23420.45, 'index_exit_price': 23195.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-01 09:16:00', 'exit_datetime': '2025-04-01 12:00:00'}
2025-05-26 12:01:08,124 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 2, about to append. Total records before: 1
2025-05-26 12:01:08,124 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 2. Total records now: 2
2025-05-26 12:01:08,124 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 3 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 12:01:08,124 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-26 12:01:08,124 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-26 12:01:08,160 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.036s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-26 12:01:08,195 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.035s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-26 12:01:08,195 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Query results: entry_df size=1, exit_df size=1
2025-05-26 12:01:08,195 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Got entry and exit data, proceeding...
2025-05-26 12:01:08,196 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
                        trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23550.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY03APR2523550CE
ce_open                           61.15
ce_high                           61.75
ce_low                             55.0
ce_close                          56.45
ce_volume                       1147575
ce_oi                           2744250
ce_coi                                0
ce_iv                             12.93
ce_delta                           0.34
ce_gamma                         0.0014
ce_theta                         -19.86
ce_vega                            7.25
ce_rho                          56.1949
pe_symbol           NIFTY03APR2523550PE
pe_open                           174.0
pe_high                           180.1
pe_low                            167.8
pe_close                          175.5
pe_volume                        326550
pe_oi                           1975725
pe_coi                                0
pe_iv                             13.83
pe_delta                          -0.66
pe_gamma                         0.0013
pe_theta                         -14.93
pe_vega                            7.34
pe_rho                        -112.5433
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-26 12:01:08,197 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23300.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY03APR2523300CE
ce_open                           75.59
ce_high                           76.15
ce_low                            73.15
ce_close                          76.15
ce_volume                        250950
ce_oi                           7928250
ce_coi                           109950
ce_iv                             14.54
ce_delta                           0.37
ce_gamma                         0.0013
ce_theta                         -23.12
ce_vega                            7.53
ce_rho                          63.7039
pe_symbol           NIFTY03APR2523300PE
pe_open                           152.9
pe_high                          158.35
pe_low                           151.65
pe_close                         153.44
pe_volume                        252000
pe_oi                           4303500
pe_coi                           -10725
pe_iv                             13.14
pe_delta                          -0.63
pe_gamma                         0.0015
pe_theta                         -14.47
pe_vega                            7.45
pe_rho                        -107.0027
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-26 12:01:08,197 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-26 12:01:08,197 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-26 12:01:08,197 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-26 12:01:08,197 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Entering risk rules block
2025-05-26 12:01:08,197 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-26 12:01:08,198 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140143174831008
2025-05-26 12:01:08,198 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140143174831008, OptionType.PUT ID: 140143174831120
2025-05-26 12:01:08,198 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-26 12:01:08,198 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-26 12:01:08,198 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-26 12:01:08,199 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-26 12:01:08,526 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.327s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-26 12:01:08,528 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-26 12:01:08,529 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-26 12:01:08,611 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-26 12:01:08,616 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 3 - Tick data for specific strike 23550.0 is EMPTY after filtering windowed data.
2025-05-26 12:01:08,616 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Starting risk rules loop, has 3 rules
2025-05-26 12:01:08,616 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 1 of 3
2025-05-26 12:01:08,616 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-26 12:01:08,632 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - evaluate_risk_rule returned: triggered=False
2025-05-26 12:01:08,632 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Finished processing risk rule 1
2025-05-26 12:01:08,632 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 2 of 3
2025-05-26 12:01:08,632 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - About to call evaluate_risk_rule for rule type TAKEPROFIT
2025-05-26 12:01:08,637 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - evaluate_risk_rule returned: triggered=True
2025-05-26 12:01:08,639 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Breaking out of risk rules loop due to triggered rule
2025-05-26 12:01:08,639 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-26 12:01:08,639 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Reached trade building section (outside all conditionals)
2025-05-26 12:01:08,639 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 3
2025-05-26 12:01:08,639 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 3, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 12:01:08,640 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23420.45, 'atm_strike': 23450.0, 'strike': 23550.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'OTM2', 'put_strike_type': 'ITM2', 'ce_symbol': 'NIFTY03APR2523550CE', 'ce_open': 61.15, 'ce_high': 61.75, 'ce_low': 55.0, 'ce_close': 56.45, 'ce_volume': 1147575, 'ce_oi': 2744250, 'ce_coi': 0, 'ce_iv': 12.93, 'ce_delta': 0.34, 'ce_gamma': 0.0014, 'ce_theta': -19.86, 'ce_vega': 7.25, 'ce_rho': 56.1949, 'pe_symbol': 'NIFTY03APR2523550PE', 'pe_open': 174.0, 'pe_high': 180.1, 'pe_low': 167.8, 'pe_close': 175.5, 'pe_volume': 326550, 'pe_oi': 1975725, 'pe_coi': 0, 'pe_iv': 13.83, 'pe_delta': -0.66, 'pe_gamma': 0.0013, 'pe_theta': -14.93, 'pe_vega': 7.34, 'pe_rho': -112.5433, 'future_open': 23534.5, 'future_high': 23543.95, 'future_low': 23527.0, 'future_close': 23536.75, 'future_volume': 86475, 'future_oi': 12605100, 'future_coi': 0}
2025-05-26 12:01:08,640 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23195.3, 'atm_strike': 23200.0, 'strike': 23300.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'OTM2', 'put_strike_type': 'ITM2', 'ce_symbol': 'NIFTY03APR2523300CE', 'ce_open': 75.59, 'ce_high': 76.15, 'ce_low': 73.15, 'ce_close': 76.15, 'ce_volume': 250950, 'ce_oi': 7928250, 'ce_coi': 109950, 'ce_iv': 14.54, 'ce_delta': 0.37, 'ce_gamma': 0.0013, 'ce_theta': -23.12, 'ce_vega': 7.53, 'ce_rho': 63.7039, 'pe_symbol': 'NIFTY03APR2523300PE', 'pe_open': 152.9, 'pe_high': 158.35, 'pe_low': 151.65, 'pe_close': 153.44, 'pe_volume': 252000, 'pe_oi': 4303500, 'pe_coi': -10725, 'pe_iv': 13.14, 'pe_delta': -0.63, 'pe_gamma': 0.0015, 'pe_theta': -14.47, 'pe_vega': 7.45, 'pe_rho': -107.0027, 'future_open': 23347.0, 'future_high': 23348.85, 'future_low': 23339.0, 'future_close': 23348.1, 'future_volume': 19050, 'future_oi': 13928550, 'future_coi': -28275, 'close': 1450.2, 'datetime': Timestamp('2025-04-01 12:00:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-26 12:01:08,640 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=CALL, Transaction=BUY, Lots=1
2025-05-26 12:01:08,640 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-26 12:01:08,640 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-26 12:01:08,640 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-26 12:01:08,640 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: 985.0000000000001, PnL (Slippage): 985.0000000000001, Net PnL: 985.0000000000001
2025-05-26 12:01:08,641 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-26 12:01:08,641 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '3', 'entry_date': '2025-04-01', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2025-04-01', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23550, 'instrument_type': 'CE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 56.45, 'exit_price': 76.15, 'points': 19.700000000000003, 'pointsAfterSlippage': 19.700000000000003, 'pnl': 985.0000000000001, 'pnlAfterSlippage': 985.0000000000001, 'expenses': 0.0, 'netPnlAfterExpenses': 985.0000000000001, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23420.45, 'index_exit_price': 23195.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-01 09:16:00', 'exit_datetime': '2025-04-01 12:00:00'}
2025-05-26 12:01:08,641 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 3, about to append. Total records before: 2
2025-05-26 12:01:08,641 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 3. Total records now: 3
2025-05-26 12:01:08,641 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 4 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 12:01:08,641 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-26 12:01:08,641 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-26 12:01:08,673 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.032s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-26 12:01:08,712 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.038s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-26 12:01:08,712 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Query results: entry_df size=1, exit_df size=1
2025-05-26 12:01:08,713 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Got entry and exit data, proceeding...
2025-05-26 12:01:08,713 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
                        trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23350.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY03APR2523350CE
ce_open                           164.4
ce_high                           166.5
ce_low                            156.3
ce_close                          158.0
ce_volume                        408675
ce_oi                            381750
ce_coi                                0
ce_iv                             13.71
ce_delta                           0.62
ce_gamma                         0.0014
ce_theta                         -23.53
ce_vega                            7.54
ce_rho                         105.3755
pe_symbol           NIFTY03APR2523350PE
pe_open                            78.3
pe_high                           80.95
pe_low                            71.84
pe_close                          76.59
pe_volume                       1021875
pe_oi                           1656000
pe_coi                                0
pe_iv                             14.49
pe_delta                          -0.38
pe_gamma                         0.0013
pe_theta                         -18.33
pe_vega                            7.58
pe_rho                         -64.7877
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-26 12:01:08,714 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23100.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY03APR2523100CE
ce_open                           186.9
ce_high                           188.0
ce_low                            181.8
ce_close                          187.2
ce_volume                         77775
ce_oi                            957825
ce_coi                            12900
ce_iv                             15.68
ce_delta                           0.66
ce_gamma                         0.0012
ce_theta                         -25.85
ce_vega                            7.36
ce_rho                         106.8743
pe_symbol           NIFTY03APR2523100PE
pe_open                            63.7
pe_high                            67.2
pe_low                            63.05
pe_close                          64.15
pe_volume                        269475
pe_oi                           3243600
pe_coi                            78900
pe_iv                             14.17
pe_delta                          -0.34
pe_gamma                         0.0013
pe_theta                         -17.23
pe_vega                            7.25
pe_rho                         -57.8964
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-26 12:01:08,714 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-26 12:01:08,715 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-26 12:01:08,715 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-26 12:01:08,715 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Entering risk rules block
2025-05-26 12:01:08,715 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-26 12:01:08,715 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140143174831120
2025-05-26 12:01:08,715 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140143174831008, OptionType.PUT ID: 140143174831120
2025-05-26 12:01:08,715 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-26 12:01:08,716 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-26 12:01:08,716 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-26 12:01:08,717 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-26 12:01:09,044 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.327s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-26 12:01:09,046 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-26 12:01:09,047 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-26 12:01:09,116 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-26 12:01:09,120 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 4 - Tick data for specific strike 23350.0 is EMPTY after filtering windowed data.
2025-05-26 12:01:09,120 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Starting risk rules loop, has 3 rules
2025-05-26 12:01:09,120 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 1 of 3
2025-05-26 12:01:09,120 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-26 12:01:09,124 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - evaluate_risk_rule returned: triggered=True
2025-05-26 12:01:09,126 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Breaking out of risk rules loop due to triggered rule
2025-05-26 12:01:09,126 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-26 12:01:09,126 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Reached trade building section (outside all conditionals)
2025-05-26 12:01:09,126 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 4
2025-05-26 12:01:09,126 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 12:01:09,126 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23420.45, 'atm_strike': 23450.0, 'strike': 23350.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY03APR2523350CE', 'ce_open': 164.4, 'ce_high': 166.5, 'ce_low': 156.3, 'ce_close': 158.0, 'ce_volume': 408675, 'ce_oi': 381750, 'ce_coi': 0, 'ce_iv': 13.71, 'ce_delta': 0.62, 'ce_gamma': 0.0014, 'ce_theta': -23.53, 'ce_vega': 7.54, 'ce_rho': 105.3755, 'pe_symbol': 'NIFTY03APR2523350PE', 'pe_open': 78.3, 'pe_high': 80.95, 'pe_low': 71.84, 'pe_close': 76.59, 'pe_volume': 1021875, 'pe_oi': 1656000, 'pe_coi': 0, 'pe_iv': 14.49, 'pe_delta': -0.38, 'pe_gamma': 0.0013, 'pe_theta': -18.33, 'pe_vega': 7.58, 'pe_rho': -64.7877, 'future_open': 23534.5, 'future_high': 23543.95, 'future_low': 23527.0, 'future_close': 23536.75, 'future_volume': 86475, 'future_oi': 12605100, 'future_coi': 0}
2025-05-26 12:01:09,126 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23195.3, 'atm_strike': 23200.0, 'strike': 23100.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY03APR2523100CE', 'ce_open': 186.9, 'ce_high': 188.0, 'ce_low': 181.8, 'ce_close': 187.2, 'ce_volume': 77775, 'ce_oi': 957825, 'ce_coi': 12900, 'ce_iv': 15.68, 'ce_delta': 0.66, 'ce_gamma': 0.0012, 'ce_theta': -25.85, 'ce_vega': 7.36, 'ce_rho': 106.8743, 'pe_symbol': 'NIFTY03APR2523100PE', 'pe_open': 63.7, 'pe_high': 67.2, 'pe_low': 63.05, 'pe_close': 64.15, 'pe_volume': 269475, 'pe_oi': 3243600, 'pe_coi': 78900, 'pe_iv': 14.17, 'pe_delta': -0.34, 'pe_gamma': 0.0013, 'pe_theta': -17.23, 'pe_vega': 7.25, 'pe_rho': -57.8964, 'future_open': 23347.0, 'future_high': 23348.85, 'future_low': 23339.0, 'future_close': 23348.1, 'future_volume': 19050, 'future_oi': 13928550, 'future_coi': -28275, 'close': 2.15, 'datetime': Timestamp('2025-04-01 12:00:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-26 12:01:09,126 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-26 12:01:09,127 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-26 12:01:09,127 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-26 12:01:09,127 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-26 12:01:09,127 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -621.9999999999999, PnL (Slippage): -621.9999999999999, Net PnL: -621.9999999999999
2025-05-26 12:01:09,127 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-26 12:01:09,127 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2025-04-01', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2025-04-01', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23350, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 76.59, 'exit_price': 64.15, 'points': -12.439999999999998, 'pointsAfterSlippage': -12.439999999999998, 'pnl': -621.9999999999999, 'pnlAfterSlippage': -621.9999999999999, 'expenses': 0.0, 'netPnlAfterExpenses': -621.9999999999999, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23420.45, 'index_exit_price': 23195.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-01 09:16:00', 'exit_datetime': '2025-04-01 12:00:00'}
2025-05-26 12:01:09,127 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 4, about to append. Total records before: 3
2025-05-26 12:01:09,127 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 4. Total records now: 4
2025-05-26 12:01:09,127 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Number of trade records generated: 4
2025-05-26 12:01:09,127 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Generated 4 trade records via model-driven path
2025-05-26 12:01:09,127 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Calculating tick-by-tick P&L for Max Profit/Loss tracking
2025-05-26 12:01:09,128 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG Tick P&L: First trade fields: ['portfolio_name', 'strategy', 'leg_id', 'entry_date', 'entry_time', 'entry_day', 'exit_date', 'exit_time', 'exit_day', 'symbol', 'expiry', 'strike', 'instrument_type', 'side', 'filled_quantity', 'entry_price', 'exit_price', 'points', 'pointsAfterSlippage', 'pnl', 'pnlAfterSlippage', 'expenses', 'netPnlAfterExpenses', 're_entry_no', 'stop_loss_entry_number', 'take_profit_entry_number', 'reason', 'strategy_entry_number', 'index_entry_price', 'index_exit_price', 'max_profit', 'max_loss', 'entry_datetime', 'exit_datetime']
2025-05-26 12:01:09,128 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG Tick P&L: First trade sample: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '1', 'entry_date': '2025-04-01', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2025-04-01', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23450, 'instrument_type': 'CE', 'side': 'SELL', 'filled_quantity': 50.0, 'entry_price': 100.75, 'exit_price': 124.2, 'points': -23.450000000000003, 'pointsAfterSlippage': -23.450000000000003, 'pnl': -1172.5000000000002, 'pnlAfterSlippage': -1172.5000000000002, 'expenses': 0.0, 'netPnlAfterExpenses': -1172.5000000000002, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23420.45, 'index_exit_price': 23195.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-01 09:16:00', 'exit_datetime': '2025-04-01 12:00:00'}
2025-05-26 12:01:09,159 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.032s, returned 375 rows. Query: SELECT SUBSTRING('SELECT DISTINCT EXTRACT(HOUR FROM trade_time) * 10000 + 
                                       EXTRACT(MINUTE FROM trade_time) * 100 + 
                                       EXTRACT(SECOND FROM trad', 1, 200) ...
2025-05-26 12:01:09,171 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.011s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,186 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,200 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,216 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,234 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,250 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,265 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,281 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,297 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,313 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,329 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,345 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,361 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,377 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,393 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,409 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,424 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,440 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,456 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,472 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,488 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,504 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,520 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,536 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,552 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,568 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,584 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,600 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,616 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,632 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,648 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,664 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,680 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,696 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,712 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,728 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,744 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,760 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,776 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,792 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,808 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,824 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,840 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,852 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.012s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,868 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,880 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.011s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,895 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,907 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.011s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,923 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,935 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.011s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,952 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,967 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,983 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:09,999 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,015 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,030 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,047 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,062 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,078 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,094 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,110 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,126 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,142 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,158 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,174 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,189 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,204 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,219 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,234 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,249 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,263 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,279 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,293 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,308 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,323 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,337 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,352 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,367 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,382 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,396 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,411 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,426 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,441 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,456 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,471 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,486 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,501 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,516 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,531 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,546 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,561 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,576 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,591 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,605 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,620 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,635 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,650 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,665 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,680 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,695 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,710 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,725 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,740 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,755 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,770 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,785 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,800 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,815 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,829 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,844 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,859 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,873 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,888 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,903 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,918 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,933 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,948 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,963 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,977 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:10,992 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,008 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,023 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,037 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,052 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,067 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,082 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,097 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,112 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,127 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,142 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,157 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,172 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,187 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,202 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,217 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,232 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,247 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,262 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,276 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,292 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,307 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,322 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,337 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,351 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,366 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,381 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,396 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,411 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,425 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,441 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,456 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,471 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,486 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,501 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,516 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,531 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,546 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,561 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.014s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,613 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.051s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,632 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,652 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,671 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,690 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,709 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,728 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,747 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,767 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,785 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,804 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,823 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,842 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,861 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,880 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,900 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,923 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.023s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,943 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,962 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:11,983 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.021s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,002 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,021 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,040 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,060 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,079 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,098 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,117 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,136 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,155 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,175 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,195 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,214 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,233 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,253 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,272 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,291 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,310 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,329 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,348 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,368 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,387 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,406 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,425 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,445 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,464 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,483 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,502 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,522 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,541 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,560 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,579 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,598 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,617 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,636 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,655 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,674 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,692 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,711 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,730 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,749 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,768 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,787 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,806 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,825 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,844 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,863 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,883 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,905 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.022s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,924 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,943 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,962 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:12,981 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,000 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,019 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,038 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,057 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,076 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,095 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,114 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,133 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,152 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,171 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,190 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,209 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,228 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,247 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,266 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,285 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,304 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,323 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,342 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,361 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,380 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,399 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,418 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,437 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,456 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,475 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,494 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,513 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,532 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,551 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,570 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,589 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,608 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,627 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,647 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,665 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,684 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,704 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,723 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,742 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,761 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,780 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,799 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,819 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,838 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,857 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,876 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,896 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,915 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,934 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,954 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,973 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:13,992 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,011 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,030 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,049 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,068 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,087 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,106 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,124 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,142 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,160 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,179 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,197 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,216 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,234 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,253 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,272 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,290 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,309 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,327 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,346 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,365 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,384 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,403 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,422 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,441 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,460 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,479 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,498 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,517 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,536 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,555 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,574 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,593 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,612 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,632 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,650 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,670 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,689 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,708 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,727 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,746 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,765 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,784 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,803 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,822 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,841 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,860 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,879 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,898 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,917 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,936 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,955 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,973 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:14,992 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,011 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,030 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,049 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,068 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,087 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,106 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,125 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,144 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,163 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,182 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,201 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,220 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,239 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,258 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,277 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,296 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,316 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,335 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,353 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,373 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,392 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,411 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,430 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,453 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.022s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,476 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.023s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,497 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,516 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,535 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,558 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.022s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,578 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,597 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,616 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,635 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,654 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,673 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,692 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,711 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,730 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,749 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,768 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,788 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,806 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,825 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,844 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,863 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,882 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,901 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,920 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,940 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,959 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,978 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:15,997 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,016 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,035 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,054 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,074 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,093 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,112 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,131 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,150 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,170 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,189 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,208 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,227 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,246 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,266 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,285 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,304 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,324 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,343 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,362 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,381 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,400 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,420 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,439 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,458 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,477 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,496 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,516 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,535 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,554 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,573 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,627 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.054s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,646 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,666 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,685 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,704 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,723 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,742 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,761 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,780 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,799 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,819 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,837 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,852 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,872 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,891 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,910 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,930 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,949 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,968 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:16,987 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,006 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,025 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,044 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,064 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,083 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,102 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,122 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,141 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,160 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,179 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,199 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,218 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,238 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,259 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,278 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,297 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,318 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,335 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,355 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,372 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,391 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,411 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,429 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,449 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,468 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,487 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,506 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,525 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,544 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,563 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,582 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,602 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,621 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,640 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,659 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,678 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,696 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,715 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,735 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,754 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,773 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,792 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,813 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,833 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,852 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,872 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,891 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,911 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,930 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,949 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,968 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:17,987 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,006 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,025 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,044 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,063 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,082 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,101 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,120 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,139 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,158 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,177 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,196 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,215 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,234 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,253 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,272 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,291 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,310 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,330 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,349 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,368 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,387 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,406 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,424 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,443 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,462 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,481 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,500 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,519 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,538 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,557 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,576 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,595 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,614 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,633 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,652 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,672 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,691 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,710 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,729 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,748 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,767 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,787 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,807 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,826 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,845 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,868 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.023s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,888 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,907 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,926 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,945 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,965 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:18,984 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,003 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,021 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,041 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,060 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,079 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,098 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,117 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,136 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,155 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,174 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,193 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,212 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,231 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,250 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,273 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.022s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,292 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,311 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,330 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,350 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,369 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,388 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,407 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,426 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,445 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,464 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,483 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,503 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,521 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,540 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,561 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,580 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,599 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,618 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,637 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,656 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,675 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,694 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,714 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,737 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.023s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,757 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,776 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,795 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,814 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,833 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,852 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,871 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,890 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,909 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,928 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,947 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,967 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:19,986 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,005 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,024 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,043 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,062 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,081 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,101 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,120 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,139 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,158 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,177 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,196 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,216 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,235 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,255 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,274 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,293 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,312 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,331 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,348 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,367 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,386 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,405 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,425 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,444 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,463 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,482 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,501 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,520 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,539 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,558 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,577 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,596 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,615 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,634 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,654 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,673 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,692 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,712 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,731 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,751 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,769 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,789 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,808 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,827 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,846 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,865 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,885 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,904 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,923 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,942 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,966 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.024s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:20,986 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:21,005 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:21,024 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:21,043 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:21,062 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:21,081 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:21,100 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:21,119 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:21,138 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:21,158 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:21,176 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:21,196 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:21,215 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:21,234 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:21,254 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                     ', 1, 200) ...
2025-05-26 12:01:21,278 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Calculated tick P&L for date 250401: 375 timestamps
2025-05-26 12:01:21,278 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Closed reusable HeavyDB connection from get_trades_for_portfolio
2025-05-26 12:01:21,318 - backtester_stable.BTRUN.builders - WARNING - utils.MARGIN_INFO not found or empty - margin requirements will be 0. Attempting to populate.
2025-05-26 12:01:21,319 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'margin_symbol_info.json' at /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-26 12:01:21,319 - backtester_stable.BTRUN.core.utils - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json (Broker: angel)
2025-05-26 12:01:21,319 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.MARGIN_INFO for angel.
2025-05-26 12:01:21,319 - backtester_stable.BTRUN.builders - ERROR - utils.MARGIN_INFO still empty after populate attempt. Margin req will be 0.
2025-05-26 12:01:21,325 - backtester_stable.BTRUN.builders - DEBUG - Strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL margin requirement: 0.00
2025-05-26 12:01:21,325 - backtester_stable.BTRUN.builders - INFO - Total portfolio margin requirement: 0.00
2025-05-26 12:01:21,325 - backtester_stable.BTRUN.builders - DEBUG - Converting tick PnL to daywise DataFrame.
2025-05-26 12:01:21,326 - backtester_stable.BTRUN.builders - INFO - Backtest response parsed.
2025-05-26 12:01:21,371 - backtester_stable.BTRUN.core.runtime - INFO - Generated metrics_df with 50 records for strategies: ['Combined' 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL']
2025-05-26 12:01:21,457 - backtester_stable.BTRUN.core.runtime - INFO - Saving Excel output to output/tbs_apr3_heavydb.xlsx
2025-05-26 12:01:21,457 - backtester_stable.BTRUN.core.runtime - DEBUG - [DEBUG save_backtest_results] save_excel=True, metrics_df is empty: False
2025-05-26 12:01:21,457 - backtester_stable.BTRUN.core.io - INFO - 2025-05-26 12:01:21.457627, Started writing stats to excel file: output/tbs_apr3_heavydb.xlsx
2025-05-26 12:01:21,457 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Temporarily switching to CPU-only mode.
2025-05-26 12:01:21,504 - backtester_stable.BTRUN.core.io - INFO - Added PortfolioParameter sheet in legacy 2x2 format
2025-05-26 12:01:21,537 - backtester_stable.BTRUN.core.io - INFO - Added GeneralParameter sheet from input files
2025-05-26 12:01:21,550 - backtester_stable.BTRUN.core.io - INFO - Added LegParameter sheet from input files
2025-05-26 12:01:21,550 - backtester_stable.BTRUN.core.io - INFO - Filtered metrics to show only 'Combined' entries: 25 rows
2025-05-26 12:01:21,587 - backtester_stable.BTRUN.core.io - INFO - Added PORTFOLIO Results sheet in legacy format
2025-05-26 12:01:21,633 - backtester_stable.BTRUN.core.io - INFO - 2025-05-26 12:01:21.633585, Excel file prepared successfully, Time taken: 0.18s
2025-05-26 12:01:21,633 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Restored GPU_ENABLED to: False
2025-05-26 12:01:21,633 - backtester_stable.BTRUN.core.runtime - INFO - Saving JSON output to output/tbs_apr3_heavydb.json
2025-05-26 12:01:21,652 - backtester_stable.BTRUN.core.io - INFO - --- JSON DUMP DEBUG START ---
2025-05-26 12:01:21,652 - backtester_stable.BTRUN.core.io - INFO - --- JSON DUMP DEBUG END ---
2025-05-26 12:01:21,653 - backtester_stable.BTRUN.core.io - INFO - Successfully wrote JSON output to output/tbs_apr3_heavydb.json
2025-05-26 12:01:21,658 - backtester_stable.BTRUN.core.io - ERROR - Text-to-speech failed: This means you probably do not have eSpeak or eSpeak-ng installed!
