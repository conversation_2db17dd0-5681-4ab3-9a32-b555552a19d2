2025-05-09 20:49:05,154 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-09 20:49:05,154 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-09 20:49:05,154 - __main__ - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-09 20:49:05,154 - __main__ - DEBUG - HeavyDB host: **************, DB: heavydb
2025-05-09 20:49:05,154 - __main__ - DEBUG - GPU enabled: True
2025-05-09 20:49:05,159 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting BTRunPortfolio_GPU.py - Debugging enabled
2025-05-09 20:49:05,159 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Current working directory: /srv/samba/shared
2025-05-09 20:49:05,159 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-09 20:49:05,159 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Importing BTRUN modules...
2025-05-09 20:49:05,159 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Config module imported from: /srv/samba/shared/bt/backtester_stable/BTRUN/config.py
2025-05-09 20:49:05,159 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - HeavyDB settings - Host: **************, Database: heavydb
2025-05-09 20:49:05,159 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - GPU enabled: True
2025-05-09 20:49:05,159 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Successfully imported heavydb_helpers module
2025-05-09 20:49:05,159 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Entering main function
2025-05-09 20:49:05,159 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Parsing command line arguments
2025-05-09 20:49:05,160 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Parsed arguments: Namespace(config=None, legacy_excel=True, strategies=None, start_date=None, end_date=None, output_dir='Trades', portfolio_name=None, slippage=0.1, capital=None, margin_multiplier=1.0, no_json=False, no_excel=False, no_charts=False, cpu_only=False, merge_output=False, debug=False)
2025-05-09 20:49:05,161 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Text-to-speech notifications not available
2025-05-09 20:49:05,161 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - ERROR - Unhandled exception in main: No module named 'BTRUN'
2025-05-09 20:49:05,161 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/Old_BTRunPortfolio_GPU_old.py", line 559, in main
    results = run_portfolio_backtest(args)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py", line 174, in _row_run_backtest
    from BTRUN import gpu_helpers as _gh
ModuleNotFoundError: No module named 'BTRUN'

2025-05-09 20:49:05,161 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Exiting main function
