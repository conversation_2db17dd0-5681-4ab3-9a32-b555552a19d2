2025-05-29 14:18:05,756 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-29 14:18:05,756 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-29 14:18:05,757 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/../models', '/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages', '/srv/samba/shared']
2025-05-29 14:18:05,757 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-29 14:18:05,757 - __main__ - DEBUG - GPU enabled: False
2025-05-29 14:18:05,760 - bt.common.gpu_worker_pool - INFO - Calculated optimal workers: 8 (CPU: 72, Memory: 251.8GB)
2025-05-29 14:18:05,760 - __main__ - INFO - GPU acceleration enabled: False
2025-05-29 14:18:05,760 - __main__ - INFO - HeavyDB integration available (Host: 127.0.0.1)
2025-05-29 14:18:05,993 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-29 14:18:05,993 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-29 14:18:05,993 - __main__ - INFO - Runtime flags – workers: 8, retry_cpu: False
2025-05-29 14:18:05,993 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-29 14:18:05,993 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-29 14:18:05,993 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-29 14:18:05,999 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-29 14:18:06,000 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-29 14:18:06,000 - backtester_stable.BTRUN.core.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-29 14:18:06,000 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-29 14:18:06,000 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-29 14:18:06,001 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-29 14:18:06,001 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-29 14:18:06,001 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-29 14:18:06,001 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-29 14:18:06,001 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_2024_clean.xlsx
2025-05-29 14:18:06,001 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-29 14:18:06,001 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_2024_clean.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-29 14:18:06,111 - __main__ - ERROR - Error parsing portfolio with modern_portfolio_parser: 'EndDate'
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/indexes/base.py", line 3802, in get_loc
    return self._engine.get_loc(casted_key)
  File "pandas/_libs/index.pyx", line 138, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/index.pyx", line 165, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 5745, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 5753, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'EndDate'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py", line 474, in _row_run_backtest
    parsed_portfolio_models: Dict[str, PortfolioModel] = modern_portfolio_parser.parse_portfolio_excel(main_portfolio_excel_path)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/excel_parser/portfolio_parser.py", line 68, in parse_portfolio_excel
    end_date = _coerce_date(prow["EndDate"])
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/series.py", line 981, in __getitem__
    return self._get_value(key)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/series.py", line 1089, in _get_value
    loc = self.index.get_loc(label)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/indexes/base.py", line 3804, in get_loc
    raise KeyError(key) from err
KeyError: 'EndDate'
