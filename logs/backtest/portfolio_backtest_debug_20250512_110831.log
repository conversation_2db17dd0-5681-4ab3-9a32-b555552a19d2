2025-05-12 11:08:31,009 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-12 11:08:31,009 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-12 11:08:31,010 - __main__ - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-12 11:08:31,010 - __main__ - DEBUG - HeavyDB host: **************, DB: heavydb
2025-05-12 11:08:31,010 - __main__ - DEBUG - GPU enabled: True
2025-05-12 11:08:31,011 - __main__ - INFO - GPU acceleration enabled: True
2025-05-12 11:08:31,079 - __main__ - INFO - GPU Memory: 35415MB free / 40384MB total
2025-05-12 11:08:31,079 - __main__ - INFO - HeavyDB integration available (Host: **************)
2025-05-12 11:08:31,293 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-12 11:08:31,293 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-12 11:08:31,293 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-12 11:08:31,295 - bt.backtester_stable.BTRUN.Util - INFO - Running necessary functions before starting BT...
2025-05-12 11:08:31,295 - bt.backtester_stable.BTRUN.config - DEBUG - Found fixture at /srv/samba/shared/LOTSIZE.csv
2025-05-12 11:08:31,296 - bt.backtester_stable.BTRUN.Util - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-12 11:08:31,300 - bt.backtester_stable.BTRUN.Util - INFO - Successfully populated LOT_SIZE: 50 entries.
2025-05-12 11:08:31,300 - bt.backtester_stable.BTRUN.config - WARNING - Fixture margin_symbol_info.json not found in any expected location
2025-05-12 11:08:31,301 - bt.backtester_stable.BTRUN.Util - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/margin_symbol_info.json (Broker: angel)
2025-05-12 11:08:31,301 - bt.backtester_stable.BTRUN.Util - INFO - Pre-BT functions complete.
2025-05-12 11:08:31,301 - __main__ - INFO - Using new Excel input format (PortfolioSetting & StrategySetting)
2025-05-12 11:08:31,301 - __main__ - INFO - Reading PortfolioSetting & StrategySetting from /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-12 11:08:31,397 - __main__ - DEBUG - Loaded 1 PortfolioSetting rows, 21 StrategySetting rows
2025-05-12 11:08:31,397 - __main__ - INFO - Queueing portfolio: NIF0DTE
2025-05-12 11:08:31,397 - __main__ - DEBUG - Building portfolio request for portfolio: NIF0DTE
2025-05-12 11:08:31,399 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:08:31,399 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:08:31,399 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:08:31,399 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:08:31,399 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:08:31,399 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:08:31,399 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:08:31,400 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:08:31,400 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:08:31,400 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:08:31,400 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:08:31,400 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:08:31,400 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:08:31,400 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:08:31,493 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-12 11:08:31,493 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY CALL at 2025-05-12 09:16:00.493675 via method 'ATM'
2025-05-12 11:08:31,493 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-12 11:08:31,494 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY PUT at 2025-05-12 09:16:00.494013 via method 'ATM'
2025-05-12 11:08:31,494 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-12 11:08:31,494 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY CALL at 2025-05-12 09:16:00.494305 via method 'ATM'
2025-05-12 11:08:31,494 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-12 11:08:31,494 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY PUT at 2025-05-12 09:16:00.494599 via method 'ATM'
2025-05-12 11:08:31,494 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:08:31,494 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:08:31,495 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:08:31,495 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:08:31,495 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:08:31,495 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-12 11:08:31,502 - bt.backtester_stable.BTRUN.util_adapter - DEBUG - Adapter built PortfolioModel with 1 strategies, 4 total legs
2025-05-12 11:08:31,502 - __main__ - DEBUG - Attached portfolio_model to request (1 strategies)
2025-05-12 11:08:31,503 - __main__ - DEBUG - Built request for NIF0DTE with 1 strategies
2025-05-12 11:08:31,503 - __main__ - DEBUG - First strategy sample: {"id": "", "name": "RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL", "evaluator": "Tbs", "type": "STRATEGYTYPE.INTRADAY", "index": "NSEINDEX.NIFTY", "underlying": "CASH", "entry_time": 33360, "exit_time": 43200, "last_entry_time": 43200, "strike_selection_time": 33360, "multiplier": 1, "is_tick_bt": false, "legs": [{"id": "1", "option_type": "OPTIONTYPE.CALL", "side": "SIDE.SELL", "expiry_type": "EXPIRYTYPE.WEEKLY", "strike_selection": {"type": "BY_ATM_STRIKE", "value": 0.0}, "quantity": 50, "multipl
2025-05-12 11:08:31,503 - __main__ - INFO - Executing 1 portfolios sequentially
2025-05-12 11:08:31,503 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting BTRunPortfolio_GPU.py - Debugging enabled
2025-05-12 11:08:31,503 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Current working directory: /srv/samba/shared
2025-05-12 11:08:31,503 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-12 11:08:31,503 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Importing BTRUN modules...
2025-05-12 11:08:31,504 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Config module imported from: /srv/samba/shared/bt/backtester_stable/BTRUN/config.py
2025-05-12 11:08:31,504 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - HeavyDB settings - Host: **************, Database: heavydb
2025-05-12 11:08:31,504 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - GPU enabled: True
2025-05-12 11:08:31,504 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Successfully imported heavydb_helpers module
2025-05-12 11:08:31,504 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting single backtest for portfolio
2025-05-12 11:08:31,504 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Using HeavyDB for data access
2025-05-12 11:08:31,504 - bt.backtester_stable.BTRUN.runtime - INFO - Fetching trades from local HeavyDB for portfolio ''
2025-05-12 11:08:31,718 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-12 11:08:31,931 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-12 11:08:35,199 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 3.268s, returned 563 rows
2025-05-12 11:08:35,239 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Cached 563 distinct trade dates
2025-05-12 11:08:51,435 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 16.195s, returned 1 rows
2025-05-12 11:09:07,671 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 16.236s, returned 1 rows
2025-05-12 11:09:24,463 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 16.790s, returned 1 rows
2025-05-12 11:09:41,385 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 16.922s, returned 1 rows
2025-05-12 11:09:57,720 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 16.333s, returned 1 rows
2025-05-12 11:10:14,602 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 16.882s, returned 1 rows
2025-05-12 11:10:30,912 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 16.308s, returned 1 rows
2025-05-12 11:10:47,257 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 16.345s, returned 1 rows
2025-05-12 11:11:03,151 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 15.893s, returned 1 rows
2025-05-12 11:11:20,164 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 17.012s, returned 1 rows
2025-05-12 11:11:36,425 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 16.260s, returned 1 rows
2025-05-12 11:11:52,856 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 16.431s, returned 1 rows
2025-05-12 11:12:09,237 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 16.379s, returned 1 rows
2025-05-12 11:12:25,527 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 16.290s, returned 1 rows
2025-05-12 11:12:43,359 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 17.830s, returned 1 rows
2025-05-12 11:12:59,401 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 16.042s, returned 1 rows
2025-05-12 11:12:59,402 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Generated 8 trade records via model-driven path
2025-05-12 11:12:59,403 - bt.backtester_stable.BTRUN.builders - INFO - Parsing backtest response...
2025-05-12 11:13:00,293 - bt.backtester_stable.BTRUN.builders - WARNING - Margin calculation in builders.parse_backtest_response is a placeholder.
2025-05-12 11:13:00,293 - bt.backtester_stable.BTRUN.builders - DEBUG - Converting tick PnL to daywise DataFrame.
2025-05-12 11:13:00,294 - bt.backtester_stable.BTRUN.builders - INFO - Backtest response parsed.
2025-05-12 11:13:00,330 - bt.backtester_stable.BTRUN.gpu_helpers - WARNING - Failed to convert to cuDF: Could not convert 'Total' with type str: tried to convert to int64. Falling back to pandas.
2025-05-12 11:13:00,343 - bt.backtester_stable.BTRUN.gpu_helpers - WARNING - Failed to convert to cuDF: Could not convert 'Total' with type str: tried to convert to int64. Falling back to pandas.
2025-05-12 11:13:00,343 - bt.backtester_stable.BTRUN.runtime - INFO - Saving Excel output to backtest_output_adapter3/NIF0DTE_12052025_110831.xlsx
2025-05-12 11:13:00,344 - bt.backtester_stable.BTRUN.io - INFO - 2025-05-12 11:13:00.344035, Started writing stats to excel file: backtest_output_adapter3/NIF0DTE_12052025_110831.xlsx
2025-05-12 11:13:00,344 - bt.backtester_stable.BTRUN.gpu_helpers - DEBUG - Temporarily switching to CPU-only mode.
2025-05-12 11:13:00,364 - bt.backtester_stable.BTRUN.io - WARNING - Skipping rename of column 'exit_price' to 'Exit at' as it would duplicate an existing column name.
2025-05-12 11:13:00,388 - bt.backtester_stable.BTRUN.gpu_helpers - DEBUG - Restored GPU_ENABLED to: True
2025-05-12 11:13:00,388 - bt.backtester_stable.BTRUN.io - INFO - 2025-05-12 11:13:00.388777, Excel file prepared successfully, Time taken: 0.04s
2025-05-12 11:13:00,388 - bt.backtester_stable.BTRUN.runtime - INFO - Saving JSON output to backtest_output_adapter3/NIF0DTE_12052025_110831.json
2025-05-12 11:13:00,417 - bt.backtester_stable.BTRUN.io - INFO - Successfully wrote JSON output to backtest_output_adapter3/NIF0DTE_12052025_110831.json
2025-05-12 11:13:00,417 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Backtest completed in 268.91 seconds
2025-05-12 11:13:00,417 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Portfolio backtest completed successfully with 8 trades
2025-05-12 11:13:00,417 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Saved json file to backtest_output_adapter3/NIF0DTE_12052025_110831.json
2025-05-12 11:13:00,417 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Saved excel file to backtest_output_adapter3/NIF0DTE_12052025_110831.xlsx
2025-05-12 11:13:00,418 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Backtest Summary: {
  "Total PnL": -3231000.0,
  "Win Rate": 0.25,
  "Sharpe Ratio": -0.4025,
  "CAGR": NaN,
  "Max Drawdown": -2465250.0
}
