2025-05-25 12:34:32,723 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-25 12:34:32,723 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-25 12:34:32,723 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/../models', '/srv/samba/shared', '/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages', '/srv/samba/shared']
2025-05-25 12:34:32,723 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-25 12:34:32,724 - __main__ - DEBUG - GPU enabled: False
2025-05-25 12:34:32,725 - __main__ - INFO - GPU acceleration enabled: False
2025-05-25 12:34:32,725 - __main__ - INFO - HeavyDB integration available (Host: 127.0.0.1)
2025-05-25 12:34:32,940 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-25 12:34:32,940 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-25 12:34:32,940 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-25 12:34:32,940 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-25 12:34:32,940 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-25 12:34:32,940 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-25 12:34:32,945 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-25 12:34:32,945 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-25 12:34:32,946 - backtester_stable.BTRUN.core.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-25 12:34:32,946 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-25 12:34:32,947 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-25 12:34:32,947 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-25 12:34:32,947 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-25 12:34:32,947 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-25 12:34:32,947 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-25 12:34:32,947 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/INPUT TBS.xlsx
2025-05-25 12:34:32,947 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-25 12:34:32,947 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/INPUT TBS.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-25 12:34:33,037 - __main__ - ERROR - Error parsing portfolio with modern_portfolio_parser: Worksheet named 'PortfolioSetting' not found
Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py", line 436, in _row_run_backtest
    parsed_portfolio_models: Dict[str, PortfolioModel] = modern_portfolio_parser.parse_portfolio_excel(main_portfolio_excel_path)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/excel_parser/portfolio_parser.py", line 48, in parse_portfolio_excel
    portfolio_df = pd.read_excel(path, sheet_name="PortfolioSetting")
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/util/_decorators.py", line 211, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/util/_decorators.py", line 331, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/io/excel/_base.py", line 490, in read_excel
    data = io.parse(
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/io/excel/_base.py", line 1734, in parse
    return self._reader.parse(
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/io/excel/_base.py", line 760, in parse
    sheet = self.get_sheet_by_name(asheetname)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/io/excel/_openpyxl.py", line 577, in get_sheet_by_name
    self.raise_if_bad_sheet_by_name(name)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/io/excel/_base.py", line 602, in raise_if_bad_sheet_by_name
    raise ValueError(f"Worksheet named '{name}' not found")
ValueError: Worksheet named 'PortfolioSetting' not found
