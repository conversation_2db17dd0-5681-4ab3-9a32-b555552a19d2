2025-05-11 20:09:40,313 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-11 20:09:40,313 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-11 20:09:40,313 - __main__ - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-11 20:09:40,313 - __main__ - DEBUG - HeavyDB host: **************, DB: heavydb
2025-05-11 20:09:40,313 - __main__ - DEBUG - GPU enabled: True
2025-05-11 20:09:40,315 - __main__ - INFO - GPU acceleration enabled: True
2025-05-11 20:09:40,382 - __main__ - INFO - GPU Memory: 35419MB free / 40384MB total
2025-05-11 20:09:40,382 - __main__ - INFO - HeavyDB integration available (Host: **************)
2025-05-11 20:09:40,597 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:40,597 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-11 20:09:40,597 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-11 20:09:40,599 - bt.backtester_stable.BTRUN.Util - INFO - Running necessary functions before starting BT...
2025-05-11 20:09:40,599 - bt.backtester_stable.BTRUN.config - DEBUG - Found fixture at /srv/samba/shared/LOTSIZE.csv
2025-05-11 20:09:40,599 - bt.backtester_stable.BTRUN.Util - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-11 20:09:40,601 - bt.backtester_stable.BTRUN.config - WARNING - Fixture margin_symbol_info.json not found in any expected location
2025-05-11 20:09:40,601 - bt.backtester_stable.BTRUN.Util - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/margin_symbol_info.json (Broker: angel)
2025-05-11 20:09:40,601 - bt.backtester_stable.BTRUN.Util - INFO - Pre-BT functions complete.
2025-05-11 20:09:40,601 - __main__ - INFO - Using new Excel input format (PortfolioSetting & StrategySetting)
2025-05-11 20:09:40,602 - __main__ - INFO - Reading PortfolioSetting & StrategySetting from /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-11 20:09:40,697 - __main__ - DEBUG - Loaded 1 PortfolioSetting rows, 21 StrategySetting rows
2025-05-11 20:09:40,697 - __main__ - INFO - Queueing portfolio: NIF0DTE
2025-05-11 20:09:40,697 - __main__ - DEBUG - Building portfolio request for portfolio: NIF0DTE
2025-05-11 20:09:40,699 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:09:40,699 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:09:40,699 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:09:40,699 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:09:40,699 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:09:40,699 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:09:40,699 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:09:40,700 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:09:40,700 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:09:40,700 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:09:40,700 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:09:40,700 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:09:40,700 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:09:40,700 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:09:40,791 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-11 20:09:40,791 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY CALL at 2025-05-11 09:16:00.791777 via method 'ATM'
2025-05-11 20:09:40,791 - bt.backtester_stable.BTRUN.Util - ERROR - Lot size not found for NIFTY in config.LOT_SIZE. Defaulting to 1 for leg quantity calculation.
2025-05-11 20:09:40,792 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-11 20:09:40,792 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY PUT at 2025-05-11 09:16:00.792105 via method 'ATM'
2025-05-11 20:09:40,792 - bt.backtester_stable.BTRUN.Util - ERROR - Lot size not found for NIFTY in config.LOT_SIZE. Defaulting to 1 for leg quantity calculation.
2025-05-11 20:09:40,792 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-11 20:09:40,792 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY CALL at 2025-05-11 09:16:00.792416 via method 'ATM'
2025-05-11 20:09:40,792 - bt.backtester_stable.BTRUN.Util - ERROR - Lot size not found for NIFTY in config.LOT_SIZE. Defaulting to 1 for leg quantity calculation.
2025-05-11 20:09:40,792 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-11 20:09:40,792 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY PUT at 2025-05-11 09:16:00.792716 via method 'ATM'
2025-05-11 20:09:40,792 - bt.backtester_stable.BTRUN.Util - ERROR - Lot size not found for NIFTY in config.LOT_SIZE. Defaulting to 1 for leg quantity calculation.
2025-05-11 20:09:40,792 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:09:40,793 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:09:40,793 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:09:40,793 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:09:40,793 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:09:40,793 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:09:40,793 - __main__ - DEBUG - Built request for NIF0DTE with 1 strategies
2025-05-11 20:09:40,793 - __main__ - DEBUG - First strategy sample: {"id": "", "name": "RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL", "evaluator": "Tbs", "type": "STRATEGYTYPE.INTRADAY", "index": "NSEINDEX.NIFTY", "underlying": "CASH", "entry_time": 33360, "exit_time": 43200, "last_entry_time": 43200, "strike_selection_time": 33360, "multiplier": 1, "is_tick_bt": false, "legs": [{"id": "1", "option_type": "OPTIONTYPE.CALL", "side": "SIDE.SELL", "expiry_type": "EXPIRYTYPE.WEEKLY", "strike_selection": {"type": "BY_ATM_STRIKE", "value": 0.0}, "quantity": 1, "multipli
2025-05-11 20:09:40,793 - __main__ - INFO - Executing 1 portfolios sequentially
2025-05-11 20:09:40,794 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting BTRunPortfolio_GPU.py - Debugging enabled
2025-05-11 20:09:40,794 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Current working directory: /srv/samba/shared
2025-05-11 20:09:40,794 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-11 20:09:40,794 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Importing BTRUN modules...
2025-05-11 20:09:40,794 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Config module imported from: /srv/samba/shared/bt/backtester_stable/BTRUN/config.py
2025-05-11 20:09:40,794 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - HeavyDB settings - Host: **************, Database: heavydb
2025-05-11 20:09:40,794 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - GPU enabled: True
2025-05-11 20:09:40,794 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Successfully imported heavydb_helpers module
2025-05-11 20:09:40,794 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting single backtest for portfolio
2025-05-11 20:09:40,794 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Using HeavyDB for data access
2025-05-11 20:09:40,795 - bt.backtester_stable.BTRUN.runtime - INFO - Fetching trades from local HeavyDB for portfolio ''
2025-05-11 20:09:41,009 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:44,372 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 3.363s, returned 563 rows
2025-05-11 20:09:44,413 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Cached 563 distinct trade dates
2025-05-11 20:09:44,419 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Union query failed for 2025-04-01 – falling back to per-leg: entry_time
2025-05-11 20:09:44,634 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:44,638 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:44,638 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-01'
      AND en.expiry_date = DATE '2025-04-03'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:44,643 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:44,857 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:44,860 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:44,861 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-01'
      AND ex.expiry_date = DATE '2025-04-03'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:44,864 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:45,078 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:45,081 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:45,081 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-01'
      AND en.expiry_date = DATE '2025-04-03'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:45,086 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:45,302 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:45,306 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:45,306 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-01'
      AND ex.expiry_date = DATE '2025-04-03'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:45,310 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:45,525 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:45,565 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:45,565 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-01'
      AND en.expiry_date = DATE '2025-04-03'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:45,569 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:45,783 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:45,786 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:45,786 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-01'
      AND ex.expiry_date = DATE '2025-04-03'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:45,791 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:46,005 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:46,008 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:46,009 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-01'
      AND en.expiry_date = DATE '2025-04-03'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:46,013 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:46,229 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:46,232 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:46,232 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-01'
      AND ex.expiry_date = DATE '2025-04-03'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:46,237 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:46,238 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Union query failed for 2025-04-02 – falling back to per-leg: entry_time
2025-05-11 20:09:46,451 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:46,454 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:46,454 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-02'
      AND en.expiry_date = DATE '2025-04-03'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:46,459 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:46,674 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:46,677 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:46,677 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-02'
      AND ex.expiry_date = DATE '2025-04-03'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:46,681 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:46,895 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:46,898 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:46,899 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-02'
      AND en.expiry_date = DATE '2025-04-03'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:46,903 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:47,117 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:47,120 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:47,121 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-02'
      AND ex.expiry_date = DATE '2025-04-03'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:47,125 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:47,340 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:47,343 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:47,343 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-02'
      AND en.expiry_date = DATE '2025-04-03'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:47,347 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:47,562 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:47,564 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:47,565 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-02'
      AND ex.expiry_date = DATE '2025-04-03'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:47,569 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:47,784 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:47,787 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:47,787 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-02'
      AND en.expiry_date = DATE '2025-04-03'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:47,796 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:48,011 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:48,014 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:48,014 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-02'
      AND ex.expiry_date = DATE '2025-04-03'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:48,018 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:48,019 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Union query failed for 2025-04-03 – falling back to per-leg: entry_time
2025-05-11 20:09:48,233 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:48,236 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:48,236 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-03'
      AND en.expiry_date = DATE '2025-04-03'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:48,240 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:48,454 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:48,457 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:48,457 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-03'
      AND ex.expiry_date = DATE '2025-04-03'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:48,461 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:48,675 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:48,678 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:48,678 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-03'
      AND en.expiry_date = DATE '2025-04-03'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:48,682 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:48,897 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:48,900 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:48,900 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-03'
      AND ex.expiry_date = DATE '2025-04-03'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:48,905 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:49,118 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:49,121 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:49,121 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-03'
      AND en.expiry_date = DATE '2025-04-03'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:49,125 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:49,341 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:49,344 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:49,344 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-03'
      AND ex.expiry_date = DATE '2025-04-03'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:49,348 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:49,562 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:49,565 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:49,565 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-03'
      AND en.expiry_date = DATE '2025-04-03'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:49,569 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:49,783 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:49,786 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:49,786 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-03'
      AND ex.expiry_date = DATE '2025-04-03'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:49,790 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:49,791 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Union query failed for 2025-04-04 – falling back to per-leg: entry_time
2025-05-11 20:09:50,006 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:50,008 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:50,009 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-04'
      AND en.expiry_date = DATE '2025-04-10'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:50,013 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:50,233 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:50,236 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:50,236 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-04'
      AND ex.expiry_date = DATE '2025-04-10'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:50,240 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:50,463 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:50,466 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:50,467 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-04'
      AND en.expiry_date = DATE '2025-04-10'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:50,471 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:50,745 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:50,785 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:50,785 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-04'
      AND ex.expiry_date = DATE '2025-04-10'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:50,789 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:51,003 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:51,006 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:51,006 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-04'
      AND en.expiry_date = DATE '2025-04-10'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:51,010 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:51,225 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:51,228 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:51,228 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-04'
      AND ex.expiry_date = DATE '2025-04-10'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:51,232 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:51,446 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:51,449 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:51,449 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-04'
      AND en.expiry_date = DATE '2025-04-10'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:51,453 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:51,667 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:51,670 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:51,670 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-04'
      AND ex.expiry_date = DATE '2025-04-10'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:51,674 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:51,675 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Union query failed for 2025-04-07 – falling back to per-leg: entry_time
2025-05-11 20:09:51,889 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:51,892 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:51,893 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-07'
      AND en.expiry_date = DATE '2025-04-10'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:51,897 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:52,110 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:52,113 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:52,113 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-07'
      AND ex.expiry_date = DATE '2025-04-10'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:52,117 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:52,335 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:52,338 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:52,338 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-07'
      AND en.expiry_date = DATE '2025-04-10'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:52,342 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:52,565 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:52,568 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:52,568 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-07'
      AND ex.expiry_date = DATE '2025-04-10'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:52,572 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:52,786 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:52,789 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:52,789 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-07'
      AND en.expiry_date = DATE '2025-04-10'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:52,793 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:53,007 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:53,011 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:53,011 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-07'
      AND ex.expiry_date = DATE '2025-04-10'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:53,015 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:53,230 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:53,233 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:53,233 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-07'
      AND en.expiry_date = DATE '2025-04-10'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:53,237 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:53,451 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:53,454 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:53,455 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-07'
      AND ex.expiry_date = DATE '2025-04-10'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:53,466 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:53,467 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Union query failed for 2025-04-08 – falling back to per-leg: entry_time
2025-05-11 20:09:53,679 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:53,681 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:53,682 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-08'
      AND en.expiry_date = DATE '2025-04-10'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:53,686 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:53,899 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:53,902 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:53,902 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-08'
      AND ex.expiry_date = DATE '2025-04-10'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:53,906 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:54,121 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:54,124 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:54,124 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-08'
      AND en.expiry_date = DATE '2025-04-10'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:54,128 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:54,345 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:54,348 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:54,348 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-08'
      AND ex.expiry_date = DATE '2025-04-10'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:54,352 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:54,566 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:54,569 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:54,569 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-08'
      AND en.expiry_date = DATE '2025-04-10'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:54,573 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:54,788 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:54,790 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:54,791 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-08'
      AND ex.expiry_date = DATE '2025-04-10'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:54,795 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:55,009 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:55,012 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:55,012 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-08'
      AND en.expiry_date = DATE '2025-04-10'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:55,016 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:55,230 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:55,233 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:55,233 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-08'
      AND ex.expiry_date = DATE '2025-04-10'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:55,237 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:55,238 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Union query failed for 2025-04-09 – falling back to per-leg: entry_time
2025-05-11 20:09:55,451 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:55,454 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:55,454 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-09'
      AND en.expiry_date = DATE '2025-04-10'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:55,459 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:55,673 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:55,676 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:55,676 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-09'
      AND ex.expiry_date = DATE '2025-04-10'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:55,680 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:55,894 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:55,933 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:55,934 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-09'
      AND en.expiry_date = DATE '2025-04-10'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:55,938 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:56,153 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:56,156 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:56,156 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-09'
      AND ex.expiry_date = DATE '2025-04-10'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:56,160 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:56,374 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:56,377 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:56,377 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-09'
      AND en.expiry_date = DATE '2025-04-10'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:56,381 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:56,595 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:56,598 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:56,598 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-09'
      AND ex.expiry_date = DATE '2025-04-10'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:56,602 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:56,817 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:56,820 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:56,820 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-09'
      AND en.expiry_date = DATE '2025-04-10'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:56,824 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:57,037 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:57,040 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:57,040 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-09'
      AND ex.expiry_date = DATE '2025-04-10'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:57,044 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:57,045 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Union query failed for 2025-04-11 – falling back to per-leg: entry_time
2025-05-11 20:09:57,259 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:57,262 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:57,262 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-11'
      AND en.expiry_date = DATE '2025-04-17'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:57,266 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:57,481 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:57,485 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:57,485 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-11'
      AND ex.expiry_date = DATE '2025-04-17'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:57,489 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:57,703 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:57,706 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:57,707 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-11'
      AND en.expiry_date = DATE '2025-04-17'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:57,711 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:57,925 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:57,928 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:57,929 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-11'
      AND ex.expiry_date = DATE '2025-04-17'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:57,933 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:58,146 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:58,149 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:58,149 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-11'
      AND en.expiry_date = DATE '2025-04-17'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:58,153 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:58,367 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:58,370 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:58,371 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-11'
      AND ex.expiry_date = DATE '2025-04-17'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:58,375 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:58,589 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:58,592 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:58,592 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain en
    WHERE en.trade_date = DATE '2025-04-11'
      AND en.expiry_date = DATE '2025-04-17'
      AND ABS(en.strike - en.underlying_price) = 0
      AND en.trade_time >= '091500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY en.leg_id ORDER BY en.trade_time ASC) = 1
2025-05-11 20:09:58,596 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:58,810 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:09:58,813 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    
2025-05-11 20:09:58,813 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT * FROM nifty_option_chain ex
    WHERE ex.trade_date = DATE '2025-04-11'
      AND ex.expiry_date = DATE '2025-04-17'
      AND ABS(ex.strike - ex.underlying_price) = 0
      AND ex.trade_time <= '151500'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY ex.leg_id ORDER BY ex.trade_time DESC) = 1
2025-05-11 20:09:58,817 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 25, in __getattr__
    return self[item]
KeyError: 'entry_time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 709, in get_trades_for_portfolio
    entry_sql_union = _build_union_sql(legs, trade_date_iso, is_entry=True, alias_prefix="en")
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 829, in _build_union_sql
    time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/models/leg.py", line 27, in __getattr__
    raise AttributeError(item) from exc
AttributeError: entry_time

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='SQL Error: Encountered "QUALIFY" at line 6, column 5.\nWas expecting one of:\n    <EOF> \n    "EXCEPT" ...\n    "FETCH" ...\n    "GROUP" ...\n    "HAVING" ...\n    "INTERSECT" ...\n    "LIMIT" ...\n    "OFFSET" ...\n    "ORDER" ...\n    "MINUS" ...\n    "UESCAPE" ...\n    "UNION" ...\n    "WINDOW" ...\n    <QUOTED_STRING> ...\n    "." ...\n    "NOT" ...\n    "IN" ...\n    "<" ...\n    "<=" ...\n    ">" ...\n    ">=" ...\n    "=" ...\n    "<>" ...\n    "!=" ...\n    "BETWEEN" ...\n    "LIKE" ...\n    "SIMILAR" ...\n    "+" ...\n    "-" ...\n    "*" ...\n    "/" ...\n    "%" ...\n    "||" ...\n    "AND" ...\n    "OR" ...\n    "IS" ...\n    "MEMBER" ...\n    "SUBMULTISET" ...\n    "CONTAINS" ...\n    "OVERLAPS" ...\n    "EQUALS" ...\n    "PRECEDES" ...\n    "SUCCEEDS" ...\n    "IMMEDIATELY" ...\n    "MULTISET" ...\n    "[" ...\n    "FORMAT" ...\n    ')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 138, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: Encountered "QUALIFY" at line 6, column 5.
Was expecting one of:
    <EOF> 
    "EXCEPT" ...
    "FETCH" ...
    "GROUP" ...
    "HAVING" ...
    "INTERSECT" ...
    "LIMIT" ...
    "OFFSET" ...
    "ORDER" ...
    "MINUS" ...
    "UESCAPE" ...
    "UNION" ...
    "WINDOW" ...
    <QUOTED_STRING> ...
    "." ...
    "NOT" ...
    "IN" ...
    "<" ...
    "<=" ...
    ">" ...
    ">=" ...
    "=" ...
    "<>" ...
    "!=" ...
    "BETWEEN" ...
    "LIKE" ...
    "SIMILAR" ...
    "+" ...
    "-" ...
    "*" ...
    "/" ...
    "%" ...
    "||" ...
    "AND" ...
    "OR" ...
    "IS" ...
    "MEMBER" ...
    "SUBMULTISET" ...
    "CONTAINS" ...
    "OVERLAPS" ...
    "EQUALS" ...
    "PRECEDES" ...
    "SUCCEEDS" ...
    "IMMEDIATELY" ...
    "MULTISET" ...
    "[" ...
    "FORMAT" ...
    

2025-05-11 20:09:58,818 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Generated 0 trade records via HeavyDB snapshot across 8 trading days
2025-05-11 20:09:58,818 - bt.backtester_stable.BTRUN.builders - INFO - Parsing backtest response...
2025-05-11 20:09:58,818 - bt.backtester_stable.BTRUN.builders - WARNING - No trades data in backtest response or invalid format.
2025-05-11 20:09:58,819 - bt.backtester_stable.BTRUN.runtime - WARNING - No trades found in backtest response
2025-05-11 20:09:58,820 - bt.backtester_stable.BTRUN.runtime - INFO - Saving Excel output to backtest_output/NIF0DTE_11052025_200940.xlsx
2025-05-11 20:09:58,820 - bt.backtester_stable.BTRUN.runtime - WARNING - Empty metrics DataFrame - not saving Excel output
2025-05-11 20:09:58,820 - bt.backtester_stable.BTRUN.runtime - INFO - Saving JSON output to backtest_output/NIF0DTE_11052025_200940.json
2025-05-11 20:09:58,820 - bt.backtester_stable.BTRUN.runtime - WARNING - Empty metrics DataFrame - not saving JSON output
2025-05-11 20:09:58,821 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Backtest completed in 18.03 seconds
2025-05-11 20:09:58,821 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Portfolio backtest completed successfully with 0 trades
2025-05-11 20:09:58,821 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Saved json file to backtest_output/NIF0DTE_11052025_200940.json
2025-05-11 20:09:58,821 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Saved excel file to backtest_output/NIF0DTE_11052025_200940.xlsx
