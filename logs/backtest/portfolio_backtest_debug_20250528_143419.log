2025-05-28 14:34:19,069 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-28 14:34:19,070 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-28 14:34:19,070 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/../models', '/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages', '/srv/samba/shared']
2025-05-28 14:34:19,070 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-28 14:34:19,070 - __main__ - DEBUG - GPU enabled: False
2025-05-28 14:34:19,072 - __main__ - INFO - GPU acceleration enabled: False
2025-05-28 14:34:19,072 - __main__ - INFO - HeavyDB integration available (Host: 127.0.0.1)
2025-05-28 14:34:19,297 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-28 14:34:19,297 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-28 14:34:19,298 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-28 14:34:19,298 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-28 14:34:19,298 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-28 14:34:19,298 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-28 14:34:19,303 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-28 14:34:19,304 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-28 14:34:19,304 - backtester_stable.BTRUN.core.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-28 14:34:19,304 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-28 14:34:19,304 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-28 14:34:19,304 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-28 14:34:19,305 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-28 14:34:19,305 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-28 14:34:19,305 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-28 14:34:19,305 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-28 14:34:19,305 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-28 14:34:19,305 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-28 14:34:19,428 - __main__ - INFO - Successfully parsed 1 portfolio(s) using modern parser.
2025-05-28 14:34:19,429 - __main__ - INFO - Queueing portfolio: NIF0DTE (from modern_portfolio_parser)
2025-05-28 14:34:19,429 - __main__ - INFO - Executing 1 portfolios sequentially (multiprocessing temporarily disabled for debugging)
2025-05-28 14:34:19,429 - __main__ - INFO - Processing payload 1/1: NIF0DTE
2025-05-28 14:34:19,429 - backtester_stable.BTRUN.core.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'NIF0DTE'
2025-05-28 14:34:19,648 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-28 14:34:19,681 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.031s, returned 563 rows. Query: SELECT SUBSTRING('SELECT DISTINCT trade_date FROM nifty_option_chain', 1, 200) ...
2025-05-28 14:34:19,721 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - Cached 563 distinct trade dates
2025-05-28 14:34:19,721 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] portfolio_model_dict keys: ['portfolio_name', 'start_date', 'end_date', 'slippage_percent', 'margin_multiplier', 'is_tick_bt', 'strategies', 'extra_params']
2025-05-28 14:34:19,722 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 1 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-28 14:34:19,722 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-01-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-28 14:34:19,722 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-01-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-28 14:34:19,761 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.038s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-01-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-28 14:34:19,796 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.035s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-01-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-28 14:34:19,797 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Query results: entry_df size=1, exit_df size=1
2025-05-28 14:34:19,798 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 1 of 3
2025-05-28 14:34:19,798 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-28 14:34:19,798 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 2 of 3
2025-05-28 14:34:19,798 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-28 14:34:19,798 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 3 of 3
2025-05-28 14:34:19,798 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-28 14:34:19,798 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 1, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-28 14:34:19,798 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2024, 1, 3), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2024, 1, 4), 'index_name': 'NIFTY', 'spot': 21615.75, 'atm_strike': 21600.0, 'strike': 21600.0, 'dte': 1, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY04JAN2421600CE', 'ce_open': 88.5, 'ce_high': 89.95, 'ce_low': 87.0, 'ce_close': 89.7, 'ce_volume': 1328300, 'ce_oi': 3474200, 'ce_coi': 0, 'ce_iv': 13.19, 'ce_delta': 0.55, 'ce_gamma': 0.0021, 'ce_theta': -26.25, 'ce_vega': 5.73, 'ce_rho': 53.6298, 'pe_symbol': 'NIFTY04JAN2421600PE', 'pe_open': 77.2, 'pe_high': 77.2, 'pe_low': 74.0, 'pe_close': 74.95, 'pe_volume': 2019050, 'pe_oi': 5942000, 'pe_coi': 0, 'pe_iv': 15.06, 'pe_delta': -0.45, 'pe_gamma': 0.0018, 'pe_theta': -23.6, 'pe_vega': 5.75, 'pe_rho': -44.3187, 'future_open': 21706.1, 'future_high': 21710.0, 'future_low': 21705.05, 'future_close': 21708.0, 'future_volume': 47300, 'future_oi': 11836050, 'future_coi': 0}
2025-05-28 14:34:19,798 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2024, 1, 3), 'trade_time': 120000, 'expiry_date': datetime.date(2024, 1, 4), 'index_name': 'NIFTY', 'spot': 21572.3, 'atm_strike': 21600.0, 'strike': 21600.0, 'dte': 1, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY04JAN2421600CE', 'ce_open': 66.84, 'ce_high': 67.09, 'ce_low': 64.55, 'ce_close': 66.25, 'ce_volume': 751150, 'ce_oi': 11196100, 'ce_coi': 0, 'ce_iv': 12.96, 'ce_delta': 0.45, 'ce_gamma': 0.0021, 'ce_theta': -25.39, 'ce_vega': 5.75, 'ce_rho': 44.766, 'pe_symbol': 'NIFTY04JAN2421600PE', 'pe_open': 66.95, 'pe_high': 68.9, 'pe_low': 66.3, 'pe_close': 66.84, 'pe_volume': 728900, 'pe_oi': 10438850, 'pe_coi': 0, 'pe_iv': 9.93, 'pe_delta': -0.55, 'pe_gamma': 0.0028, 'pe_theta': -14.06, 'pe_vega': 5.73, 'pe_rho': -53.6056, 'future_open': 21680.0, 'future_high': 21683.0, 'future_low': 21677.3, 'future_close': 21679.05, 'future_volume': 4400, 'future_oi': 11893500, 'future_coi': 3700, 'exit_reason': 'Exit Time Hit'}
2025-05-28 14:34:19,798 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=CALL, Transaction=SELL, Lots=1
2025-05-28 14:34:19,799 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-28 14:34:19,799 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-28 14:34:19,799 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-28 14:34:19,799 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: 1172.5000000000002, PnL (Slippage): 1172.5000000000002, Net PnL: 1172.5000000000002
2025-05-28 14:34:19,799 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-28 14:34:19,799 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '1', 'entry_date': '2024-01-03', 'entry_time': '09:16:00', 'entry_day': 'Wednesday', 'exit_date': '2024-01-03', 'exit_time': '12:00:00', 'exit_day': 'Wednesday', 'symbol': 'NIFTY', 'expiry': '2024-01-04', 'strike': 21600, 'instrument_type': 'CE', 'side': 'SELL', 'filled_quantity': 50.0, 'entry_price': 89.7, 'exit_price': 66.25, 'points': 23.450000000000003, 'pointsAfterSlippage': 23.450000000000003, 'pnl': 1172.5000000000002, 'pnlAfterSlippage': 1172.5000000000002, 'expenses': 0.0, 'netPnlAfterExpenses': 1172.5000000000002, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 21615.75, 'index_exit_price': 21572.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2024-01-03 09:16:00', 'exit_datetime': '2024-01-03 12:00:00'}
2025-05-28 14:34:19,799 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 1
2025-05-28 14:34:19,799 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 2 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-28 14:34:19,799 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-01-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-28 14:34:19,799 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-01-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-28 14:34:19,841 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.041s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-01-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-28 14:34:19,881 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.040s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-01-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-28 14:34:19,882 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Query results: entry_df size=1, exit_df size=1
2025-05-28 14:34:19,883 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 1 of 3
2025-05-28 14:34:19,883 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-28 14:34:19,883 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 2 of 3
2025-05-28 14:34:19,883 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-28 14:34:19,883 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 3 of 3
2025-05-28 14:34:19,883 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-28 14:34:19,884 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 2, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-28 14:34:19,884 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2024, 1, 3), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2024, 1, 4), 'index_name': 'NIFTY', 'spot': 21615.75, 'atm_strike': 21600.0, 'strike': 21600.0, 'dte': 1, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY04JAN2421600CE', 'ce_open': 88.5, 'ce_high': 89.95, 'ce_low': 87.0, 'ce_close': 89.7, 'ce_volume': 1328300, 'ce_oi': 3474200, 'ce_coi': 0, 'ce_iv': 13.19, 'ce_delta': 0.55, 'ce_gamma': 0.0021, 'ce_theta': -26.25, 'ce_vega': 5.73, 'ce_rho': 53.6298, 'pe_symbol': 'NIFTY04JAN2421600PE', 'pe_open': 77.2, 'pe_high': 77.2, 'pe_low': 74.0, 'pe_close': 74.95, 'pe_volume': 2019050, 'pe_oi': 5942000, 'pe_coi': 0, 'pe_iv': 15.06, 'pe_delta': -0.45, 'pe_gamma': 0.0018, 'pe_theta': -23.6, 'pe_vega': 5.75, 'pe_rho': -44.3187, 'future_open': 21706.1, 'future_high': 21710.0, 'future_low': 21705.05, 'future_close': 21708.0, 'future_volume': 47300, 'future_oi': 11836050, 'future_coi': 0}
2025-05-28 14:34:19,884 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2024, 1, 3), 'trade_time': 120000, 'expiry_date': datetime.date(2024, 1, 4), 'index_name': 'NIFTY', 'spot': 21572.3, 'atm_strike': 21600.0, 'strike': 21600.0, 'dte': 1, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY04JAN2421600CE', 'ce_open': 66.84, 'ce_high': 67.09, 'ce_low': 64.55, 'ce_close': 66.25, 'ce_volume': 751150, 'ce_oi': 11196100, 'ce_coi': 0, 'ce_iv': 12.96, 'ce_delta': 0.45, 'ce_gamma': 0.0021, 'ce_theta': -25.39, 'ce_vega': 5.75, 'ce_rho': 44.766, 'pe_symbol': 'NIFTY04JAN2421600PE', 'pe_open': 66.95, 'pe_high': 68.9, 'pe_low': 66.3, 'pe_close': 66.84, 'pe_volume': 728900, 'pe_oi': 10438850, 'pe_coi': 0, 'pe_iv': 9.93, 'pe_delta': -0.55, 'pe_gamma': 0.0028, 'pe_theta': -14.06, 'pe_vega': 5.73, 'pe_rho': -53.6056, 'future_open': 21680.0, 'future_high': 21683.0, 'future_low': 21677.3, 'future_close': 21679.05, 'future_volume': 4400, 'future_oi': 11893500, 'future_coi': 3700, 'exit_reason': 'Exit Time Hit'}
2025-05-28 14:34:19,884 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=SELL, Lots=1
2025-05-28 14:34:19,884 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-28 14:34:19,884 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-28 14:34:19,885 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-28 14:34:19,885 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: 405.5, PnL (Slippage): 405.5, Net PnL: 405.5
2025-05-28 14:34:19,885 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-28 14:34:19,885 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '2', 'entry_date': '2024-01-03', 'entry_time': '09:16:00', 'entry_day': 'Wednesday', 'exit_date': '2024-01-03', 'exit_time': '12:00:00', 'exit_day': 'Wednesday', 'symbol': 'NIFTY', 'expiry': '2024-01-04', 'strike': 21600, 'instrument_type': 'PE', 'side': 'SELL', 'filled_quantity': 50.0, 'entry_price': 74.95, 'exit_price': 66.84, 'points': 8.11, 'pointsAfterSlippage': 8.11, 'pnl': 405.5, 'pnlAfterSlippage': 405.5, 'expenses': 0.0, 'netPnlAfterExpenses': 405.5, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 21615.75, 'index_exit_price': 21572.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2024-01-03 09:16:00', 'exit_datetime': '2024-01-03 12:00:00'}
2025-05-28 14:34:19,885 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 2
2025-05-28 14:34:19,885 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 3 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-28 14:34:19,885 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-01-03' AND oc.expiry_bucket = 'CW' AND oc.strike = (oc.atm_strike + (2.0 * 50)) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-28 14:34:19,885 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-01-03' AND oc.expiry_bucket = 'CW' AND oc.strike = (oc.atm_strike + (2.0 * 50)) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-28 14:34:19,928 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.042s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-01-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike + (2.0 * 50)) AND oc.ce_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-28 14:34:19,974 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.045s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-01-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike + (2.0 * 50)) AND oc.ce_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-28 14:34:19,974 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Query results: entry_df size=1, exit_df size=1
2025-05-28 14:34:19,976 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 1 of 3
2025-05-28 14:34:19,976 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-28 14:34:19,976 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 2 of 3
2025-05-28 14:34:19,976 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-28 14:34:19,976 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 3 of 3
2025-05-28 14:34:19,976 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-28 14:34:19,977 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 3, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-28 14:34:19,977 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2024, 1, 3), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2024, 1, 4), 'index_name': 'NIFTY', 'spot': 21615.75, 'atm_strike': 21600.0, 'strike': 21700.0, 'dte': 1, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'OTM2', 'put_strike_type': 'ITM2', 'ce_symbol': 'NIFTY04JAN2421700CE', 'ce_open': 44.5, 'ce_high': 45.45, 'ce_low': 43.05, 'ce_close': 45.0, 'ce_volume': 1292500, 'ce_oi': 9159300, 'ce_coi': 0, 'ce_iv': 13.2, 'ce_delta': 0.37, 'ce_gamma': 0.0019, 'ce_theta': -23.64, 'ce_vega': 5.38, 'ce_rho': 33.9451, 'pe_symbol': 'NIFTY04JAN2421700PE', 'pe_open': 133.0, 'pe_high': 133.19, 'pe_low': 129.19, 'pe_close': 130.4, 'pe_volume': 1135600, 'pe_oi': 4799150, 'pe_coi': 0, 'pe_iv': 15.21, 'pe_delta': -0.63, 'pe_gamma': 0.0017, 'pe_theta': -21.57, 'pe_vega': 5.48, 'pe_rho': -61.946, 'future_open': 21706.1, 'future_high': 21710.0, 'future_low': 21705.05, 'future_close': 21708.0, 'future_volume': 47300, 'future_oi': 11836050, 'future_coi': 0}
2025-05-28 14:34:19,977 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2024, 1, 3), 'trade_time': 120000, 'expiry_date': datetime.date(2024, 1, 4), 'index_name': 'NIFTY', 'spot': 21572.3, 'atm_strike': 21600.0, 'strike': 21700.0, 'dte': 1, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'OTM2', 'put_strike_type': 'ITM2', 'ce_symbol': 'NIFTY04JAN2421700CE', 'ce_open': 26.7, 'ce_high': 26.85, 'ce_low': 25.7, 'ce_close': 26.2, 'ce_volume': 478850, 'ce_oi': 13555800, 'ce_coi': 0, 'ce_iv': 12.07, 'ce_delta': 0.16, 'ce_gamma': 0.0018, 'ce_theta': -18.42, 'ce_vega': 4.62, 'ce_rho': 24.3548, 'pe_symbol': 'NIFTY04JAN2421700PE', 'pe_open': 127.55, 'pe_high': 130.0, 'pe_low': 126.3, 'pe_close': 127.45, 'pe_volume': 215500, 'pe_oi': 3183950, 'pe_coi': 0, 'pe_iv': 8.04, 'pe_delta': -0.84, 'pe_gamma': 0.0021, 'pe_theta': -3.47, 'pe_vega': 3.48, 'pe_rho': -82.5441, 'future_open': 21680.0, 'future_high': 21683.0, 'future_low': 21677.3, 'future_close': 21679.05, 'future_volume': 4400, 'future_oi': 11893500, 'future_coi': 3700, 'exit_reason': 'Exit Time Hit'}
2025-05-28 14:34:19,977 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=CALL, Transaction=BUY, Lots=1
2025-05-28 14:34:19,978 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-28 14:34:19,978 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-28 14:34:19,978 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-28 14:34:19,978 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -940.0, PnL (Slippage): -940.0, Net PnL: -940.0
2025-05-28 14:34:19,979 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-28 14:34:19,979 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '3', 'entry_date': '2024-01-03', 'entry_time': '09:16:00', 'entry_day': 'Wednesday', 'exit_date': '2024-01-03', 'exit_time': '12:00:00', 'exit_day': 'Wednesday', 'symbol': 'NIFTY', 'expiry': '2024-01-04', 'strike': 21700, 'instrument_type': 'CE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 45.0, 'exit_price': 26.2, 'points': -18.8, 'pointsAfterSlippage': -18.8, 'pnl': -940.0, 'pnlAfterSlippage': -940.0, 'expenses': 0.0, 'netPnlAfterExpenses': -940.0, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 21615.75, 'index_exit_price': 21572.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2024-01-03 09:16:00', 'exit_datetime': '2024-01-03 12:00:00'}
2025-05-28 14:34:19,979 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 3
2025-05-28 14:34:19,979 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 4 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-28 14:34:19,979 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-01-03' AND oc.expiry_bucket = 'CW' AND oc.strike = (oc.atm_strike - (2.0 * 50)) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-28 14:34:19,979 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-01-03' AND oc.expiry_bucket = 'CW' AND oc.strike = (oc.atm_strike - (2.0 * 50)) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-28 14:34:20,023 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.043s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-01-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike - (2.0 * 50)) AND oc.pe_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-28 14:34:20,067 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.044s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-01-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = (oc.atm_strike - (2.0 * 50)) AND oc.pe_symbol IS NOT NULL AND oc.trade_time ', 1, 200) ...
2025-05-28 14:34:20,067 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Query results: entry_df size=1, exit_df size=1
2025-05-28 14:34:20,069 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 1 of 3
2025-05-28 14:34:20,069 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-28 14:34:20,069 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 2 of 3
2025-05-28 14:34:20,070 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-28 14:34:20,070 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 3 of 3
2025-05-28 14:34:20,070 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-28 14:34:20,070 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-28 14:34:20,070 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2024, 1, 3), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2024, 1, 4), 'index_name': 'NIFTY', 'spot': 21615.75, 'atm_strike': 21600.0, 'strike': 21500.0, 'dte': 1, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY04JAN2421500CE', 'ce_open': 152.6, 'ce_high': 155.3, 'ce_low': 151.8, 'ce_close': 154.35, 'ce_volume': 159000, 'ce_oi': 1485800, 'ce_coi': 0, 'ce_iv': 13.11, 'ce_delta': 0.72, 'ce_gamma': 0.0017, 'ce_theta': -22.87, 'ce_vega': 4.64, 'ce_rho': 72.0855, 'pe_symbol': 'NIFTY04JAN2421500PE', 'pe_open': 41.75, 'pe_high': 41.75, 'pe_low': 39.15, 'pe_close': 39.75, 'pe_volume': 988650, 'pe_oi': 6403900, 'pe_coi': 0, 'pe_iv': 15.37, 'pe_delta': -0.28, 'pe_gamma': 0.0015, 'pe_theta': -21.31, 'pe_vega': 4.92, 'pe_rho': -27.9001, 'future_open': 21706.1, 'future_high': 21710.0, 'future_low': 21705.05, 'future_close': 21708.0, 'future_volume': 47300, 'future_oi': 11836050, 'future_coi': 0}
2025-05-28 14:34:20,071 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2024, 1, 3), 'trade_time': 120000, 'expiry_date': datetime.date(2024, 1, 4), 'index_name': 'NIFTY', 'spot': 21572.3, 'atm_strike': 21600.0, 'strike': 21500.0, 'dte': 1, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY04JAN2421500CE', 'ce_open': 132.85, 'ce_high': 133.19, 'ce_low': 129.65, 'ce_close': 132.15, 'ce_volume': 203150, 'ce_oi': 3395250, 'ce_coi': 0, 'ce_iv': 14.73, 'ce_delta': 0.69, 'ce_gamma': 0.0017, 'ce_theta': -27.79, 'ce_vega': 5.35, 'ce_rho': 62.7576, 'pe_symbol': 'NIFTY04JAN2421500PE', 'pe_open': 33.2, 'pe_high': 34.1, 'pe_low': 32.75, 'pe_close': 33.15, 'pe_volume': 387450, 'pe_oi': 9387350, 'pe_coi': 0, 'pe_iv': 11.49, 'pe_delta': -0.31, 'pe_gamma': 0.0021, 'pe_theta': -15.98, 'pe_vega': 5.1, 'pe_rho': -30.2188, 'future_open': 21680.0, 'future_high': 21683.0, 'future_low': 21677.3, 'future_close': 21679.05, 'future_volume': 4400, 'future_oi': 11893500, 'future_coi': 3700, 'exit_reason': 'Exit Time Hit'}
2025-05-28 14:34:20,071 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-28 14:34:20,071 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-28 14:34:20,071 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-28 14:34:20,072 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-28 14:34:20,072 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -330.00000000000006, PnL (Slippage): -330.00000000000006, Net PnL: -330.00000000000006
2025-05-28 14:34:20,072 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-28 14:34:20,072 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2024-01-03', 'entry_time': '09:16:00', 'entry_day': 'Wednesday', 'exit_date': '2024-01-03', 'exit_time': '12:00:00', 'exit_day': 'Wednesday', 'symbol': 'NIFTY', 'expiry': '2024-01-04', 'strike': 21500, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 39.75, 'exit_price': 33.15, 'points': -6.600000000000001, 'pointsAfterSlippage': -6.600000000000001, 'pnl': -330.00000000000006, 'pnlAfterSlippage': -330.00000000000006, 'expenses': 0.0, 'netPnlAfterExpenses': -330.00000000000006, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 21615.75, 'index_exit_price': 21572.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2024-01-03 09:16:00', 'exit_datetime': '2024-01-03 12:00:00'}
2025-05-28 14:34:20,072 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Added trade record for leg 4
2025-05-28 14:34:20,072 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Number of trade records generated: 4
2025-05-28 14:34:20,073 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Generated 4 trade records via model-driven path
2025-05-28 14:34:20,073 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Calculating tick-by-tick P&L for Max Profit/Loss tracking
2025-05-28 14:34:20,073 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG Tick P&L: First trade fields: ['portfolio_name', 'strategy', 'leg_id', 'entry_date', 'entry_time', 'entry_day', 'exit_date', 'exit_time', 'exit_day', 'symbol', 'expiry', 'strike', 'instrument_type', 'side', 'filled_quantity', 'entry_price', 'exit_price', 'points', 'pointsAfterSlippage', 'pnl', 'pnlAfterSlippage', 'expenses', 'netPnlAfterExpenses', 're_entry_no', 'stop_loss_entry_number', 'take_profit_entry_number', 'reason', 'strategy_entry_number', 'index_entry_price', 'index_exit_price', 'max_profit', 'max_loss', 'entry_datetime', 'exit_datetime']
2025-05-28 14:34:20,073 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG Tick P&L: First trade sample: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '1', 'entry_date': '2024-01-03', 'entry_time': '09:16:00', 'entry_day': 'Wednesday', 'exit_date': '2024-01-03', 'exit_time': '12:00:00', 'exit_day': 'Wednesday', 'symbol': 'NIFTY', 'expiry': '2024-01-04', 'strike': 21600, 'instrument_type': 'CE', 'side': 'SELL', 'filled_quantity': 50.0, 'entry_price': 89.7, 'exit_price': 66.25, 'points': 23.450000000000003, 'pointsAfterSlippage': 23.450000000000003, 'pnl': 1172.5000000000002, 'pnlAfterSlippage': 1172.5000000000002, 'expenses': 0.0, 'netPnlAfterExpenses': 1172.5000000000002, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 21615.75, 'index_exit_price': 21572.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2024-01-03 09:16:00', 'exit_datetime': '2024-01-03 12:00:00'}
2025-05-28 14:34:20,108 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.035s, returned 375 rows. Query: SELECT SUBSTRING('SELECT DISTINCT EXTRACT(HOUR FROM trade_time) * 10000 + 
                                       EXTRACT(MINUTE FROM trade_time) * 100 + 
                                       EXTRACT(SECOND FROM trad', 1, 200) ...
2025-05-28 14:34:20,129 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,149 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,169 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,189 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,209 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,229 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,250 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,269 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,290 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,311 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,331 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,351 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,371 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,391 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,411 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,431 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,451 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,471 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,491 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,511 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,531 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,551 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,571 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,590 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,610 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,630 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,650 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,669 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,689 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,709 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,728 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,748 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,768 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,788 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,808 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,828 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,847 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,868 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,887 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,908 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,928 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,948 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,968 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:20,988 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,008 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,028 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,048 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,068 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,089 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,109 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,128 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,149 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,168 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,188 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,209 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,229 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,249 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,269 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,289 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,309 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,328 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,348 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,368 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,388 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,408 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,428 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,448 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,468 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,488 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,509 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,529 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,549 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,569 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,589 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,608 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,628 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,648 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,668 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,688 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,708 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,729 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,749 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,769 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,789 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,809 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,829 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,849 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,869 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,889 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,909 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,929 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,949 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,969 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:21,989 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,009 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,029 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,049 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,069 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,089 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,109 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,129 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,150 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,170 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,189 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,209 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,229 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,249 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,269 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,290 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,309 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,329 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,349 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,369 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,388 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,408 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,429 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,449 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,469 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,489 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,509 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,529 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,549 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,569 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,589 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,609 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,628 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,648 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,668 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,688 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,708 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,727 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,747 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,767 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,787 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,807 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,827 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,847 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,867 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,887 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,907 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,932 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.025s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,952 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,972 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:22,991 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,011 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,030 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,049 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,068 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,088 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,107 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,126 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,145 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,165 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,185 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,204 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,223 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,242 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,261 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,281 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,300 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,320 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,339 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,358 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,378 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,397 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,416 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,436 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,456 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,475 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,495 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,514 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,533 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,553 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,572 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,592 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,612 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,631 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,651 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,670 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,689 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,708 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,728 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,747 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,766 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,786 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,805 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,825 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,844 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,864 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,883 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,902 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,922 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,941 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,960 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,980 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:23,999 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,019 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,039 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,058 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,113 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.055s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,133 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,152 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,171 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,191 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,210 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,230 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,249 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,268 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,287 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,306 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,325 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,345 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,365 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,384 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,403 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,423 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,442 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,461 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,481 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,500 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,519 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,539 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,558 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,578 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,597 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,616 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,636 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,655 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,674 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,694 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,713 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,732 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,751 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,771 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,791 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,810 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,830 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,849 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,869 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,888 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,908 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,927 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,947 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,966 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:24,986 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,005 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,025 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,044 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,064 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,084 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,103 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,122 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,142 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,161 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,181 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,200 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,220 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,239 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,258 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,278 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,297 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,317 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,336 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,356 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,375 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,394 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,413 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,432 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,452 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,471 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,491 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,510 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,529 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,549 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,568 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,587 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,607 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,626 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,645 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,664 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,684 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,703 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,722 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,742 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,762 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,781 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,800 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,819 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,838 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,857 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,877 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,896 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,916 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,935 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,959 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.023s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,978 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:25,998 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,018 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,038 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,058 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,078 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,098 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,119 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,139 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,159 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,179 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,199 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,222 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.022s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,242 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,261 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,281 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,301 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,321 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,341 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,361 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,380 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,400 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,420 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,440 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,460 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,479 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,499 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,519 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,538 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,559 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,578 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,598 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,617 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,637 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,657 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,676 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,696 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,716 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,736 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,756 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,778 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.021s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,797 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,818 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,837 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,857 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,877 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,897 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,917 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,937 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,956 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,976 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:26,996 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,016 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,036 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,056 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,076 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,096 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,116 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,136 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,156 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,176 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,196 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,216 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,236 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,256 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,276 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,296 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,316 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,336 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,356 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,376 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,396 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,416 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,436 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,456 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,475 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,496 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,516 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,536 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,556 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,575 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,596 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,615 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,636 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,659 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.023s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,683 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.023s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,704 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,724 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,744 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,764 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,784 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,804 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,824 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,845 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,865 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,885 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,905 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,925 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,945 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,965 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:27,985 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,005 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,024 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,044 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,064 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,084 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,104 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,123 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,143 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,163 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,183 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,202 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,222 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,242 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,262 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,281 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,301 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,321 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,341 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,361 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,382 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,402 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,422 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,442 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,462 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,482 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,502 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,522 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,542 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,562 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,581 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,601 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,621 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,641 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,661 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,682 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,701 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,721 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,741 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,761 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,783 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.022s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,803 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,824 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,843 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,864 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,884 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,904 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,923 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,943 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,964 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:28,984 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,003 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,024 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,044 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,064 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,121 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.056s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,140 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,162 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.021s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,182 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,202 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,222 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,242 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,262 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,282 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,302 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,322 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,341 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,362 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,381 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,402 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,421 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,441 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,461 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,481 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,501 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,521 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,540 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,560 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,580 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,600 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,620 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,640 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,660 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,680 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,700 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,721 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,740 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,761 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,780 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,800 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,820 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,840 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,860 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,881 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,901 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,921 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,941 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,961 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:29,981 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,000 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,020 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,040 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,060 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,080 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,100 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,120 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,140 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,160 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,180 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,200 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,220 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,240 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,260 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,279 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,299 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,319 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,338 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,358 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,378 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,398 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,418 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,438 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,458 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,478 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,498 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,518 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,537 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,557 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,577 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,596 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,617 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,637 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,656 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,676 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,698 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.022s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,718 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,738 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,762 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.023s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,782 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,802 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,822 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,842 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,862 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,882 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,902 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,930 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.028s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,955 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.024s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:30,980 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.025s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,000 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,020 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,040 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,061 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,080 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,100 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,120 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,140 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,159 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,179 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,199 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,219 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,238 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,258 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,278 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,298 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,318 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,338 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,358 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,378 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,397 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,420 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.022s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,440 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,460 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,480 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,500 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,520 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,540 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,560 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,580 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,600 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,620 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,640 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,661 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.021s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,681 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,701 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,721 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,741 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,761 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,780 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,800 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,820 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,840 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,860 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,880 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,900 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,920 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,939 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,959 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,979 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:31,999 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,018 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,038 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,058 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,078 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,097 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,117 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,138 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.021s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,158 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,178 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,198 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,219 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,238 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,258 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,278 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,298 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,318 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,337 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,357 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,377 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,397 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,417 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,437 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,457 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,477 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,497 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,516 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,536 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,556 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,576 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,596 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,616 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,636 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,656 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,676 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,696 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,716 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,736 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,757 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,777 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,797 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,817 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,837 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,856 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,876 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,895 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,915 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,935 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,955 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:32,979 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.023s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:33,000 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:33,020 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:33,040 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:33,060 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:33,080 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:33,100 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:33,119 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:33,139 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:33,159 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:33,179 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:33,199 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:33,219 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:33,239 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:33,259 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:33,279 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:33,298 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT ce_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:33,318 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 14:34:33,341 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Calculated tick P&L for date 240103: 375 timestamps
2025-05-28 14:34:33,342 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Closed reusable HeavyDB connection from get_trades_for_portfolio
2025-05-28 14:34:33,379 - backtester_stable.BTRUN.builders - WARNING - utils.MARGIN_INFO not found or empty - margin requirements will be 0. Attempting to populate.
2025-05-28 14:34:33,379 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'margin_symbol_info.json' at /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-28 14:34:33,379 - backtester_stable.BTRUN.core.utils - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json (Broker: angel)
2025-05-28 14:34:33,380 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.MARGIN_INFO for angel.
2025-05-28 14:34:33,380 - backtester_stable.BTRUN.builders - ERROR - utils.MARGIN_INFO still empty after populate attempt. Margin req will be 0.
2025-05-28 14:34:33,386 - backtester_stable.BTRUN.builders - DEBUG - Strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL margin requirement: 0.00
2025-05-28 14:34:33,386 - backtester_stable.BTRUN.builders - INFO - Total portfolio margin requirement: 0.00
2025-05-28 14:34:33,387 - backtester_stable.BTRUN.builders - DEBUG - Converting tick PnL to daywise DataFrame.
2025-05-28 14:34:33,388 - backtester_stable.BTRUN.builders - INFO - Backtest response parsed.
2025-05-28 14:34:33,432 - backtester_stable.BTRUN.core.runtime - INFO - Generated metrics_df with 50 records for strategies: ['Combined' 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL']
2025-05-28 14:34:33,510 - backtester_stable.BTRUN.core.runtime - INFO - Saving Excel output to ./gpu_debug.xlsx
2025-05-28 14:34:33,510 - backtester_stable.BTRUN.core.runtime - DEBUG - [DEBUG save_backtest_results] save_excel=True, metrics_df is empty: False
2025-05-28 14:34:33,510 - backtester_stable.BTRUN.core.io - INFO - 2025-05-28 14:34:33.510216, Started writing stats to excel file: ./gpu_debug.xlsx
2025-05-28 14:34:33,510 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Temporarily switching to CPU-only mode.
2025-05-28 14:34:33,527 - backtester_stable.BTRUN.core.io - INFO - transaction_dfs keys: ['portfolio', 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL']
2025-05-28 14:34:33,527 - backtester_stable.BTRUN.core.io - INFO - transaction_dfs[portfolio] shape: (4, 39)
2025-05-28 14:34:33,570 - backtester_stable.BTRUN.core.io - INFO - transaction_dfs[portfolio] head:
  portfolio_name                                   strategy   leg_id  entry_date entry_time  ...  entryDate   exitDate entry_price_slippage exit_price_slippage     pnl
0        NIF0DTE  RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL  SELL_CE  2024-01-03   09:16:00  ... 2024-01-03 2024-01-03             89.61030            66.31625  1172.5
1        NIF0DTE  RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL  SELL_PE  2024-01-03   09:16:00  ... 2024-01-03 2024-01-03             74.87505            66.90684   405.5
2        NIF0DTE  RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL   BUY_CE  2024-01-03   09:16:00  ... 2024-01-03 2024-01-03             45.04500            26.17380  -940.0
3        NIF0DTE  RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL   BUY_PE  2024-01-03   09:16:00  ... 2024-01-03 2024-01-03             39.78975            33.11685  -330.0

[4 rows x 39 columns]
2025-05-28 14:34:33,570 - backtester_stable.BTRUN.core.io - INFO - transaction_dfs[RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL] shape: (4, 39)
2025-05-28 14:34:33,613 - backtester_stable.BTRUN.core.io - INFO - transaction_dfs[RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL] head:
  portfolio_name                                   strategy   leg_id  entry_date entry_time  ...  entryDate   exitDate entry_price_slippage exit_price_slippage     pnl
0        NIF0DTE  RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL  SELL_CE  2024-01-03   09:16:00  ... 2024-01-03 2024-01-03             89.61030            66.31625  1172.5
1        NIF0DTE  RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL  SELL_PE  2024-01-03   09:16:00  ... 2024-01-03 2024-01-03             74.87505            66.90684   405.5
2        NIF0DTE  RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL   BUY_CE  2024-01-03   09:16:00  ... 2024-01-03 2024-01-03             45.04500            26.17380  -940.0
3        NIF0DTE  RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL   BUY_PE  2024-01-03   09:16:00  ... 2024-01-03 2024-01-03             39.78975            33.11685  -330.0

[4 rows x 39 columns]
2025-05-28 14:34:33,627 - backtester_stable.BTRUN.core.io - INFO - Added PortfolioParameter sheet in legacy Head/Value format
2025-05-28 14:34:33,649 - backtester_stable.BTRUN.core.io - INFO - Added GeneralParameter sheet in legacy column order
2025-05-28 14:34:33,674 - backtester_stable.BTRUN.core.io - INFO - Added LegParameter sheet from input files
2025-05-28 14:34:33,676 - backtester_stable.BTRUN.core.io - INFO - Added Max Profit and Loss sheet
2025-05-28 14:34:33,701 - backtester_stable.BTRUN.core.io - INFO - Added PORTFOLIO Results sheet in legacy format
2025-05-28 14:34:33,701 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Restored GPU_ENABLED to: False
2025-05-28 14:34:33,701 - backtester_stable.BTRUN.core.runtime - ERROR - Error saving backtest results: name 'format_transaction_df' is not defined
2025-05-28 14:34:33,710 - backtester_stable.BTRUN.core.runtime - ERROR - Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/core/runtime.py", line 343, in run_full_backtest
    json_path, excel_path = save_backtest_results(
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/core/runtime.py", line 241, in save_backtest_results
    io.prepare_output_file(
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/core/io.py", line 286, in prepare_output_file
    portfolio_trans_df = format_transaction_df(portfolio_trans_df)
NameError: name 'format_transaction_df' is not defined

2025-05-28 14:34:33,715 - backtester_stable.BTRUN.core.io - ERROR - Text-to-speech failed: This means you probably do not have eSpeak or eSpeak-ng installed!
