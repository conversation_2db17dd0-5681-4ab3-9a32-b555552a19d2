2025-05-26 11:59:45,370 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-26 11:59:45,370 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-26 11:59:45,371 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/../models', '/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages', '/srv/samba/shared']
2025-05-26 11:59:45,371 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-26 11:59:45,371 - __main__ - DEBUG - GPU enabled: False
2025-05-26 11:59:45,372 - __main__ - INFO - GPU acceleration enabled: False
2025-05-26 11:59:45,373 - __main__ - INFO - HeavyDB integration available (Host: 127.0.0.1)
2025-05-26 11:59:45,592 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-26 11:59:45,592 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-26 11:59:45,593 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-26 11:59:45,593 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-26 11:59:45,593 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-26 11:59:45,593 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-26 11:59:45,599 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-26 11:59:45,600 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-26 11:59:45,600 - backtester_stable.BTRUN.core.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-26 11:59:45,600 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-26 11:59:45,600 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-26 11:59:45,600 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-26 11:59:45,600 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-26 11:59:45,600 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-26 11:59:45,600 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-26 11:59:45,600 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-26 11:59:45,600 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-26 11:59:45,600 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-26 11:59:45,725 - __main__ - INFO - Successfully parsed 1 portfolio(s) using modern parser.
2025-05-26 11:59:45,725 - __main__ - INFO - Queueing portfolio: NIF0DTE (from modern_portfolio_parser)
2025-05-26 11:59:45,726 - __main__ - INFO - Executing 1 portfolios sequentially (multiprocessing temporarily disabled for debugging)
2025-05-26 11:59:45,726 - __main__ - INFO - Processing payload 1/1: NIF0DTE
2025-05-26 11:59:45,726 - backtester_stable.BTRUN.core.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'NIF0DTE'
2025-05-26 11:59:45,981 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-26 11:59:46,010 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.028s, returned 563 rows. Query: SELECT SUBSTRING('SELECT DISTINCT trade_date FROM nifty_option_chain', 1, 200) ...
2025-05-26 11:59:46,053 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - Cached 563 distinct trade dates
2025-05-26 11:59:46,053 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] portfolio_model_dict keys: ['portfolio_name', 'start_date', 'end_date', 'slippage_percent', 'margin_multiplier', 'is_tick_bt', 'strategies', 'extra_params']
2025-05-26 11:59:46,054 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 1 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 11:59:46,054 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-26 11:59:46,054 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-26 11:59:46,095 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.040s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-26 11:59:46,133 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.038s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-26 11:59:46,133 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Query results: entry_df size=1, exit_df size=1
2025-05-26 11:59:46,134 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Got entry and exit data, proceeding...
2025-05-26 11:59:46,134 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
                        trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523450CE
ce_open                          102.85
ce_high                          106.65
ce_low                            97.85
ce_close                         100.75
ce_volume                       1015050
ce_oi                            978075
ce_coi                                0
ce_iv                             13.44
ce_delta                           0.48
ce_gamma                         0.0015
ce_theta                         -23.27
ce_vega                            7.95
ce_rho                          81.3115
pe_symbol           NIFTY03APR2523450PE
pe_open                           118.2
pe_high                           122.3
pe_low                            112.2
pe_close                          115.7
pe_volume                        877125
pe_oi                           1618950
pe_coi                                0
pe_iv                             13.74
pe_delta                          -0.52
pe_gamma                         0.0015
pe_theta                          -17.3
pe_vega                            7.95
pe_rho                         -88.3744
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-26 11:59:46,135 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23200.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                           123.6
ce_high                           124.3
ce_low                           119.25
ce_close                          124.2
ce_volume                        326775
ce_oi                           4374450
ce_coi                           193725
ce_iv                             14.99
ce_delta                           0.52
ce_gamma                         0.0013
ce_theta                         -25.57
ce_vega                            7.87
ce_rho                          86.2799
pe_symbol           NIFTY03APR2523200PE
pe_open                           100.2
pe_high                           105.2
pe_low                            99.55
pe_close                         100.75
pe_volume                        526875
pe_oi                           5030025
pe_coi                           101175
pe_iv                             13.55
pe_delta                          -0.48
pe_gamma                         0.0015
pe_theta                         -17.08
pe_vega                            7.87
pe_rho                         -81.3242
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-26 11:59:46,135 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-26 11:59:46,135 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-26 11:59:46,135 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-26 11:59:46,135 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Entering risk rules block
2025-05-26 11:59:46,136 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-26 11:59:46,136 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140661121026976
2025-05-26 11:59:46,136 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140661121026976, OptionType.PUT ID: 140661121027088
2025-05-26 11:59:46,136 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-26 11:59:46,136 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-26 11:59:46,136 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-26 11:59:46,138 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-26 11:59:46,432 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.294s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-26 11:59:46,434 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-26 11:59:46,435 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-26 11:59:46,492 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-26 11:59:46,496 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 1 - Tick data for specific strike 23450.0 is EMPTY after filtering windowed data.
2025-05-26 11:59:46,496 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Starting risk rules loop, has 3 rules
2025-05-26 11:59:46,496 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 1 of 3
2025-05-26 11:59:46,496 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-26 11:59:46,500 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - evaluate_risk_rule returned: triggered=True
2025-05-26 11:59:46,503 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Breaking out of risk rules loop due to triggered rule
2025-05-26 11:59:46,503 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-26 11:59:46,503 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Reached trade building section (outside all conditionals)
2025-05-26 11:59:46,503 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 1
2025-05-26 11:59:46,503 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 1, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 11:59:46,503 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23420.45, 'atm_strike': 23450.0, 'strike': 23450.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523450CE', 'ce_open': 102.85, 'ce_high': 106.65, 'ce_low': 97.85, 'ce_close': 100.75, 'ce_volume': 1015050, 'ce_oi': 978075, 'ce_coi': 0, 'ce_iv': 13.44, 'ce_delta': 0.48, 'ce_gamma': 0.0015, 'ce_theta': -23.27, 'ce_vega': 7.95, 'ce_rho': 81.3115, 'pe_symbol': 'NIFTY03APR2523450PE', 'pe_open': 118.2, 'pe_high': 122.3, 'pe_low': 112.2, 'pe_close': 115.7, 'pe_volume': 877125, 'pe_oi': 1618950, 'pe_coi': 0, 'pe_iv': 13.74, 'pe_delta': -0.52, 'pe_gamma': 0.0015, 'pe_theta': -17.3, 'pe_vega': 7.95, 'pe_rho': -88.3744, 'future_open': 23534.5, 'future_high': 23543.95, 'future_low': 23527.0, 'future_close': 23536.75, 'future_volume': 86475, 'future_oi': 12605100, 'future_coi': 0}
2025-05-26 11:59:46,503 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23195.3, 'atm_strike': 23200.0, 'strike': 23200.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523200CE', 'ce_open': 123.6, 'ce_high': 124.3, 'ce_low': 119.25, 'ce_close': 124.2, 'ce_volume': 326775, 'ce_oi': 4374450, 'ce_coi': 193725, 'ce_iv': 14.99, 'ce_delta': 0.52, 'ce_gamma': 0.0013, 'ce_theta': -25.57, 'ce_vega': 7.87, 'ce_rho': 86.2799, 'pe_symbol': 'NIFTY03APR2523200PE', 'pe_open': 100.2, 'pe_high': 105.2, 'pe_low': 99.55, 'pe_close': 100.75, 'pe_volume': 526875, 'pe_oi': 5030025, 'pe_coi': 101175, 'pe_iv': 13.55, 'pe_delta': -0.48, 'pe_gamma': 0.0015, 'pe_theta': -17.08, 'pe_vega': 7.87, 'pe_rho': -81.3242, 'future_open': 23347.0, 'future_high': 23348.85, 'future_low': 23339.0, 'future_close': 23348.1, 'future_volume': 19050, 'future_oi': 13928550, 'future_coi': -28275, 'close': 1450.2, 'datetime': Timestamp('2025-04-01 12:00:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-26 11:59:46,504 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=CALL, Transaction=SELL, Lots=1
2025-05-26 11:59:46,504 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-26 11:59:46,504 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-26 11:59:46,504 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-26 11:59:46,504 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -1172.5000000000002, PnL (Slippage): -1172.5000000000002, Net PnL: -1172.5000000000002
2025-05-26 11:59:46,504 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-26 11:59:46,504 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '1', 'entry_date': '2025-04-01', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2025-04-01', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23450, 'instrument_type': 'CE', 'side': 'SELL', 'filled_quantity': 50.0, 'entry_price': 100.75, 'exit_price': 124.2, 'points': -23.450000000000003, 'pointsAfterSlippage': -23.450000000000003, 'pnl': -1172.5000000000002, 'pnlAfterSlippage': -1172.5000000000002, 'expenses': 0.0, 'netPnlAfterExpenses': -1172.5000000000002, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23420.45, 'index_exit_price': 23195.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-01 09:16:00', 'exit_datetime': '2025-04-01 12:00:00'}
2025-05-26 11:59:46,504 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 1, about to append. Total records before: 0
2025-05-26 11:59:46,504 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 1. Total records now: 1
2025-05-26 11:59:46,504 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 2 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 11:59:46,505 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-26 11:59:46,505 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-26 11:59:46,530 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.025s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-26 11:59:46,565 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.036s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-26 11:59:46,566 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Query results: entry_df size=1, exit_df size=1
2025-05-26 11:59:46,566 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Got entry and exit data, proceeding...
2025-05-26 11:59:46,567 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
                        trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523450CE
ce_open                          102.85
ce_high                          106.65
ce_low                            97.85
ce_close                         100.75
ce_volume                       1015050
ce_oi                            978075
ce_coi                                0
ce_iv                             13.44
ce_delta                           0.48
ce_gamma                         0.0015
ce_theta                         -23.27
ce_vega                            7.95
ce_rho                          81.3115
pe_symbol           NIFTY03APR2523450PE
pe_open                           118.2
pe_high                           122.3
pe_low                            112.2
pe_close                          115.7
pe_volume                        877125
pe_oi                           1618950
pe_coi                                0
pe_iv                             13.74
pe_delta                          -0.52
pe_gamma                         0.0015
pe_theta                          -17.3
pe_vega                            7.95
pe_rho                         -88.3744
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-26 11:59:46,567 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23200.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                           123.6
ce_high                           124.3
ce_low                           119.25
ce_close                          124.2
ce_volume                        326775
ce_oi                           4374450
ce_coi                           193725
ce_iv                             14.99
ce_delta                           0.52
ce_gamma                         0.0013
ce_theta                         -25.57
ce_vega                            7.87
ce_rho                          86.2799
pe_symbol           NIFTY03APR2523200PE
pe_open                           100.2
pe_high                           105.2
pe_low                            99.55
pe_close                         100.75
pe_volume                        526875
pe_oi                           5030025
pe_coi                           101175
pe_iv                             13.55
pe_delta                          -0.48
pe_gamma                         0.0015
pe_theta                         -17.08
pe_vega                            7.87
pe_rho                         -81.3242
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-26 11:59:46,567 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-26 11:59:46,568 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-26 11:59:46,568 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-26 11:59:46,568 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Entering risk rules block
2025-05-26 11:59:46,569 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-26 11:59:46,569 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140661121027088
2025-05-26 11:59:46,569 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140661121026976, OptionType.PUT ID: 140661121027088
2025-05-26 11:59:46,569 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-26 11:59:46,569 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-26 11:59:46,569 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-26 11:59:46,570 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-26 11:59:46,868 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.298s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-26 11:59:46,870 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-26 11:59:46,871 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-26 11:59:46,927 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-26 11:59:46,930 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 2 - Tick data for specific strike 23450.0 is EMPTY after filtering windowed data.
2025-05-26 11:59:46,930 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Starting risk rules loop, has 3 rules
2025-05-26 11:59:46,930 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 1 of 3
2025-05-26 11:59:46,931 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-26 11:59:46,941 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-26 11:59:46,941 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 1
2025-05-26 11:59:46,941 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 2 of 3
2025-05-26 11:59:46,941 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type TAKEPROFIT
2025-05-26 11:59:46,951 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-26 11:59:46,952 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 2
2025-05-26 11:59:46,952 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 3 of 3
2025-05-26 11:59:46,952 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type TRAIL
2025-05-26 11:59:46,955 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-26 11:59:46,955 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 3
2025-05-26 11:59:46,955 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-26 11:59:46,955 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Reached trade building section (outside all conditionals)
2025-05-26 11:59:46,955 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 2
2025-05-26 11:59:46,955 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 2, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 11:59:46,955 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23420.45, 'atm_strike': 23450.0, 'strike': 23450.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523450CE', 'ce_open': 102.85, 'ce_high': 106.65, 'ce_low': 97.85, 'ce_close': 100.75, 'ce_volume': 1015050, 'ce_oi': 978075, 'ce_coi': 0, 'ce_iv': 13.44, 'ce_delta': 0.48, 'ce_gamma': 0.0015, 'ce_theta': -23.27, 'ce_vega': 7.95, 'ce_rho': 81.3115, 'pe_symbol': 'NIFTY03APR2523450PE', 'pe_open': 118.2, 'pe_high': 122.3, 'pe_low': 112.2, 'pe_close': 115.7, 'pe_volume': 877125, 'pe_oi': 1618950, 'pe_coi': 0, 'pe_iv': 13.74, 'pe_delta': -0.52, 'pe_gamma': 0.0015, 'pe_theta': -17.3, 'pe_vega': 7.95, 'pe_rho': -88.3744, 'future_open': 23534.5, 'future_high': 23543.95, 'future_low': 23527.0, 'future_close': 23536.75, 'future_volume': 86475, 'future_oi': 12605100, 'future_coi': 0}
2025-05-26 11:59:46,955 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(12, 0), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23195.3, 'atm_strike': 23200.0, 'strike': 23200.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523200CE', 'ce_open': 123.6, 'ce_high': 124.3, 'ce_low': 119.25, 'ce_close': 124.2, 'ce_volume': 326775, 'ce_oi': 4374450, 'ce_coi': 193725, 'ce_iv': 14.99, 'ce_delta': 0.52, 'ce_gamma': 0.0013, 'ce_theta': -25.57, 'ce_vega': 7.87, 'ce_rho': 86.2799, 'pe_symbol': 'NIFTY03APR2523200PE', 'pe_open': 100.2, 'pe_high': 105.2, 'pe_low': 99.55, 'pe_close': 100.75, 'pe_volume': 526875, 'pe_oi': 5030025, 'pe_coi': 101175, 'pe_iv': 13.55, 'pe_delta': -0.48, 'pe_gamma': 0.0015, 'pe_theta': -17.08, 'pe_vega': 7.87, 'pe_rho': -81.3242, 'future_open': 23347.0, 'future_high': 23348.85, 'future_low': 23339.0, 'future_close': 23348.1, 'future_volume': 19050, 'future_oi': 13928550, 'future_coi': -28275}
2025-05-26 11:59:46,955 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=SELL, Lots=1
2025-05-26 11:59:46,955 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-26 11:59:46,956 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-26 11:59:46,956 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-26 11:59:46,956 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: 747.5000000000001, PnL (Slippage): 747.5000000000001, Net PnL: 747.5000000000001
2025-05-26 11:59:46,956 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-26 11:59:46,956 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '2', 'entry_date': '2025-04-01', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2025-04-01', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23450, 'instrument_type': 'PE', 'side': 'SELL', 'filled_quantity': 50.0, 'entry_price': 115.7, 'exit_price': 100.75, 'points': 14.950000000000003, 'pointsAfterSlippage': 14.950000000000003, 'pnl': 747.5000000000001, 'pnlAfterSlippage': 747.5000000000001, 'expenses': 0.0, 'netPnlAfterExpenses': 747.5000000000001, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23420.45, 'index_exit_price': 23195.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-01 09:16:00', 'exit_datetime': '2025-04-01 12:00:00'}
2025-05-26 11:59:46,956 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 2, about to append. Total records before: 1
2025-05-26 11:59:46,956 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 2. Total records now: 2
2025-05-26 11:59:46,956 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 3 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 11:59:46,956 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-26 11:59:46,956 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-26 11:59:46,982 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.025s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-26 11:59:47,007 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.025s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-26 11:59:47,007 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Query results: entry_df size=1, exit_df size=1
2025-05-26 11:59:47,007 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Got entry and exit data, proceeding...
2025-05-26 11:59:47,008 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
                        trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23550.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY03APR2523550CE
ce_open                           61.15
ce_high                           61.75
ce_low                             55.0
ce_close                          56.45
ce_volume                       1147575
ce_oi                           2744250
ce_coi                                0
ce_iv                             12.93
ce_delta                           0.34
ce_gamma                         0.0014
ce_theta                         -19.86
ce_vega                            7.25
ce_rho                          56.1949
pe_symbol           NIFTY03APR2523550PE
pe_open                           174.0
pe_high                           180.1
pe_low                            167.8
pe_close                          175.5
pe_volume                        326550
pe_oi                           1975725
pe_coi                                0
pe_iv                             13.83
pe_delta                          -0.66
pe_gamma                         0.0013
pe_theta                         -14.93
pe_vega                            7.34
pe_rho                        -112.5433
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-26 11:59:47,009 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23300.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY03APR2523300CE
ce_open                           75.59
ce_high                           76.15
ce_low                            73.15
ce_close                          76.15
ce_volume                        250950
ce_oi                           7928250
ce_coi                           109950
ce_iv                             14.54
ce_delta                           0.37
ce_gamma                         0.0013
ce_theta                         -23.12
ce_vega                            7.53
ce_rho                          63.7039
pe_symbol           NIFTY03APR2523300PE
pe_open                           152.9
pe_high                          158.35
pe_low                           151.65
pe_close                         153.44
pe_volume                        252000
pe_oi                           4303500
pe_coi                           -10725
pe_iv                             13.14
pe_delta                          -0.63
pe_gamma                         0.0015
pe_theta                         -14.47
pe_vega                            7.45
pe_rho                        -107.0027
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-26 11:59:47,009 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-26 11:59:47,009 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-26 11:59:47,009 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-26 11:59:47,009 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Entering risk rules block
2025-05-26 11:59:47,010 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-26 11:59:47,010 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140661121026976
2025-05-26 11:59:47,010 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140661121026976, OptionType.PUT ID: 140661121027088
2025-05-26 11:59:47,010 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-26 11:59:47,010 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-26 11:59:47,010 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-26 11:59:47,011 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-26 11:59:47,324 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.313s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-26 11:59:47,326 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-26 11:59:47,327 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-26 11:59:47,381 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-26 11:59:47,385 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 3 - Tick data for specific strike 23550.0 is EMPTY after filtering windowed data.
2025-05-26 11:59:47,385 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Starting risk rules loop, has 3 rules
2025-05-26 11:59:47,386 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 1 of 3
2025-05-26 11:59:47,386 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-26 11:59:47,397 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - evaluate_risk_rule returned: triggered=False
2025-05-26 11:59:47,397 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Finished processing risk rule 1
2025-05-26 11:59:47,397 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 2 of 3
2025-05-26 11:59:47,397 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - About to call evaluate_risk_rule for rule type TAKEPROFIT
2025-05-26 11:59:47,401 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - evaluate_risk_rule returned: triggered=True
2025-05-26 11:59:47,402 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Breaking out of risk rules loop due to triggered rule
2025-05-26 11:59:47,403 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-26 11:59:47,403 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Reached trade building section (outside all conditionals)
2025-05-26 11:59:47,403 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 3
2025-05-26 11:59:47,403 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 3, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 11:59:47,403 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23420.45, 'atm_strike': 23450.0, 'strike': 23550.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'OTM2', 'put_strike_type': 'ITM2', 'ce_symbol': 'NIFTY03APR2523550CE', 'ce_open': 61.15, 'ce_high': 61.75, 'ce_low': 55.0, 'ce_close': 56.45, 'ce_volume': 1147575, 'ce_oi': 2744250, 'ce_coi': 0, 'ce_iv': 12.93, 'ce_delta': 0.34, 'ce_gamma': 0.0014, 'ce_theta': -19.86, 'ce_vega': 7.25, 'ce_rho': 56.1949, 'pe_symbol': 'NIFTY03APR2523550PE', 'pe_open': 174.0, 'pe_high': 180.1, 'pe_low': 167.8, 'pe_close': 175.5, 'pe_volume': 326550, 'pe_oi': 1975725, 'pe_coi': 0, 'pe_iv': 13.83, 'pe_delta': -0.66, 'pe_gamma': 0.0013, 'pe_theta': -14.93, 'pe_vega': 7.34, 'pe_rho': -112.5433, 'future_open': 23534.5, 'future_high': 23543.95, 'future_low': 23527.0, 'future_close': 23536.75, 'future_volume': 86475, 'future_oi': 12605100, 'future_coi': 0}
2025-05-26 11:59:47,403 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23195.3, 'atm_strike': 23200.0, 'strike': 23300.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'OTM2', 'put_strike_type': 'ITM2', 'ce_symbol': 'NIFTY03APR2523300CE', 'ce_open': 75.59, 'ce_high': 76.15, 'ce_low': 73.15, 'ce_close': 76.15, 'ce_volume': 250950, 'ce_oi': 7928250, 'ce_coi': 109950, 'ce_iv': 14.54, 'ce_delta': 0.37, 'ce_gamma': 0.0013, 'ce_theta': -23.12, 'ce_vega': 7.53, 'ce_rho': 63.7039, 'pe_symbol': 'NIFTY03APR2523300PE', 'pe_open': 152.9, 'pe_high': 158.35, 'pe_low': 151.65, 'pe_close': 153.44, 'pe_volume': 252000, 'pe_oi': 4303500, 'pe_coi': -10725, 'pe_iv': 13.14, 'pe_delta': -0.63, 'pe_gamma': 0.0015, 'pe_theta': -14.47, 'pe_vega': 7.45, 'pe_rho': -107.0027, 'future_open': 23347.0, 'future_high': 23348.85, 'future_low': 23339.0, 'future_close': 23348.1, 'future_volume': 19050, 'future_oi': 13928550, 'future_coi': -28275, 'close': 1450.2, 'datetime': Timestamp('2025-04-01 12:00:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-26 11:59:47,403 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=CALL, Transaction=BUY, Lots=1
2025-05-26 11:59:47,403 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-26 11:59:47,403 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-26 11:59:47,404 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-26 11:59:47,404 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: 985.0000000000001, PnL (Slippage): 985.0000000000001, Net PnL: 985.0000000000001
2025-05-26 11:59:47,404 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-26 11:59:47,404 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '3', 'entry_date': '2025-04-01', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2025-04-01', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23550, 'instrument_type': 'CE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 56.45, 'exit_price': 76.15, 'points': 19.700000000000003, 'pointsAfterSlippage': 19.700000000000003, 'pnl': 985.0000000000001, 'pnlAfterSlippage': 985.0000000000001, 'expenses': 0.0, 'netPnlAfterExpenses': 985.0000000000001, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23420.45, 'index_exit_price': 23195.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-01 09:16:00', 'exit_datetime': '2025-04-01 12:00:00'}
2025-05-26 11:59:47,404 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 3, about to append. Total records before: 2
2025-05-26 11:59:47,404 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 3. Total records now: 3
2025-05-26 11:59:47,404 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 4 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 11:59:47,404 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-26 11:59:47,404 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-26 11:59:47,436 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.032s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-26 11:59:47,472 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.036s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-26 11:59:47,472 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Query results: entry_df size=1, exit_df size=1
2025-05-26 11:59:47,473 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Got entry and exit data, proceeding...
2025-05-26 11:59:47,473 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
                        trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23350.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY03APR2523350CE
ce_open                           164.4
ce_high                           166.5
ce_low                            156.3
ce_close                          158.0
ce_volume                        408675
ce_oi                            381750
ce_coi                                0
ce_iv                             13.71
ce_delta                           0.62
ce_gamma                         0.0014
ce_theta                         -23.53
ce_vega                            7.54
ce_rho                         105.3755
pe_symbol           NIFTY03APR2523350PE
pe_open                            78.3
pe_high                           80.95
pe_low                            71.84
pe_close                          76.59
pe_volume                       1021875
pe_oi                           1656000
pe_coi                                0
pe_iv                             14.49
pe_delta                          -0.38
pe_gamma                         0.0013
pe_theta                         -18.33
pe_vega                            7.58
pe_rho                         -64.7877
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-26 11:59:47,474 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     12:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23195.3
atm_strike                      23200.0
strike                          23100.0
dte                                   2
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY03APR2523100CE
ce_open                           186.9
ce_high                           188.0
ce_low                            181.8
ce_close                          187.2
ce_volume                         77775
ce_oi                            957825
ce_coi                            12900
ce_iv                             15.68
ce_delta                           0.66
ce_gamma                         0.0012
ce_theta                         -25.85
ce_vega                            7.36
ce_rho                         106.8743
pe_symbol           NIFTY03APR2523100PE
pe_open                            63.7
pe_high                            67.2
pe_low                            63.05
pe_close                          64.15
pe_volume                        269475
pe_oi                           3243600
pe_coi                            78900
pe_iv                             14.17
pe_delta                          -0.34
pe_gamma                         0.0013
pe_theta                         -17.23
pe_vega                            7.25
pe_rho                         -57.8964
future_open                     23347.0
future_high                    23348.85
future_low                      23339.0
future_close                    23348.1
future_volume                     19050
future_oi                      13928550
future_coi                       -28275
2025-05-26 11:59:47,474 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-26 11:59:47,474 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-26 11:59:47,474 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-26 11:59:47,474 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Entering risk rules block
2025-05-26 11:59:47,475 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-26 11:59:47,475 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140661121027088
2025-05-26 11:59:47,475 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140661121026976, OptionType.PUT ID: 140661121027088
2025-05-26 11:59:47,475 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-26 11:59:47,475 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-26 11:59:47,475 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-26 11:59:47,476 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-26 11:59:47,808 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.331s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-26 11:59:47,810 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-26 11:59:47,811 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-26 11:59:47,892 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-26 11:59:47,897 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 4 - Tick data for specific strike 23350.0 is EMPTY after filtering windowed data.
2025-05-26 11:59:47,897 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Starting risk rules loop, has 3 rules
2025-05-26 11:59:47,897 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 1 of 3
2025-05-26 11:59:47,897 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-26 11:59:47,902 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - evaluate_risk_rule returned: triggered=True
2025-05-26 11:59:47,904 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Breaking out of risk rules loop due to triggered rule
2025-05-26 11:59:47,904 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-26 11:59:47,904 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Reached trade building section (outside all conditionals)
2025-05-26 11:59:47,905 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 4
2025-05-26 11:59:47,905 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-26 11:59:47,905 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23420.45, 'atm_strike': 23450.0, 'strike': 23350.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY03APR2523350CE', 'ce_open': 164.4, 'ce_high': 166.5, 'ce_low': 156.3, 'ce_close': 158.0, 'ce_volume': 408675, 'ce_oi': 381750, 'ce_coi': 0, 'ce_iv': 13.71, 'ce_delta': 0.62, 'ce_gamma': 0.0014, 'ce_theta': -23.53, 'ce_vega': 7.54, 'ce_rho': 105.3755, 'pe_symbol': 'NIFTY03APR2523350PE', 'pe_open': 78.3, 'pe_high': 80.95, 'pe_low': 71.84, 'pe_close': 76.59, 'pe_volume': 1021875, 'pe_oi': 1656000, 'pe_coi': 0, 'pe_iv': 14.49, 'pe_delta': -0.38, 'pe_gamma': 0.0013, 'pe_theta': -18.33, 'pe_vega': 7.58, 'pe_rho': -64.7877, 'future_open': 23534.5, 'future_high': 23543.95, 'future_low': 23527.0, 'future_close': 23536.75, 'future_volume': 86475, 'future_oi': 12605100, 'future_coi': 0}
2025-05-26 11:59:47,905 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': 120000, 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23195.3, 'atm_strike': 23200.0, 'strike': 23100.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY03APR2523100CE', 'ce_open': 186.9, 'ce_high': 188.0, 'ce_low': 181.8, 'ce_close': 187.2, 'ce_volume': 77775, 'ce_oi': 957825, 'ce_coi': 12900, 'ce_iv': 15.68, 'ce_delta': 0.66, 'ce_gamma': 0.0012, 'ce_theta': -25.85, 'ce_vega': 7.36, 'ce_rho': 106.8743, 'pe_symbol': 'NIFTY03APR2523100PE', 'pe_open': 63.7, 'pe_high': 67.2, 'pe_low': 63.05, 'pe_close': 64.15, 'pe_volume': 269475, 'pe_oi': 3243600, 'pe_coi': 78900, 'pe_iv': 14.17, 'pe_delta': -0.34, 'pe_gamma': 0.0013, 'pe_theta': -17.23, 'pe_vega': 7.25, 'pe_rho': -57.8964, 'future_open': 23347.0, 'future_high': 23348.85, 'future_low': 23339.0, 'future_close': 23348.1, 'future_volume': 19050, 'future_oi': 13928550, 'future_coi': -28275, 'close': 2.15, 'datetime': Timestamp('2025-04-01 12:00:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-26 11:59:47,905 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-26 11:59:47,905 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-26 11:59:47,905 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-26 11:59:47,905 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-26 11:59:47,906 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -621.9999999999999, PnL (Slippage): -621.9999999999999, Net PnL: -621.9999999999999
2025-05-26 11:59:47,906 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-26 11:59:47,906 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2025-04-01', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2025-04-01', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23350, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 76.59, 'exit_price': 64.15, 'points': -12.439999999999998, 'pointsAfterSlippage': -12.439999999999998, 'pnl': -621.9999999999999, 'pnlAfterSlippage': -621.9999999999999, 'expenses': 0.0, 'netPnlAfterExpenses': -621.9999999999999, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23420.45, 'index_exit_price': 23195.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-01 09:16:00', 'exit_datetime': '2025-04-01 12:00:00'}
2025-05-26 11:59:47,906 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 4, about to append. Total records before: 3
2025-05-26 11:59:47,906 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 4. Total records now: 4
2025-05-26 11:59:47,906 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Number of trade records generated: 4
2025-05-26 11:59:47,906 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Generated 4 trade records via model-driven path
2025-05-26 11:59:47,906 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Calculating tick-by-tick P&L for Max Profit/Loss tracking
2025-05-26 11:59:47,906 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG Tick P&L: First trade fields: ['portfolio_name', 'strategy', 'leg_id', 'entry_date', 'entry_time', 'entry_day', 'exit_date', 'exit_time', 'exit_day', 'symbol', 'expiry', 'strike', 'instrument_type', 'side', 'filled_quantity', 'entry_price', 'exit_price', 'points', 'pointsAfterSlippage', 'pnl', 'pnlAfterSlippage', 'expenses', 'netPnlAfterExpenses', 're_entry_no', 'stop_loss_entry_number', 'take_profit_entry_number', 'reason', 'strategy_entry_number', 'index_entry_price', 'index_exit_price', 'max_profit', 'max_loss', 'entry_datetime', 'exit_datetime']
2025-05-26 11:59:47,907 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG Tick P&L: First trade sample: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '1', 'entry_date': '2025-04-01', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2025-04-01', 'exit_time': '12:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23450, 'instrument_type': 'CE', 'side': 'SELL', 'filled_quantity': 50.0, 'entry_price': 100.75, 'exit_price': 124.2, 'points': -23.450000000000003, 'pointsAfterSlippage': -23.450000000000003, 'pnl': -1172.5000000000002, 'pnlAfterSlippage': -1172.5000000000002, 'expenses': 0.0, 'netPnlAfterExpenses': -1172.5000000000002, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23420.45, 'index_exit_price': 23195.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-01 09:16:00', 'exit_datetime': '2025-04-01 12:00:00'}
2025-05-26 11:59:47,938 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.031s, returned 375 rows. Query: SELECT SUBSTRING('SELECT DISTINCT EXTRACT(HOUR FROM trade_time) * 10000 + 
                                       EXTRACT(MINUTE FROM trade_time) * 100 + 
                                       EXTRACT(SECOND FROM trad', 1, 200) ...
2025-05-26 11:59:47,957 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:47,973 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:47,992 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,010 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,030 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,049 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,068 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,088 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,108 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,127 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,147 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,166 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,186 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,206 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,225 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,245 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,265 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,284 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,304 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,323 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,343 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,363 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,383 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,402 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,422 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,441 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,461 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,480 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,500 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,519 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,539 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,558 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,578 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,597 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,616 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,636 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,656 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,675 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,695 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,714 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,734 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,754 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,773 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,793 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,812 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,832 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,850 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,870 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,889 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,909 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,929 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,949 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,969 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:48,988 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,008 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,027 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,047 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,067 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,087 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,107 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,126 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,146 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,166 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,185 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,205 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,225 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,244 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,264 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,283 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,303 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,323 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,342 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,364 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.021s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,384 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,404 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,423 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,443 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,463 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,482 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,502 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,521 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,541 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,560 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,580 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,600 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,619 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,639 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,658 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,678 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,698 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,717 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,737 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,756 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,776 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,796 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,816 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,835 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,855 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,874 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,894 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,913 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,933 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,952 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,972 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:49,992 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,012 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,032 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,051 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,071 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,091 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,111 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,131 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,151 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,172 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,191 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,211 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,231 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,250 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,269 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,289 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,309 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,328 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,348 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,368 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,421 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.053s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,441 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,460 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,480 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,500 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,520 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,539 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,559 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,579 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,598 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,618 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,637 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,657 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,677 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,696 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,716 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,736 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,755 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,775 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,795 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,814 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,834 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,853 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,873 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,893 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,912 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,932 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,951 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,971 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:50,990 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,010 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,029 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,048 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,068 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,087 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,107 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,126 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,146 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,165 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,184 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,204 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,224 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,243 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,263 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,283 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,302 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,322 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,341 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,361 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,380 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,399 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,419 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,439 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,458 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,478 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,497 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,517 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,536 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,556 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,576 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,595 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,615 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,635 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,654 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,674 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,694 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,713 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,733 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,753 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,772 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,792 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,811 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,831 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,847 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,866 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,886 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,906 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,925 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,945 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,964 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:51,984 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,003 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,023 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,043 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,063 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,082 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,102 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,121 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,141 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,161 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,180 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,200 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,220 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,239 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,259 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,278 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,298 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,318 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,337 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,357 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,377 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,396 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,416 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,436 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,456 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,475 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,495 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,514 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,534 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,554 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,573 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,593 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,613 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,632 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,652 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,671 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,691 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,711 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,730 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,749 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,769 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,789 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,809 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,828 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,848 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,867 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,887 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,907 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,927 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,946 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,966 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:52,985 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,005 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,025 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,044 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,064 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,084 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,103 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,123 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,142 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,163 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,182 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,201 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,221 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,240 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,260 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,279 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,299 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,319 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,338 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,358 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,378 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,398 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,417 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,437 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,456 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,476 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,496 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,515 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,534 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,554 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,573 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,593 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,612 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,632 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,652 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,671 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,691 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,711 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,730 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,750 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,770 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,789 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,809 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,828 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,848 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,871 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.023s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,891 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,911 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,930 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,950 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,970 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:53,989 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,009 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,028 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,048 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,067 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,087 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,107 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,126 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,146 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,165 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,185 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,204 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,224 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,243 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,263 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,283 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,302 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,322 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,341 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,361 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,381 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,401 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,421 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,440 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,460 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,480 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,500 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,519 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,539 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,559 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,579 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,598 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,618 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,637 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,658 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,677 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,697 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,716 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,736 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,756 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,775 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,795 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,815 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,835 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,850 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,870 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,890 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,909 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,928 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,948 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,968 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:54,987 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,007 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,027 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,046 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,066 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,085 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,105 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,125 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,144 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,164 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,184 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,204 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,223 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,243 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,262 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,282 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,302 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,322 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,341 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,361 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,417 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.056s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,437 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,457 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,476 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,495 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,515 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,535 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,555 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,574 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,594 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,613 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,633 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,653 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,673 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,692 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,712 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,732 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,752 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,772 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,791 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,811 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,830 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,850 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,869 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,888 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,908 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,928 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,948 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,968 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:55,988 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,008 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,029 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,050 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,069 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,089 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,109 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,129 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,149 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,168 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,188 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,208 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,228 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,248 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,268 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,288 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,307 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,327 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,347 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,366 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,386 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,405 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,425 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,444 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,464 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,484 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,504 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,523 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,543 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,563 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,582 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,601 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,620 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,639 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,658 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,677 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,696 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,715 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,734 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,753 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,772 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,791 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,811 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,829 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,846 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,865 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,880 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.015s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,899 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,918 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,937 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,957 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,976 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:56,995 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,014 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,033 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,053 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,071 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,091 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,110 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,129 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,148 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,168 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,187 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,206 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,225 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,244 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,263 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,282 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,301 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,321 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,340 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,360 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,379 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,398 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,417 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,436 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,456 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,474 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,494 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,513 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,532 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,551 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,570 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,589 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,608 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,627 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,646 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,665 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,684 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,703 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,723 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,742 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,761 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,780 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,799 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,818 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,837 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,856 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,875 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,894 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,912 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,931 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,950 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,969 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:57,988 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,008 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,027 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,046 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,065 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,085 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,104 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,123 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,142 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,161 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,180 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,198 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,217 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,236 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,255 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,274 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,293 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,312 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,331 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,350 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,369 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,387 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,407 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,425 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,444 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,463 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,482 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,501 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,520 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,539 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,558 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,577 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,596 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,615 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,634 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,653 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,672 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,691 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,710 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,729 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,748 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,767 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,786 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,805 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,824 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,843 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,863 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,881 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,900 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,919 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,939 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,957 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,976 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:58,995 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,015 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,034 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,053 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,072 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,091 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,110 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,129 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,148 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,167 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,186 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,205 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,224 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,243 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,262 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,282 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,301 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,320 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,339 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,358 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,378 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,397 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,416 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,435 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,453 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,473 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,492 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,511 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,530 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,549 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,568 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,587 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,606 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,625 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,645 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,664 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,683 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,702 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,721 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,740 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,759 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,778 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,797 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,817 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,836 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,856 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,875 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,894 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,913 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,932 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,952 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,971 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 11:59:59,990 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,009 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,028 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,048 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,067 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,086 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,105 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,124 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,143 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,162 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,183 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,202 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,221 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,240 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,259 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,279 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,298 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,317 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,336 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,355 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,375 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,428 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.053s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,447 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,467 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,486 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,505 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,524 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,544 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,563 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,582 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,601 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,620 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,639 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,658 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,677 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,696 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,716 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,735 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,754 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,773 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,792 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,811 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,830 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,849 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,868 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT future_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2025-04-01''
                 ', 1, 200) ...
2025-05-26 12:00:00,891 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Calculated tick P&L for date 250401: 375 timestamps
2025-05-26 12:00:00,891 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Closed reusable HeavyDB connection from get_trades_for_portfolio
2025-05-26 12:00:00,926 - backtester_stable.BTRUN.builders - WARNING - utils.MARGIN_INFO not found or empty - margin requirements will be 0. Attempting to populate.
2025-05-26 12:00:00,927 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'margin_symbol_info.json' at /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-26 12:00:00,927 - backtester_stable.BTRUN.core.utils - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json (Broker: angel)
2025-05-26 12:00:00,927 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.MARGIN_INFO for angel.
2025-05-26 12:00:00,927 - backtester_stable.BTRUN.builders - ERROR - utils.MARGIN_INFO still empty after populate attempt. Margin req will be 0.
2025-05-26 12:00:00,933 - backtester_stable.BTRUN.builders - DEBUG - Strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL margin requirement: 0.00
2025-05-26 12:00:00,933 - backtester_stable.BTRUN.builders - INFO - Total portfolio margin requirement: 0.00
2025-05-26 12:00:00,933 - backtester_stable.BTRUN.builders - DEBUG - Converting tick PnL to daywise DataFrame.
2025-05-26 12:00:00,935 - backtester_stable.BTRUN.builders - INFO - Backtest response parsed.
2025-05-26 12:00:00,980 - backtester_stable.BTRUN.core.runtime - INFO - Generated metrics_df with 50 records for strategies: ['Combined' 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL']
2025-05-26 12:00:01,060 - backtester_stable.BTRUN.core.runtime - INFO - Saving Excel output to output/tbs_apr3_heavydb.xlsx
2025-05-26 12:00:01,060 - backtester_stable.BTRUN.core.runtime - DEBUG - [DEBUG save_backtest_results] save_excel=True, metrics_df is empty: False
2025-05-26 12:00:01,060 - backtester_stable.BTRUN.core.io - INFO - 2025-05-26 12:00:01.060911, Started writing stats to excel file: output/tbs_apr3_heavydb.xlsx
2025-05-26 12:00:01,061 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Temporarily switching to CPU-only mode.
2025-05-26 12:00:01,108 - backtester_stable.BTRUN.core.io - INFO - Added PortfolioParameter sheet in legacy 2x2 format
2025-05-26 12:00:01,141 - backtester_stable.BTRUN.core.io - INFO - Added GeneralParameter sheet from input files
2025-05-26 12:00:01,154 - backtester_stable.BTRUN.core.io - INFO - Added LegParameter sheet from input files
2025-05-26 12:00:01,154 - backtester_stable.BTRUN.core.io - INFO - Filtered metrics to show only 'Combined' entries: 25 rows
2025-05-26 12:00:01,206 - backtester_stable.BTRUN.core.io - INFO - Added PORTFOLIO Results sheet in legacy format
2025-05-26 12:00:01,267 - backtester_stable.BTRUN.core.io - INFO - 2025-05-26 12:00:01.267656, Excel file prepared successfully, Time taken: 0.21s
2025-05-26 12:00:01,267 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Restored GPU_ENABLED to: False
2025-05-26 12:00:01,267 - backtester_stable.BTRUN.core.runtime - INFO - Saving JSON output to output/tbs_apr3_heavydb.json
2025-05-26 12:00:01,293 - backtester_stable.BTRUN.core.io - INFO - --- JSON DUMP DEBUG START ---
2025-05-26 12:00:01,293 - backtester_stable.BTRUN.core.io - INFO - --- JSON DUMP DEBUG END ---
2025-05-26 12:00:01,294 - backtester_stable.BTRUN.core.io - INFO - Successfully wrote JSON output to output/tbs_apr3_heavydb.json
2025-05-26 12:00:01,299 - backtester_stable.BTRUN.core.io - ERROR - Text-to-speech failed: This means you probably do not have eSpeak or eSpeak-ng installed!
