2025-05-21 15:13:37,830 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-21 15:13:37,830 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-21 15:13:37,830 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages']
2025-05-21 15:13:37,830 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-21 15:13:37,830 - __main__ - DEBUG - GPU enabled: False
2025-05-21 15:13:37,832 - __main__ - INFO - No config provided – defaulting to Excel input format
2025-05-21 15:13:37,832 - __main__ - INFO - GPU acceleration enabled: False
2025-05-21 15:13:37,832 - __main__ - INFO - HeavyDB integration available (Host: 127.0.0.1)
2025-05-21 15:13:38,053 - backtester_stable.BTRUN.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-21 15:13:38,053 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-21 15:13:38,053 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-21 15:13:38,053 - backtester_stable.BTRUN.utils - INFO - Running necessary functions before starting BT...
2025-05-21 15:13:38,053 - backtester_stable.BTRUN.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/bt/backtester_stable/BTRUN/input/fixtures/LOTSIZE.csv
2025-05-21 15:13:38,053 - backtester_stable.BTRUN.utils - INFO - Loading lot sizes from: /srv/samba/shared/bt/backtester_stable/BTRUN/input/fixtures/LOTSIZE.csv
2025-05-21 15:13:38,058 - backtester_stable.BTRUN.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-21 15:13:38,059 - backtester_stable.BTRUN.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-21 15:13:38,059 - backtester_stable.BTRUN.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-21 15:13:38,059 - backtester_stable.BTRUN.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-21 15:13:38,059 - backtester_stable.BTRUN.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-21 15:13:38,059 - backtester_stable.BTRUN.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-21 15:13:38,059 - backtester_stable.BTRUN.utils - INFO - Pre-BT functions complete.
2025-05-21 15:13:38,059 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-21 15:13:38,059 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-21 15:13:38,059 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_exit_153000.xlsx
2025-05-21 15:13:38,059 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-21 15:13:38,060 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_exit_153000.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-21 15:13:38,188 - __main__ - INFO - Successfully parsed 1 portfolio(s) using modern parser.
2025-05-21 15:13:38,188 - __main__ - INFO - Queueing portfolio: NIF0DTE (from modern_portfolio_parser)
2025-05-21 15:13:38,189 - __main__ - INFO - Executing 1 portfolios sequentially (multiprocessing temporarily disabled for debugging)
2025-05-21 15:13:38,189 - __main__ - INFO - Processing payload 1/1: NIF0DTE
2025-05-21 15:13:38,189 - backtester_stable.BTRUN.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'NIF0DTE'
2025-05-21 15:13:38,402 - backtester_stable.BTRUN.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-21 15:13:38,421 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.018s, returned 563 rows. Query: SELECT SUBSTRING('SELECT DISTINCT trade_date FROM nifty_option_chain', 1, 200) ...
2025-05-21 15:13:38,463 - backtester_stable.BTRUN.heavydb_data_access - INFO - Cached 563 distinct trade dates
2025-05-21 15:13:38,463 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] portfolio_model_dict keys: ['portfolio_name', 'start_date', 'end_date', 'slippage_percent', 'margin_multiplier', 'is_tick_bt', 'strategies', 'extra_params']
2025-05-21 15:13:38,464 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-21 15:13:38,464 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '15:30:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-21 15:13:38,489 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.024s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:38,527 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.038s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''15:30:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:38,528 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523450CE
ce_open                          102.85
ce_high                          106.65
ce_low                            97.85
ce_close                         100.75
ce_volume                       1015050
ce_oi                            978075
ce_coi                                0
ce_iv                             13.44
ce_delta                           0.48
ce_gamma                         0.0015
ce_theta                         -23.27
ce_vega                            7.95
ce_rho                          81.3115
pe_symbol           NIFTY03APR2523450PE
pe_open                           118.2
pe_high                           122.3
pe_low                            112.2
pe_close                          115.7
pe_volume                        877125
pe_oi                           1618950
pe_coi                                0
pe_iv                             13.74
pe_delta                          -0.52
pe_gamma                         0.0015
pe_theta                          -17.3
pe_vega                            7.95
pe_rho                         -88.3744
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-21 15:13:38,529 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     15:29:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23179.7
atm_strike                      23200.0
strike                          23200.0
dte                                   2
expiry_bucket                        CW
zone_id                               5
zone_name                         CLOSE
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                           106.2
ce_high                           106.7
ce_low                           104.05
ce_close                         104.95
ce_volume                        740475
ce_oi                           5364075
ce_coi                                0
ce_iv                             13.55
ce_delta                            0.5
ce_gamma                         0.0015
ce_theta                          -23.3
ce_vega                            7.87
ce_rho                          82.7051
pe_symbol           NIFTY03APR2523200PE
pe_open                          101.25
pe_high                          103.55
pe_low                           101.25
pe_close                         103.45
pe_volume                        870825
pe_oi                           5307000
pe_coi                                0
pe_iv                             12.92
pe_delta                           -0.5
pe_gamma                         0.0016
pe_theta                          -16.0
pe_vega                            7.87
pe_rho                         -85.2566
future_open                    23312.65
future_high                     23319.0
future_low                      23310.0
future_close                    23315.0
future_volume                     42450
future_oi                      13645500
future_coi                            0
2025-05-21 15:13:38,529 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 15:13:38,529 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 15:29:00
2025-05-21 15:13:38,529 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 15:13:38,529 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 139992718291264
2025-05-21 15:13:38,529 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139992718291264, OptionType.PUT ID: 139992718291376
2025-05-21 15:13:38,530 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 15:13:38,530 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-21 15:13:38,530 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-21 15:13:38,531 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 15:13:38,831 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.299s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 15:13:38,833 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-21 15:13:38,833 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-21 15:13:38,888 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 15:13:38,897 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-21 15:13:38,897 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '15:30:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-21 15:13:38,944 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.047s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:38,972 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.027s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''15:30:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:38,973 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523450CE
ce_open                          102.85
ce_high                          106.65
ce_low                            97.85
ce_close                         100.75
ce_volume                       1015050
ce_oi                            978075
ce_coi                                0
ce_iv                             13.44
ce_delta                           0.48
ce_gamma                         0.0015
ce_theta                         -23.27
ce_vega                            7.95
ce_rho                          81.3115
pe_symbol           NIFTY03APR2523450PE
pe_open                           118.2
pe_high                           122.3
pe_low                            112.2
pe_close                          115.7
pe_volume                        877125
pe_oi                           1618950
pe_coi                                0
pe_iv                             13.74
pe_delta                          -0.52
pe_gamma                         0.0015
pe_theta                          -17.3
pe_vega                            7.95
pe_rho                         -88.3744
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-21 15:13:38,974 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     15:29:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23179.7
atm_strike                      23200.0
strike                          23200.0
dte                                   2
expiry_bucket                        CW
zone_id                               5
zone_name                         CLOSE
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                           106.2
ce_high                           106.7
ce_low                           104.05
ce_close                         104.95
ce_volume                        740475
ce_oi                           5364075
ce_coi                                0
ce_iv                             13.55
ce_delta                            0.5
ce_gamma                         0.0015
ce_theta                          -23.3
ce_vega                            7.87
ce_rho                          82.7051
pe_symbol           NIFTY03APR2523200PE
pe_open                          101.25
pe_high                          103.55
pe_low                           101.25
pe_close                         103.45
pe_volume                        870825
pe_oi                           5307000
pe_coi                                0
pe_iv                             12.92
pe_delta                           -0.5
pe_gamma                         0.0016
pe_theta                          -16.0
pe_vega                            7.87
pe_rho                         -85.2566
future_open                    23312.65
future_high                     23319.0
future_low                      23310.0
future_close                    23315.0
future_volume                     42450
future_oi                      13645500
future_coi                            0
2025-05-21 15:13:38,974 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 15:13:38,974 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 15:29:00
2025-05-21 15:13:38,974 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 15:13:38,975 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 139992718291376
2025-05-21 15:13:38,975 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139992718291264, OptionType.PUT ID: 139992718291376
2025-05-21 15:13:38,975 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 15:13:38,975 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-21 15:13:38,975 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-21 15:13:38,976 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 15:13:39,280 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.304s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 15:13:39,282 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-21 15:13:39,282 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-21 15:13:39,337 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 15:13:39,382 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-21 15:13:39,382 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '15:30:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-21 15:13:39,421 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.039s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:39,459 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.038s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''15:30:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:39,461 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523450CE
ce_open                          102.85
ce_high                          106.65
ce_low                            97.85
ce_close                         100.75
ce_volume                       1015050
ce_oi                            978075
ce_coi                                0
ce_iv                             13.44
ce_delta                           0.48
ce_gamma                         0.0015
ce_theta                         -23.27
ce_vega                            7.95
ce_rho                          81.3115
pe_symbol           NIFTY03APR2523450PE
pe_open                           118.2
pe_high                           122.3
pe_low                            112.2
pe_close                          115.7
pe_volume                        877125
pe_oi                           1618950
pe_coi                                0
pe_iv                             13.74
pe_delta                          -0.52
pe_gamma                         0.0015
pe_theta                          -17.3
pe_vega                            7.95
pe_rho                         -88.3744
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-21 15:13:39,461 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     15:29:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23179.7
atm_strike                      23200.0
strike                          23200.0
dte                                   2
expiry_bucket                        CW
zone_id                               5
zone_name                         CLOSE
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                           106.2
ce_high                           106.7
ce_low                           104.05
ce_close                         104.95
ce_volume                        740475
ce_oi                           5364075
ce_coi                                0
ce_iv                             13.55
ce_delta                            0.5
ce_gamma                         0.0015
ce_theta                          -23.3
ce_vega                            7.87
ce_rho                          82.7051
pe_symbol           NIFTY03APR2523200PE
pe_open                          101.25
pe_high                          103.55
pe_low                           101.25
pe_close                         103.45
pe_volume                        870825
pe_oi                           5307000
pe_coi                                0
pe_iv                             12.92
pe_delta                           -0.5
pe_gamma                         0.0016
pe_theta                          -16.0
pe_vega                            7.87
pe_rho                         -85.2566
future_open                    23312.65
future_high                     23319.0
future_low                      23310.0
future_close                    23315.0
future_volume                     42450
future_oi                      13645500
future_coi                            0
2025-05-21 15:13:39,461 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 15:13:39,461 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 15:29:00
2025-05-21 15:13:39,462 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 15:13:39,462 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 139992718291264
2025-05-21 15:13:39,462 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139992718291264, OptionType.PUT ID: 139992718291376
2025-05-21 15:13:39,462 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 15:13:39,462 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-21 15:13:39,462 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-21 15:13:39,464 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 15:13:39,763 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.299s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 15:13:39,765 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-21 15:13:39,765 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-21 15:13:39,819 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 15:13:39,846 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-21 15:13:39,846 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '15:30:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-21 15:13:39,874 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.027s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:39,911 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.037s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''15:30:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:39,912 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523450CE
ce_open                          102.85
ce_high                          106.65
ce_low                            97.85
ce_close                         100.75
ce_volume                       1015050
ce_oi                            978075
ce_coi                                0
ce_iv                             13.44
ce_delta                           0.48
ce_gamma                         0.0015
ce_theta                         -23.27
ce_vega                            7.95
ce_rho                          81.3115
pe_symbol           NIFTY03APR2523450PE
pe_open                           118.2
pe_high                           122.3
pe_low                            112.2
pe_close                          115.7
pe_volume                        877125
pe_oi                           1618950
pe_coi                                0
pe_iv                             13.74
pe_delta                          -0.52
pe_gamma                         0.0015
pe_theta                          -17.3
pe_vega                            7.95
pe_rho                         -88.3744
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-21 15:13:39,912 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     15:29:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23179.7
atm_strike                      23200.0
strike                          23200.0
dte                                   2
expiry_bucket                        CW
zone_id                               5
zone_name                         CLOSE
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                           106.2
ce_high                           106.7
ce_low                           104.05
ce_close                         104.95
ce_volume                        740475
ce_oi                           5364075
ce_coi                                0
ce_iv                             13.55
ce_delta                            0.5
ce_gamma                         0.0015
ce_theta                          -23.3
ce_vega                            7.87
ce_rho                          82.7051
pe_symbol           NIFTY03APR2523200PE
pe_open                          101.25
pe_high                          103.55
pe_low                           101.25
pe_close                         103.45
pe_volume                        870825
pe_oi                           5307000
pe_coi                                0
pe_iv                             12.92
pe_delta                           -0.5
pe_gamma                         0.0016
pe_theta                          -16.0
pe_vega                            7.87
pe_rho                         -85.2566
future_open                    23312.65
future_high                     23319.0
future_low                      23310.0
future_close                    23315.0
future_volume                     42450
future_oi                      13645500
future_coi                            0
2025-05-21 15:13:39,913 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 15:13:39,913 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 15:29:00
2025-05-21 15:13:39,913 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 15:13:39,913 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 139992718291376
2025-05-21 15:13:39,913 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139992718291264, OptionType.PUT ID: 139992718291376
2025-05-21 15:13:39,913 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 15:13:39,913 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-21 15:13:39,913 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-21 15:13:39,915 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 15:13:40,229 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.314s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 15:13:40,231 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-21 15:13:40,232 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-21 15:13:40,314 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 15:13:40,326 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-21 15:13:40,326 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23420.45, 'atm_strike': 23450.0, 'strike': 23450.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523450CE', 'ce_open': 102.85, 'ce_high': 106.65, 'ce_low': 97.85, 'ce_close': 100.75, 'ce_volume': 1015050, 'ce_oi': 978075, 'ce_coi': 0, 'ce_iv': 13.44, 'ce_delta': 0.48, 'ce_gamma': 0.0015, 'ce_theta': -23.27, 'ce_vega': 7.95, 'ce_rho': 81.3115, 'pe_symbol': 'NIFTY03APR2523450PE', 'pe_open': 118.2, 'pe_high': 122.3, 'pe_low': 112.2, 'pe_close': 115.7, 'pe_volume': 877125, 'pe_oi': 1618950, 'pe_coi': 0, 'pe_iv': 13.74, 'pe_delta': -0.52, 'pe_gamma': 0.0015, 'pe_theta': -17.3, 'pe_vega': 7.95, 'pe_rho': -88.3744, 'future_open': 23534.5, 'future_high': 23543.95, 'future_low': 23527.0, 'future_close': 23536.75, 'future_volume': 86475, 'future_oi': 12605100, 'future_coi': 0}
2025-05-21 15:13:40,327 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': 153000, 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23179.7, 'atm_strike': 23200.0, 'strike': 23200.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 5, 'zone_name': 'CLOSE', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523200CE', 'ce_open': 106.2, 'ce_high': 106.7, 'ce_low': 104.05, 'ce_close': 104.95, 'ce_volume': 740475, 'ce_oi': 5364075, 'ce_coi': 0, 'ce_iv': 13.55, 'ce_delta': 0.5, 'ce_gamma': 0.0015, 'ce_theta': -23.3, 'ce_vega': 7.87, 'ce_rho': 82.7051, 'pe_symbol': 'NIFTY03APR2523200PE', 'pe_open': 101.25, 'pe_high': 103.55, 'pe_low': 101.25, 'pe_close': 103.45, 'pe_volume': 870825, 'pe_oi': 5307000, 'pe_coi': 0, 'pe_iv': 12.92, 'pe_delta': -0.5, 'pe_gamma': 0.0016, 'pe_theta': -16.0, 'pe_vega': 7.87, 'pe_rho': -85.2566, 'future_open': 23312.65, 'future_high': 23319.0, 'future_low': 23310.0, 'future_close': 23315.0, 'future_volume': 42450, 'future_oi': 13645500, 'future_coi': 0, 'close': 2.15, 'datetime': Timestamp('2025-04-01 09:17:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-21 15:13:40,327 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-21 15:13:40,327 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -612.5, PnL (Slippage): -612.5, Net PnL: -612.5
2025-05-21 15:13:40,327 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-21 15:13:40,327 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2025-04-01', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2025-04-01', 'exit_time': '15:30:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23450, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 115.7, 'exit_price': 103.45, 'points': -12.25, 'pointsAfterSlippage': -12.25, 'pnl': -612.5, 'pnlAfterSlippage': -612.5, 'expenses': 0.0, 'netPnlAfterExpenses': -612.5, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23420.45, 'index_exit_price': 23179.7, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-01 09:16:00', 'exit_datetime': '2025-04-01 15:30:00'}
2025-05-21 15:13:40,328 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-21 15:13:40,328 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '15:30:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-21 15:13:40,353 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.025s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:40,402 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.049s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''15:30:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:40,403 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
trade_date                   2025-04-02
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23204.8
atm_strike                      23200.0
strike                          23200.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                          107.95
ce_high                           113.5
ce_low                           105.15
ce_close                          110.5
ce_volume                       2666850
ce_oi                           5862000
ce_coi                                0
ce_iv                             16.53
ce_delta                           0.53
ce_gamma                         0.0015
ce_theta                         -34.48
ce_vega                             6.2
ce_rho                          54.4841
pe_symbol           NIFTY03APR2523200PE
pe_open                            86.9
pe_high                            94.9
pe_low                            85.55
pe_close                          86.15
pe_volume                       3014850
pe_oi                           5689575
pe_coi                                0
pe_iv                             15.06
pe_delta                          -0.47
pe_gamma                         0.0017
pe_theta                         -25.36
pe_vega                             6.2
pe_rho                         -49.7483
future_open                     23339.0
future_high                     23344.7
future_low                     23331.25
future_close                   23344.15
future_volume                     43725
future_oi                      12808425
future_coi                            0
2025-05-21 15:13:40,404 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2025-04-02
trade_time                     15:29:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23330.8
atm_strike                      23300.0
strike                          23300.0
dte                                   1
expiry_bucket                        CW
zone_id                               5
zone_name                         CLOSE
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523300CE
ce_open                            79.7
ce_high                           80.59
ce_low                            78.34
ce_close                          79.59
ce_volume                       1305750
ce_oi                          11853825
ce_coi                                0
ce_iv                              9.06
ce_delta                           0.58
ce_gamma                         0.0027
ce_theta                         -20.39
ce_vega                            5.98
ce_rho                          64.3746
pe_symbol           NIFTY03APR2523300PE
pe_open                            67.0
pe_high                           67.25
pe_low                             65.4
pe_close                          65.75
pe_volume                       1764750
pe_oi                           9670125
pe_coi                                0
pe_iv                             13.59
pe_delta                          -0.42
pe_gamma                         0.0018
pe_theta                         -22.59
pe_vega                            6.13
pe_rho                         -44.5765
future_open                     23440.0
future_high                    23440.45
future_low                     23433.85
future_close                    23435.0
future_volume                     65325
future_oi                      12627450
future_coi                            0
2025-05-21 15:13:40,404 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 15:13:40,404 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 15:29:00
2025-05-21 15:13:40,405 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 15:13:40,405 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 139992718291264
2025-05-21 15:13:40,405 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139992718291264, OptionType.PUT ID: 139992718291376
2025-05-21 15:13:40,405 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 15:13:40,405 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-21 15:13:40,405 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-21 15:13:40,407 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-02' AND trade_date <= '2025-04-02'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 15:13:40,702 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.295s, returned 18845 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 15:13:40,704 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18845 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-21 15:13:40,705 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-21 15:13:40,775 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 15:13:40,784 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-21 15:13:40,784 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '15:30:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-21 15:13:40,809 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.024s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:40,832 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.023s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''15:30:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:40,833 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
trade_date                   2025-04-02
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23204.8
atm_strike                      23200.0
strike                          23200.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                          107.95
ce_high                           113.5
ce_low                           105.15
ce_close                          110.5
ce_volume                       2666850
ce_oi                           5862000
ce_coi                                0
ce_iv                             16.53
ce_delta                           0.53
ce_gamma                         0.0015
ce_theta                         -34.48
ce_vega                             6.2
ce_rho                          54.4841
pe_symbol           NIFTY03APR2523200PE
pe_open                            86.9
pe_high                            94.9
pe_low                            85.55
pe_close                          86.15
pe_volume                       3014850
pe_oi                           5689575
pe_coi                                0
pe_iv                             15.06
pe_delta                          -0.47
pe_gamma                         0.0017
pe_theta                         -25.36
pe_vega                             6.2
pe_rho                         -49.7483
future_open                     23339.0
future_high                     23344.7
future_low                     23331.25
future_close                   23344.15
future_volume                     43725
future_oi                      12808425
future_coi                            0
2025-05-21 15:13:40,834 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2025-04-02
trade_time                     15:29:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23330.8
atm_strike                      23300.0
strike                          23300.0
dte                                   1
expiry_bucket                        CW
zone_id                               5
zone_name                         CLOSE
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523300CE
ce_open                            79.7
ce_high                           80.59
ce_low                            78.34
ce_close                          79.59
ce_volume                       1305750
ce_oi                          11853825
ce_coi                                0
ce_iv                              9.06
ce_delta                           0.58
ce_gamma                         0.0027
ce_theta                         -20.39
ce_vega                            5.98
ce_rho                          64.3746
pe_symbol           NIFTY03APR2523300PE
pe_open                            67.0
pe_high                           67.25
pe_low                             65.4
pe_close                          65.75
pe_volume                       1764750
pe_oi                           9670125
pe_coi                                0
pe_iv                             13.59
pe_delta                          -0.42
pe_gamma                         0.0018
pe_theta                         -22.59
pe_vega                            6.13
pe_rho                         -44.5765
future_open                     23440.0
future_high                    23440.45
future_low                     23433.85
future_close                    23435.0
future_volume                     65325
future_oi                      12627450
future_coi                            0
2025-05-21 15:13:40,834 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 15:13:40,834 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 15:29:00
2025-05-21 15:13:40,835 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 15:13:40,835 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 139992718291376
2025-05-21 15:13:40,835 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139992718291264, OptionType.PUT ID: 139992718291376
2025-05-21 15:13:40,835 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 15:13:40,835 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-21 15:13:40,835 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-21 15:13:40,836 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-02' AND trade_date <= '2025-04-02'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 15:13:41,091 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.255s, returned 18845 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 15:13:41,093 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18845 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-21 15:13:41,093 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-21 15:13:41,144 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 15:13:41,188 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-21 15:13:41,188 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '15:30:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-21 15:13:41,227 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.038s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:41,266 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.039s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''15:30:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:41,267 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
trade_date                   2025-04-02
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23204.8
atm_strike                      23200.0
strike                          23200.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                          107.95
ce_high                           113.5
ce_low                           105.15
ce_close                          110.5
ce_volume                       2666850
ce_oi                           5862000
ce_coi                                0
ce_iv                             16.53
ce_delta                           0.53
ce_gamma                         0.0015
ce_theta                         -34.48
ce_vega                             6.2
ce_rho                          54.4841
pe_symbol           NIFTY03APR2523200PE
pe_open                            86.9
pe_high                            94.9
pe_low                            85.55
pe_close                          86.15
pe_volume                       3014850
pe_oi                           5689575
pe_coi                                0
pe_iv                             15.06
pe_delta                          -0.47
pe_gamma                         0.0017
pe_theta                         -25.36
pe_vega                             6.2
pe_rho                         -49.7483
future_open                     23339.0
future_high                     23344.7
future_low                     23331.25
future_close                   23344.15
future_volume                     43725
future_oi                      12808425
future_coi                            0
2025-05-21 15:13:41,268 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2025-04-02
trade_time                     15:29:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23330.8
atm_strike                      23300.0
strike                          23300.0
dte                                   1
expiry_bucket                        CW
zone_id                               5
zone_name                         CLOSE
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523300CE
ce_open                            79.7
ce_high                           80.59
ce_low                            78.34
ce_close                          79.59
ce_volume                       1305750
ce_oi                          11853825
ce_coi                                0
ce_iv                              9.06
ce_delta                           0.58
ce_gamma                         0.0027
ce_theta                         -20.39
ce_vega                            5.98
ce_rho                          64.3746
pe_symbol           NIFTY03APR2523300PE
pe_open                            67.0
pe_high                           67.25
pe_low                             65.4
pe_close                          65.75
pe_volume                       1764750
pe_oi                           9670125
pe_coi                                0
pe_iv                             13.59
pe_delta                          -0.42
pe_gamma                         0.0018
pe_theta                         -22.59
pe_vega                            6.13
pe_rho                         -44.5765
future_open                     23440.0
future_high                    23440.45
future_low                     23433.85
future_close                    23435.0
future_volume                     65325
future_oi                      12627450
future_coi                            0
2025-05-21 15:13:41,268 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 15:13:41,268 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 15:29:00
2025-05-21 15:13:41,268 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 15:13:41,268 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 139992718291264
2025-05-21 15:13:41,268 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139992718291264, OptionType.PUT ID: 139992718291376
2025-05-21 15:13:41,268 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 15:13:41,269 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-21 15:13:41,269 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-21 15:13:41,270 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-02' AND trade_date <= '2025-04-02'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 15:13:41,577 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.307s, returned 18845 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 15:13:41,578 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18845 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-21 15:13:41,579 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-21 15:13:41,630 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 15:13:41,657 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-21 15:13:41,657 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '15:30:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-21 15:13:41,700 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.042s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:41,736 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.035s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''15:30:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:41,737 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
trade_date                   2025-04-02
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23204.8
atm_strike                      23200.0
strike                          23200.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                          107.95
ce_high                           113.5
ce_low                           105.15
ce_close                          110.5
ce_volume                       2666850
ce_oi                           5862000
ce_coi                                0
ce_iv                             16.53
ce_delta                           0.53
ce_gamma                         0.0015
ce_theta                         -34.48
ce_vega                             6.2
ce_rho                          54.4841
pe_symbol           NIFTY03APR2523200PE
pe_open                            86.9
pe_high                            94.9
pe_low                            85.55
pe_close                          86.15
pe_volume                       3014850
pe_oi                           5689575
pe_coi                                0
pe_iv                             15.06
pe_delta                          -0.47
pe_gamma                         0.0017
pe_theta                         -25.36
pe_vega                             6.2
pe_rho                         -49.7483
future_open                     23339.0
future_high                     23344.7
future_low                     23331.25
future_close                   23344.15
future_volume                     43725
future_oi                      12808425
future_coi                            0
2025-05-21 15:13:41,737 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2025-04-02
trade_time                     15:29:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23330.8
atm_strike                      23300.0
strike                          23300.0
dte                                   1
expiry_bucket                        CW
zone_id                               5
zone_name                         CLOSE
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523300CE
ce_open                            79.7
ce_high                           80.59
ce_low                            78.34
ce_close                          79.59
ce_volume                       1305750
ce_oi                          11853825
ce_coi                                0
ce_iv                              9.06
ce_delta                           0.58
ce_gamma                         0.0027
ce_theta                         -20.39
ce_vega                            5.98
ce_rho                          64.3746
pe_symbol           NIFTY03APR2523300PE
pe_open                            67.0
pe_high                           67.25
pe_low                             65.4
pe_close                          65.75
pe_volume                       1764750
pe_oi                           9670125
pe_coi                                0
pe_iv                             13.59
pe_delta                          -0.42
pe_gamma                         0.0018
pe_theta                         -22.59
pe_vega                            6.13
pe_rho                         -44.5765
future_open                     23440.0
future_high                    23440.45
future_low                     23433.85
future_close                    23435.0
future_volume                     65325
future_oi                      12627450
future_coi                            0
2025-05-21 15:13:41,738 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 15:13:41,738 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 15:29:00
2025-05-21 15:13:41,738 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 15:13:41,738 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 139992718291376
2025-05-21 15:13:41,738 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 139992718291264, OptionType.PUT ID: 139992718291376
2025-05-21 15:13:41,738 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 15:13:41,738 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-21 15:13:41,739 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-21 15:13:41,740 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-02' AND trade_date <= '2025-04-02'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 15:13:42,037 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.297s, returned 18845 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 15:13:42,038 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18845 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-21 15:13:42,039 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-21 15:13:42,090 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 15:13:42,099 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-21 15:13:42,099 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 2), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23204.8, 'atm_strike': 23200.0, 'strike': 23200.0, 'dte': 1, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523200CE', 'ce_open': 107.95, 'ce_high': 113.5, 'ce_low': 105.15, 'ce_close': 110.5, 'ce_volume': 2666850, 'ce_oi': 5862000, 'ce_coi': 0, 'ce_iv': 16.53, 'ce_delta': 0.53, 'ce_gamma': 0.0015, 'ce_theta': -34.48, 'ce_vega': 6.2, 'ce_rho': 54.4841, 'pe_symbol': 'NIFTY03APR2523200PE', 'pe_open': 86.9, 'pe_high': 94.9, 'pe_low': 85.55, 'pe_close': 86.15, 'pe_volume': 3014850, 'pe_oi': 5689575, 'pe_coi': 0, 'pe_iv': 15.06, 'pe_delta': -0.47, 'pe_gamma': 0.0017, 'pe_theta': -25.36, 'pe_vega': 6.2, 'pe_rho': -49.7483, 'future_open': 23339.0, 'future_high': 23344.7, 'future_low': 23331.25, 'future_close': 23344.15, 'future_volume': 43725, 'future_oi': 12808425, 'future_coi': 0}
2025-05-21 15:13:42,099 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 2), 'trade_time': 153000, 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23330.8, 'atm_strike': 23300.0, 'strike': 23300.0, 'dte': 1, 'expiry_bucket': 'CW', 'zone_id': 5, 'zone_name': 'CLOSE', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523300CE', 'ce_open': 79.7, 'ce_high': 80.59, 'ce_low': 78.34, 'ce_close': 79.59, 'ce_volume': 1305750, 'ce_oi': 11853825, 'ce_coi': 0, 'ce_iv': 9.06, 'ce_delta': 0.58, 'ce_gamma': 0.0027, 'ce_theta': -20.39, 'ce_vega': 5.98, 'ce_rho': 64.3746, 'pe_symbol': 'NIFTY03APR2523300PE', 'pe_open': 67.0, 'pe_high': 67.25, 'pe_low': 65.4, 'pe_close': 65.75, 'pe_volume': 1764750, 'pe_oi': 9670125, 'pe_coi': 0, 'pe_iv': 13.59, 'pe_delta': -0.42, 'pe_gamma': 0.0018, 'pe_theta': -22.59, 'pe_vega': 6.13, 'pe_rho': -44.5765, 'future_open': 23440.0, 'future_high': 23440.45, 'future_low': 23433.85, 'future_close': 23435.0, 'future_volume': 65325, 'future_oi': 12627450, 'future_coi': 0, 'close': 1.14, 'datetime': Timestamp('2025-04-02 09:17:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-21 15:13:42,099 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-21 15:13:42,100 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -1020.0000000000002, PnL (Slippage): -1020.0000000000002, Net PnL: -1020.0000000000002
2025-05-21 15:13:42,100 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-21 15:13:42,100 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2025-04-02', 'entry_time': '09:16:00', 'entry_day': 'Wednesday', 'exit_date': '2025-04-02', 'exit_time': '15:30:00', 'exit_day': 'Wednesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23200, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 86.15, 'exit_price': 65.75, 'points': -20.400000000000006, 'pointsAfterSlippage': -20.400000000000006, 'pnl': -1020.0000000000002, 'pnlAfterSlippage': -1020.0000000000002, 'expenses': 0.0, 'netPnlAfterExpenses': -1020.0000000000002, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23204.8, 'index_exit_price': 23330.8, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-02 09:16:00', 'exit_datetime': '2025-04-02 15:30:00'}
2025-05-21 15:13:42,100 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Number of trade records generated: 2
2025-05-21 15:13:42,100 - backtester_stable.BTRUN.heavydb_trade_processing - INFO - Generated 2 trade records via model-driven path
2025-05-21 15:13:42,100 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Closed reusable HeavyDB connection from get_trades_for_portfolio
2025-05-21 15:13:42,116 - backtester_stable.BTRUN.builders - WARNING - utils.MARGIN_INFO not found or empty - margin requirements will be 0. Attempting to populate.
2025-05-21 15:13:42,117 - backtester_stable.BTRUN.config - INFO - Found fixture 'margin_symbol_info.json' at /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-21 15:13:42,117 - backtester_stable.BTRUN.utils - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json (Broker: angel)
2025-05-21 15:13:42,117 - backtester_stable.BTRUN.utils - INFO - Successfully populated config.MARGIN_INFO for angel.
2025-05-21 15:13:42,117 - backtester_stable.BTRUN.builders - ERROR - utils.MARGIN_INFO still empty after populate attempt. Margin req will be 0.
2025-05-21 15:13:42,119 - backtester_stable.BTRUN.builders - DEBUG - Strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL margin requirement: 0.00
2025-05-21 15:13:42,119 - backtester_stable.BTRUN.builders - INFO - Total portfolio margin requirement: 0.00
2025-05-21 15:13:42,119 - backtester_stable.BTRUN.builders - DEBUG - Converting tick PnL to daywise DataFrame.
2025-05-21 15:13:42,120 - backtester_stable.BTRUN.builders - INFO - Backtest response parsed.
2025-05-21 15:13:42,149 - backtester_stable.BTRUN.runtime - INFO - Generated metrics_df with 50 records for strategies: ['Combined' 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL']
2025-05-21 15:13:42,207 - backtester_stable.BTRUN.runtime - INFO - Saving Excel output to /srv/samba/shared/Trades/exit_time_test_153000_20250521_151335.xlsx
2025-05-21 15:13:42,207 - backtester_stable.BTRUN.runtime - DEBUG - [DEBUG save_backtest_results] save_excel=True, metrics_df is empty: False
2025-05-21 15:13:42,207 - backtester_stable.BTRUN.io - INFO - 2025-05-21 15:13:42.207841, Started writing stats to excel file: /srv/samba/shared/Trades/exit_time_test_153000_20250521_151335.xlsx
2025-05-21 15:13:42,207 - backtester_stable.BTRUN.gpu_helpers - DEBUG - Temporarily switching to CPU-only mode.
2025-05-21 15:13:42,252 - backtester_stable.BTRUN.io - INFO - 2025-05-21 15:13:42.252819, Excel file prepared successfully, Time taken: 0.04s
2025-05-21 15:13:42,252 - backtester_stable.BTRUN.gpu_helpers - DEBUG - Restored GPU_ENABLED to: False
2025-05-21 15:13:42,253 - backtester_stable.BTRUN.runtime - INFO - Saving JSON output to /srv/samba/shared/Trades/exit_time_test_153000_20250521_151335.json
2025-05-21 15:13:42,271 - backtester_stable.BTRUN.io - INFO - --- JSON DUMP DEBUG START ---
2025-05-21 15:13:42,271 - backtester_stable.BTRUN.io - INFO - --- JSON DUMP DEBUG END ---
2025-05-21 15:13:42,271 - backtester_stable.BTRUN.io - INFO - Successfully wrote JSON output to /srv/samba/shared/Trades/exit_time_test_153000_20250521_151335.json
2025-05-21 15:13:42,272 - backtester_stable.BTRUN.io - WARNING - pyttsx3 not installed - skipping text-to-speech
