2025-05-21 15:13:14,941 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-21 15:13:14,941 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-21 15:13:14,942 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages']
2025-05-21 15:13:14,942 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-21 15:13:14,942 - __main__ - DEBUG - GPU enabled: False
2025-05-21 15:13:14,943 - __main__ - INFO - No config provided – defaulting to Excel input format
2025-05-21 15:13:14,944 - __main__ - INFO - GPU acceleration enabled: False
2025-05-21 15:13:14,944 - __main__ - INFO - HeavyDB integration available (Host: 127.0.0.1)
2025-05-21 15:13:15,164 - backtester_stable.BTRUN.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-21 15:13:15,164 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-21 15:13:15,165 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-21 15:13:15,165 - backtester_stable.BTRUN.utils - INFO - Running necessary functions before starting BT...
2025-05-21 15:13:15,165 - backtester_stable.BTRUN.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/bt/backtester_stable/BTRUN/input/fixtures/LOTSIZE.csv
2025-05-21 15:13:15,165 - backtester_stable.BTRUN.utils - INFO - Loading lot sizes from: /srv/samba/shared/bt/backtester_stable/BTRUN/input/fixtures/LOTSIZE.csv
2025-05-21 15:13:15,170 - backtester_stable.BTRUN.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-21 15:13:15,171 - backtester_stable.BTRUN.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-21 15:13:15,171 - backtester_stable.BTRUN.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-21 15:13:15,171 - backtester_stable.BTRUN.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-21 15:13:15,171 - backtester_stable.BTRUN.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-21 15:13:15,172 - backtester_stable.BTRUN.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-21 15:13:15,172 - backtester_stable.BTRUN.utils - INFO - Pre-BT functions complete.
2025-05-21 15:13:15,172 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-21 15:13:15,172 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-21 15:13:15,172 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_exit_100000.xlsx
2025-05-21 15:13:15,172 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-21 15:13:15,172 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_exit_100000.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-21 15:13:15,304 - __main__ - INFO - Successfully parsed 1 portfolio(s) using modern parser.
2025-05-21 15:13:15,304 - __main__ - INFO - Queueing portfolio: NIF0DTE (from modern_portfolio_parser)
2025-05-21 15:13:15,304 - __main__ - INFO - Executing 1 portfolios sequentially (multiprocessing temporarily disabled for debugging)
2025-05-21 15:13:15,304 - __main__ - INFO - Processing payload 1/1: NIF0DTE
2025-05-21 15:13:15,304 - backtester_stable.BTRUN.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'NIF0DTE'
2025-05-21 15:13:15,519 - backtester_stable.BTRUN.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-21 15:13:15,550 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.031s, returned 563 rows. Query: SELECT SUBSTRING('SELECT DISTINCT trade_date FROM nifty_option_chain', 1, 200) ...
2025-05-21 15:13:15,595 - backtester_stable.BTRUN.heavydb_data_access - INFO - Cached 563 distinct trade dates
2025-05-21 15:13:15,595 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] portfolio_model_dict keys: ['portfolio_name', 'start_date', 'end_date', 'slippage_percent', 'margin_multiplier', 'is_tick_bt', 'strategies', 'extra_params']
2025-05-21 15:13:15,596 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-21 15:13:15,596 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '10:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-21 15:13:15,636 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.040s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:15,671 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.035s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''10:00:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:15,672 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523450CE
ce_open                          102.85
ce_high                          106.65
ce_low                            97.85
ce_close                         100.75
ce_volume                       1015050
ce_oi                            978075
ce_coi                                0
ce_iv                             13.44
ce_delta                           0.48
ce_gamma                         0.0015
ce_theta                         -23.27
ce_vega                            7.95
ce_rho                          81.3115
pe_symbol           NIFTY03APR2523450PE
pe_open                           118.2
pe_high                           122.3
pe_low                            112.2
pe_close                          115.7
pe_volume                        877125
pe_oi                           1618950
pe_coi                                0
pe_iv                             13.74
pe_delta                          -0.52
pe_gamma                         0.0015
pe_theta                          -17.3
pe_vega                            7.95
pe_rho                         -88.3744
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-21 15:13:15,673 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     10:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23500.35
atm_strike                      23500.0
strike                          23500.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523500CE
ce_open                           119.1
ce_high                           124.1
ce_low                            111.8
ce_close                          111.8
ce_volume                       1108125
ce_oi                           7222050
ce_coi                           334950
ce_iv                              12.9
ce_delta                           0.53
ce_gamma                         0.0015
ce_theta                         -22.79
ce_vega                            7.96
ce_rho                          89.2491
pe_symbol           NIFTY03APR2523500PE
pe_open                            92.7
pe_high                           100.6
pe_low                            89.15
pe_close                          100.6
pe_volume                       1443075
pe_oi                          10773225
pe_coi                           -65400
pe_iv                             13.67
pe_delta                          -0.47
pe_gamma                         0.0015
pe_theta                         -17.51
pe_vega                            7.96
pe_rho                         -81.1401
future_open                     23630.0
future_high                     23640.0
future_low                      23616.0
future_close                    23616.0
future_volume                     16575
future_oi                      13150200
future_coi                         -300
2025-05-21 15:13:15,673 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 15:13:15,673 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 10:00:00
2025-05-21 15:13:15,673 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 15:13:15,674 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140151834538304
2025-05-21 15:13:15,674 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140151834538304, OptionType.PUT ID: 140151834538416
2025-05-21 15:13:15,674 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 15:13:15,674 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-21 15:13:15,674 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-21 15:13:15,675 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 15:13:15,957 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.281s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 15:13:15,959 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-21 15:13:15,959 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-21 15:13:16,014 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 15:13:16,023 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-21 15:13:16,023 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '10:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-21 15:13:16,049 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.025s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:16,088 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.039s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''10:00:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:16,089 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523450CE
ce_open                          102.85
ce_high                          106.65
ce_low                            97.85
ce_close                         100.75
ce_volume                       1015050
ce_oi                            978075
ce_coi                                0
ce_iv                             13.44
ce_delta                           0.48
ce_gamma                         0.0015
ce_theta                         -23.27
ce_vega                            7.95
ce_rho                          81.3115
pe_symbol           NIFTY03APR2523450PE
pe_open                           118.2
pe_high                           122.3
pe_low                            112.2
pe_close                          115.7
pe_volume                        877125
pe_oi                           1618950
pe_coi                                0
pe_iv                             13.74
pe_delta                          -0.52
pe_gamma                         0.0015
pe_theta                          -17.3
pe_vega                            7.95
pe_rho                         -88.3744
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-21 15:13:16,090 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     10:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23500.35
atm_strike                      23500.0
strike                          23500.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523500CE
ce_open                           119.1
ce_high                           124.1
ce_low                            111.8
ce_close                          111.8
ce_volume                       1108125
ce_oi                           7222050
ce_coi                           334950
ce_iv                              12.9
ce_delta                           0.53
ce_gamma                         0.0015
ce_theta                         -22.79
ce_vega                            7.96
ce_rho                          89.2491
pe_symbol           NIFTY03APR2523500PE
pe_open                            92.7
pe_high                           100.6
pe_low                            89.15
pe_close                          100.6
pe_volume                       1443075
pe_oi                          10773225
pe_coi                           -65400
pe_iv                             13.67
pe_delta                          -0.47
pe_gamma                         0.0015
pe_theta                         -17.51
pe_vega                            7.96
pe_rho                         -81.1401
future_open                     23630.0
future_high                     23640.0
future_low                      23616.0
future_close                    23616.0
future_volume                     16575
future_oi                      13150200
future_coi                         -300
2025-05-21 15:13:16,090 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 15:13:16,090 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 10:00:00
2025-05-21 15:13:16,090 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 15:13:16,091 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140151834538416
2025-05-21 15:13:16,091 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140151834538304, OptionType.PUT ID: 140151834538416
2025-05-21 15:13:16,091 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 15:13:16,091 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-21 15:13:16,091 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-21 15:13:16,092 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 15:13:16,383 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.290s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 15:13:16,384 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-21 15:13:16,385 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-21 15:13:16,440 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 15:13:16,456 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-21 15:13:16,456 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '10:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-21 15:13:16,497 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.040s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:16,546 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.049s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''10:00:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:16,547 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523450CE
ce_open                          102.85
ce_high                          106.65
ce_low                            97.85
ce_close                         100.75
ce_volume                       1015050
ce_oi                            978075
ce_coi                                0
ce_iv                             13.44
ce_delta                           0.48
ce_gamma                         0.0015
ce_theta                         -23.27
ce_vega                            7.95
ce_rho                          81.3115
pe_symbol           NIFTY03APR2523450PE
pe_open                           118.2
pe_high                           122.3
pe_low                            112.2
pe_close                          115.7
pe_volume                        877125
pe_oi                           1618950
pe_coi                                0
pe_iv                             13.74
pe_delta                          -0.52
pe_gamma                         0.0015
pe_theta                          -17.3
pe_vega                            7.95
pe_rho                         -88.3744
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-21 15:13:16,547 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     10:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23500.35
atm_strike                      23500.0
strike                          23500.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523500CE
ce_open                           119.1
ce_high                           124.1
ce_low                            111.8
ce_close                          111.8
ce_volume                       1108125
ce_oi                           7222050
ce_coi                           334950
ce_iv                              12.9
ce_delta                           0.53
ce_gamma                         0.0015
ce_theta                         -22.79
ce_vega                            7.96
ce_rho                          89.2491
pe_symbol           NIFTY03APR2523500PE
pe_open                            92.7
pe_high                           100.6
pe_low                            89.15
pe_close                          100.6
pe_volume                       1443075
pe_oi                          10773225
pe_coi                           -65400
pe_iv                             13.67
pe_delta                          -0.47
pe_gamma                         0.0015
pe_theta                         -17.51
pe_vega                            7.96
pe_rho                         -81.1401
future_open                     23630.0
future_high                     23640.0
future_low                      23616.0
future_close                    23616.0
future_volume                     16575
future_oi                      13150200
future_coi                         -300
2025-05-21 15:13:16,548 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 15:13:16,548 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 10:00:00
2025-05-21 15:13:16,548 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 15:13:16,548 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140151834538304
2025-05-21 15:13:16,548 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140151834538304, OptionType.PUT ID: 140151834538416
2025-05-21 15:13:16,548 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 15:13:16,549 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-21 15:13:16,549 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-21 15:13:16,550 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 15:13:16,947 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.396s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 15:13:16,949 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-21 15:13:16,949 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-21 15:13:17,003 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 15:13:17,018 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-21 15:13:17,018 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-01' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '10:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-21 15:13:17,066 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.047s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:17,107 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.041s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-01'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''10:00:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:17,108 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
trade_date                   2025-04-01
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23420.45
atm_strike                      23450.0
strike                          23450.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523450CE
ce_open                          102.85
ce_high                          106.65
ce_low                            97.85
ce_close                         100.75
ce_volume                       1015050
ce_oi                            978075
ce_coi                                0
ce_iv                             13.44
ce_delta                           0.48
ce_gamma                         0.0015
ce_theta                         -23.27
ce_vega                            7.95
ce_rho                          81.3115
pe_symbol           NIFTY03APR2523450PE
pe_open                           118.2
pe_high                           122.3
pe_low                            112.2
pe_close                          115.7
pe_volume                        877125
pe_oi                           1618950
pe_coi                                0
pe_iv                             13.74
pe_delta                          -0.52
pe_gamma                         0.0015
pe_theta                          -17.3
pe_vega                            7.95
pe_rho                         -88.3744
future_open                     23534.5
future_high                    23543.95
future_low                      23527.0
future_close                   23536.75
future_volume                     86475
future_oi                      12605100
future_coi                            0
2025-05-21 15:13:17,109 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2025-04-01
trade_time                     10:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                           23500.35
atm_strike                      23500.0
strike                          23500.0
dte                                   2
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523500CE
ce_open                           119.1
ce_high                           124.1
ce_low                            111.8
ce_close                          111.8
ce_volume                       1108125
ce_oi                           7222050
ce_coi                           334950
ce_iv                              12.9
ce_delta                           0.53
ce_gamma                         0.0015
ce_theta                         -22.79
ce_vega                            7.96
ce_rho                          89.2491
pe_symbol           NIFTY03APR2523500PE
pe_open                            92.7
pe_high                           100.6
pe_low                            89.15
pe_close                          100.6
pe_volume                       1443075
pe_oi                          10773225
pe_coi                           -65400
pe_iv                             13.67
pe_delta                          -0.47
pe_gamma                         0.0015
pe_theta                         -17.51
pe_vega                            7.96
pe_rho                         -81.1401
future_open                     23630.0
future_high                     23640.0
future_low                      23616.0
future_close                    23616.0
future_volume                     16575
future_oi                      13150200
future_coi                         -300
2025-05-21 15:13:17,109 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 15:13:17,109 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 10:00:00
2025-05-21 15:13:17,110 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 15:13:17,110 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140151834538416
2025-05-21 15:13:17,110 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140151834538304, OptionType.PUT ID: 140151834538416
2025-05-21 15:13:17,110 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 15:13:17,110 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-21 15:13:17,110 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-21 15:13:17,111 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-01' AND trade_date <= '2025-04-01'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 15:13:17,440 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.328s, returned 19682 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 15:13:17,442 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 19682 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-21 15:13:17,442 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-21 15:13:17,497 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 15:13:17,506 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-21 15:13:17,506 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23420.45, 'atm_strike': 23450.0, 'strike': 23450.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523450CE', 'ce_open': 102.85, 'ce_high': 106.65, 'ce_low': 97.85, 'ce_close': 100.75, 'ce_volume': 1015050, 'ce_oi': 978075, 'ce_coi': 0, 'ce_iv': 13.44, 'ce_delta': 0.48, 'ce_gamma': 0.0015, 'ce_theta': -23.27, 'ce_vega': 7.95, 'ce_rho': 81.3115, 'pe_symbol': 'NIFTY03APR2523450PE', 'pe_open': 118.2, 'pe_high': 122.3, 'pe_low': 112.2, 'pe_close': 115.7, 'pe_volume': 877125, 'pe_oi': 1618950, 'pe_coi': 0, 'pe_iv': 13.74, 'pe_delta': -0.52, 'pe_gamma': 0.0015, 'pe_theta': -17.3, 'pe_vega': 7.95, 'pe_rho': -88.3744, 'future_open': 23534.5, 'future_high': 23543.95, 'future_low': 23527.0, 'future_close': 23536.75, 'future_volume': 86475, 'future_oi': 12605100, 'future_coi': 0}
2025-05-21 15:13:17,506 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 1), 'trade_time': 100000, 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23500.35, 'atm_strike': 23500.0, 'strike': 23500.0, 'dte': 2, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523500CE', 'ce_open': 119.1, 'ce_high': 124.1, 'ce_low': 111.8, 'ce_close': 111.8, 'ce_volume': 1108125, 'ce_oi': 7222050, 'ce_coi': 334950, 'ce_iv': 12.9, 'ce_delta': 0.53, 'ce_gamma': 0.0015, 'ce_theta': -22.79, 'ce_vega': 7.96, 'ce_rho': 89.2491, 'pe_symbol': 'NIFTY03APR2523500PE', 'pe_open': 92.7, 'pe_high': 100.6, 'pe_low': 89.15, 'pe_close': 100.6, 'pe_volume': 1443075, 'pe_oi': 10773225, 'pe_coi': -65400, 'pe_iv': 13.67, 'pe_delta': -0.47, 'pe_gamma': 0.0015, 'pe_theta': -17.51, 'pe_vega': 7.96, 'pe_rho': -81.1401, 'future_open': 23630.0, 'future_high': 23640.0, 'future_low': 23616.0, 'future_close': 23616.0, 'future_volume': 16575, 'future_oi': 13150200, 'future_coi': -300, 'close': 2.15, 'datetime': Timestamp('2025-04-01 09:17:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-21 15:13:17,506 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-21 15:13:17,506 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -755.0000000000005, PnL (Slippage): -755.0000000000005, Net PnL: -755.0000000000005
2025-05-21 15:13:17,506 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-21 15:13:17,507 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2025-04-01', 'entry_time': '09:16:00', 'entry_day': 'Tuesday', 'exit_date': '2025-04-01', 'exit_time': '10:00:00', 'exit_day': 'Tuesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23450, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 115.7, 'exit_price': 100.6, 'points': -15.100000000000009, 'pointsAfterSlippage': -15.100000000000009, 'pnl': -755.0000000000005, 'pnlAfterSlippage': -755.0000000000005, 'expenses': 0.0, 'netPnlAfterExpenses': -755.0000000000005, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23420.45, 'index_exit_price': 23500.35, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-01 09:16:00', 'exit_datetime': '2025-04-01 10:00:00'}
2025-05-21 15:13:17,507 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-21 15:13:17,507 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '10:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-21 15:13:17,548 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.041s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:17,585 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.037s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''10:00:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:17,587 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
trade_date                   2025-04-02
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23204.8
atm_strike                      23200.0
strike                          23200.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                          107.95
ce_high                           113.5
ce_low                           105.15
ce_close                          110.5
ce_volume                       2666850
ce_oi                           5862000
ce_coi                                0
ce_iv                             16.53
ce_delta                           0.53
ce_gamma                         0.0015
ce_theta                         -34.48
ce_vega                             6.2
ce_rho                          54.4841
pe_symbol           NIFTY03APR2523200PE
pe_open                            86.9
pe_high                            94.9
pe_low                            85.55
pe_close                          86.15
pe_volume                       3014850
pe_oi                           5689575
pe_coi                                0
pe_iv                             15.06
pe_delta                          -0.47
pe_gamma                         0.0017
pe_theta                         -25.36
pe_vega                             6.2
pe_rho                         -49.7483
future_open                     23339.0
future_high                     23344.7
future_low                     23331.25
future_close                   23344.15
future_volume                     43725
future_oi                      12808425
future_coi                            0
2025-05-21 15:13:17,587 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2025-04-02
trade_time                     10:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23226.4
atm_strike                      23250.0
strike                          23250.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523250CE
ce_open                           99.15
ce_high                           99.15
ce_low                            81.75
ce_close                          83.15
ce_volume                       2474775
ce_oi                           6867825
ce_coi                           129150
ce_iv                             14.39
ce_delta                           0.48
ce_gamma                         0.0018
ce_theta                          -30.2
ce_vega                            6.21
ce_rho                          49.7165
pe_symbol           NIFTY03APR2523250PE
pe_open                            82.5
pe_high                            98.9
pe_low                             82.5
pe_close                           96.9
pe_volume                       1701075
pe_oi                           5025900
pe_coi                            39150
pe_iv                              14.5
pe_delta                          -0.52
pe_gamma                         0.0018
pe_theta                         -24.03
pe_vega                            6.21
pe_rho                         -54.9864
future_open                     23382.0
future_high                     23382.0
future_low                      23355.0
future_close                    23359.2
future_volume                     15900
future_oi                      12718425
future_coi                        -3450
2025-05-21 15:13:17,588 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 15:13:17,588 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 10:00:00
2025-05-21 15:13:17,588 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 15:13:17,588 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140151834538304
2025-05-21 15:13:17,588 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140151834538304, OptionType.PUT ID: 140151834538416
2025-05-21 15:13:17,589 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 15:13:17,589 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-21 15:13:17,589 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-21 15:13:17,590 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-02' AND trade_date <= '2025-04-02'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 15:13:17,913 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.323s, returned 18845 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 15:13:17,916 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18845 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-21 15:13:17,916 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-21 15:13:17,998 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 15:13:18,011 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-21 15:13:18,011 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '10:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-21 15:13:18,063 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.051s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:18,106 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.043s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''10:00:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:18,108 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
trade_date                   2025-04-02
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23204.8
atm_strike                      23200.0
strike                          23200.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                          107.95
ce_high                           113.5
ce_low                           105.15
ce_close                          110.5
ce_volume                       2666850
ce_oi                           5862000
ce_coi                                0
ce_iv                             16.53
ce_delta                           0.53
ce_gamma                         0.0015
ce_theta                         -34.48
ce_vega                             6.2
ce_rho                          54.4841
pe_symbol           NIFTY03APR2523200PE
pe_open                            86.9
pe_high                            94.9
pe_low                            85.55
pe_close                          86.15
pe_volume                       3014850
pe_oi                           5689575
pe_coi                                0
pe_iv                             15.06
pe_delta                          -0.47
pe_gamma                         0.0017
pe_theta                         -25.36
pe_vega                             6.2
pe_rho                         -49.7483
future_open                     23339.0
future_high                     23344.7
future_low                     23331.25
future_close                   23344.15
future_volume                     43725
future_oi                      12808425
future_coi                            0
2025-05-21 15:13:18,109 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2025-04-02
trade_time                     10:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23226.4
atm_strike                      23250.0
strike                          23250.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523250CE
ce_open                           99.15
ce_high                           99.15
ce_low                            81.75
ce_close                          83.15
ce_volume                       2474775
ce_oi                           6867825
ce_coi                           129150
ce_iv                             14.39
ce_delta                           0.48
ce_gamma                         0.0018
ce_theta                          -30.2
ce_vega                            6.21
ce_rho                          49.7165
pe_symbol           NIFTY03APR2523250PE
pe_open                            82.5
pe_high                            98.9
pe_low                             82.5
pe_close                           96.9
pe_volume                       1701075
pe_oi                           5025900
pe_coi                            39150
pe_iv                              14.5
pe_delta                          -0.52
pe_gamma                         0.0018
pe_theta                         -24.03
pe_vega                            6.21
pe_rho                         -54.9864
future_open                     23382.0
future_high                     23382.0
future_low                      23355.0
future_close                    23359.2
future_volume                     15900
future_oi                      12718425
future_coi                        -3450
2025-05-21 15:13:18,109 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 15:13:18,109 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 10:00:00
2025-05-21 15:13:18,110 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 15:13:18,110 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140151834538416
2025-05-21 15:13:18,110 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140151834538304, OptionType.PUT ID: 140151834538416
2025-05-21 15:13:18,110 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 15:13:18,110 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-21 15:13:18,110 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-21 15:13:18,112 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-02' AND trade_date <= '2025-04-02'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 15:13:18,435 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.323s, returned 18845 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 15:13:18,437 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18845 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-21 15:13:18,438 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-21 15:13:18,518 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 15:13:18,540 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-21 15:13:18,540 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '10:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-21 15:13:18,584 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.043s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:18,624 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.040s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''10:00:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:18,625 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
trade_date                   2025-04-02
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23204.8
atm_strike                      23200.0
strike                          23200.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                          107.95
ce_high                           113.5
ce_low                           105.15
ce_close                          110.5
ce_volume                       2666850
ce_oi                           5862000
ce_coi                                0
ce_iv                             16.53
ce_delta                           0.53
ce_gamma                         0.0015
ce_theta                         -34.48
ce_vega                             6.2
ce_rho                          54.4841
pe_symbol           NIFTY03APR2523200PE
pe_open                            86.9
pe_high                            94.9
pe_low                            85.55
pe_close                          86.15
pe_volume                       3014850
pe_oi                           5689575
pe_coi                                0
pe_iv                             15.06
pe_delta                          -0.47
pe_gamma                         0.0017
pe_theta                         -25.36
pe_vega                             6.2
pe_rho                         -49.7483
future_open                     23339.0
future_high                     23344.7
future_low                     23331.25
future_close                   23344.15
future_volume                     43725
future_oi                      12808425
future_coi                            0
2025-05-21 15:13:18,626 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2025-04-02
trade_time                     10:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23226.4
atm_strike                      23250.0
strike                          23250.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523250CE
ce_open                           99.15
ce_high                           99.15
ce_low                            81.75
ce_close                          83.15
ce_volume                       2474775
ce_oi                           6867825
ce_coi                           129150
ce_iv                             14.39
ce_delta                           0.48
ce_gamma                         0.0018
ce_theta                          -30.2
ce_vega                            6.21
ce_rho                          49.7165
pe_symbol           NIFTY03APR2523250PE
pe_open                            82.5
pe_high                            98.9
pe_low                             82.5
pe_close                           96.9
pe_volume                       1701075
pe_oi                           5025900
pe_coi                            39150
pe_iv                              14.5
pe_delta                          -0.52
pe_gamma                         0.0018
pe_theta                         -24.03
pe_vega                            6.21
pe_rho                         -54.9864
future_open                     23382.0
future_high                     23382.0
future_low                      23355.0
future_close                    23359.2
future_volume                     15900
future_oi                      12718425
future_coi                        -3450
2025-05-21 15:13:18,626 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 15:13:18,627 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 10:00:00
2025-05-21 15:13:18,627 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 15:13:18,627 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140151834538304
2025-05-21 15:13:18,627 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140151834538304, OptionType.PUT ID: 140151834538416
2025-05-21 15:13:18,627 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 15:13:18,628 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-21 15:13:18,628 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-21 15:13:18,629 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-02' AND trade_date <= '2025-04-02'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 15:13:18,947 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.317s, returned 18845 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 15:13:18,949 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18845 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-21 15:13:18,950 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-21 15:13:19,038 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 15:13:19,059 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-21 15:13:19,059 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2025-04-02' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME '10:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-21 15:13:19,093 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.034s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time >= TIME ''09:16:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:19,123 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.029s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2025-04-02'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.trade_time <= TIME ''10:00:00''
ORDER BY oc.trade_time ', 1, 200) ...
2025-05-21 15:13:19,124 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
trade_date                   2025-04-02
trade_time                     09:16:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23204.8
atm_strike                      23200.0
strike                          23200.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523200CE
ce_open                          107.95
ce_high                           113.5
ce_low                           105.15
ce_close                          110.5
ce_volume                       2666850
ce_oi                           5862000
ce_coi                                0
ce_iv                             16.53
ce_delta                           0.53
ce_gamma                         0.0015
ce_theta                         -34.48
ce_vega                             6.2
ce_rho                          54.4841
pe_symbol           NIFTY03APR2523200PE
pe_open                            86.9
pe_high                            94.9
pe_low                            85.55
pe_close                          86.15
pe_volume                       3014850
pe_oi                           5689575
pe_coi                                0
pe_iv                             15.06
pe_delta                          -0.47
pe_gamma                         0.0017
pe_theta                         -25.36
pe_vega                             6.2
pe_rho                         -49.7483
future_open                     23339.0
future_high                     23344.7
future_low                     23331.25
future_close                   23344.15
future_volume                     43725
future_oi                      12808425
future_coi                            0
2025-05-21 15:13:19,125 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2025-04-02
trade_time                     10:00:00
expiry_date                  2025-04-03
index_name                        NIFTY
spot                            23226.4
atm_strike                      23250.0
strike                          23250.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY03APR2523250CE
ce_open                           99.15
ce_high                           99.15
ce_low                            81.75
ce_close                          83.15
ce_volume                       2474775
ce_oi                           6867825
ce_coi                           129150
ce_iv                             14.39
ce_delta                           0.48
ce_gamma                         0.0018
ce_theta                          -30.2
ce_vega                            6.21
ce_rho                          49.7165
pe_symbol           NIFTY03APR2523250PE
pe_open                            82.5
pe_high                            98.9
pe_low                             82.5
pe_close                           96.9
pe_volume                       1701075
pe_oi                           5025900
pe_coi                            39150
pe_iv                              14.5
pe_delta                          -0.52
pe_gamma                         0.0018
pe_theta                         -24.03
pe_vega                            6.21
pe_rho                         -54.9864
future_open                     23382.0
future_high                     23382.0
future_low                      23355.0
future_close                    23359.2
future_volume                     15900
future_oi                      12718425
future_coi                        -3450
2025-05-21 15:13:19,125 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-21 15:13:19,126 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 10:00:00
2025-05-21 15:13:19,126 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-21 15:13:19,126 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140151834538416
2025-05-21 15:13:19,126 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140151834538304, OptionType.PUT ID: 140151834538416
2025-05-21 15:13:19,126 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-21 15:13:19,126 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-21 15:13:19,127 - backtester_stable.BTRUN.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-21 15:13:19,128 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2025-04-02' AND trade_date <= '2025-04-02'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-21 15:13:19,425 - backtester_stable.BTRUN.heavydb_connection - INFO - Query executed in 0.297s, returned 18845 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-21 15:13:19,428 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 18845 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-21 15:13:19,428 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-21 15:13:19,508 - backtester_stable.BTRUN.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-21 15:13:19,521 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-21 15:13:19,521 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2025, 4, 2), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23204.8, 'atm_strike': 23200.0, 'strike': 23200.0, 'dte': 1, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523200CE', 'ce_open': 107.95, 'ce_high': 113.5, 'ce_low': 105.15, 'ce_close': 110.5, 'ce_volume': 2666850, 'ce_oi': 5862000, 'ce_coi': 0, 'ce_iv': 16.53, 'ce_delta': 0.53, 'ce_gamma': 0.0015, 'ce_theta': -34.48, 'ce_vega': 6.2, 'ce_rho': 54.4841, 'pe_symbol': 'NIFTY03APR2523200PE', 'pe_open': 86.9, 'pe_high': 94.9, 'pe_low': 85.55, 'pe_close': 86.15, 'pe_volume': 3014850, 'pe_oi': 5689575, 'pe_coi': 0, 'pe_iv': 15.06, 'pe_delta': -0.47, 'pe_gamma': 0.0017, 'pe_theta': -25.36, 'pe_vega': 6.2, 'pe_rho': -49.7483, 'future_open': 23339.0, 'future_high': 23344.7, 'future_low': 23331.25, 'future_close': 23344.15, 'future_volume': 43725, 'future_oi': 12808425, 'future_coi': 0}
2025-05-21 15:13:19,522 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2025, 4, 2), 'trade_time': 100000, 'expiry_date': datetime.date(2025, 4, 3), 'index_name': 'NIFTY', 'spot': 23226.4, 'atm_strike': 23250.0, 'strike': 23250.0, 'dte': 1, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ATM', 'put_strike_type': 'ATM', 'ce_symbol': 'NIFTY03APR2523250CE', 'ce_open': 99.15, 'ce_high': 99.15, 'ce_low': 81.75, 'ce_close': 83.15, 'ce_volume': 2474775, 'ce_oi': 6867825, 'ce_coi': 129150, 'ce_iv': 14.39, 'ce_delta': 0.48, 'ce_gamma': 0.0018, 'ce_theta': -30.2, 'ce_vega': 6.21, 'ce_rho': 49.7165, 'pe_symbol': 'NIFTY03APR2523250PE', 'pe_open': 82.5, 'pe_high': 98.9, 'pe_low': 82.5, 'pe_close': 96.9, 'pe_volume': 1701075, 'pe_oi': 5025900, 'pe_coi': 39150, 'pe_iv': 14.5, 'pe_delta': -0.52, 'pe_gamma': 0.0018, 'pe_theta': -24.03, 'pe_vega': 6.21, 'pe_rho': -54.9864, 'future_open': 23382.0, 'future_high': 23382.0, 'future_low': 23355.0, 'future_close': 23359.2, 'future_volume': 15900, 'future_oi': 12718425, 'future_coi': -3450, 'close': 1.14, 'datetime': Timestamp('2025-04-02 09:17:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-21 15:13:19,522 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-21 15:13:19,522 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: 537.5, PnL (Slippage): 537.5, Net PnL: 537.5
2025-05-21 15:13:19,522 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-21 15:13:19,522 - backtester_stable.BTRUN.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2025-04-02', 'entry_time': '09:16:00', 'entry_day': 'Wednesday', 'exit_date': '2025-04-02', 'exit_time': '10:00:00', 'exit_day': 'Wednesday', 'symbol': 'NIFTY', 'expiry': '2025-04-03', 'strike': 23200, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 86.15, 'exit_price': 96.9, 'points': 10.75, 'pointsAfterSlippage': 10.75, 'pnl': 537.5, 'pnlAfterSlippage': 537.5, 'expenses': 0.0, 'netPnlAfterExpenses': 537.5, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 23204.8, 'index_exit_price': 23226.4, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2025-04-02 09:16:00', 'exit_datetime': '2025-04-02 10:00:00'}
2025-05-21 15:13:19,522 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - [DEBUG] Number of trade records generated: 2
2025-05-21 15:13:19,522 - backtester_stable.BTRUN.heavydb_trade_processing - INFO - Generated 2 trade records via model-driven path
2025-05-21 15:13:19,523 - backtester_stable.BTRUN.heavydb_trade_processing - DEBUG - Closed reusable HeavyDB connection from get_trades_for_portfolio
2025-05-21 15:13:19,549 - backtester_stable.BTRUN.builders - WARNING - utils.MARGIN_INFO not found or empty - margin requirements will be 0. Attempting to populate.
2025-05-21 15:13:19,550 - backtester_stable.BTRUN.config - INFO - Found fixture 'margin_symbol_info.json' at /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-21 15:13:19,550 - backtester_stable.BTRUN.utils - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json (Broker: angel)
2025-05-21 15:13:19,550 - backtester_stable.BTRUN.utils - INFO - Successfully populated config.MARGIN_INFO for angel.
2025-05-21 15:13:19,550 - backtester_stable.BTRUN.builders - ERROR - utils.MARGIN_INFO still empty after populate attempt. Margin req will be 0.
2025-05-21 15:13:19,552 - backtester_stable.BTRUN.builders - DEBUG - Strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL margin requirement: 0.00
2025-05-21 15:13:19,553 - backtester_stable.BTRUN.builders - INFO - Total portfolio margin requirement: 0.00
2025-05-21 15:13:19,553 - backtester_stable.BTRUN.builders - DEBUG - Converting tick PnL to daywise DataFrame.
2025-05-21 15:13:19,554 - backtester_stable.BTRUN.builders - INFO - Backtest response parsed.
2025-05-21 15:13:19,600 - backtester_stable.BTRUN.runtime - INFO - Generated metrics_df with 50 records for strategies: ['Combined' 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL']
2025-05-21 15:13:19,691 - backtester_stable.BTRUN.runtime - INFO - Saving Excel output to /srv/samba/shared/Trades/exit_time_test_100000_20250521_151312.xlsx
2025-05-21 15:13:19,691 - backtester_stable.BTRUN.runtime - DEBUG - [DEBUG save_backtest_results] save_excel=True, metrics_df is empty: False
2025-05-21 15:13:19,691 - backtester_stable.BTRUN.io - INFO - 2025-05-21 15:13:19.691311, Started writing stats to excel file: /srv/samba/shared/Trades/exit_time_test_100000_20250521_151312.xlsx
2025-05-21 15:13:19,691 - backtester_stable.BTRUN.gpu_helpers - DEBUG - Temporarily switching to CPU-only mode.
2025-05-21 15:13:19,759 - backtester_stable.BTRUN.io - INFO - 2025-05-21 15:13:19.759886, Excel file prepared successfully, Time taken: 0.07s
2025-05-21 15:13:19,760 - backtester_stable.BTRUN.gpu_helpers - DEBUG - Restored GPU_ENABLED to: False
2025-05-21 15:13:19,760 - backtester_stable.BTRUN.runtime - INFO - Saving JSON output to /srv/samba/shared/Trades/exit_time_test_100000_20250521_151312.json
2025-05-21 15:13:19,788 - backtester_stable.BTRUN.io - INFO - --- JSON DUMP DEBUG START ---
2025-05-21 15:13:19,788 - backtester_stable.BTRUN.io - INFO - --- JSON DUMP DEBUG END ---
2025-05-21 15:13:19,788 - backtester_stable.BTRUN.io - INFO - Successfully wrote JSON output to /srv/samba/shared/Trades/exit_time_test_100000_20250521_151312.json
2025-05-21 15:13:19,789 - backtester_stable.BTRUN.io - WARNING - pyttsx3 not installed - skipping text-to-speech
