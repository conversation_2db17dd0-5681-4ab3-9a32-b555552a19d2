2025-05-11 08:49:41,390 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-11 08:49:41,390 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-11 08:49:41,390 - __main__ - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-11 08:49:41,390 - __main__ - DEBUG - HeavyDB host: **************, DB: heavydb
2025-05-11 08:49:41,390 - __main__ - DEBUG - GPU enabled: True
2025-05-11 08:49:41,391 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting BTRunPortfolio_GPU.py - Debugging enabled
2025-05-11 08:49:41,391 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Current working directory: /srv/samba/shared
2025-05-11 08:49:41,391 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-11 08:49:41,391 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Importing BTRUN modules...
2025-05-11 08:49:41,391 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Config module imported from: /srv/samba/shared/bt/backtester_stable/BTRUN/config.py
2025-05-11 08:49:41,391 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - HeavyDB settings - Host: **************, Database: heavydb
2025-05-11 08:49:41,391 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - GPU enabled: True
2025-05-11 08:49:41,391 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Successfully imported heavydb_helpers module
2025-05-11 08:49:41,393 - __main__ - INFO - GPU acceleration enabled: True
2025-05-11 08:49:41,461 - __main__ - INFO - GPU Memory: 35419MB free / 40384MB total
2025-05-11 08:49:41,461 - __main__ - INFO - HeavyDB integration available (Host: **************)
2025-05-11 08:49:41,678 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 08:49:41,678 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-11 08:49:41,678 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-11 08:49:41,693 - bt.backtester_stable.BTRUN.Util - INFO - Running necessary functions before starting BT...
2025-05-11 08:49:41,693 - bt.backtester_stable.BTRUN.config - DEBUG - Found fixture at /srv/samba/shared/LOTSIZE.csv
2025-05-11 08:49:41,693 - bt.backtester_stable.BTRUN.Util - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-11 08:49:41,695 - bt.backtester_stable.BTRUN.config - WARNING - Fixture margin_symbol_info.json not found in any expected location
2025-05-11 08:49:41,695 - bt.backtester_stable.BTRUN.Util - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/margin_symbol_info.json (Broker: angel)
2025-05-11 08:49:41,695 - bt.backtester_stable.BTRUN.Util - INFO - Pre-BT functions complete.
2025-05-11 08:49:41,695 - __main__ - INFO - Using new Excel input format (PortfolioSetting & StrategySetting)
2025-05-11 08:49:41,696 - bt.backtester_stable.BTRUN.config - INFO - Found portfolio file at /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-11 08:49:41,696 - __main__ - INFO - Reading PortfolioSetting & StrategySetting from /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-11 08:49:41,791 - __main__ - DEBUG - Loaded 1 PortfolioSetting rows, 21 StrategySetting rows
2025-05-11 08:49:41,791 - __main__ - INFO - Queueing portfolio: NIF0DTE
2025-05-11 08:49:41,791 - __main__ - DEBUG - Building portfolio request for portfolio: NIF0DTE
2025-05-11 08:49:41,793 - __main__ - DEBUG - Built request for NIF0DTE with 0 strategies
2025-05-11 08:49:41,793 - __main__ - WARNING - No strategies parsed for NIF0DTE; check Excel files and parsing logic
2025-05-11 08:49:41,793 - __main__ - INFO - Executing 1 portfolios sequentially
2025-05-11 08:49:41,793 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting single backtest for portfolio
2025-05-11 08:49:41,794 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Using HeavyDB for data access
2025-05-11 08:49:41,794 - bt.backtester_stable.BTRUN.runtime - INFO - Fetching trades from local HeavyDB for portfolio ''
2025-05-11 08:49:42,008 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 08:49:45,435 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 3.427s, returned 563 rows
2025-05-11 08:49:45,479 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Cached 563 distinct trade dates
2025-05-11 08:49:45,480 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - No strategies found inside bt_params – returning empty trade list
2025-05-11 08:49:45,480 - bt.backtester_stable.BTRUN.builders - INFO - Parsing backtest response...
2025-05-11 08:49:45,480 - bt.backtester_stable.BTRUN.builders - WARNING - No trades data in backtest response or invalid format.
2025-05-11 08:49:45,480 - bt.backtester_stable.BTRUN.runtime - WARNING - No trades found in backtest response
2025-05-11 08:49:45,481 - bt.backtester_stable.BTRUN.runtime - INFO - Saving JSON output to Trades/NIF0DTE_11052025_084941.json
2025-05-11 08:49:45,481 - bt.backtester_stable.BTRUN.runtime - WARNING - Empty metrics DataFrame - not saving JSON output
2025-05-11 08:49:45,482 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Backtest completed in 3.69 seconds
2025-05-11 08:49:45,482 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Portfolio backtest completed successfully with 0 trades
2025-05-11 08:49:45,482 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Saved json file to Trades/NIF0DTE_11052025_084941.json
2025-05-11 08:49:45,482 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Saved excel file to Trades/NIF0DTE_11052025_084941.xlsx
2025-05-11 08:49:45,482 - __main__ - INFO - Profiling portfolio: 3.69s GPUfree 35419MB
