2025-05-29 13:37:31,498 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-29 13:37:31,498 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-29 13:37:31,498 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/../models', '/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages', '/srv/samba/shared']
2025-05-29 13:37:31,498 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-29 13:37:31,498 - __main__ - DEBUG - GPU enabled: False
2025-05-29 13:37:31,500 - __main__ - INFO - GPU optimization enabled with 2 GPU workers, 2 CPU workers
2025-05-29 13:37:31,500 - __main__ - INFO - GPU acceleration enabled: False
2025-05-29 13:37:31,500 - __main__ - INFO - HeavyDB integration available (Host: 127.0.0.1)
2025-05-29 13:37:31,731 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-29 13:37:31,731 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-29 13:37:31,732 - __main__ - INFO - Runtime flags – workers: 2, retry_cpu: False
2025-05-29 13:37:31,732 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-29 13:37:31,732 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-29 13:37:31,732 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-29 13:37:31,738 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-29 13:37:31,738 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-29 13:37:31,738 - backtester_stable.BTRUN.core.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-29 13:37:31,739 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-29 13:37:31,739 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-29 13:37:31,739 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-29 13:37:31,739 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-29 13:37:31,739 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-29 13:37:31,739 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-29 13:37:31,739 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_exit_120000.xlsx
2025-05-29 13:37:31,739 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-29 13:37:31,739 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_exit_120000.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-29 13:37:31,873 - __main__ - INFO - Successfully parsed 1 portfolio(s) using modern parser.
2025-05-29 13:37:31,873 - __main__ - INFO - Using GPU-optimized batch processing
2025-05-29 13:37:31,873 - __main__ - INFO - Processing portfolio: NIF0DTE with GPU optimization
2025-05-29 13:37:31,873 - __main__ - INFO - Created 5 date batches for parallel processing
2025-05-29 13:37:31,911 - common.gpu_worker_pool - INFO - Starting GPU worker pool with 2 GPU workers on 1 GPUs
2025-05-29 13:37:31,913 - common.gpu_worker_pool - INFO - CPU fallback enabled with 2 CPU workers
2025-05-29 13:37:32,023 - __main__ - ERROR - Batch processing failed: Pickling an AuthenticationString object is disallowed for security reasons
2025-05-29 13:37:32,024 - __main__ - ERROR - Batch processing failed: Pickling an AuthenticationString object is disallowed for security reasons
2025-05-29 13:37:32,024 - __main__ - ERROR - Batch processing failed: Pickling an AuthenticationString object is disallowed for security reasons
2025-05-29 13:37:32,024 - __main__ - ERROR - Batch processing failed: Pickling an AuthenticationString object is disallowed for security reasons
2025-05-29 13:37:32,024 - __main__ - ERROR - Batch processing failed: Pickling an AuthenticationString object is disallowed for security reasons
2025-05-29 13:37:32,025 - common.gpu_worker_pool - INFO - Stopping GPU worker pool
2025-05-29 13:37:32,188 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-29 13:37:32,188 - common.gpu_worker_pool - INFO - GPU worker 0 initialized on device 0 with HeavyDB connection
2025-05-29 13:37:32,195 - bt.backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-29 13:37:32,196 - common.gpu_worker_pool - INFO - GPU worker 0 initialized on device 0 with HeavyDB connection
2025-05-29 13:37:32,951 - __main__ - ERROR - Failed to process portfolio NIF0DTE
2025-05-29 13:37:32,966 - backtester_stable.BTRUN.core.io - ERROR - Text-to-speech failed: This means you probably do not have eSpeak or eSpeak-ng installed!
