2025-05-10 10:48:37,641 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-10 10:48:37,642 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-10 10:48:37,642 - __main__ - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-10 10:48:37,642 - __main__ - DEBUG - HeavyDB host: **************, DB: heavydb
2025-05-10 10:48:37,642 - __main__ - DEBUG - GPU enabled: True
2025-05-10 10:48:37,643 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting BTRunPortfolio_GPU.py - Debugging enabled
2025-05-10 10:48:37,643 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Current working directory: /srv/samba/shared
2025-05-10 10:48:37,643 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-10 10:48:37,644 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Importing BTRUN modules...
2025-05-10 10:48:37,644 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Config module imported from: /srv/samba/shared/bt/backtester_stable/BTRUN/config.py
2025-05-10 10:48:37,644 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - HeavyDB settings - Host: **************, Database: heavydb
2025-05-10 10:48:37,644 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - GPU enabled: True
2025-05-10 10:48:37,644 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Successfully imported heavydb_helpers module
2025-05-10 10:48:37,644 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Entering main function
2025-05-10 10:48:37,644 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Parsing command line arguments
2025-05-10 10:48:37,645 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Parsed arguments: Namespace(config=None, legacy_excel=True, strategies=None, start_date=None, end_date=None, output_dir='Trades', portfolio_name=None, slippage=0.1, capital=None, margin_multiplier=1.0, no_json=False, no_excel=False, no_charts=False, cpu_only=False, merge_output=False, debug=False)
2025-05-10 10:48:37,646 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Text-to-speech notifications not available
2025-05-10 10:48:37,646 - __main__ - INFO - GPU acceleration enabled: True
2025-05-10 10:48:37,717 - __main__ - INFO - GPU Memory: 35421MB free / 40384MB total
2025-05-10 10:48:37,717 - __main__ - INFO - HeavyDB integration available (Host: **************)
2025-05-10 10:48:37,934 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-10 10:48:37,935 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-10 10:48:37,935 - __main__ - INFO - Using new Excel input format (PortfolioSetting & StrategySetting)
2025-05-10 10:48:37,935 - bt.backtester_stable.BTRUN.config - INFO - Found portfolio file at /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-10 10:48:37,935 - __main__ - INFO - Reading PortfolioSetting & StrategySetting from /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-10 10:48:38,035 - __main__ - DEBUG - Loaded 1 PortfolioSetting rows, 21 StrategySetting rows
2025-05-10 10:48:38,114 - __main__ - DEBUG - Loaded 1 rows from /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx
2025-05-10 10:48:38,115 - __main__ - INFO - Processing portfolio: NIF0DTE
2025-05-10 10:48:38,115 - __main__ - DEBUG - Building portfolio request for portfolio: NIF0DTE
2025-05-10 10:48:38,116 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting single backtest for NIF0DTE
2025-05-10 10:48:38,116 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Using HeavyDB for data access
2025-05-10 10:48:38,116 - bt.backtester_stable.BTRUN.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'NIF0DTE'
2025-05-10 10:48:38,334 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-10 10:48:43,123 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 4.789s, returned 5998 rows
2025-05-10 10:48:43,581 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Generated 11996 synthetic trade rows from option chain
2025-05-10 10:48:43,582 - bt.backtester_stable.BTRUN.builders - INFO - Parsing backtest response...
2025-05-10 10:48:44,566 - bt.backtester_stable.BTRUN.builders - WARNING - Margin calculation in builders.parse_backtest_response is a placeholder.
2025-05-10 10:48:44,566 - bt.backtester_stable.BTRUN.builders - DEBUG - Converting tick PnL to daywise DataFrame.
2025-05-10 10:48:44,568 - bt.backtester_stable.BTRUN.builders - INFO - Backtest response parsed.
2025-05-10 10:48:47,194 - bt.backtester_stable.BTRUN.gpu_helpers - WARNING - Failed to convert to cuDF: Could not convert 'Total' with type str: tried to convert to int64. Falling back to pandas.
2025-05-10 10:48:47,248 - bt.backtester_stable.BTRUN.gpu_helpers - WARNING - Failed to convert to cuDF: Could not convert 'Total' with type str: tried to convert to int64. Falling back to pandas.
2025-05-10 10:48:47,249 - bt.backtester_stable.BTRUN.runtime - INFO - Saving Excel output to Trades/NIF0DTE_10052025_104838.xlsx
2025-05-10 10:48:47,249 - bt.backtester_stable.BTRUN.io - INFO - 2025-05-10 10:48:47.249493, Started writing stats to excel file: Trades/NIF0DTE_10052025_104838.xlsx
2025-05-10 10:48:47,249 - bt.backtester_stable.BTRUN.gpu_helpers - DEBUG - Temporarily switching to CPU-only mode.
2025-05-10 10:48:47,276 - bt.backtester_stable.BTRUN.io - WARNING - Skipping rename of column 'exit_price' to 'Exit at' as it would duplicate an existing column name.
2025-05-10 10:48:52,745 - bt.backtester_stable.BTRUN.gpu_helpers - DEBUG - Restored GPU_ENABLED to: True
2025-05-10 10:48:52,745 - bt.backtester_stable.BTRUN.io - INFO - 2025-05-10 10:48:52.745774, Excel file prepared successfully, Time taken: 5.5s
2025-05-10 10:48:52,746 - bt.backtester_stable.BTRUN.runtime - INFO - Saving JSON output to Trades/NIF0DTE_10052025_104838.json
2025-05-10 10:48:53,344 - bt.backtester_stable.BTRUN.io - INFO - Successfully wrote JSON output to Trades/NIF0DTE_10052025_104838.json
2025-05-10 10:48:53,354 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Backtest completed in 15.24 seconds
2025-05-10 10:48:53,354 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Portfolio backtest completed successfully with 11996 trades
2025-05-10 10:48:53,354 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Saved json file to Trades/NIF0DTE_10052025_104838.json
2025-05-10 10:48:53,354 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Saved excel file to Trades/NIF0DTE_10052025_104838.xlsx
2025-05-10 10:48:53,355 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Backtest Summary: {
  "Total PnL": 0.0,
  "Win Rate": 0.0,
  "Sharpe Ratio": 0.0,
  "CAGR": 0.0,
  "Max Drawdown": 0.0
}
2025-05-10 10:48:53,356 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Backtest completed with 1 results
2025-05-10 10:48:53,356 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Exiting main function
