2025-05-11 20:57:10,056 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-11 20:57:10,056 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-11 20:57:10,057 - __main__ - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-11 20:57:10,057 - __main__ - DEBUG - HeavyDB host: **************, DB: heavydb
2025-05-11 20:57:10,057 - __main__ - DEBUG - GPU enabled: True
2025-05-11 20:57:10,060 - __main__ - INFO - No config provided – defaulting to Excel input format
2025-05-11 20:57:10,060 - __main__ - INFO - GPU acceleration enabled: True
2025-05-11 20:57:10,144 - __main__ - INFO - GPU Memory: 34535MB free / 40384MB total
2025-05-11 20:57:10,144 - __main__ - INFO - HeavyDB integration available (Host: **************)
2025-05-11 20:57:10,421 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:57:10,421 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-11 20:57:10,422 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-11 20:57:10,426 - bt.backtester_stable.BTRUN.Util - INFO - Running necessary functions before starting BT...
2025-05-11 20:57:10,426 - bt.backtester_stable.BTRUN.config - DEBUG - Found fixture at /srv/samba/shared/LOTSIZE.csv
2025-05-11 20:57:10,426 - bt.backtester_stable.BTRUN.Util - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-11 20:57:10,436 - bt.backtester_stable.BTRUN.Util - INFO - Successfully populated LOT_SIZE: 50 entries.
2025-05-11 20:57:10,436 - bt.backtester_stable.BTRUN.config - WARNING - Fixture margin_symbol_info.json not found in any expected location
2025-05-11 20:57:10,437 - bt.backtester_stable.BTRUN.Util - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/margin_symbol_info.json (Broker: angel)
2025-05-11 20:57:10,437 - bt.backtester_stable.BTRUN.Util - INFO - Pre-BT functions complete.
2025-05-11 20:57:10,437 - __main__ - INFO - Using new Excel input format (PortfolioSetting & StrategySetting)
2025-05-11 20:57:10,437 - __main__ - INFO - Reading PortfolioSetting & StrategySetting from /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-11 20:57:10,553 - __main__ - DEBUG - Loaded 1 PortfolioSetting rows, 21 StrategySetting rows
2025-05-11 20:57:10,554 - __main__ - INFO - Queueing portfolio: NIF0DTE
2025-05-11 20:57:10,554 - __main__ - DEBUG - Building portfolio request for portfolio: NIF0DTE
2025-05-11 20:57:10,555 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:57:10,556 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:57:10,556 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:57:10,556 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:57:10,556 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:57:10,556 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:57:10,556 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:57:10,556 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:57:10,557 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:57:10,557 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:57:10,557 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:57:10,557 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:57:10,557 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:57:10,557 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:57:10,659 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-11 20:57:10,659 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY CALL at 2025-05-11 09:16:00.659876 via method 'ATM'
2025-05-11 20:57:10,660 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-11 20:57:10,660 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY PUT at 2025-05-11 09:16:00.660208 via method 'ATM'
2025-05-11 20:57:10,660 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-11 20:57:10,660 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY CALL at 2025-05-11 09:16:00.660491 via method 'ATM'
2025-05-11 20:57:10,660 - bt.backtester_stable.BTRUN.Util - WARNING - entry_date missing in getBackendLegJson, using StartDate from strategy.
2025-05-11 20:57:10,660 - bt.backtester_stable.BTRUN.Util - INFO - Getting strike for NIFTY PUT at 2025-05-11 09:16:00.660764 via method 'ATM'
2025-05-11 20:57:10,661 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:57:10,661 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:57:10,661 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:57:10,661 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:57:10,661 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:57:10,661 - __main__ - DEBUG - Skipping disabled strategy: None
2025-05-11 20:57:10,661 - __main__ - DEBUG - Built request for NIF0DTE with 1 strategies
2025-05-11 20:57:10,662 - __main__ - DEBUG - First strategy sample: {"id": "", "name": "RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL", "evaluator": "Tbs", "type": "STRATEGYTYPE.INTRADAY", "index": "NSEINDEX.NIFTY", "underlying": "CASH", "entry_time": 33360, "exit_time": 43200, "last_entry_time": 43200, "strike_selection_time": 33360, "multiplier": 1, "is_tick_bt": false, "legs": [{"id": "1", "option_type": "OPTIONTYPE.CALL", "side": "SIDE.SELL", "expiry_type": "EXPIRYTYPE.WEEKLY", "strike_selection": {"type": "BY_ATM_STRIKE", "value": 0.0}, "quantity": 50, "multipl
2025-05-11 20:57:10,662 - __main__ - INFO - Executing 1 portfolios sequentially
2025-05-11 20:57:10,663 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting BTRunPortfolio_GPU.py - Debugging enabled
2025-05-11 20:57:10,663 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Current working directory: /srv/samba/shared
2025-05-11 20:57:10,663 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-11 20:57:10,663 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Importing BTRUN modules...
2025-05-11 20:57:10,663 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Config module imported from: /srv/samba/shared/bt/backtester_stable/BTRUN/config.py
2025-05-11 20:57:10,663 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - HeavyDB settings - Host: **************, Database: heavydb
2025-05-11 20:57:10,663 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - GPU enabled: True
2025-05-11 20:57:10,663 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Successfully imported heavydb_helpers module
2025-05-11 20:57:10,663 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting single backtest for portfolio
2025-05-11 20:57:10,663 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Using HeavyDB for data access
2025-05-11 20:57:10,663 - bt.backtester_stable.BTRUN.runtime - INFO - Fetching trades from local HeavyDB for portfolio ''
2025-05-11 20:57:10,884 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 20:57:17,456 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 6.572s, returned 563 rows
2025-05-11 20:57:17,500 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Cached 563 distinct trade dates
2025-05-11 20:57:17,509 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Union query failed for 2025-04-01 – falling back to per-leg: entry_time
2025-05-11 20:57:17,510 - __main__ - ERROR - GPU worker failed for None: 'EntryWindow' object has no attribute 'time'
