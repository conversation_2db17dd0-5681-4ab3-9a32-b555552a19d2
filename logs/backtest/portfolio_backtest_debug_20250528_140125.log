2025-05-28 14:01:25,044 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-28 14:01:25,045 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-28 14:01:25,045 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/../models', '/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages', '/srv/samba/shared']
2025-05-28 14:01:25,045 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-28 14:01:25,045 - __main__ - DEBUG - GPU enabled: False
2025-05-28 14:01:25,047 - __main__ - INFO - GPU acceleration enabled: False
2025-05-28 14:01:25,047 - __main__ - INFO - HeavyDB integration available (Host: 127.0.0.1)
2025-05-28 14:01:25,267 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-28 14:01:25,267 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-28 14:01:25,267 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-28 14:01:25,267 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-28 14:01:25,267 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-28 14:01:25,267 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-28 14:01:25,272 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-28 14:01:25,273 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-28 14:01:25,273 - backtester_stable.BTRUN.core.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-28 14:01:25,273 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-28 14:01:25,273 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-28 14:01:25,273 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-28 14:01:25,273 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-28 14:01:25,273 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-28 14:01:25,273 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-28 14:01:25,273 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-28 14:01:25,274 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-28 14:01:25,274 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-28 14:01:25,396 - __main__ - INFO - Successfully parsed 1 portfolio(s) using modern parser.
2025-05-28 14:01:25,397 - __main__ - INFO - Queueing portfolio: NIF0DTE (from modern_portfolio_parser)
2025-05-28 14:01:25,397 - __main__ - INFO - Executing 1 portfolios sequentially (multiprocessing temporarily disabled for debugging)
2025-05-28 14:01:25,397 - __main__ - INFO - Processing payload 1/1: NIF0DTE
2025-05-28 14:01:25,397 - backtester_stable.BTRUN.core.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'NIF0DTE'
2025-05-28 14:01:25,618 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-28 14:01:25,648 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.030s, returned 563 rows. Query: SELECT SUBSTRING('SELECT DISTINCT trade_date FROM nifty_option_chain', 1, 200) ...
2025-05-28 14:01:25,690 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - Cached 563 distinct trade dates
2025-05-28 14:01:25,690 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] portfolio_model_dict keys: ['portfolio_name', 'start_date', 'end_date', 'slippage_percent', 'margin_multiplier', 'is_tick_bt', 'strategies', 'extra_params']
2025-05-28 14:01:25,691 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 1 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-28 14:01:25,691 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-01-03' AND oc.expiry_bucket = 'CW' AND 
                oc.strike = (
                    SELECT strike 
                    FROM (
                        SELECT 
                            strike,
                            strike + ce_close - pe_close as synthetic_future,
                            ABS(strike + ce_close - pe_close - oc.spot_price) as diff
                        FROM oc
                        WHERE ce_close IS NOT NULL AND pe_close IS NOT NULL
                        ORDER BY diff
                        LIMIT 1
                    ) atm
                )
             AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-28 14:01:25,691 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-01-03' AND oc.expiry_bucket = 'CW' AND 
                oc.strike = (
                    SELECT strike 
                    FROM (
                        SELECT 
                            strike,
                            strike + ce_close - pe_close as synthetic_future,
                            ABS(strike + ce_close - pe_close - oc.spot_price) as diff
                        FROM oc
                        WHERE ce_close IS NOT NULL AND pe_close IS NOT NULL
                        ORDER BY diff
                        LIMIT 1
                    ) atm
                )
             AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-28 14:01:25,699 - backtester_stable.BTRUN.core.heavydb_connection - ERROR - Query execution failed: SQL Error: From line 5, column 6 to line 5, column 9: Object 'oc' not found
2025-05-28 14:01:25,699 - backtester_stable.BTRUN.core.heavydb_connection - ERROR - Failed Query (first 200 chars): SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-01-03'' AND oc.expiry_bucket = ''CW'' AND 
                oc.strike = (
                    SELECT strike 
                    FR',1, 200) ...
2025-05-28 14:01:25,703 - backtester_stable.BTRUN.core.heavydb_connection - ERROR - Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg="SQL Error: From line 5, column 6 to line 5, column 9: Object 'oc' not found")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/core/heavydb_connection.py", line 129, in execute_query
    result = conn_to_use.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: From line 5, column 6 to line 5, column 9: Object 'oc' not found

2025-05-28 14:01:25,709 - backtester_stable.BTRUN.core.heavydb_connection - ERROR - Query execution failed: SQL Error: From line 5, column 6 to line 5, column 9: Object 'oc' not found
2025-05-28 14:01:25,709 - backtester_stable.BTRUN.core.heavydb_connection - ERROR - Failed Query (first 200 chars): SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-01-03'' AND oc.expiry_bucket = ''CW'' AND 
                oc.strike = (
                    SELECT strike 
                    FR',1, 200) ...
2025-05-28 14:01:25,709 - backtester_stable.BTRUN.core.heavydb_connection - ERROR - Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg="SQL Error: From line 5, column 6 to line 5, column 9: Object 'oc' not found")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/core/heavydb_connection.py", line 129, in execute_query
    result = conn_to_use.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: From line 5, column 6 to line 5, column 9: Object 'oc' not found

2025-05-28 14:01:25,709 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Query results: entry_df size=0, exit_df size=0
2025-05-28 14:01:25,709 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1: No entry or exit data for 2024-01-03
2025-05-28 14:01:25,710 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 skipped - entry_df empty: True, exit_df empty: True
2025-05-28 14:01:25,710 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 2 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-28 14:01:25,710 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-01-03' AND oc.expiry_bucket = 'CW' AND 
                oc.strike = (
                    SELECT strike 
                    FROM (
                        SELECT 
                            strike,
                            strike + ce_close - pe_close as synthetic_future,
                            ABS(strike + ce_close - pe_close - oc.spot_price) as diff
                        FROM oc
                        WHERE ce_close IS NOT NULL AND pe_close IS NOT NULL
                        ORDER BY diff
                        LIMIT 1
                    ) atm
                )
             AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-28 14:01:25,710 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-01-03' AND oc.expiry_bucket = 'CW' AND 
                oc.strike = (
                    SELECT strike 
                    FROM (
                        SELECT 
                            strike,
                            strike + ce_close - pe_close as synthetic_future,
                            ABS(strike + ce_close - pe_close - oc.spot_price) as diff
                        FROM oc
                        WHERE ce_close IS NOT NULL AND pe_close IS NOT NULL
                        ORDER BY diff
                        LIMIT 1
                    ) atm
                )
             AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-28 14:01:25,716 - backtester_stable.BTRUN.core.heavydb_connection - ERROR - Query execution failed: SQL Error: From line 5, column 6 to line 5, column 9: Object 'oc' not found
2025-05-28 14:01:25,716 - backtester_stable.BTRUN.core.heavydb_connection - ERROR - Failed Query (first 200 chars): SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-01-03'' AND oc.expiry_bucket = ''CW'' AND 
                oc.strike = (
                    SELECT strike 
                    FR',1, 200) ...
2025-05-28 14:01:25,716 - backtester_stable.BTRUN.core.heavydb_connection - ERROR - Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg="SQL Error: From line 5, column 6 to line 5, column 9: Object 'oc' not found")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/core/heavydb_connection.py", line 129, in execute_query
    result = conn_to_use.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: From line 5, column 6 to line 5, column 9: Object 'oc' not found

2025-05-28 14:01:25,722 - backtester_stable.BTRUN.core.heavydb_connection - ERROR - Query execution failed: SQL Error: From line 5, column 6 to line 5, column 9: Object 'oc' not found
2025-05-28 14:01:25,722 - backtester_stable.BTRUN.core.heavydb_connection - ERROR - Failed Query (first 200 chars): SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-01-03'' AND oc.expiry_bucket = ''CW'' AND 
                oc.strike = (
                    SELECT strike 
                    FR',1, 200) ...
2025-05-28 14:01:25,723 - backtester_stable.BTRUN.core.heavydb_connection - ERROR - Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg="SQL Error: From line 5, column 6 to line 5, column 9: Object 'oc' not found")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/core/heavydb_connection.py", line 129, in execute_query
    result = conn_to_use.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: From line 5, column 6 to line 5, column 9: Object 'oc' not found

2025-05-28 14:01:25,723 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Query results: entry_df size=0, exit_df size=0
2025-05-28 14:01:25,723 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2: No entry or exit data for 2024-01-03
2025-05-28 14:01:25,723 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 skipped - entry_df empty: True, exit_df empty: True
2025-05-28 14:01:25,723 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 3 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-28 14:01:25,723 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-01-03' AND oc.expiry_bucket = 'CW' AND oc.strike = 
                    (
                        SELECT strike + (2.0 * 50)
                        FROM (
                            SELECT 
                                strike,
                                strike + ce_close - pe_close as synthetic_future,
                                ABS(strike + ce_close - pe_close - oc.spot_price) as diff
                            FROM oc
                            WHERE ce_close IS NOT NULL AND pe_close IS NOT NULL
                            ORDER BY diff
                            LIMIT 1
                        ) atm
                    )
                 AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-28 14:01:25,723 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-01-03' AND oc.expiry_bucket = 'CW' AND oc.strike = 
                    (
                        SELECT strike + (2.0 * 50)
                        FROM (
                            SELECT 
                                strike,
                                strike + ce_close - pe_close as synthetic_future,
                                ABS(strike + ce_close - pe_close - oc.spot_price) as diff
                            FROM oc
                            WHERE ce_close IS NOT NULL AND pe_close IS NOT NULL
                            ORDER BY diff
                            LIMIT 1
                        ) atm
                    )
                 AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-28 14:01:25,730 - backtester_stable.BTRUN.core.heavydb_connection - ERROR - Query execution failed: SQL Error: From line 5, column 6 to line 5, column 9: Object 'oc' not found
2025-05-28 14:01:25,730 - backtester_stable.BTRUN.core.heavydb_connection - ERROR - Failed Query (first 200 chars): SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-01-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = 
                    (
                        SELECT strike + (2.0 * 50)
  ',1, 200) ...
2025-05-28 14:01:25,730 - backtester_stable.BTRUN.core.heavydb_connection - ERROR - Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg="SQL Error: From line 5, column 6 to line 5, column 9: Object 'oc' not found")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/core/heavydb_connection.py", line 129, in execute_query
    result = conn_to_use.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: From line 5, column 6 to line 5, column 9: Object 'oc' not found

2025-05-28 14:01:25,736 - backtester_stable.BTRUN.core.heavydb_connection - ERROR - Query execution failed: SQL Error: From line 5, column 6 to line 5, column 9: Object 'oc' not found
2025-05-28 14:01:25,737 - backtester_stable.BTRUN.core.heavydb_connection - ERROR - Failed Query (first 200 chars): SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-01-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = 
                    (
                        SELECT strike + (2.0 * 50)
  ',1, 200) ...
2025-05-28 14:01:25,737 - backtester_stable.BTRUN.core.heavydb_connection - ERROR - Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg="SQL Error: From line 5, column 6 to line 5, column 9: Object 'oc' not found")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/core/heavydb_connection.py", line 129, in execute_query
    result = conn_to_use.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: From line 5, column 6 to line 5, column 9: Object 'oc' not found

2025-05-28 14:01:25,737 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Query results: entry_df size=0, exit_df size=0
2025-05-28 14:01:25,737 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3: No entry or exit data for 2024-01-03
2025-05-28 14:01:25,737 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 skipped - entry_df empty: True, exit_df empty: True
2025-05-28 14:01:25,737 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 4 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-28 14:01:25,738 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-01-03' AND oc.expiry_bucket = 'CW' AND oc.strike = 
                    (
                        SELECT strike - (2.0 * 50)
                        FROM (
                            SELECT 
                                strike,
                                strike + ce_close - pe_close as synthetic_future,
                                ABS(strike + ce_close - pe_close - oc.spot_price) as diff
                            FROM oc
                            WHERE ce_close IS NOT NULL AND pe_close IS NOT NULL
                            ORDER BY diff
                            LIMIT 1
                        ) atm
                    )
                 AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-28 14:01:25,738 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-01-03' AND oc.expiry_bucket = 'CW' AND oc.strike = 
                    (
                        SELECT strike - (2.0 * 50)
                        FROM (
                            SELECT 
                                strike,
                                strike + ce_close - pe_close as synthetic_future,
                                ABS(strike + ce_close - pe_close - oc.spot_price) as diff
                            FROM oc
                            WHERE ce_close IS NOT NULL AND pe_close IS NOT NULL
                            ORDER BY diff
                            LIMIT 1
                        ) atm
                    )
                 AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-28 14:01:25,744 - backtester_stable.BTRUN.core.heavydb_connection - ERROR - Query execution failed: SQL Error: From line 5, column 6 to line 5, column 9: Object 'oc' not found
2025-05-28 14:01:25,744 - backtester_stable.BTRUN.core.heavydb_connection - ERROR - Failed Query (first 200 chars): SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-01-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = 
                    (
                        SELECT strike - (2.0 * 50)
  ',1, 200) ...
2025-05-28 14:01:25,745 - backtester_stable.BTRUN.core.heavydb_connection - ERROR - Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg="SQL Error: From line 5, column 6 to line 5, column 9: Object 'oc' not found")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/core/heavydb_connection.py", line 129, in execute_query
    result = conn_to_use.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: From line 5, column 6 to line 5, column 9: Object 'oc' not found

2025-05-28 14:01:25,751 - backtester_stable.BTRUN.core.heavydb_connection - ERROR - Query execution failed: SQL Error: From line 5, column 6 to line 5, column 9: Object 'oc' not found
2025-05-28 14:01:25,752 - backtester_stable.BTRUN.core.heavydb_connection - ERROR - Failed Query (first 200 chars): SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-01-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = 
                    (
                        SELECT strike - (2.0 * 50)
  ',1, 200) ...
2025-05-28 14:01:25,752 - backtester_stable.BTRUN.core.heavydb_connection - ERROR - Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg="SQL Error: From line 5, column 6 to line 5, column 9: Object 'oc' not found")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/core/heavydb_connection.py", line 129, in execute_query
    result = conn_to_use.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.ProgrammingError: SQL Error: From line 5, column 6 to line 5, column 9: Object 'oc' not found

2025-05-28 14:01:25,752 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Query results: entry_df size=0, exit_df size=0
2025-05-28 14:01:25,753 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4: No entry or exit data for 2024-01-03
2025-05-28 14:01:25,753 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 skipped - entry_df empty: True, exit_df empty: True
2025-05-28 14:01:25,753 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - ERROR - Error during get_trades_for_portfolio main logic: local variable 'risk_exit_row' referenced before assignment
Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/heavydb_trade_processing.py", line 302, in get_trades_for_portfolio
    if strategy_exit_time_int and hasattr(risk_exit_row, 'get') and risk_exit_row.get('exit_reason') in ['Stop Loss Hit', 'Target Hit', 'Trail SL Hit']:
UnboundLocalError: local variable 'risk_exit_row' referenced before assignment
2025-05-28 14:01:25,753 - debug_detail - ERROR - TRADE_PROC_DETAIL: Exception in get_trades_for_portfolio: local variable 'risk_exit_row' referenced before assignment
Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/heavydb_trade_processing.py", line 302, in get_trades_for_portfolio
    if strategy_exit_time_int and hasattr(risk_exit_row, 'get') and risk_exit_row.get('exit_reason') in ['Stop Loss Hit', 'Target Hit', 'Trail SL Hit']:
UnboundLocalError: local variable 'risk_exit_row' referenced before assignment
2025-05-28 14:01:25,754 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Closed reusable HeavyDB connection from get_trades_for_portfolio
2025-05-28 14:01:25,754 - debug_detail - WARNING - BUILDERS: No 'trades' data in bt_response['data'] or invalid format.
2025-05-28 14:01:25,754 - backtester_stable.BTRUN.core.runtime - WARNING - No trades found in backtest response after parsing in runtime.
2025-05-28 14:01:25,756 - backtester_stable.BTRUN.core.runtime - INFO - Saving Excel output to comparison_results_20250528_140025/gpu_output.xlsx
2025-05-28 14:01:25,756 - backtester_stable.BTRUN.core.runtime - DEBUG - [DEBUG save_backtest_results] save_excel=True, metrics_df is empty: True
2025-05-28 14:01:25,757 - backtester_stable.BTRUN.core.io - INFO - 2025-05-28 14:01:25.756987, Started writing stats to excel file: comparison_results_20250528_140025/gpu_output.xlsx
2025-05-28 14:01:25,757 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Temporarily switching to CPU-only mode.
2025-05-28 14:01:25,772 - backtester_stable.BTRUN.core.io - INFO - Added PortfolioParameter sheet in legacy 2x2 format
2025-05-28 14:01:25,774 - backtester_stable.BTRUN.core.io - INFO - Added GeneralParameter sheet
2025-05-28 14:01:25,774 - backtester_stable.BTRUN.core.io - WARNING - Metrics DataFrame is empty. Skipping Metrics sheet.
2025-05-28 14:01:25,774 - backtester_stable.BTRUN.core.io - WARNING - Daily Max P&L DataFrame is empty. Skipping Max Profit and Loss sheet.
2025-05-28 14:01:25,774 - backtester_stable.BTRUN.core.io - WARNING - Portfolio Transaction DataFrame is empty. Skipping PORTFOLIO Trans sheet.
2025-05-28 14:01:25,783 - backtester_stable.BTRUN.core.io - INFO - 2025-05-28 14:01:25.783197, Excel file prepared successfully, Time taken: 0.03s
2025-05-28 14:01:25,783 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Restored GPU_ENABLED to: False
2025-05-28 14:01:25,783 - backtester_stable.BTRUN.core.runtime - INFO - Saving JSON output to comparison_results_20250528_140025/gpu_output.json
2025-05-28 14:01:25,783 - backtester_stable.BTRUN.core.runtime - WARNING - Empty metrics DataFrame - not saving JSON output
2025-05-28 14:01:25,786 - backtester_stable.BTRUN.core.io - ERROR - Text-to-speech failed: This means you probably do not have eSpeak or eSpeak-ng installed!
