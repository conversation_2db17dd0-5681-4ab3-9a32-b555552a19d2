2025-05-09 20:04:00,758 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-09 20:04:00,758 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-09 20:04:00,758 - __main__ - DEBUG - Python path: ['/srv/samba/shared', '/srv/samba/shared/bt/backtester_stable', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-09 20:04:00,758 - __main__ - DEBUG - HeavyDB host: localhost, DB: heavydb
2025-05-09 20:04:00,758 - __main__ - DEBUG - GPU enabled: True
2025-05-09 20:04:00,763 - BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting BTRunPortfolio_GPU.py - Debugging enabled
2025-05-09 20:04:00,763 - BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Current working directory: /srv/samba/shared
2025-05-09 20:04:00,763 - BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Python path: ['/srv/samba/shared', '/srv/samba/shared/bt/backtester_stable', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-09 20:04:00,763 - BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Importing BTRUN modules...
2025-05-09 20:04:00,763 - BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Config module imported from: /srv/samba/shared/bt/backtester_stable/BTRUN/config.py
2025-05-09 20:04:00,763 - BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - HeavyDB settings - Host: localhost, Database: heavydb
2025-05-09 20:04:00,763 - BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - GPU enabled: True
2025-05-09 20:04:00,763 - BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Successfully imported heavydb_helpers module
2025-05-09 20:04:00,763 - BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Entering main function
2025-05-09 20:04:00,763 - BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Parsing command line arguments
2025-05-09 20:04:00,764 - BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Parsed arguments: Namespace(config=None, legacy_excel=True, strategies=None, start_date=None, end_date=None, output_dir='Trades', portfolio_name=None, slippage=0.1, capital=None, margin_multiplier=1.0, no_json=False, no_excel=False, no_charts=False, cpu_only=True, merge_output=False, debug=True)
2025-05-09 20:04:00,764 - BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Debug logging enabled via command-line argument
2025-05-09 20:04:00,765 - BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Text-to-speech notifications not available
2025-05-09 20:04:00,765 - BTRUN.gpu_helpers - INFO - Switched to CPU-only mode programmatically
2025-05-09 20:04:00,765 - BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Forced CPU-only mode via command-line argument
2025-05-09 20:04:00,765 - BTRUN.Old_BTRunPortfolio_GPU_old - INFO - GPU acceleration enabled: False
2025-05-09 20:04:00,765 - BTRUN.Old_BTRunPortfolio_GPU_old - INFO - HeavyDB integration available (Host: localhost)
2025-05-09 20:04:00,765 - BTRUN.heavydb_helpers - ERROR - pymapd module not found - using mock_heavydb instead
2025-05-09 20:04:00,766 - BTRUN.mock_heavydb - INFO - Initialized mock HeavyDB connection with fixture dir: /srv/samba/shared/input/fixtures
2025-05-09 20:04:00,766 - BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Successfully connected to HeavyDB
2025-05-09 20:04:00,766 - BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Created output directory: Trades
2025-05-09 20:04:00,766 - BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Using legacy Excel input format
2025-05-09 20:04:00,766 - BTRUN.config - INFO - Found portfolio file at /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-09 20:04:00,766 - __main__ - INFO - Reading PortfolioSetting & StrategySetting from /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-09 20:04:00,861 - __main__ - DEBUG - Loaded 1 PortfolioSetting rows, 21 StrategySetting rows
2025-05-09 20:04:00,937 - __main__ - DEBUG - Loaded 1 rows from /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx
2025-05-09 20:04:00,938 - BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Loaded 3 portfolios from legacy Excel
2025-05-09 20:04:00,938 - BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Processing portfolio #PortfolioSetting
2025-05-09 20:04:00,938 - BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Building portfolio request from legacy Excel config for portfolio #PortfolioSetting
2025-05-09 20:04:00,938 - BTRUN.Old_BTRunPortfolio_GPU_old - ERROR - Error building legacy portfolio request: 'PortfolioParameter'
2025-05-09 20:04:00,938 - BTRUN.Old_BTRunPortfolio_GPU_old - ERROR - Unhandled exception in main: name 'traceback' is not defined
2025-05-09 20:04:00,941 - BTRUN.Old_BTRunPortfolio_GPU_old - ERROR - Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/indexes/base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'PortfolioParameter'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/Old_BTRunPortfolio_GPU_old.py", line 225, in build_legacy_portfolio_request
    portfolio_df = portfolio_dfs[portfolio_no]["PortfolioParameter"]
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/frame.py", line 4102, in __getitem__
    indexer = self.columns.get_loc(key)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/indexes/base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'PortfolioParameter'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/Old_BTRunPortfolio_GPU_old.py", line 559, in main
    results = run_portfolio_backtest(args)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/Old_BTRunPortfolio_GPU_old.py", line 304, in run_portfolio_backtest
    request = build_legacy_portfolio_request(portfolio_no, portfolio_dfs)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/Old_BTRunPortfolio_GPU_old.py", line 251, in build_legacy_portfolio_request
    logger.error(f"Stack trace: {traceback.format_exc()}")
NameError: name 'traceback' is not defined

2025-05-09 20:04:00,941 - BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Exiting main function
