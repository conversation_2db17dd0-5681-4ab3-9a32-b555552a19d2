2025-05-10 10:13:12,007 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-10 10:13:12,007 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-10 10:13:12,007 - __main__ - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-10 10:13:12,007 - __main__ - DEBUG - HeavyDB host: **************, DB: heavydb
2025-05-10 10:13:12,007 - __main__ - DEBUG - GPU enabled: True
2025-05-10 10:13:12,008 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting BTRunPortfolio_GPU.py - Debugging enabled
2025-05-10 10:13:12,008 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Current working directory: /srv/samba/shared
2025-05-10 10:13:12,008 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-10 10:13:12,008 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Importing BTRUN modules...
2025-05-10 10:13:12,008 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Config module imported from: /srv/samba/shared/bt/backtester_stable/BTRUN/config.py
2025-05-10 10:13:12,008 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - HeavyDB settings - Host: **************, Database: heavydb
2025-05-10 10:13:12,008 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - GPU enabled: True
2025-05-10 10:13:12,009 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Successfully imported heavydb_helpers module
2025-05-10 10:13:12,009 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Entering main function
2025-05-10 10:13:12,009 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Parsing command line arguments
2025-05-10 10:13:12,010 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Parsed arguments: Namespace(config=None, legacy_excel=True, strategies=None, start_date=None, end_date=None, output_dir='Trades', portfolio_name=None, slippage=0.1, capital=None, margin_multiplier=1.0, no_json=False, no_excel=False, no_charts=False, cpu_only=False, merge_output=False, debug=False)
2025-05-10 10:13:12,010 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Text-to-speech notifications not available
2025-05-10 10:13:12,010 - __main__ - INFO - GPU acceleration enabled: False
2025-05-10 10:13:12,011 - __main__ - INFO - HeavyDB integration available (Host: **************)
2025-05-10 10:13:12,225 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-10 10:13:12,226 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-10 10:13:12,226 - __main__ - INFO - Using new Excel input format (PortfolioSetting & StrategySetting)
2025-05-10 10:13:12,226 - bt.backtester_stable.BTRUN.config - INFO - Found portfolio file at /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-10 10:13:12,226 - __main__ - INFO - Reading PortfolioSetting & StrategySetting from /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-10 10:13:12,341 - __main__ - DEBUG - Loaded 1 PortfolioSetting rows, 21 StrategySetting rows
2025-05-10 10:13:12,424 - __main__ - DEBUG - Loaded 1 rows from /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx
2025-05-10 10:13:12,424 - __main__ - INFO - Processing portfolio: NIF0DTE
2025-05-10 10:13:12,424 - __main__ - DEBUG - Building portfolio request for portfolio: NIF0DTE
2025-05-10 10:13:12,426 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting single backtest for NIF0DTE
2025-05-10 10:13:12,426 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Using HeavyDB for data access
2025-05-10 10:13:12,426 - bt.backtester_stable.BTRUN.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'NIF0DTE'
2025-05-10 10:13:12,641 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-10 10:13:17,856 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query execution failed: Invalid DATE string (01_04_2025)
2025-05-10 10:13:17,856 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Query: SELECT trade_date, trade_time, expiry_date, strike, ce_close, pe_close FROM nifty_option_chain WHERE strike IS NOT NULL  AND trade_date BETWEEN '01_04_2025' AND '26_12_2026' LIMIT 20000
2025-05-10 10:13:17,860 - bt.backtester_stable.BTRUN.heavydb_helpers - ERROR - Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 112, in execute
    result = self.connection._client.sql_execute(
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2322, in sql_execute
    return self.recv_sql_execute()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 2351, in recv_sql_execute
    raise result.e
heavydb.thrift.ttypes.TDBException: TDBException(error_msg='Invalid DATE string (01_04_2025)')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_helpers.py", line 122, in execute_query
    result = conn.execute(optimized_query)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 395, in execute
    return c.execute(operation, parameters=parameters)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/cursor.py", line 121, in execute
    raise _translate_exception(e) from e
heavydb.exceptions.Error: Invalid DATE string (01_04_2025)

2025-05-10 10:13:17,861 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - No option-chain rows found for portfolio 'NIF0DTE' between 01_04_2025 and 26_12_2026
2025-05-10 10:13:17,861 - bt.backtester_stable.BTRUN.builders - INFO - Parsing backtest response...
2025-05-10 10:13:17,861 - bt.backtester_stable.BTRUN.builders - WARNING - No trades data in backtest response or invalid format.
2025-05-10 10:13:17,861 - bt.backtester_stable.BTRUN.runtime - WARNING - No trades found in backtest response
2025-05-10 10:13:17,862 - bt.backtester_stable.BTRUN.runtime - INFO - Saving Excel output to Trades/NIF0DTE_10052025_101312.xlsx
2025-05-10 10:13:17,862 - bt.backtester_stable.BTRUN.runtime - WARNING - Empty metrics DataFrame - not saving Excel output
2025-05-10 10:13:17,862 - bt.backtester_stable.BTRUN.runtime - INFO - Saving JSON output to Trades/NIF0DTE_10052025_101312.json
2025-05-10 10:13:17,862 - bt.backtester_stable.BTRUN.runtime - WARNING - Empty metrics DataFrame - not saving JSON output
2025-05-10 10:13:17,863 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Backtest completed in 5.44 seconds
2025-05-10 10:13:17,863 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Portfolio backtest completed successfully with 0 trades
2025-05-10 10:13:17,863 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Saved json file to Trades/NIF0DTE_10052025_101312.json
2025-05-10 10:13:17,863 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Saved excel file to Trades/NIF0DTE_10052025_101312.xlsx
2025-05-10 10:13:17,863 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Backtest completed with 1 results
2025-05-10 10:13:17,863 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Exiting main function
