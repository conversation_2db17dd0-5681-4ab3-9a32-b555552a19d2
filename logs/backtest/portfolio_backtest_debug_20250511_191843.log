2025-05-11 19:18:43,395 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-11 19:18:43,395 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-11 19:18:43,395 - __main__ - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-11 19:18:43,396 - __main__ - DEBUG - HeavyDB host: **************, DB: heavydb
2025-05-11 19:18:43,396 - __main__ - DEBUG - GPU enabled: True
2025-05-11 19:18:43,397 - __main__ - INFO - GPU acceleration enabled: True
2025-05-11 19:18:43,467 - __main__ - INFO - GPU Memory: 35419MB free / 40384MB total
2025-05-11 19:18:43,467 - __main__ - INFO - HeavyDB integration available (Host: **************)
2025-05-11 19:18:43,685 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 19:18:43,685 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-11 19:18:43,686 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-11 19:18:43,688 - bt.backtester_stable.BTRUN.Util - INFO - Running necessary functions before starting BT...
2025-05-11 19:18:43,688 - bt.backtester_stable.BTRUN.config - DEBUG - Found fixture at /srv/samba/shared/LOTSIZE.csv
2025-05-11 19:18:43,688 - bt.backtester_stable.BTRUN.Util - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-11 19:18:43,691 - bt.backtester_stable.BTRUN.config - WARNING - Fixture margin_symbol_info.json not found in any expected location
2025-05-11 19:18:43,691 - bt.backtester_stable.BTRUN.Util - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/margin_symbol_info.json (Broker: angel)
2025-05-11 19:18:43,691 - bt.backtester_stable.BTRUN.Util - INFO - Pre-BT functions complete.
2025-05-11 19:18:43,691 - __main__ - INFO - Using new Excel input format (PortfolioSetting & StrategySetting)
2025-05-11 19:18:43,691 - __main__ - INFO - Reading PortfolioSetting & StrategySetting from /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-11 19:18:43,792 - __main__ - DEBUG - Loaded 1 PortfolioSetting rows, 21 StrategySetting rows
2025-05-11 19:18:43,792 - __main__ - INFO - Queueing portfolio: NIF0DTE
2025-05-11 19:18:43,792 - __main__ - DEBUG - Building portfolio request for portfolio: NIF0DTE
2025-05-11 19:18:43,801 - __main__ - ERROR - Failed to build PortfolioModel: Portfolio must contain at least one strategy
Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py", line 232, in _row_build_request
    pf_model = build_portfolio(portfolio_row, strategy_models)
  File "/srv/samba/shared/excel_parser/to_models.py", line 131, in build_portfolio
    return PortfolioModel(
  File "<string>", line 9, in __init__
  File "/srv/samba/shared/models/portfolio.py", line 24, in __post_init__
    raise ValueError("Portfolio must contain at least one strategy")
ValueError: Portfolio must contain at least one strategy
2025-05-11 19:18:43,803 - __main__ - DEBUG - Built request for NIF0DTE with 0 strategies
2025-05-11 19:18:43,803 - __main__ - WARNING - No strategies parsed for NIF0DTE; check Excel files and parsing logic
2025-05-11 19:18:43,803 - __main__ - INFO - Executing 1 portfolios sequentially
2025-05-11 19:18:43,804 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting BTRunPortfolio_GPU.py - Debugging enabled
2025-05-11 19:18:43,804 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Current working directory: /srv/samba/shared
2025-05-11 19:18:43,804 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Python path: ['/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/usr/lib/python3/dist-packages']
2025-05-11 19:18:43,804 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Importing BTRUN modules...
2025-05-11 19:18:43,804 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Config module imported from: /srv/samba/shared/bt/backtester_stable/BTRUN/config.py
2025-05-11 19:18:43,804 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - HeavyDB settings - Host: **************, Database: heavydb
2025-05-11 19:18:43,804 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - GPU enabled: True
2025-05-11 19:18:43,804 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Successfully imported heavydb_helpers module
2025-05-11 19:18:43,804 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Starting single backtest for portfolio
2025-05-11 19:18:43,804 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Using HeavyDB for data access
2025-05-11 19:18:43,804 - bt.backtester_stable.BTRUN.runtime - INFO - Fetching trades from local HeavyDB for portfolio ''
2025-05-11 19:18:44,019 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-11 19:18:47,397 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Query executed in 3.379s, returned 563 rows
2025-05-11 19:18:47,439 - bt.backtester_stable.BTRUN.heavydb_helpers - INFO - Cached 563 distinct trade dates
2025-05-11 19:18:47,440 - bt.backtester_stable.BTRUN.heavydb_helpers - WARNING - No strategies found inside bt_params – returning empty trade list
2025-05-11 19:18:47,440 - bt.backtester_stable.BTRUN.builders - INFO - Parsing backtest response...
2025-05-11 19:18:47,440 - bt.backtester_stable.BTRUN.builders - WARNING - No trades data in backtest response or invalid format.
2025-05-11 19:18:47,440 - bt.backtester_stable.BTRUN.runtime - WARNING - No trades found in backtest response
2025-05-11 19:18:47,441 - bt.backtester_stable.BTRUN.runtime - INFO - Saving Excel output to backtest_output/NIF0DTE_11052025_191843.xlsx
2025-05-11 19:18:47,441 - bt.backtester_stable.BTRUN.runtime - WARNING - Empty metrics DataFrame - not saving Excel output
2025-05-11 19:18:47,441 - bt.backtester_stable.BTRUN.runtime - INFO - Saving JSON output to backtest_output/NIF0DTE_11052025_191843.json
2025-05-11 19:18:47,441 - bt.backtester_stable.BTRUN.runtime - WARNING - Empty metrics DataFrame - not saving JSON output
2025-05-11 19:18:47,441 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - DEBUG - Backtest completed in 3.64 seconds
2025-05-11 19:18:47,442 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Portfolio backtest completed successfully with 0 trades
2025-05-11 19:18:47,442 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Saved json file to backtest_output/NIF0DTE_11052025_191843.json
2025-05-11 19:18:47,442 - bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old - INFO - Saved excel file to backtest_output/NIF0DTE_11052025_191843.xlsx
