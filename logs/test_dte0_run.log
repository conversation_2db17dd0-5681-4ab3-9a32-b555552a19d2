CONFIG.PY: Attempting to load config.py...
Partially restored config.py is being imported - Phase 10.
USING PARTIALLY RESTORED DEBUG CONFIG.PY - get_table_name active!
USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
CONFIG.PY: Finished loading config.py.
2025-05-29 21:25:41,383 - root - ERROR - Failed to import BTRUN config module
Warning: Could not import required modules: No module named 'bt.backtester_stable.models'
2025-05-30 02:55:41,389 - __main__ - INFO - Running in sequential (single-worker) mode.
2025-05-30 02:55:41,389 - __main__ - INFO - GPU optimization is not available (missing dependencies or import error). Running sequentially.
2025-05-30 02:55:41,389 - __main__ - INFO - GPU optimization disabled or unavailable. Running sequentially.
2025-05-30 02:55:41,389 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-30 02:55:41,390 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-30 02:55:41,390 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-30 02:55:41,395 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-30 02:55:41,395 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-30 02:55:41,395 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-30 02:55:41,396 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-30 02:55:41,396 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-30 02:55:41,396 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-30 02:55:41,396 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-30 02:55:41,396 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-30 02:55:41,396 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/test_dte0_portfolio.xlsx
2025-05-30 02:55:41,396 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared
2025-05-30 02:55:41,396 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/test_dte0_portfolio.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared
2025-05-30 02:55:41,496 - __main__ - ERROR - Error in main (sequential path): 'Enabled'
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/indexes/base.py", line 3802, in get_loc
    return self._engine.get_loc(casted_key)
  File "pandas/_libs/index.pyx", line 138, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/index.pyx", line 165, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 5745, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 5753, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'Enabled'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py", line 757, in main
    parsed_portfolio_models = modern_portfolio_parser.parse_portfolio_excel(main_portfolio_excel_path)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/excel_parser/portfolio_parser.py", line 52, in parse_portfolio_excel
    portfolio_df = portfolio_df[portfolio_df["Enabled"].astype(str).str.upper() == "YES"].copy()
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/frame.py", line 3807, in __getitem__
    indexer = self.columns.get_loc(key)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/indexes/base.py", line 3804, in get_loc
    raise KeyError(key) from err
KeyError: 'Enabled'
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/indexes/base.py", line 3802, in get_loc
    return self._engine.get_loc(casted_key)
  File "pandas/_libs/index.pyx", line 138, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/index.pyx", line 165, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 5745, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 5753, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'Enabled'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py", line 792, in <module>
    main()
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py", line 757, in main
    parsed_portfolio_models = modern_portfolio_parser.parse_portfolio_excel(main_portfolio_excel_path)
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/excel_parser/portfolio_parser.py", line 52, in parse_portfolio_excel
    portfolio_df = portfolio_df[portfolio_df["Enabled"].astype(str).str.upper() == "YES"].copy()
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/frame.py", line 3807, in __getitem__
    indexer = self.columns.get_loc(key)
  File "/home/<USER>/.local/lib/python3.10/site-packages/pandas/core/indexes/base.py", line 3804, in get_loc
    raise KeyError(key) from err
KeyError: 'Enabled'
