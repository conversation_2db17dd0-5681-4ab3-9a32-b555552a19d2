TV Backtester Comparison Test
Started at: 2025-05-30 21:28:36.416060

================================================================================
Running CPU-based TV Backtester (BT_TV_GPU_aggregated_v4.py)
================================================================================
STDOUT: 
STDERR: 2025-05-30 21:28:36,847 - __main__ - ERROR - Import error: No module named 'backtester_stable'

Return code: 1
❌ CPU version failed

================================================================================
Running GPU-enhanced TV Backtester (BT_TV_GPU_enhanced.py)
================================================================================
STDOUT: CONFIG.PY: Attempting to load config.py...
CONFIG.PY: Finished loading config.py.

STDERR: 2025-05-30 21:28:37,309 - bt.backtester_stable.BTRUN.core.config - WARNING - Partially restored config.py is being imported - Phase 10.
2025-05-30 21:28:37,309 - bt.backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - get_table_name active!
2025-05-30 21:28:37,309 - bt.backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
2025-05-30 21:28:37,309 - bt.backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
2025-05-30 21:28:38,701 - numba.cuda.cudadrv.driver - INFO - init
2025-05-30 21:28:39,002 - bt.backtester_stable.BTRUN.core.gpu_helpers - INFO - cuDF is available - GPU acceleration enabled
2025-05-30 21:28:39,255 - bt.backtester_stable.BTRUN.strategies.tv_processor - INFO - Using DirectDAL for database access
2025-05-30 21:28:39,276 - root - ERROR - Failed to import BTRUN config module
2025-05-30 21:28:39,278 - __main__ - ERROR - Import error: No module named 'backtester_stable'
Traceback (most recent call last):
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/BT_TV_GPU_enhanced.py", line 40, in <module>
    from bt.backtester_stable.BTRUN.excel_parser.tv_parser_enhanced import EnhancedTvParser
  File "/srv/samba/shared/bt/backtester_stable/BTRUN/excel_parser/tv_parser_enhanced.py", line 14, in <module>
    from backtester_stable.models.tv_models import TvSettingModel, RawTvSignalModel, ProcessedTvSignalModel
ModuleNotFoundError: No module named 'backtester_stable'

Return code: 1
❌ GPU version failed

================================================================================
Comparing Outputs
================================================================================
❌ Cannot compare - one or both files missing

Completed at: 2025-05-30 21:28:39.607964
