2025-05-21 18:56:59,376 - column_mapping_test - INFO - Starting column mapping test...
2025-05-21 18:56:59,376 - column_mapping_test - INFO - Running backtester with portfolio file: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_fixed.xlsx
2025-05-21 18:56:59,376 - column_mapping_test - INFO - Running command: python3 /srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py --portfolio-excel /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_fixed.xlsx --output-path /srv/samba/shared/Trades/column_test_20250521_185659.xlsx
2025-05-21 18:57:06,530 - column_mapping_test - INFO - Backtester completed successfully: /srv/samba/shared/Trades/column_test_20250521_185659.xlsx
2025-05-21 18:57:06,531 - column_mapping_test - INFO - Analyzing output file: /srv/samba/shared/Trades/column_test_20250521_185659.xlsx
2025-05-21 18:57:06,651 - column_mapping_test - ERROR - Missing sheets in output file: ['PORTFOLIO Results', 'STRATEGY Trans', 'STRATEGY Results']
2025-05-21 18:57:06,651 - column_mapping_test - ERROR - Output file validation failed, aborting test
