============================= test session starts ==============================
platform linux -- Python 3.10.12, pytest-8.3.5, pluggy-1.5.0
rootdir: /srv/samba/shared/bt
configfile: pytest.ini
plugins: env-1.1.5, mock-3.14.0, anyio-4.9.0
collected 8 items

bt/tests/test_portfolio_mapping.py FFFFFFFF                              [100%]

=================================== FAILURES ===================================
_______________ test_portfolio_setting_field_mapped[start_date] ________________

field = 'start_date'

    @pytest.mark.parametrize('field', PORTFOLIO_SETTING_FIELDS)
    def test_portfolio_setting_field_mapped(field):
>       portfolios = portfolio_parser.parse_portfolio_excel(EXCEL_PATH)

bt/tests/test_portfolio_mapping.py:16: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
bt/backtester_stable/BTRUN/excel_parser/portfolio_parser.py:122: in parse_portfolio_excel
    strats = parse_strategy_excel(st_actual, multiplier)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

path = PosixPath('/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx')
multiplier = 1.0

    def parse_strategy_excel(path: str | os.PathLike, multiplier: float = 1.0) -> List[StrategyModel]:
        """Parse GP/LP sheets of one strategy Excel, return StrategyModel list (1)."""
        path = Path(path)
        if not path.exists():
            raise FileNotFoundError(path)
    
        gp_df = pd.read_excel(path, sheet_name="GeneralParameter")
        lp_df = pd.read_excel(path, sheet_name="LegParameter")
    
        # --- Mapping enforcement: ensure all columns are mapped or explicitly ignored ---
        # Reference: docs/column_mapping.md
        allowed_gp_cols = {
            # All mapped or explicitly ignored columns from docs/column_mapping.md (GeneralParameter)
            "StrategyName", "MoveSlToCost", "Underlying", "Index", "Weekdays", "DTE", "StrikeSelectionTime", "StartTime", "LastEntryTime", "EndTime",
            "StrategyProfit", "StrategyLoss", "StrategyProfitReExecuteNo", "StrategyLossReExecuteNo", "StrategyTrailingType", "PnLCalTime", "LockPercent",
            "TrailPercent", "SqOff1Time", "SqOff1Percent", "SqOff2Time", "SqOff2Percent", "ProfitReaches", "LockMinProfitAt", "IncreaseInProfit",
            "TrailMinProfitBy", "TgtTrackingFrom", "TgtRegisterPriceFrom", "SlTrackingFrom", "SlRegisterPriceFrom", "PnLCalculationFrom",
            "ConsiderHedgePnLForStgyPnL", "StoplossCheckingInterval", "TargetCheckingInterval", "ReEntryCheckingInterval", "OnExpiryDayTradeNextExpiry",
            "Enabled", "PortfolioName", "StrategyType", "StrategyExcelFilePath", "isTickBt"
        }
        allowed_lp_cols = {
            # All mapped or explicitly ignored columns from docs/column_mapping.md (LegParameter)
            "StrategyName", "IsIdle", "LegID", "Instrument", "Transaction", "Expiry", "W&Type", "W&TValue", "TrailW&T", "StrikeMethod", "MatchPremium",
            "StrikeValue", "StrikePremiumCondition", "SLType", "SLValue", "TGTType", "TGTValue", "TrailSLType", "SL_TrailAt", "SL_TrailBy", "Lots",
            "ReEntryType", "ReEnteriesCount", "OnEntry_OpenTradeOn", "OnEntry_SqOffTradeOff", "OnEntry_SqOffAllLegs", "OnEntry_OpenTradeDelay",
            "OnEntry_SqOffDelay", "OnExit_OpenTradeOn", "OnExit_SqOffTradeOff", "OnExit_SqOffAllLegs", "OnExit_OpenAllLegs", "OnExit_OpenTradeDelay",
            "OnExit_SqOffDelay", "OpenHedge", "HedgeStrikeMethod", "HedgeStrikeValue", "HedgeStrikePremiumCondition", "ConsiderEMAForEntry",
            "EmaEntryCondition", "ConsiderVwapForEntry", "VwapEntryCondition", "RsiEntryCondition", "EMAPeriod", "STPeriod", "StartTime", "EndTime",
            # Some columns may appear in legacy or variant sheets
            "Index", "StrikeDistance"
        }
        # Check GeneralParameter columns
        for col in gp_df.columns:
            if col not in allowed_gp_cols:
                raise ValueError(f"Unmapped column in GeneralParameter: {col}. Please update docs/column_mapping.md and parser.")
        # Check LegParameter columns
        for col in lp_df.columns:
            if col not in allowed_lp_cols:
                raise ValueError(f"Unmapped column in LegParameter: {col}. Please update docs/column_mapping.md and parser.")
    
        # Legacy compatibility: if the GeneralParameter sheet lacks an "Enabled" column, assume all rows are enabled
        if "Enabled" not in gp_df.columns:
            gp_df["Enabled"] = "YES"
        gp_df = gp_df[gp_df["Enabled"].astype(str).str.upper() == "YES"].copy()
        if gp_df.empty:
            return []
    
        strategies: list[StrategyModel] = []
        for _, grow in gp_df.iterrows():
            sname = str(grow["StrategyName"])
            start_time = hhmmss_to_time(grow.get("StartTime", 0))
            end_time = hhmmss_to_time(grow.get("EndTime", 0))
    
            legs: list[LegModel] = []
            legs_df = lp_df[lp_df["StrategyName"] == sname]
            for _, lrow in legs_df.iterrows():
                leg = _parse_leg_row(lrow, multiplier)
                if leg:
                    legs.append(leg)
    
            # capture extra columns not explicitly mapped
            extra_params = {col: grow[col] for col in grow.index if col not in (
                "StrategyName","StrategyType","Enabled","StartTime","EndTime","isTickBt"
            )}
    
            # Convert time objects to strings
            start_time_str = start_time.strftime("%H:%M:%S") if isinstance(start_time, datetime.time) else start_time
            end_time_str = end_time.strftime("%H:%M:%S") if isinstance(end_time, datetime.time) else end_time
    
>           strategy = StrategyModel(
                strategy_name=sname,
                evaluator=str(grow.get("StrategyType", "Tbs")),
                is_tick_bt=str(grow.get("isTickBt", "NO")).upper() == "YES",
                entry_start=start_time_str,
                entry_end=end_time_str,
                legs=legs,
                extra_params=extra_params,
            )
E           pydantic_core._pydantic_core.ValidationError: 22 validation errors for StrategyModel
E           strategy_excel_path
E             Field required [type=missing, input_value={'strategy_name': 'RS,916...TradeNextExpiry': 'no'}}, input_type=dict]
E               For further information visit https://errors.pydantic.dev/2.11/v/missing
E           extra_params.DTE
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrikeSelectionTime
E             Input should be a valid string [type=string_type, input_value=91600, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.LastEntryTime
E             Input should be a valid string [type=string_type, input_value=120000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyProfit
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyLoss
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyProfitReExecuteNo
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyLossReExecuteNo
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.PnLCalTime
E             Input should be a valid string [type=string_type, input_value=230000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.LockPercent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.TrailPercent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff1Time
E             Input should be a valid string [type=string_type, input_value=230000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff1Percent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff2Time
E             Input should be a valid string [type=string_type, input_value=230000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff2Percent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.ProfitReaches
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.LockMinProfitAt
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.IncreaseInProfit
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.TrailMinProfitBy
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StoplossCheckingInterval
E             Input should be a valid string [type=string_type, input_value=1, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.TargetCheckingInterval
E             Input should be a valid string [type=string_type, input_value=1, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.ReEntryCheckingInterval
E             Input should be a valid string [type=string_type, input_value=1, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type

bt/backtester_stable/BTRUN/excel_parser/strategy_parser.py:138: ValidationError
----------------------------- Captured stdout call -----------------------------
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=100, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  1
Instrument                                                          call
Transaction                                                         sell
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            0
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                              100
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 0, dtype: object
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=100, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  2
Instrument                                                           put
Transaction                                                         sell
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            0
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                              100
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 1, dtype: object
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  3
Instrument                                                          call
Transaction                                                          buy
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            2
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                                0
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 2, dtype: object
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  4
Instrument                                                           put
Transaction                                                          buy
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            2
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                                0
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 3, dtype: object
________________ test_portfolio_setting_field_mapped[end_date] _________________

field = 'end_date'

    @pytest.mark.parametrize('field', PORTFOLIO_SETTING_FIELDS)
    def test_portfolio_setting_field_mapped(field):
>       portfolios = portfolio_parser.parse_portfolio_excel(EXCEL_PATH)

bt/tests/test_portfolio_mapping.py:16: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
bt/backtester_stable/BTRUN/excel_parser/portfolio_parser.py:122: in parse_portfolio_excel
    strats = parse_strategy_excel(st_actual, multiplier)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

path = PosixPath('/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx')
multiplier = 1.0

    def parse_strategy_excel(path: str | os.PathLike, multiplier: float = 1.0) -> List[StrategyModel]:
        """Parse GP/LP sheets of one strategy Excel, return StrategyModel list (1)."""
        path = Path(path)
        if not path.exists():
            raise FileNotFoundError(path)
    
        gp_df = pd.read_excel(path, sheet_name="GeneralParameter")
        lp_df = pd.read_excel(path, sheet_name="LegParameter")
    
        # --- Mapping enforcement: ensure all columns are mapped or explicitly ignored ---
        # Reference: docs/column_mapping.md
        allowed_gp_cols = {
            # All mapped or explicitly ignored columns from docs/column_mapping.md (GeneralParameter)
            "StrategyName", "MoveSlToCost", "Underlying", "Index", "Weekdays", "DTE", "StrikeSelectionTime", "StartTime", "LastEntryTime", "EndTime",
            "StrategyProfit", "StrategyLoss", "StrategyProfitReExecuteNo", "StrategyLossReExecuteNo", "StrategyTrailingType", "PnLCalTime", "LockPercent",
            "TrailPercent", "SqOff1Time", "SqOff1Percent", "SqOff2Time", "SqOff2Percent", "ProfitReaches", "LockMinProfitAt", "IncreaseInProfit",
            "TrailMinProfitBy", "TgtTrackingFrom", "TgtRegisterPriceFrom", "SlTrackingFrom", "SlRegisterPriceFrom", "PnLCalculationFrom",
            "ConsiderHedgePnLForStgyPnL", "StoplossCheckingInterval", "TargetCheckingInterval", "ReEntryCheckingInterval", "OnExpiryDayTradeNextExpiry",
            "Enabled", "PortfolioName", "StrategyType", "StrategyExcelFilePath", "isTickBt"
        }
        allowed_lp_cols = {
            # All mapped or explicitly ignored columns from docs/column_mapping.md (LegParameter)
            "StrategyName", "IsIdle", "LegID", "Instrument", "Transaction", "Expiry", "W&Type", "W&TValue", "TrailW&T", "StrikeMethod", "MatchPremium",
            "StrikeValue", "StrikePremiumCondition", "SLType", "SLValue", "TGTType", "TGTValue", "TrailSLType", "SL_TrailAt", "SL_TrailBy", "Lots",
            "ReEntryType", "ReEnteriesCount", "OnEntry_OpenTradeOn", "OnEntry_SqOffTradeOff", "OnEntry_SqOffAllLegs", "OnEntry_OpenTradeDelay",
            "OnEntry_SqOffDelay", "OnExit_OpenTradeOn", "OnExit_SqOffTradeOff", "OnExit_SqOffAllLegs", "OnExit_OpenAllLegs", "OnExit_OpenTradeDelay",
            "OnExit_SqOffDelay", "OpenHedge", "HedgeStrikeMethod", "HedgeStrikeValue", "HedgeStrikePremiumCondition", "ConsiderEMAForEntry",
            "EmaEntryCondition", "ConsiderVwapForEntry", "VwapEntryCondition", "RsiEntryCondition", "EMAPeriod", "STPeriod", "StartTime", "EndTime",
            # Some columns may appear in legacy or variant sheets
            "Index", "StrikeDistance"
        }
        # Check GeneralParameter columns
        for col in gp_df.columns:
            if col not in allowed_gp_cols:
                raise ValueError(f"Unmapped column in GeneralParameter: {col}. Please update docs/column_mapping.md and parser.")
        # Check LegParameter columns
        for col in lp_df.columns:
            if col not in allowed_lp_cols:
                raise ValueError(f"Unmapped column in LegParameter: {col}. Please update docs/column_mapping.md and parser.")
    
        # Legacy compatibility: if the GeneralParameter sheet lacks an "Enabled" column, assume all rows are enabled
        if "Enabled" not in gp_df.columns:
            gp_df["Enabled"] = "YES"
        gp_df = gp_df[gp_df["Enabled"].astype(str).str.upper() == "YES"].copy()
        if gp_df.empty:
            return []
    
        strategies: list[StrategyModel] = []
        for _, grow in gp_df.iterrows():
            sname = str(grow["StrategyName"])
            start_time = hhmmss_to_time(grow.get("StartTime", 0))
            end_time = hhmmss_to_time(grow.get("EndTime", 0))
    
            legs: list[LegModel] = []
            legs_df = lp_df[lp_df["StrategyName"] == sname]
            for _, lrow in legs_df.iterrows():
                leg = _parse_leg_row(lrow, multiplier)
                if leg:
                    legs.append(leg)
    
            # capture extra columns not explicitly mapped
            extra_params = {col: grow[col] for col in grow.index if col not in (
                "StrategyName","StrategyType","Enabled","StartTime","EndTime","isTickBt"
            )}
    
            # Convert time objects to strings
            start_time_str = start_time.strftime("%H:%M:%S") if isinstance(start_time, datetime.time) else start_time
            end_time_str = end_time.strftime("%H:%M:%S") if isinstance(end_time, datetime.time) else end_time
    
>           strategy = StrategyModel(
                strategy_name=sname,
                evaluator=str(grow.get("StrategyType", "Tbs")),
                is_tick_bt=str(grow.get("isTickBt", "NO")).upper() == "YES",
                entry_start=start_time_str,
                entry_end=end_time_str,
                legs=legs,
                extra_params=extra_params,
            )
E           pydantic_core._pydantic_core.ValidationError: 22 validation errors for StrategyModel
E           strategy_excel_path
E             Field required [type=missing, input_value={'strategy_name': 'RS,916...TradeNextExpiry': 'no'}}, input_type=dict]
E               For further information visit https://errors.pydantic.dev/2.11/v/missing
E           extra_params.DTE
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrikeSelectionTime
E             Input should be a valid string [type=string_type, input_value=91600, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.LastEntryTime
E             Input should be a valid string [type=string_type, input_value=120000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyProfit
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyLoss
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyProfitReExecuteNo
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyLossReExecuteNo
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.PnLCalTime
E             Input should be a valid string [type=string_type, input_value=230000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.LockPercent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.TrailPercent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff1Time
E             Input should be a valid string [type=string_type, input_value=230000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff1Percent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff2Time
E             Input should be a valid string [type=string_type, input_value=230000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff2Percent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.ProfitReaches
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.LockMinProfitAt
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.IncreaseInProfit
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.TrailMinProfitBy
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StoplossCheckingInterval
E             Input should be a valid string [type=string_type, input_value=1, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.TargetCheckingInterval
E             Input should be a valid string [type=string_type, input_value=1, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.ReEntryCheckingInterval
E             Input should be a valid string [type=string_type, input_value=1, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type

bt/backtester_stable/BTRUN/excel_parser/strategy_parser.py:138: ValidationError
----------------------------- Captured stdout call -----------------------------
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=100, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  1
Instrument                                                          call
Transaction                                                         sell
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            0
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                              100
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 0, dtype: object
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=100, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  2
Instrument                                                           put
Transaction                                                         sell
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            0
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                              100
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 1, dtype: object
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  3
Instrument                                                          call
Transaction                                                          buy
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            2
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                                0
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 2, dtype: object
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  4
Instrument                                                           put
Transaction                                                          buy
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            2
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                                0
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 3, dtype: object
_______________ test_portfolio_setting_field_mapped[is_tick_bt] ________________

field = 'is_tick_bt'

    @pytest.mark.parametrize('field', PORTFOLIO_SETTING_FIELDS)
    def test_portfolio_setting_field_mapped(field):
>       portfolios = portfolio_parser.parse_portfolio_excel(EXCEL_PATH)

bt/tests/test_portfolio_mapping.py:16: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
bt/backtester_stable/BTRUN/excel_parser/portfolio_parser.py:122: in parse_portfolio_excel
    strats = parse_strategy_excel(st_actual, multiplier)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

path = PosixPath('/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx')
multiplier = 1.0

    def parse_strategy_excel(path: str | os.PathLike, multiplier: float = 1.0) -> List[StrategyModel]:
        """Parse GP/LP sheets of one strategy Excel, return StrategyModel list (1)."""
        path = Path(path)
        if not path.exists():
            raise FileNotFoundError(path)
    
        gp_df = pd.read_excel(path, sheet_name="GeneralParameter")
        lp_df = pd.read_excel(path, sheet_name="LegParameter")
    
        # --- Mapping enforcement: ensure all columns are mapped or explicitly ignored ---
        # Reference: docs/column_mapping.md
        allowed_gp_cols = {
            # All mapped or explicitly ignored columns from docs/column_mapping.md (GeneralParameter)
            "StrategyName", "MoveSlToCost", "Underlying", "Index", "Weekdays", "DTE", "StrikeSelectionTime", "StartTime", "LastEntryTime", "EndTime",
            "StrategyProfit", "StrategyLoss", "StrategyProfitReExecuteNo", "StrategyLossReExecuteNo", "StrategyTrailingType", "PnLCalTime", "LockPercent",
            "TrailPercent", "SqOff1Time", "SqOff1Percent", "SqOff2Time", "SqOff2Percent", "ProfitReaches", "LockMinProfitAt", "IncreaseInProfit",
            "TrailMinProfitBy", "TgtTrackingFrom", "TgtRegisterPriceFrom", "SlTrackingFrom", "SlRegisterPriceFrom", "PnLCalculationFrom",
            "ConsiderHedgePnLForStgyPnL", "StoplossCheckingInterval", "TargetCheckingInterval", "ReEntryCheckingInterval", "OnExpiryDayTradeNextExpiry",
            "Enabled", "PortfolioName", "StrategyType", "StrategyExcelFilePath", "isTickBt"
        }
        allowed_lp_cols = {
            # All mapped or explicitly ignored columns from docs/column_mapping.md (LegParameter)
            "StrategyName", "IsIdle", "LegID", "Instrument", "Transaction", "Expiry", "W&Type", "W&TValue", "TrailW&T", "StrikeMethod", "MatchPremium",
            "StrikeValue", "StrikePremiumCondition", "SLType", "SLValue", "TGTType", "TGTValue", "TrailSLType", "SL_TrailAt", "SL_TrailBy", "Lots",
            "ReEntryType", "ReEnteriesCount", "OnEntry_OpenTradeOn", "OnEntry_SqOffTradeOff", "OnEntry_SqOffAllLegs", "OnEntry_OpenTradeDelay",
            "OnEntry_SqOffDelay", "OnExit_OpenTradeOn", "OnExit_SqOffTradeOff", "OnExit_SqOffAllLegs", "OnExit_OpenAllLegs", "OnExit_OpenTradeDelay",
            "OnExit_SqOffDelay", "OpenHedge", "HedgeStrikeMethod", "HedgeStrikeValue", "HedgeStrikePremiumCondition", "ConsiderEMAForEntry",
            "EmaEntryCondition", "ConsiderVwapForEntry", "VwapEntryCondition", "RsiEntryCondition", "EMAPeriod", "STPeriod", "StartTime", "EndTime",
            # Some columns may appear in legacy or variant sheets
            "Index", "StrikeDistance"
        }
        # Check GeneralParameter columns
        for col in gp_df.columns:
            if col not in allowed_gp_cols:
                raise ValueError(f"Unmapped column in GeneralParameter: {col}. Please update docs/column_mapping.md and parser.")
        # Check LegParameter columns
        for col in lp_df.columns:
            if col not in allowed_lp_cols:
                raise ValueError(f"Unmapped column in LegParameter: {col}. Please update docs/column_mapping.md and parser.")
    
        # Legacy compatibility: if the GeneralParameter sheet lacks an "Enabled" column, assume all rows are enabled
        if "Enabled" not in gp_df.columns:
            gp_df["Enabled"] = "YES"
        gp_df = gp_df[gp_df["Enabled"].astype(str).str.upper() == "YES"].copy()
        if gp_df.empty:
            return []
    
        strategies: list[StrategyModel] = []
        for _, grow in gp_df.iterrows():
            sname = str(grow["StrategyName"])
            start_time = hhmmss_to_time(grow.get("StartTime", 0))
            end_time = hhmmss_to_time(grow.get("EndTime", 0))
    
            legs: list[LegModel] = []
            legs_df = lp_df[lp_df["StrategyName"] == sname]
            for _, lrow in legs_df.iterrows():
                leg = _parse_leg_row(lrow, multiplier)
                if leg:
                    legs.append(leg)
    
            # capture extra columns not explicitly mapped
            extra_params = {col: grow[col] for col in grow.index if col not in (
                "StrategyName","StrategyType","Enabled","StartTime","EndTime","isTickBt"
            )}
    
            # Convert time objects to strings
            start_time_str = start_time.strftime("%H:%M:%S") if isinstance(start_time, datetime.time) else start_time
            end_time_str = end_time.strftime("%H:%M:%S") if isinstance(end_time, datetime.time) else end_time
    
>           strategy = StrategyModel(
                strategy_name=sname,
                evaluator=str(grow.get("StrategyType", "Tbs")),
                is_tick_bt=str(grow.get("isTickBt", "NO")).upper() == "YES",
                entry_start=start_time_str,
                entry_end=end_time_str,
                legs=legs,
                extra_params=extra_params,
            )
E           pydantic_core._pydantic_core.ValidationError: 22 validation errors for StrategyModel
E           strategy_excel_path
E             Field required [type=missing, input_value={'strategy_name': 'RS,916...TradeNextExpiry': 'no'}}, input_type=dict]
E               For further information visit https://errors.pydantic.dev/2.11/v/missing
E           extra_params.DTE
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrikeSelectionTime
E             Input should be a valid string [type=string_type, input_value=91600, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.LastEntryTime
E             Input should be a valid string [type=string_type, input_value=120000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyProfit
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyLoss
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyProfitReExecuteNo
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyLossReExecuteNo
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.PnLCalTime
E             Input should be a valid string [type=string_type, input_value=230000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.LockPercent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.TrailPercent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff1Time
E             Input should be a valid string [type=string_type, input_value=230000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff1Percent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff2Time
E             Input should be a valid string [type=string_type, input_value=230000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff2Percent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.ProfitReaches
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.LockMinProfitAt
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.IncreaseInProfit
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.TrailMinProfitBy
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StoplossCheckingInterval
E             Input should be a valid string [type=string_type, input_value=1, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.TargetCheckingInterval
E             Input should be a valid string [type=string_type, input_value=1, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.ReEntryCheckingInterval
E             Input should be a valid string [type=string_type, input_value=1, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type

bt/backtester_stable/BTRUN/excel_parser/strategy_parser.py:138: ValidationError
----------------------------- Captured stdout call -----------------------------
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=100, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  1
Instrument                                                          call
Transaction                                                         sell
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            0
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                              100
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 0, dtype: object
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=100, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  2
Instrument                                                           put
Transaction                                                         sell
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            0
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                              100
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 1, dtype: object
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  3
Instrument                                                          call
Transaction                                                          buy
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            2
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                                0
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 2, dtype: object
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  4
Instrument                                                           put
Transaction                                                          buy
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            2
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                                0
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 3, dtype: object
_____________ test_portfolio_setting_field_mapped[portfolio_name] ______________

field = 'portfolio_name'

    @pytest.mark.parametrize('field', PORTFOLIO_SETTING_FIELDS)
    def test_portfolio_setting_field_mapped(field):
>       portfolios = portfolio_parser.parse_portfolio_excel(EXCEL_PATH)

bt/tests/test_portfolio_mapping.py:16: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
bt/backtester_stable/BTRUN/excel_parser/portfolio_parser.py:122: in parse_portfolio_excel
    strats = parse_strategy_excel(st_actual, multiplier)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

path = PosixPath('/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx')
multiplier = 1.0

    def parse_strategy_excel(path: str | os.PathLike, multiplier: float = 1.0) -> List[StrategyModel]:
        """Parse GP/LP sheets of one strategy Excel, return StrategyModel list (1)."""
        path = Path(path)
        if not path.exists():
            raise FileNotFoundError(path)
    
        gp_df = pd.read_excel(path, sheet_name="GeneralParameter")
        lp_df = pd.read_excel(path, sheet_name="LegParameter")
    
        # --- Mapping enforcement: ensure all columns are mapped or explicitly ignored ---
        # Reference: docs/column_mapping.md
        allowed_gp_cols = {
            # All mapped or explicitly ignored columns from docs/column_mapping.md (GeneralParameter)
            "StrategyName", "MoveSlToCost", "Underlying", "Index", "Weekdays", "DTE", "StrikeSelectionTime", "StartTime", "LastEntryTime", "EndTime",
            "StrategyProfit", "StrategyLoss", "StrategyProfitReExecuteNo", "StrategyLossReExecuteNo", "StrategyTrailingType", "PnLCalTime", "LockPercent",
            "TrailPercent", "SqOff1Time", "SqOff1Percent", "SqOff2Time", "SqOff2Percent", "ProfitReaches", "LockMinProfitAt", "IncreaseInProfit",
            "TrailMinProfitBy", "TgtTrackingFrom", "TgtRegisterPriceFrom", "SlTrackingFrom", "SlRegisterPriceFrom", "PnLCalculationFrom",
            "ConsiderHedgePnLForStgyPnL", "StoplossCheckingInterval", "TargetCheckingInterval", "ReEntryCheckingInterval", "OnExpiryDayTradeNextExpiry",
            "Enabled", "PortfolioName", "StrategyType", "StrategyExcelFilePath", "isTickBt"
        }
        allowed_lp_cols = {
            # All mapped or explicitly ignored columns from docs/column_mapping.md (LegParameter)
            "StrategyName", "IsIdle", "LegID", "Instrument", "Transaction", "Expiry", "W&Type", "W&TValue", "TrailW&T", "StrikeMethod", "MatchPremium",
            "StrikeValue", "StrikePremiumCondition", "SLType", "SLValue", "TGTType", "TGTValue", "TrailSLType", "SL_TrailAt", "SL_TrailBy", "Lots",
            "ReEntryType", "ReEnteriesCount", "OnEntry_OpenTradeOn", "OnEntry_SqOffTradeOff", "OnEntry_SqOffAllLegs", "OnEntry_OpenTradeDelay",
            "OnEntry_SqOffDelay", "OnExit_OpenTradeOn", "OnExit_SqOffTradeOff", "OnExit_SqOffAllLegs", "OnExit_OpenAllLegs", "OnExit_OpenTradeDelay",
            "OnExit_SqOffDelay", "OpenHedge", "HedgeStrikeMethod", "HedgeStrikeValue", "HedgeStrikePremiumCondition", "ConsiderEMAForEntry",
            "EmaEntryCondition", "ConsiderVwapForEntry", "VwapEntryCondition", "RsiEntryCondition", "EMAPeriod", "STPeriod", "StartTime", "EndTime",
            # Some columns may appear in legacy or variant sheets
            "Index", "StrikeDistance"
        }
        # Check GeneralParameter columns
        for col in gp_df.columns:
            if col not in allowed_gp_cols:
                raise ValueError(f"Unmapped column in GeneralParameter: {col}. Please update docs/column_mapping.md and parser.")
        # Check LegParameter columns
        for col in lp_df.columns:
            if col not in allowed_lp_cols:
                raise ValueError(f"Unmapped column in LegParameter: {col}. Please update docs/column_mapping.md and parser.")
    
        # Legacy compatibility: if the GeneralParameter sheet lacks an "Enabled" column, assume all rows are enabled
        if "Enabled" not in gp_df.columns:
            gp_df["Enabled"] = "YES"
        gp_df = gp_df[gp_df["Enabled"].astype(str).str.upper() == "YES"].copy()
        if gp_df.empty:
            return []
    
        strategies: list[StrategyModel] = []
        for _, grow in gp_df.iterrows():
            sname = str(grow["StrategyName"])
            start_time = hhmmss_to_time(grow.get("StartTime", 0))
            end_time = hhmmss_to_time(grow.get("EndTime", 0))
    
            legs: list[LegModel] = []
            legs_df = lp_df[lp_df["StrategyName"] == sname]
            for _, lrow in legs_df.iterrows():
                leg = _parse_leg_row(lrow, multiplier)
                if leg:
                    legs.append(leg)
    
            # capture extra columns not explicitly mapped
            extra_params = {col: grow[col] for col in grow.index if col not in (
                "StrategyName","StrategyType","Enabled","StartTime","EndTime","isTickBt"
            )}
    
            # Convert time objects to strings
            start_time_str = start_time.strftime("%H:%M:%S") if isinstance(start_time, datetime.time) else start_time
            end_time_str = end_time.strftime("%H:%M:%S") if isinstance(end_time, datetime.time) else end_time
    
>           strategy = StrategyModel(
                strategy_name=sname,
                evaluator=str(grow.get("StrategyType", "Tbs")),
                is_tick_bt=str(grow.get("isTickBt", "NO")).upper() == "YES",
                entry_start=start_time_str,
                entry_end=end_time_str,
                legs=legs,
                extra_params=extra_params,
            )
E           pydantic_core._pydantic_core.ValidationError: 22 validation errors for StrategyModel
E           strategy_excel_path
E             Field required [type=missing, input_value={'strategy_name': 'RS,916...TradeNextExpiry': 'no'}}, input_type=dict]
E               For further information visit https://errors.pydantic.dev/2.11/v/missing
E           extra_params.DTE
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrikeSelectionTime
E             Input should be a valid string [type=string_type, input_value=91600, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.LastEntryTime
E             Input should be a valid string [type=string_type, input_value=120000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyProfit
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyLoss
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyProfitReExecuteNo
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyLossReExecuteNo
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.PnLCalTime
E             Input should be a valid string [type=string_type, input_value=230000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.LockPercent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.TrailPercent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff1Time
E             Input should be a valid string [type=string_type, input_value=230000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff1Percent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff2Time
E             Input should be a valid string [type=string_type, input_value=230000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff2Percent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.ProfitReaches
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.LockMinProfitAt
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.IncreaseInProfit
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.TrailMinProfitBy
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StoplossCheckingInterval
E             Input should be a valid string [type=string_type, input_value=1, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.TargetCheckingInterval
E             Input should be a valid string [type=string_type, input_value=1, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.ReEntryCheckingInterval
E             Input should be a valid string [type=string_type, input_value=1, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type

bt/backtester_stable/BTRUN/excel_parser/strategy_parser.py:138: ValidationError
----------------------------- Captured stdout call -----------------------------
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=100, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  1
Instrument                                                          call
Transaction                                                         sell
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            0
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                              100
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 0, dtype: object
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=100, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  2
Instrument                                                           put
Transaction                                                         sell
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            0
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                              100
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 1, dtype: object
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  3
Instrument                                                          call
Transaction                                                          buy
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            2
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                                0
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 2, dtype: object
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  4
Instrument                                                           put
Transaction                                                          buy
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            2
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                                0
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 3, dtype: object
____________ test_portfolio_setting_field_mapped[margin_multiplier] ____________

field = 'margin_multiplier'

    @pytest.mark.parametrize('field', PORTFOLIO_SETTING_FIELDS)
    def test_portfolio_setting_field_mapped(field):
>       portfolios = portfolio_parser.parse_portfolio_excel(EXCEL_PATH)

bt/tests/test_portfolio_mapping.py:16: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
bt/backtester_stable/BTRUN/excel_parser/portfolio_parser.py:122: in parse_portfolio_excel
    strats = parse_strategy_excel(st_actual, multiplier)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

path = PosixPath('/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx')
multiplier = 1.0

    def parse_strategy_excel(path: str | os.PathLike, multiplier: float = 1.0) -> List[StrategyModel]:
        """Parse GP/LP sheets of one strategy Excel, return StrategyModel list (1)."""
        path = Path(path)
        if not path.exists():
            raise FileNotFoundError(path)
    
        gp_df = pd.read_excel(path, sheet_name="GeneralParameter")
        lp_df = pd.read_excel(path, sheet_name="LegParameter")
    
        # --- Mapping enforcement: ensure all columns are mapped or explicitly ignored ---
        # Reference: docs/column_mapping.md
        allowed_gp_cols = {
            # All mapped or explicitly ignored columns from docs/column_mapping.md (GeneralParameter)
            "StrategyName", "MoveSlToCost", "Underlying", "Index", "Weekdays", "DTE", "StrikeSelectionTime", "StartTime", "LastEntryTime", "EndTime",
            "StrategyProfit", "StrategyLoss", "StrategyProfitReExecuteNo", "StrategyLossReExecuteNo", "StrategyTrailingType", "PnLCalTime", "LockPercent",
            "TrailPercent", "SqOff1Time", "SqOff1Percent", "SqOff2Time", "SqOff2Percent", "ProfitReaches", "LockMinProfitAt", "IncreaseInProfit",
            "TrailMinProfitBy", "TgtTrackingFrom", "TgtRegisterPriceFrom", "SlTrackingFrom", "SlRegisterPriceFrom", "PnLCalculationFrom",
            "ConsiderHedgePnLForStgyPnL", "StoplossCheckingInterval", "TargetCheckingInterval", "ReEntryCheckingInterval", "OnExpiryDayTradeNextExpiry",
            "Enabled", "PortfolioName", "StrategyType", "StrategyExcelFilePath", "isTickBt"
        }
        allowed_lp_cols = {
            # All mapped or explicitly ignored columns from docs/column_mapping.md (LegParameter)
            "StrategyName", "IsIdle", "LegID", "Instrument", "Transaction", "Expiry", "W&Type", "W&TValue", "TrailW&T", "StrikeMethod", "MatchPremium",
            "StrikeValue", "StrikePremiumCondition", "SLType", "SLValue", "TGTType", "TGTValue", "TrailSLType", "SL_TrailAt", "SL_TrailBy", "Lots",
            "ReEntryType", "ReEnteriesCount", "OnEntry_OpenTradeOn", "OnEntry_SqOffTradeOff", "OnEntry_SqOffAllLegs", "OnEntry_OpenTradeDelay",
            "OnEntry_SqOffDelay", "OnExit_OpenTradeOn", "OnExit_SqOffTradeOff", "OnExit_SqOffAllLegs", "OnExit_OpenAllLegs", "OnExit_OpenTradeDelay",
            "OnExit_SqOffDelay", "OpenHedge", "HedgeStrikeMethod", "HedgeStrikeValue", "HedgeStrikePremiumCondition", "ConsiderEMAForEntry",
            "EmaEntryCondition", "ConsiderVwapForEntry", "VwapEntryCondition", "RsiEntryCondition", "EMAPeriod", "STPeriod", "StartTime", "EndTime",
            # Some columns may appear in legacy or variant sheets
            "Index", "StrikeDistance"
        }
        # Check GeneralParameter columns
        for col in gp_df.columns:
            if col not in allowed_gp_cols:
                raise ValueError(f"Unmapped column in GeneralParameter: {col}. Please update docs/column_mapping.md and parser.")
        # Check LegParameter columns
        for col in lp_df.columns:
            if col not in allowed_lp_cols:
                raise ValueError(f"Unmapped column in LegParameter: {col}. Please update docs/column_mapping.md and parser.")
    
        # Legacy compatibility: if the GeneralParameter sheet lacks an "Enabled" column, assume all rows are enabled
        if "Enabled" not in gp_df.columns:
            gp_df["Enabled"] = "YES"
        gp_df = gp_df[gp_df["Enabled"].astype(str).str.upper() == "YES"].copy()
        if gp_df.empty:
            return []
    
        strategies: list[StrategyModel] = []
        for _, grow in gp_df.iterrows():
            sname = str(grow["StrategyName"])
            start_time = hhmmss_to_time(grow.get("StartTime", 0))
            end_time = hhmmss_to_time(grow.get("EndTime", 0))
    
            legs: list[LegModel] = []
            legs_df = lp_df[lp_df["StrategyName"] == sname]
            for _, lrow in legs_df.iterrows():
                leg = _parse_leg_row(lrow, multiplier)
                if leg:
                    legs.append(leg)
    
            # capture extra columns not explicitly mapped
            extra_params = {col: grow[col] for col in grow.index if col not in (
                "StrategyName","StrategyType","Enabled","StartTime","EndTime","isTickBt"
            )}
    
            # Convert time objects to strings
            start_time_str = start_time.strftime("%H:%M:%S") if isinstance(start_time, datetime.time) else start_time
            end_time_str = end_time.strftime("%H:%M:%S") if isinstance(end_time, datetime.time) else end_time
    
>           strategy = StrategyModel(
                strategy_name=sname,
                evaluator=str(grow.get("StrategyType", "Tbs")),
                is_tick_bt=str(grow.get("isTickBt", "NO")).upper() == "YES",
                entry_start=start_time_str,
                entry_end=end_time_str,
                legs=legs,
                extra_params=extra_params,
            )
E           pydantic_core._pydantic_core.ValidationError: 22 validation errors for StrategyModel
E           strategy_excel_path
E             Field required [type=missing, input_value={'strategy_name': 'RS,916...TradeNextExpiry': 'no'}}, input_type=dict]
E               For further information visit https://errors.pydantic.dev/2.11/v/missing
E           extra_params.DTE
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrikeSelectionTime
E             Input should be a valid string [type=string_type, input_value=91600, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.LastEntryTime
E             Input should be a valid string [type=string_type, input_value=120000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyProfit
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyLoss
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyProfitReExecuteNo
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyLossReExecuteNo
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.PnLCalTime
E             Input should be a valid string [type=string_type, input_value=230000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.LockPercent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.TrailPercent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff1Time
E             Input should be a valid string [type=string_type, input_value=230000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff1Percent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff2Time
E             Input should be a valid string [type=string_type, input_value=230000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff2Percent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.ProfitReaches
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.LockMinProfitAt
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.IncreaseInProfit
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.TrailMinProfitBy
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StoplossCheckingInterval
E             Input should be a valid string [type=string_type, input_value=1, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.TargetCheckingInterval
E             Input should be a valid string [type=string_type, input_value=1, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.ReEntryCheckingInterval
E             Input should be a valid string [type=string_type, input_value=1, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type

bt/backtester_stable/BTRUN/excel_parser/strategy_parser.py:138: ValidationError
----------------------------- Captured stdout call -----------------------------
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=100, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  1
Instrument                                                          call
Transaction                                                         sell
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            0
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                              100
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 0, dtype: object
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=100, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  2
Instrument                                                           put
Transaction                                                         sell
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            0
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                              100
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 1, dtype: object
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  3
Instrument                                                          call
Transaction                                                          buy
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            2
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                                0
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 2, dtype: object
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  4
Instrument                                                           put
Transaction                                                          buy
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            2
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                                0
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 3, dtype: object
____________ test_portfolio_setting_field_mapped[slippage_percent] _____________

field = 'slippage_percent'

    @pytest.mark.parametrize('field', PORTFOLIO_SETTING_FIELDS)
    def test_portfolio_setting_field_mapped(field):
>       portfolios = portfolio_parser.parse_portfolio_excel(EXCEL_PATH)

bt/tests/test_portfolio_mapping.py:16: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
bt/backtester_stable/BTRUN/excel_parser/portfolio_parser.py:122: in parse_portfolio_excel
    strats = parse_strategy_excel(st_actual, multiplier)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

path = PosixPath('/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx')
multiplier = 1.0

    def parse_strategy_excel(path: str | os.PathLike, multiplier: float = 1.0) -> List[StrategyModel]:
        """Parse GP/LP sheets of one strategy Excel, return StrategyModel list (1)."""
        path = Path(path)
        if not path.exists():
            raise FileNotFoundError(path)
    
        gp_df = pd.read_excel(path, sheet_name="GeneralParameter")
        lp_df = pd.read_excel(path, sheet_name="LegParameter")
    
        # --- Mapping enforcement: ensure all columns are mapped or explicitly ignored ---
        # Reference: docs/column_mapping.md
        allowed_gp_cols = {
            # All mapped or explicitly ignored columns from docs/column_mapping.md (GeneralParameter)
            "StrategyName", "MoveSlToCost", "Underlying", "Index", "Weekdays", "DTE", "StrikeSelectionTime", "StartTime", "LastEntryTime", "EndTime",
            "StrategyProfit", "StrategyLoss", "StrategyProfitReExecuteNo", "StrategyLossReExecuteNo", "StrategyTrailingType", "PnLCalTime", "LockPercent",
            "TrailPercent", "SqOff1Time", "SqOff1Percent", "SqOff2Time", "SqOff2Percent", "ProfitReaches", "LockMinProfitAt", "IncreaseInProfit",
            "TrailMinProfitBy", "TgtTrackingFrom", "TgtRegisterPriceFrom", "SlTrackingFrom", "SlRegisterPriceFrom", "PnLCalculationFrom",
            "ConsiderHedgePnLForStgyPnL", "StoplossCheckingInterval", "TargetCheckingInterval", "ReEntryCheckingInterval", "OnExpiryDayTradeNextExpiry",
            "Enabled", "PortfolioName", "StrategyType", "StrategyExcelFilePath", "isTickBt"
        }
        allowed_lp_cols = {
            # All mapped or explicitly ignored columns from docs/column_mapping.md (LegParameter)
            "StrategyName", "IsIdle", "LegID", "Instrument", "Transaction", "Expiry", "W&Type", "W&TValue", "TrailW&T", "StrikeMethod", "MatchPremium",
            "StrikeValue", "StrikePremiumCondition", "SLType", "SLValue", "TGTType", "TGTValue", "TrailSLType", "SL_TrailAt", "SL_TrailBy", "Lots",
            "ReEntryType", "ReEnteriesCount", "OnEntry_OpenTradeOn", "OnEntry_SqOffTradeOff", "OnEntry_SqOffAllLegs", "OnEntry_OpenTradeDelay",
            "OnEntry_SqOffDelay", "OnExit_OpenTradeOn", "OnExit_SqOffTradeOff", "OnExit_SqOffAllLegs", "OnExit_OpenAllLegs", "OnExit_OpenTradeDelay",
            "OnExit_SqOffDelay", "OpenHedge", "HedgeStrikeMethod", "HedgeStrikeValue", "HedgeStrikePremiumCondition", "ConsiderEMAForEntry",
            "EmaEntryCondition", "ConsiderVwapForEntry", "VwapEntryCondition", "RsiEntryCondition", "EMAPeriod", "STPeriod", "StartTime", "EndTime",
            # Some columns may appear in legacy or variant sheets
            "Index", "StrikeDistance"
        }
        # Check GeneralParameter columns
        for col in gp_df.columns:
            if col not in allowed_gp_cols:
                raise ValueError(f"Unmapped column in GeneralParameter: {col}. Please update docs/column_mapping.md and parser.")
        # Check LegParameter columns
        for col in lp_df.columns:
            if col not in allowed_lp_cols:
                raise ValueError(f"Unmapped column in LegParameter: {col}. Please update docs/column_mapping.md and parser.")
    
        # Legacy compatibility: if the GeneralParameter sheet lacks an "Enabled" column, assume all rows are enabled
        if "Enabled" not in gp_df.columns:
            gp_df["Enabled"] = "YES"
        gp_df = gp_df[gp_df["Enabled"].astype(str).str.upper() == "YES"].copy()
        if gp_df.empty:
            return []
    
        strategies: list[StrategyModel] = []
        for _, grow in gp_df.iterrows():
            sname = str(grow["StrategyName"])
            start_time = hhmmss_to_time(grow.get("StartTime", 0))
            end_time = hhmmss_to_time(grow.get("EndTime", 0))
    
            legs: list[LegModel] = []
            legs_df = lp_df[lp_df["StrategyName"] == sname]
            for _, lrow in legs_df.iterrows():
                leg = _parse_leg_row(lrow, multiplier)
                if leg:
                    legs.append(leg)
    
            # capture extra columns not explicitly mapped
            extra_params = {col: grow[col] for col in grow.index if col not in (
                "StrategyName","StrategyType","Enabled","StartTime","EndTime","isTickBt"
            )}
    
            # Convert time objects to strings
            start_time_str = start_time.strftime("%H:%M:%S") if isinstance(start_time, datetime.time) else start_time
            end_time_str = end_time.strftime("%H:%M:%S") if isinstance(end_time, datetime.time) else end_time
    
>           strategy = StrategyModel(
                strategy_name=sname,
                evaluator=str(grow.get("StrategyType", "Tbs")),
                is_tick_bt=str(grow.get("isTickBt", "NO")).upper() == "YES",
                entry_start=start_time_str,
                entry_end=end_time_str,
                legs=legs,
                extra_params=extra_params,
            )
E           pydantic_core._pydantic_core.ValidationError: 22 validation errors for StrategyModel
E           strategy_excel_path
E             Field required [type=missing, input_value={'strategy_name': 'RS,916...TradeNextExpiry': 'no'}}, input_type=dict]
E               For further information visit https://errors.pydantic.dev/2.11/v/missing
E           extra_params.DTE
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrikeSelectionTime
E             Input should be a valid string [type=string_type, input_value=91600, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.LastEntryTime
E             Input should be a valid string [type=string_type, input_value=120000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyProfit
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyLoss
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyProfitReExecuteNo
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyLossReExecuteNo
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.PnLCalTime
E             Input should be a valid string [type=string_type, input_value=230000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.LockPercent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.TrailPercent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff1Time
E             Input should be a valid string [type=string_type, input_value=230000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff1Percent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff2Time
E             Input should be a valid string [type=string_type, input_value=230000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff2Percent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.ProfitReaches
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.LockMinProfitAt
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.IncreaseInProfit
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.TrailMinProfitBy
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StoplossCheckingInterval
E             Input should be a valid string [type=string_type, input_value=1, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.TargetCheckingInterval
E             Input should be a valid string [type=string_type, input_value=1, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.ReEntryCheckingInterval
E             Input should be a valid string [type=string_type, input_value=1, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type

bt/backtester_stable/BTRUN/excel_parser/strategy_parser.py:138: ValidationError
----------------------------- Captured stdout call -----------------------------
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=100, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  1
Instrument                                                          call
Transaction                                                         sell
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            0
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                              100
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 0, dtype: object
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=100, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  2
Instrument                                                           put
Transaction                                                         sell
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            0
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                              100
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 1, dtype: object
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  3
Instrument                                                          call
Transaction                                                          buy
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            2
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                                0
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 2, dtype: object
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  4
Instrument                                                           put
Transaction                                                          buy
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            2
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                                0
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 3, dtype: object
________________ test_strategy_setting_field_mapped[evaluator] _________________

field = 'evaluator'

    @pytest.mark.parametrize('field', STRATEGY_SETTING_FIELDS)
    def test_strategy_setting_field_mapped(field):
>       portfolios = portfolio_parser.parse_portfolio_excel(EXCEL_PATH)

bt/tests/test_portfolio_mapping.py:29: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
bt/backtester_stable/BTRUN/excel_parser/portfolio_parser.py:122: in parse_portfolio_excel
    strats = parse_strategy_excel(st_actual, multiplier)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

path = PosixPath('/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx')
multiplier = 1.0

    def parse_strategy_excel(path: str | os.PathLike, multiplier: float = 1.0) -> List[StrategyModel]:
        """Parse GP/LP sheets of one strategy Excel, return StrategyModel list (1)."""
        path = Path(path)
        if not path.exists():
            raise FileNotFoundError(path)
    
        gp_df = pd.read_excel(path, sheet_name="GeneralParameter")
        lp_df = pd.read_excel(path, sheet_name="LegParameter")
    
        # --- Mapping enforcement: ensure all columns are mapped or explicitly ignored ---
        # Reference: docs/column_mapping.md
        allowed_gp_cols = {
            # All mapped or explicitly ignored columns from docs/column_mapping.md (GeneralParameter)
            "StrategyName", "MoveSlToCost", "Underlying", "Index", "Weekdays", "DTE", "StrikeSelectionTime", "StartTime", "LastEntryTime", "EndTime",
            "StrategyProfit", "StrategyLoss", "StrategyProfitReExecuteNo", "StrategyLossReExecuteNo", "StrategyTrailingType", "PnLCalTime", "LockPercent",
            "TrailPercent", "SqOff1Time", "SqOff1Percent", "SqOff2Time", "SqOff2Percent", "ProfitReaches", "LockMinProfitAt", "IncreaseInProfit",
            "TrailMinProfitBy", "TgtTrackingFrom", "TgtRegisterPriceFrom", "SlTrackingFrom", "SlRegisterPriceFrom", "PnLCalculationFrom",
            "ConsiderHedgePnLForStgyPnL", "StoplossCheckingInterval", "TargetCheckingInterval", "ReEntryCheckingInterval", "OnExpiryDayTradeNextExpiry",
            "Enabled", "PortfolioName", "StrategyType", "StrategyExcelFilePath", "isTickBt"
        }
        allowed_lp_cols = {
            # All mapped or explicitly ignored columns from docs/column_mapping.md (LegParameter)
            "StrategyName", "IsIdle", "LegID", "Instrument", "Transaction", "Expiry", "W&Type", "W&TValue", "TrailW&T", "StrikeMethod", "MatchPremium",
            "StrikeValue", "StrikePremiumCondition", "SLType", "SLValue", "TGTType", "TGTValue", "TrailSLType", "SL_TrailAt", "SL_TrailBy", "Lots",
            "ReEntryType", "ReEnteriesCount", "OnEntry_OpenTradeOn", "OnEntry_SqOffTradeOff", "OnEntry_SqOffAllLegs", "OnEntry_OpenTradeDelay",
            "OnEntry_SqOffDelay", "OnExit_OpenTradeOn", "OnExit_SqOffTradeOff", "OnExit_SqOffAllLegs", "OnExit_OpenAllLegs", "OnExit_OpenTradeDelay",
            "OnExit_SqOffDelay", "OpenHedge", "HedgeStrikeMethod", "HedgeStrikeValue", "HedgeStrikePremiumCondition", "ConsiderEMAForEntry",
            "EmaEntryCondition", "ConsiderVwapForEntry", "VwapEntryCondition", "RsiEntryCondition", "EMAPeriod", "STPeriod", "StartTime", "EndTime",
            # Some columns may appear in legacy or variant sheets
            "Index", "StrikeDistance"
        }
        # Check GeneralParameter columns
        for col in gp_df.columns:
            if col not in allowed_gp_cols:
                raise ValueError(f"Unmapped column in GeneralParameter: {col}. Please update docs/column_mapping.md and parser.")
        # Check LegParameter columns
        for col in lp_df.columns:
            if col not in allowed_lp_cols:
                raise ValueError(f"Unmapped column in LegParameter: {col}. Please update docs/column_mapping.md and parser.")
    
        # Legacy compatibility: if the GeneralParameter sheet lacks an "Enabled" column, assume all rows are enabled
        if "Enabled" not in gp_df.columns:
            gp_df["Enabled"] = "YES"
        gp_df = gp_df[gp_df["Enabled"].astype(str).str.upper() == "YES"].copy()
        if gp_df.empty:
            return []
    
        strategies: list[StrategyModel] = []
        for _, grow in gp_df.iterrows():
            sname = str(grow["StrategyName"])
            start_time = hhmmss_to_time(grow.get("StartTime", 0))
            end_time = hhmmss_to_time(grow.get("EndTime", 0))
    
            legs: list[LegModel] = []
            legs_df = lp_df[lp_df["StrategyName"] == sname]
            for _, lrow in legs_df.iterrows():
                leg = _parse_leg_row(lrow, multiplier)
                if leg:
                    legs.append(leg)
    
            # capture extra columns not explicitly mapped
            extra_params = {col: grow[col] for col in grow.index if col not in (
                "StrategyName","StrategyType","Enabled","StartTime","EndTime","isTickBt"
            )}
    
            # Convert time objects to strings
            start_time_str = start_time.strftime("%H:%M:%S") if isinstance(start_time, datetime.time) else start_time
            end_time_str = end_time.strftime("%H:%M:%S") if isinstance(end_time, datetime.time) else end_time
    
>           strategy = StrategyModel(
                strategy_name=sname,
                evaluator=str(grow.get("StrategyType", "Tbs")),
                is_tick_bt=str(grow.get("isTickBt", "NO")).upper() == "YES",
                entry_start=start_time_str,
                entry_end=end_time_str,
                legs=legs,
                extra_params=extra_params,
            )
E           pydantic_core._pydantic_core.ValidationError: 22 validation errors for StrategyModel
E           strategy_excel_path
E             Field required [type=missing, input_value={'strategy_name': 'RS,916...TradeNextExpiry': 'no'}}, input_type=dict]
E               For further information visit https://errors.pydantic.dev/2.11/v/missing
E           extra_params.DTE
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrikeSelectionTime
E             Input should be a valid string [type=string_type, input_value=91600, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.LastEntryTime
E             Input should be a valid string [type=string_type, input_value=120000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyProfit
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyLoss
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyProfitReExecuteNo
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyLossReExecuteNo
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.PnLCalTime
E             Input should be a valid string [type=string_type, input_value=230000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.LockPercent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.TrailPercent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff1Time
E             Input should be a valid string [type=string_type, input_value=230000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff1Percent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff2Time
E             Input should be a valid string [type=string_type, input_value=230000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff2Percent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.ProfitReaches
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.LockMinProfitAt
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.IncreaseInProfit
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.TrailMinProfitBy
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StoplossCheckingInterval
E             Input should be a valid string [type=string_type, input_value=1, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.TargetCheckingInterval
E             Input should be a valid string [type=string_type, input_value=1, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.ReEntryCheckingInterval
E             Input should be a valid string [type=string_type, input_value=1, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type

bt/backtester_stable/BTRUN/excel_parser/strategy_parser.py:138: ValidationError
----------------------------- Captured stdout call -----------------------------
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=100, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  1
Instrument                                                          call
Transaction                                                         sell
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            0
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                              100
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 0, dtype: object
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=100, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  2
Instrument                                                           put
Transaction                                                         sell
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            0
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                              100
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 1, dtype: object
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  3
Instrument                                                          call
Transaction                                                          buy
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            2
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                                0
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 2, dtype: object
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  4
Instrument                                                           put
Transaction                                                          buy
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            2
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                                0
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 3, dtype: object
___________ test_strategy_setting_field_mapped[strategy_excel_path] ____________

field = 'strategy_excel_path'

    @pytest.mark.parametrize('field', STRATEGY_SETTING_FIELDS)
    def test_strategy_setting_field_mapped(field):
>       portfolios = portfolio_parser.parse_portfolio_excel(EXCEL_PATH)

bt/tests/test_portfolio_mapping.py:29: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
bt/backtester_stable/BTRUN/excel_parser/portfolio_parser.py:122: in parse_portfolio_excel
    strats = parse_strategy_excel(st_actual, multiplier)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

path = PosixPath('/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx')
multiplier = 1.0

    def parse_strategy_excel(path: str | os.PathLike, multiplier: float = 1.0) -> List[StrategyModel]:
        """Parse GP/LP sheets of one strategy Excel, return StrategyModel list (1)."""
        path = Path(path)
        if not path.exists():
            raise FileNotFoundError(path)
    
        gp_df = pd.read_excel(path, sheet_name="GeneralParameter")
        lp_df = pd.read_excel(path, sheet_name="LegParameter")
    
        # --- Mapping enforcement: ensure all columns are mapped or explicitly ignored ---
        # Reference: docs/column_mapping.md
        allowed_gp_cols = {
            # All mapped or explicitly ignored columns from docs/column_mapping.md (GeneralParameter)
            "StrategyName", "MoveSlToCost", "Underlying", "Index", "Weekdays", "DTE", "StrikeSelectionTime", "StartTime", "LastEntryTime", "EndTime",
            "StrategyProfit", "StrategyLoss", "StrategyProfitReExecuteNo", "StrategyLossReExecuteNo", "StrategyTrailingType", "PnLCalTime", "LockPercent",
            "TrailPercent", "SqOff1Time", "SqOff1Percent", "SqOff2Time", "SqOff2Percent", "ProfitReaches", "LockMinProfitAt", "IncreaseInProfit",
            "TrailMinProfitBy", "TgtTrackingFrom", "TgtRegisterPriceFrom", "SlTrackingFrom", "SlRegisterPriceFrom", "PnLCalculationFrom",
            "ConsiderHedgePnLForStgyPnL", "StoplossCheckingInterval", "TargetCheckingInterval", "ReEntryCheckingInterval", "OnExpiryDayTradeNextExpiry",
            "Enabled", "PortfolioName", "StrategyType", "StrategyExcelFilePath", "isTickBt"
        }
        allowed_lp_cols = {
            # All mapped or explicitly ignored columns from docs/column_mapping.md (LegParameter)
            "StrategyName", "IsIdle", "LegID", "Instrument", "Transaction", "Expiry", "W&Type", "W&TValue", "TrailW&T", "StrikeMethod", "MatchPremium",
            "StrikeValue", "StrikePremiumCondition", "SLType", "SLValue", "TGTType", "TGTValue", "TrailSLType", "SL_TrailAt", "SL_TrailBy", "Lots",
            "ReEntryType", "ReEnteriesCount", "OnEntry_OpenTradeOn", "OnEntry_SqOffTradeOff", "OnEntry_SqOffAllLegs", "OnEntry_OpenTradeDelay",
            "OnEntry_SqOffDelay", "OnExit_OpenTradeOn", "OnExit_SqOffTradeOff", "OnExit_SqOffAllLegs", "OnExit_OpenAllLegs", "OnExit_OpenTradeDelay",
            "OnExit_SqOffDelay", "OpenHedge", "HedgeStrikeMethod", "HedgeStrikeValue", "HedgeStrikePremiumCondition", "ConsiderEMAForEntry",
            "EmaEntryCondition", "ConsiderVwapForEntry", "VwapEntryCondition", "RsiEntryCondition", "EMAPeriod", "STPeriod", "StartTime", "EndTime",
            # Some columns may appear in legacy or variant sheets
            "Index", "StrikeDistance"
        }
        # Check GeneralParameter columns
        for col in gp_df.columns:
            if col not in allowed_gp_cols:
                raise ValueError(f"Unmapped column in GeneralParameter: {col}. Please update docs/column_mapping.md and parser.")
        # Check LegParameter columns
        for col in lp_df.columns:
            if col not in allowed_lp_cols:
                raise ValueError(f"Unmapped column in LegParameter: {col}. Please update docs/column_mapping.md and parser.")
    
        # Legacy compatibility: if the GeneralParameter sheet lacks an "Enabled" column, assume all rows are enabled
        if "Enabled" not in gp_df.columns:
            gp_df["Enabled"] = "YES"
        gp_df = gp_df[gp_df["Enabled"].astype(str).str.upper() == "YES"].copy()
        if gp_df.empty:
            return []
    
        strategies: list[StrategyModel] = []
        for _, grow in gp_df.iterrows():
            sname = str(grow["StrategyName"])
            start_time = hhmmss_to_time(grow.get("StartTime", 0))
            end_time = hhmmss_to_time(grow.get("EndTime", 0))
    
            legs: list[LegModel] = []
            legs_df = lp_df[lp_df["StrategyName"] == sname]
            for _, lrow in legs_df.iterrows():
                leg = _parse_leg_row(lrow, multiplier)
                if leg:
                    legs.append(leg)
    
            # capture extra columns not explicitly mapped
            extra_params = {col: grow[col] for col in grow.index if col not in (
                "StrategyName","StrategyType","Enabled","StartTime","EndTime","isTickBt"
            )}
    
            # Convert time objects to strings
            start_time_str = start_time.strftime("%H:%M:%S") if isinstance(start_time, datetime.time) else start_time
            end_time_str = end_time.strftime("%H:%M:%S") if isinstance(end_time, datetime.time) else end_time
    
>           strategy = StrategyModel(
                strategy_name=sname,
                evaluator=str(grow.get("StrategyType", "Tbs")),
                is_tick_bt=str(grow.get("isTickBt", "NO")).upper() == "YES",
                entry_start=start_time_str,
                entry_end=end_time_str,
                legs=legs,
                extra_params=extra_params,
            )
E           pydantic_core._pydantic_core.ValidationError: 22 validation errors for StrategyModel
E           strategy_excel_path
E             Field required [type=missing, input_value={'strategy_name': 'RS,916...TradeNextExpiry': 'no'}}, input_type=dict]
E               For further information visit https://errors.pydantic.dev/2.11/v/missing
E           extra_params.DTE
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrikeSelectionTime
E             Input should be a valid string [type=string_type, input_value=91600, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.LastEntryTime
E             Input should be a valid string [type=string_type, input_value=120000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyProfit
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyLoss
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyProfitReExecuteNo
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StrategyLossReExecuteNo
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.PnLCalTime
E             Input should be a valid string [type=string_type, input_value=230000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.LockPercent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.TrailPercent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff1Time
E             Input should be a valid string [type=string_type, input_value=230000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff1Percent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff2Time
E             Input should be a valid string [type=string_type, input_value=230000, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.SqOff2Percent
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.ProfitReaches
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.LockMinProfitAt
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.IncreaseInProfit
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.TrailMinProfitBy
E             Input should be a valid string [type=string_type, input_value=0, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.StoplossCheckingInterval
E             Input should be a valid string [type=string_type, input_value=1, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.TargetCheckingInterval
E             Input should be a valid string [type=string_type, input_value=1, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type
E           extra_params.ReEntryCheckingInterval
E             Input should be a valid string [type=string_type, input_value=1, input_type=int]
E               For further information visit https://errors.pydantic.dev/2.11/v/string_type

bt/backtester_stable/BTRUN/excel_parser/strategy_parser.py:138: ValidationError
----------------------------- Captured stdout call -----------------------------
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=100, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  1
Instrument                                                          call
Transaction                                                         sell
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            0
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                              100
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 0, dtype: object
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=100, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  2
Instrument                                                           put
Transaction                                                         sell
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            0
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                              100
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 1, dtype: object
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  3
Instrument                                                          call
Transaction                                                          buy
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            2
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                                0
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 2, dtype: object
Unknown or unsupported indicator column: StrikePremiumCondition. Storing as extra_param.
Unknown or unsupported indicator column: HedgeStrikePremiumCondition. Storing as extra_param.
Unknown SLType after normalization: PERCENTAGE. Skipping SL risk rule.
Unknown TGTType after normalization: PERCENTAGE. Skipping TGT risk rule.
Unknown TrailSLType after normalization: PERCENTAGE. Skipping TRAIL risk rule.
_parse_leg_row Exception: 16 validation errors for LegModel
extra_params.W&TValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SLValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.TGTValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailAt
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.SL_TrailBy
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.ReEnteriesCount
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnEntry_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeOn
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffTradeOff
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_OpenTradeDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.OnExit_SqOffDelay
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params.HedgeStrikeValue
  Input should be a valid string [type=string_type, input_value=0, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
extra_params._parsed_indicator_conditions
  Input should be a valid string [type=string_type, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Row: StrategyName                   RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
IsIdle                                                                no
LegID                                                                  4
Instrument                                                           put
Transaction                                                          buy
Expiry                                                           current
W&Type                                                        percentage
W&TValue                                                               0
TrailW&T                                                              no
StrikeMethod                                                         atm
MatchPremium                                                        high
StrikeValue                                                            2
StrikePremiumCondition                                                 =
SLType                                                        percentage
SLValue                                                                0
TGTType                                                       percentage
TGTValue                                                               0
TrailSLType                                                   percentage
SL_TrailAt                                                             0
SL_TrailBy                                                             0
Lots                                                                   1
ReEntryType                                           instant new strike
ReEnteriesCount                                                        0
OnEntry_OpenTradeOn                                                    0
OnEntry_SqOffTradeOff                                                  0
OnEntry_SqOffAllLegs                                                  no
OnEntry_OpenTradeDelay                                                 0
OnEntry_SqOffDelay                                                     0
OnExit_OpenTradeOn                                                     0
OnExit_SqOffTradeOff                                                   0
OnExit_SqOffAllLegs                                                   no
OnExit_OpenAllLegs                                                    no
OnExit_OpenTradeDelay                                                  0
OnExit_SqOffDelay                                                      0
OpenHedge                                                             No
HedgeStrikeMethod                                                    atm
HedgeStrikeValue                                                       0
HedgeStrikePremiumCondition                                            =
Name: 3, dtype: object
=============================== warnings summary ===============================
<frozen importlib._bootstrap_external>:1184
  <frozen importlib._bootstrap_external>:1184: DeprecationWarning: The cuda.cudart module is deprecated and will be removed in a future release, please switch to use the cuda.bindings.runtime module instead.

<frozen importlib._bootstrap_external>:1184
  <frozen importlib._bootstrap_external>:1184: DeprecationWarning: The cuda.cuda module is deprecated and will be removed in a future release, please switch to use the cuda.bindings.driver module instead.

bt/backtester_stable/BTRUN/models/time_window.py:33
  /srv/samba/shared/bt/backtester_stable/BTRUN/models/time_window.py:33: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/
    @validator("start", pre=True)

bt/backtester_stable/BTRUN/models/time_window.py:40
  /srv/samba/shared/bt/backtester_stable/BTRUN/models/time_window.py:40: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/
    @validator("end", pre=True)

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html
=========================== short test summary info ============================
FAILED bt/tests/test_portfolio_mapping.py::test_portfolio_setting_field_mapped[start_date]
FAILED bt/tests/test_portfolio_mapping.py::test_portfolio_setting_field_mapped[end_date]
FAILED bt/tests/test_portfolio_mapping.py::test_portfolio_setting_field_mapped[is_tick_bt]
FAILED bt/tests/test_portfolio_mapping.py::test_portfolio_setting_field_mapped[portfolio_name]
FAILED bt/tests/test_portfolio_mapping.py::test_portfolio_setting_field_mapped[margin_multiplier]
FAILED bt/tests/test_portfolio_mapping.py::test_portfolio_setting_field_mapped[slippage_percent]
FAILED bt/tests/test_portfolio_mapping.py::test_strategy_setting_field_mapped[evaluator]
FAILED bt/tests/test_portfolio_mapping.py::test_strategy_setting_field_mapped[strategy_excel_path]
======================== 8 failed, 4 warnings in 3.62s =========================
