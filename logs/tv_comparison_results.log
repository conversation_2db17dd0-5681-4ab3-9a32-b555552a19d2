TV Backtester CPU vs GPU Comparison
================================================================================
Started at: 2025-05-30 21:36:00.614706
Working directory: /srv/samba/shared

================================================================================
Running CPU-based TV Backtester (BT_TV_GPU_aggregated_v4)
================================================================================
Command: /bin/python3 -m backtester_stable.BTRUN.BT_TV_GPU_aggregated_v4 --tv-excel bt/backtester_stable/BTRUN/input_sheets/input_tv.xlsx --output-path Trades/tv_cpu_test_20250530_213600.xlsx --workers 4
Return code: 1
Time taken: 0.02 seconds
STDERR: /bin/python3: Error while finding module specification for 'backtester_stable.BTRUN.BT_TV_GPU_aggregated_v4' (ModuleNotFoundError: No module named 'backtester_stable')

❌ CPU version failed - no output file

================================================================================
Running GPU-enhanced TV Backtester (BT_TV_GPU_enhanced)
================================================================================
Command: /bin/python3 -m backtester_stable.BTRUN.BT_TV_GPU_enhanced --tv-excel bt/backtester_stable/BTRUN/input_sheets/input_tv.xlsx --output-path Trades/tv_gpu_test_20250530_213600.xlsx --workers auto --batch-days 7
Return code: 1
Time taken: 0.02 seconds
STDERR: /bin/python3: Error while finding module specification for 'backtester_stable.BTRUN.BT_TV_GPU_enhanced' (ModuleNotFoundError: No module named 'backtester_stable')

❌ GPU version failed - no output file

================================================================================
COMPARISON RESULTS
================================================================================

📊 Performance:
CPU version: 0.02 seconds
GPU version: 0.02 seconds
Speedup: 1.07x

❌ Cannot compare outputs - one or both files missing


Completed at: 2025-05-30 21:36:00.655431

================================================================================
SUMMARY
================================================================================
❌ CPU version failed
❌ GPU version failed
