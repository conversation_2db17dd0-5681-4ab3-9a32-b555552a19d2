=== Optimized Bulk Loader ===
Starting at: 2025-06-02 21:38:37.846839
Initial row count: 344,000
Found 29 CSV files

Processing: IV_2023_apr_nifty_futures.csv
Total rows: 357,478
multiprocessing.pool.RemoteTraceback: 
"""
Traceback (most recent call last):
  File "/usr/lib/python3.10/multiprocessing/pool.py", line 125, in worker
    result = (True, func(*args, **kwds))
  File "/srv/samba/shared/optimized_bulk_loader.py", line 53, in load_csv_batch
    '{row['opt_type']}',
KeyError: 'opt_type'
"""

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/srv/samba/shared/optimized_bulk_loader.py", line 160, in <module>
    main()
  File "/srv/samba/shared/optimized_bulk_loader.py", line 132, in main
    loaded = process_file(csv_file)
  File "/srv/samba/shared/optimized_bulk_loader.py", line 105, in process_file
    for result in pool.imap(load_csv_batch, batches):
  File "/usr/lib/python3.10/multiprocessing/pool.py", line 873, in next
    raise value
KeyError: 'opt_type'
