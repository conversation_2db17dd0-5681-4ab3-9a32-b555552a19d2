2025-06-07 17:31:01,476 [INFO] ============================================================
2025-06-07 17:31:01,476 [INFO] 🚀 Starting Enterprise GPU Backtester Server V2
2025-06-07 17:31:01,476 [INFO] ============================================================
2025-06-07 17:31:01,476 [INFO] 📍 Server URL: http://**************:8000
2025-06-07 17:31:01,476 [INFO] 📍 Local URL: http://localhost:8000
2025-06-07 17:31:01,476 [INFO] 
Available endpoints:
2025-06-07 17:31:01,476 [INFO] - Login: http://**************:8000/login
2025-06-07 17:31:01,476 [INFO] - Dashboard: http://**************:8000/
2025-06-07 17:31:01,476 [INFO] - API Docs: http://**************:8000/docs
2025-06-07 17:31:01,476 [INFO] 
Test credentials:
2025-06-07 17:31:01,476 [INFO] - Phone: 9876543210
2025-06-07 17:31:01,477 [INFO] - OTP: 123456
2025-06-07 17:31:01,477 [INFO] 
✨ Server is ready!
2025-06-07 17:31:01,477 [INFO] Press Ctrl+C to stop
2025-06-07 17:31:01,477 [INFO] ============================================================
INFO:     Started server process [3357164]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     *************:49381 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:49383 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:41514 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:49418 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:45750 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:49453 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:49456 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:52326 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:49490 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:39816 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:49524 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:49526 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:54632 - "POST /api/v1/auto-backtest/upload-and-run HTTP/1.1" 404 Not Found
INFO:     **************:60746 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:49559 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:56996 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:49607 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:49609 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:39068 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:49642 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:53166 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:49684 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:49685 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:47170 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:49721 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:39376 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:49757 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:49759 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:38936 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:49790 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:45300 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:49827 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:49829 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:54730 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:49864 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:49886 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:49899 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:49900 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:43112 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:49932 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:57480 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:49973 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:49975 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:42112 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:50066 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:35430 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:50104 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:50106 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:36820 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:50138 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:60150 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:50178 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:50179 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:51538 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:50216 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:53436 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:48284 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:50288 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:50290 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:50313 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:48468 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
