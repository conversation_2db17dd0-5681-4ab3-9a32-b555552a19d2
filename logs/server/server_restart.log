2025-06-07 17:43:29,052 [WARNING] API v1 compatibility layer not available: No module named 'api'
2025-06-07 17:43:29,063 [INFO] ============================================================
2025-06-07 17:43:29,063 [INFO] 🚀 Starting Enterprise GPU Backtester Server V2
2025-06-07 17:43:29,063 [INFO] ============================================================
2025-06-07 17:43:29,063 [INFO] 📍 Server URL: http://**************:8000
2025-06-07 17:43:29,063 [INFO] 📍 Local URL: http://localhost:8000
2025-06-07 17:43:29,063 [INFO] 
Available endpoints:
2025-06-07 17:43:29,063 [INFO] - Login: http://**************:8000/login
2025-06-07 17:43:29,063 [INFO] - Dashboard: http://**************:8000/
2025-06-07 17:43:29,063 [INFO] - API Docs: http://**************:8000/docs
2025-06-07 17:43:29,063 [INFO] 
Test credentials:
2025-06-07 17:43:29,063 [INFO] - Phone: 9876543210
2025-06-07 17:43:29,063 [INFO] - OTP: 123456
2025-06-07 17:43:29,063 [INFO] 
✨ Server is ready!
2025-06-07 17:43:29,063 [INFO] Press Ctrl+C to stop
2025-06-07 17:43:29,063 [INFO] ============================================================
INFO:     Started server process [3361425]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     *************:50389 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:37738 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:50439 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:50440 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:58238 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:50473 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:56760 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:50508 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:50510 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:34272 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *************:50542 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     **************:35196 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [3361425]
