CONFIG.PY: Attempting to load config.py...
Partially restored config.py is being imported - Phase 10.
USING PARTIALLY RESTORED DEBUG CONFIG.PY - get_table_name active!
USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
CONFIG.PY: Finished loading config.py.
2025-05-30 21:47:22,907 - backtester_stable.BTRUN.strategies.tv_processor - INFO - Using DirectDAL for database access
2025-05-30 21:47:22,927 - root - ERROR - Failed to import BTRUN config module
Traceback (most recent call last):
  File "/srv/samba/shared/test_tv_minimal.py", line 50, in <module>
    from backtester_stable.BTRUN.BT_TV_GPU_aggregated_v4 import (
ImportError: cannot import name 'run_aggregated_backtest' from 'backtester_stable.BTRUN.BT_TV_GPU_aggregated_v4' (/srv/samba/shared/bt/backtester_stable/BTRUN/BT_TV_GPU_aggregated_v4.py)
Config INPUT_FILE_FOLDER: bt/backtester_stable/BTRUN/input_sheets

universal: bt/backtester_stable/BTRUN/input_sheets/input_tv_universal.xlsx
  Exists: False

regular: bt/backtester_stable/BTRUN/input_sheets/input_tv.xlsx
  Exists: False

fixed: bt/backtester_stable/BTRUN/input_sheets/input_tv_fixed.xlsx
  Exists: False

============================================================
Testing direct V4 import and execution
============================================================
Error with V4: cannot import name 'run_aggregated_backtest' from 'backtester_stable.BTRUN.BT_TV_GPU_aggregated_v4' (/srv/samba/shared/bt/backtester_stable/BTRUN/BT_TV_GPU_aggregated_v4.py)

============================================================
Testing Enhanced version
============================================================
CONFIG.PY: Attempting to load config.py...
2025-05-30 21:47:22,937 - bt.backtester_stable.BTRUN.core.config - WARNING - Partially restored config.py is being imported - Phase 10.
2025-05-30 21:47:22,937 - bt.backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - get_table_name active!
2025-05-30 21:47:22,937 - bt.backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
2025-05-30 21:47:22,937 - bt.backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
CONFIG.PY: Finished loading config.py.
2025-05-30 21:47:22,938 - bt.backtester_stable.BTRUN.core.gpu_helpers - INFO - cuDF is available - GPU acceleration enabled
2025-05-30 21:47:22,963 - bt.backtester_stable.BTRUN.strategies.tv_processor - INFO - Using DirectDAL for database access
2025-05-30 21:47:22,964 - root - ERROR - Failed to import BTRUN config module
2025-05-30 21:47:22,967 - backtester_stable.BTRUN.BT_TV_GPU_enhanced - ERROR - TV file not found: bt/backtester_stable/BTRUN/input_sheets/input_tv.xlsx
Enhanced found 0 TV settings
