2025-05-27 15:29:33,567 - backtester_stable.BTRUN.core.config - WARNING - Partially restored config.py is being imported - Phase 10.
2025-05-27 15:29:33,567 - backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - get_table_name active!
2025-05-27 15:29:33,567 - backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
2025-05-27 15:29:33,567 - backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
2025-05-27 15:29:34,246 - ptxcompiler.patch - DEBUG - CUDA Driver version 11.8
2025-05-27 15:29:34,246 - ptxcompiler.patch - DEBUG - CUDA Runtime version 11.8
2025-05-27 15:29:34,343 - numba.cuda.cudadrv.driver - DEBUG - call runtime api: cudaRuntimeGetVersion
2025-05-27 15:29:34,873 - numba.cuda.cudadrv.driver - INFO - init
2025-05-27 15:29:34,874 - numba.cuda.cudadrv.driver - DEBUG - call driver api: cuInit
2025-05-27 15:29:34,874 - numba.cuda.cudadrv.driver - DEBUG - call driver api: cuCtxGetCurrent
2025-05-27 15:29:34,874 - numba.cuda.cudadrv.driver - DEBUG - call driver api: cuDeviceGetCount
2025-05-27 15:29:34,874 - numba.cuda.cudadrv.driver - DEBUG - call driver api: cuDeviceGet
2025-05-27 15:29:34,874 - numba.cuda.cudadrv.driver - DEBUG - call driver api: cuDeviceGetAttribute
2025-05-27 15:29:34,874 - numba.cuda.cudadrv.driver - DEBUG - call driver api: cuDeviceGetAttribute
2025-05-27 15:29:34,874 - numba.cuda.cudadrv.driver - DEBUG - call driver api: cuDeviceGetName
2025-05-27 15:29:34,875 - numba.cuda.cudadrv.driver - DEBUG - call driver api: cuDeviceGetUuid_v2
2025-05-27 15:29:34,875 - numba.cuda.cudadrv.driver - DEBUG - call driver api: cuDevicePrimaryCtxRetain
2025-05-27 15:29:35,127 - numba.cuda.cudadrv.driver - DEBUG - call driver api: cuCtxPushCurrent_v2
2025-05-27 15:29:35,128 - numba.cuda.cudadrv.driver - DEBUG - call driver api: cuMemGetInfo_v2
2025-05-27 15:29:35,171 - backtester_stable.BTRUN.core.gpu_helpers - INFO - cuDF is available - GPU acceleration enabled
2025-05-27 15:29:35,392 - thrift.transport.sslcompat - DEBUG - ipaddress module is available
2025-05-27 15:29:35,392 - thrift.transport.sslcompat - DEBUG - ssl.match_hostname is available
2025-05-27 15:29:35,428 - root - ERROR - Failed to import BTRUN config module
2025-05-27 15:29:35,436 - __main__ - INFO - ============================================================
2025-05-27 15:29:35,437 - __main__ - INFO - TV AGGREGATED BACKTEST V3 - SIMPLIFIED
2025-05-27 15:29:35,437 - __main__ - INFO - ============================================================
2025-05-27 15:29:35,437 - __main__ - INFO - Date range: 240119 to 240120
2025-05-27 15:29:35,437 - backtester_stable.BTRUN.core.gpu_helpers - INFO - Switched to CPU-only mode programmatically
2025-05-27 15:29:35,644 - __main__ - INFO - Processing TV setting: NFNDSTR
2025-05-27 15:29:35,819 - __main__ - INFO - Loaded 1764 signals
2025-05-27 15:29:36,004 - backtester_stable.BTRUN.strategies.tv_processor - INFO - [NFNDSTR] Starting manual trade synthesis for ManualTradeEntryTime: 09:18:00
2025-05-27 15:29:36,005 - __main__ - ERROR - Fatal error: RawTvSignalModel() takes no arguments
