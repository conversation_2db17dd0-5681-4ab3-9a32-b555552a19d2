CONFIG.PY: Attempting to load config.py...
Partially restored config.py is being imported - Phase 10.
USING PARTIALLY RESTORED DEBUG CONFIG.PY - get_table_name active!
USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
CONFIG.PY: Finished loading config.py.
2025-05-29 21:31:23,970 - root - ERROR - Failed to import BTRUN config module
Warning: Could not import required modules: No module named 'bt.backtester_stable.models'
2025-05-30 03:01:23,976 - __main__ - INFO - Running in sequential (single-worker) mode.
2025-05-30 03:01:23,976 - __main__ - INFO - GPU optimization is not available (missing dependencies or import error). Running sequentially.
2025-05-30 03:01:23,976 - __main__ - INFO - GPU optimization disabled or unavailable. Running sequentially.
2025-05-30 03:01:23,976 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-30 03:01:23,977 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-30 03:01:23,977 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-30 03:01:23,983 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-30 03:01:23,984 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-30 03:01:23,984 - backtester_stable.BTRUN.core.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-30 03:01:23,984 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-30 03:01:23,984 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-30 03:01:23,984 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-30 03:01:23,984 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-30 03:01:23,984 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-30 03:01:23,984 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-30 03:01:23,985 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/test_dte0_portfolio_proper.xlsx
2025-05-30 03:01:23,985 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared
2025-05-30 03:01:23,985 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/test_dte0_portfolio_proper.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared
2025-05-30 03:01:24,093 - __main__ - INFO - Processing portfolio 'DTE0_TEST' sequentially.
2025-05-30 03:01:24,093 - __main__ - INFO - Processing batch: DTE0_TEST_fulldaterange (2024-01-01 to 2024-01-07) for original portfolio 'DTE0_TEST'
[DEBUG] run_full_backtest bt_params keys: ['portfolio_model', 'portfolio_name', 'start_date', 'end_date']
2025-05-30 03:01:24,093 - backtester_stable.BTRUN.core.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'DTE0_TEST'
2025-05-30 03:01:24,311 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-30 03:01:24,341 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.029s, returned 563 rows. Query: SELECT SUBSTRING('SELECT DISTINCT trade_date FROM nifty_option_chain', 1, 200) ...
2025-05-30 03:01:24,383 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - Cached 563 distinct trade dates
2025-05-30 03:01:24,384 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] portfolio_model_dict keys: ['portfolio_name', 'start_date', 'end_date', 'slippage_percent', 'margin_multiplier', 'is_tick_bt', 'strategies', 'extra_params']
2025-05-30 03:01:24,384 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Number of trade records generated: 0
2025-05-30 03:01:24,384 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Generated 0 trade records via model-driven path
2025-05-30 03:01:24,384 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - WARNING - No tick P&L data calculated, Max Profit/Loss sheet will have no data
2025-05-30 03:01:24,384 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Closed reusable HeavyDB connection from get_trades_for_portfolio
2025-05-30 03:01:24,385 - debug_detail - WARNING - BUILDERS: No 'trades' data in bt_response['data'] or invalid format.
2025-05-30 03:01:24,385 - backtester_stable.BTRUN.core.runtime - WARNING - No trades found in backtest response after parsing in runtime.
2025-05-30 03:01:24,387 - __main__ - INFO - Worker completed batch: DTE0_TEST_fulldaterange for portfolio 'DTE0_TEST' in 0.29s
2025-05-30 03:01:24,387 - __main__ - INFO - Combining 1 batch(es) for portfolio: DTE0_TEST
2025-05-30 03:01:24,388 - __main__ - INFO - Attempting to save combined backtest results for 1 portfolio(s) to /srv/samba/shared/test_dte0_output.xlsx
2025-05-30 03:01:24,388 - backtester_stable.BTRUN.core.io - INFO - Writing results to /srv/samba/shared/test_dte0_output.xlsx
2025-05-30 03:01:24,389 - backtester_stable.BTRUN.core.io - INFO - 2025-05-29 21:31:24.389384, Started writing stats to excel file: /srv/samba/shared/test_dte0_output.xlsx
2025-05-30 03:01:24,404 - backtester_stable.BTRUN.core.io - INFO - Generated PortfolioParameter sheet from PortfolioSetting
2025-05-30 03:01:24,404 - backtester_stable.BTRUN.core.io - INFO - Added empty PORTFOLIO Trans sheet
2025-05-30 03:01:24,405 - backtester_stable.BTRUN.core.io - INFO - Added PORTFOLIO Results sheet
2025-05-30 03:01:24,405 - backtester_stable.BTRUN.core.io - INFO - 2025-05-29 21:31:24.405709, Excel file prepared successfully, Time taken: 0.02s
2025-05-30 03:01:24,414 - backtester_stable.BTRUN.core.io - INFO - Successfully wrote results to /srv/samba/shared/test_dte0_output.xlsx
2025-05-30 03:01:24,414 - __main__ - INFO - Successfully saved backtest results to /srv/samba/shared/test_dte0_output.xlsx
2025-05-30 03:01:24,414 - __main__ - INFO - Backtest results saved to /srv/samba/shared/test_dte0_output.xlsx
