2025-05-30 21:28:37,309 - bt.backtester_stable.BTRUN.core.config - WARNING - Partially restored config.py is being imported - Phase 10.
2025-05-30 21:28:37,309 - bt.backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - get_table_name active!
2025-05-30 21:28:37,309 - bt.backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
2025-05-30 21:28:37,309 - bt.backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
2025-05-30 21:28:38,701 - numba.cuda.cudadrv.driver - INFO - init
2025-05-30 21:28:39,002 - bt.backtester_stable.BTRUN.core.gpu_helpers - INFO - cuDF is available - GPU acceleration enabled
2025-05-30 21:28:39,255 - bt.backtester_stable.BTRUN.strategies.tv_processor - INFO - Using DirectDAL for database access
2025-05-30 21:28:39,276 - root - ERROR - Failed to import BTRUN config module
2025-05-30 21:28:39,278 - __main__ - ERROR - Import error: No module named 'backtester_stable'
