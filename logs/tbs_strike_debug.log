2025-05-27 19:39:16,560 - INFO - Starting TBS strike selection debug for date: 20240103
2025-05-27 19:39:16,561 - INFO - Loading Excel config: test_data/tbs_test_1day.xlsx
2025-05-27 19:39:16,681 - INFO - Debugging legacy strike selection...
2025-05-27 19:39:18,299 - INFO - Legacy system: Date=20240103, Time=0916
2025-05-27 19:39:18,300 - INFO - Legacy Leg 1: CALL, Method=ATM
2025-05-27 19:39:18,300 - INFO - 
            Legacy Strike Selection Logic for Leg 1:
            1. Get spot price at 0916
            2. Apply strike method 'ATM' for CALL
            3. If ATM:
               - Use config.USE_SYNTHETIC_FUTURE_ATM to determine method
               - Either use synthetic future or round to nearest strike
            
2025-05-27 19:39:18,300 - INFO - Legacy Leg 2: PUT, Method=ATM
2025-05-27 19:39:18,300 - INFO - 
            Legacy Strike Selection Logic for Leg 2:
            1. Get spot price at 0916
            2. Apply strike method 'ATM' for PUT
            3. If ATM:
               - Use config.USE_SYNTHETIC_FUTURE_ATM to determine method
               - Either use synthetic future or round to nearest strike
            
2025-05-27 19:39:18,300 - INFO - Debugging GPU strike selection...
2025-05-27 19:39:18,300 - ERROR - GPU strike selection debug failed: 'str' object has no attribute 'strftime'
2025-05-27 19:44:37,016 - INFO - Starting TBS strike selection debug for date: 20240103
2025-05-27 19:44:37,016 - INFO - Loading Excel config: test_data/tbs_test_1day.xlsx
2025-05-27 19:44:37,126 - INFO - Debugging legacy strike selection...
2025-05-27 19:44:38,763 - INFO - Legacy system: Date=20240103, Time=0916
2025-05-27 19:44:38,764 - INFO - Legacy Leg 1: CALL, Method=ATM
2025-05-27 19:44:38,764 - INFO - 
            Legacy Strike Selection Logic for Leg 1:
            1. Get spot price at 0916
            2. Apply strike method 'ATM' for CALL
            3. If ATM:
               - Use config.USE_SYNTHETIC_FUTURE_ATM to determine method
               - Either use synthetic future or round to nearest strike
            
2025-05-27 19:44:38,764 - INFO - Legacy Leg 2: PUT, Method=ATM
2025-05-27 19:44:38,764 - INFO - 
            Legacy Strike Selection Logic for Leg 2:
            1. Get spot price at 0916
            2. Apply strike method 'ATM' for PUT
            3. If ATM:
               - Use config.USE_SYNTHETIC_FUTURE_ATM to determine method
               - Either use synthetic future or round to nearest strike
            
2025-05-27 19:44:38,764 - INFO - Debugging GPU strike selection...
2025-05-27 19:44:38,764 - ERROR - GPU strike selection debug failed: 'str' object has no attribute 'strftime'
2025-05-27 19:46:58,266 - INFO - Starting TBS strike selection debug for date: 20240103
2025-05-27 19:46:58,266 - INFO - Loading Excel config: test_data/tbs_test_1day.xlsx
2025-05-27 19:46:58,387 - INFO - Debugging legacy strike selection...
2025-05-27 19:46:59,985 - INFO - Legacy system: Date=20240103, Time=0916
2025-05-27 19:46:59,987 - INFO - Legacy Leg 1: CALL, Method=ATM
2025-05-27 19:46:59,987 - INFO - 
            Legacy Strike Selection Logic for Leg 1:
            1. Get spot price at 0916
            2. Apply strike method 'ATM' for CALL
            3. If ATM:
               - Use config.USE_SYNTHETIC_FUTURE_ATM to determine method
               - Either use synthetic future or round to nearest strike
            
2025-05-27 19:46:59,988 - INFO - Legacy Leg 2: PUT, Method=ATM
2025-05-27 19:46:59,988 - INFO - 
            Legacy Strike Selection Logic for Leg 2:
            1. Get spot price at 0916
            2. Apply strike method 'ATM' for PUT
            3. If ATM:
               - Use config.USE_SYNTHETIC_FUTURE_ATM to determine method
               - Either use synthetic future or round to nearest strike
            
2025-05-27 19:46:59,988 - INFO - Debugging GPU strike selection...
2025-05-27 19:46:59,988 - INFO - GPU system: Date=2024-01-03, Time=0916
2025-05-27 19:46:59,990 - WARNING - Partially restored config.py is being imported - Phase 10.
2025-05-27 19:46:59,990 - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - get_table_name active!
2025-05-27 19:46:59,991 - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
2025-05-27 19:46:59,991 - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
2025-05-27 19:47:01,285 - INFO - init
2025-05-27 19:47:01,588 - INFO - cuDF is available - GPU acceleration enabled
2025-05-27 19:47:01,899 - INFO - Using DirectDAL for database access
2025-05-27 19:47:01,921 - ERROR - Failed to import BTRUN config module
2025-05-27 19:47:01,922 - ERROR - Failed to query HeavyDB: No module named 'bt.backtester_stable.BTRUN.heavydb_connection'
2025-05-27 19:47:01,923 - INFO - Comparing strike selection...
2025-05-27 19:47:01,923 - INFO - Strike selection comparison not fully implemented
2025-05-27 19:47:52,792 - INFO - Starting TBS strike selection debug for date: 20240103
2025-05-27 19:47:52,792 - INFO - Loading Excel config: test_data/tbs_test_1day.xlsx
2025-05-27 19:47:52,904 - INFO - Debugging legacy strike selection...
2025-05-27 19:47:54,561 - INFO - Legacy system: Date=20240103, Time=0916
2025-05-27 19:47:54,562 - INFO - Legacy Leg 1: CALL, Method=ATM
2025-05-27 19:47:54,562 - INFO - 
            Legacy Strike Selection Logic for Leg 1:
            1. Get spot price at 0916
            2. Apply strike method 'ATM' for CALL
            3. If ATM:
               - Use config.USE_SYNTHETIC_FUTURE_ATM to determine method
               - Either use synthetic future or round to nearest strike
            
2025-05-27 19:47:54,562 - INFO - Legacy Leg 2: PUT, Method=ATM
2025-05-27 19:47:54,562 - INFO - 
            Legacy Strike Selection Logic for Leg 2:
            1. Get spot price at 0916
            2. Apply strike method 'ATM' for PUT
            3. If ATM:
               - Use config.USE_SYNTHETIC_FUTURE_ATM to determine method
               - Either use synthetic future or round to nearest strike
            
2025-05-27 19:47:54,563 - INFO - Debugging GPU strike selection...
2025-05-27 19:47:54,563 - INFO - GPU system: Date=2024-01-03, Time=0916
2025-05-27 19:47:54,564 - WARNING - Partially restored config.py is being imported - Phase 10.
2025-05-27 19:47:54,564 - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - get_table_name active!
2025-05-27 19:47:54,565 - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
2025-05-27 19:47:54,565 - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
2025-05-27 19:47:55,878 - INFO - init
2025-05-27 19:47:56,176 - INFO - cuDF is available - GPU acceleration enabled
2025-05-27 19:47:56,481 - INFO - Using DirectDAL for database access
2025-05-27 19:47:56,502 - ERROR - Failed to import BTRUN config module
2025-05-27 19:47:56,503 - ERROR - Failed to query HeavyDB: No module named 'bt.backtester_stable.BTRUN.heavydb_connection'
2025-05-27 19:47:56,503 - ERROR - Traceback: Traceback (most recent call last):
  File "/srv/samba/shared/scripts/debug_tbs_strike_selection.py", line 127, in query_heavydb_strike_data
    from bt.backtester_stable.BTRUN.heavydb_connection import get_connection
ModuleNotFoundError: No module named 'bt.backtester_stable.BTRUN.heavydb_connection'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/srv/samba/shared/scripts/debug_tbs_strike_selection.py", line 131, in query_heavydb_strike_data
    import bt.backtester_stable.BTRUN.heavydb_connection as heavydb_conn
ModuleNotFoundError: No module named 'bt.backtester_stable.BTRUN.heavydb_connection'

2025-05-27 19:47:56,504 - INFO - Comparing strike selection...
2025-05-27 19:47:56,504 - INFO - Strike selection comparison not fully implemented
2025-05-27 19:48:31,564 - INFO - Starting TBS strike selection debug for date: 20240103
2025-05-27 19:48:31,564 - INFO - Loading Excel config: test_data/tbs_test_1day.xlsx
2025-05-27 19:48:31,677 - INFO - Debugging legacy strike selection...
2025-05-27 19:48:33,320 - INFO - Legacy system: Date=20240103, Time=0916
2025-05-27 19:48:33,320 - INFO - Legacy Leg 1: CALL, Method=ATM
2025-05-27 19:48:33,320 - INFO - 
            Legacy Strike Selection Logic for Leg 1:
            1. Get spot price at 0916
            2. Apply strike method 'ATM' for CALL
            3. If ATM:
               - Use config.USE_SYNTHETIC_FUTURE_ATM to determine method
               - Either use synthetic future or round to nearest strike
            
2025-05-27 19:48:33,321 - INFO - Legacy Leg 2: PUT, Method=ATM
2025-05-27 19:48:33,321 - INFO - 
            Legacy Strike Selection Logic for Leg 2:
            1. Get spot price at 0916
            2. Apply strike method 'ATM' for PUT
            3. If ATM:
               - Use config.USE_SYNTHETIC_FUTURE_ATM to determine method
               - Either use synthetic future or round to nearest strike
            
2025-05-27 19:48:33,321 - INFO - Debugging GPU strike selection...
2025-05-27 19:48:33,321 - INFO - GPU system: Date=2024-01-03, Time=0916
2025-05-27 19:48:33,321 - INFO - Attempting to connect to HeavyDB directly with pymapd...
2025-05-27 19:48:33,322 - ERROR - pymapd module not found. Installing...
2025-05-27 19:49:35,131 - ERROR - Failed to query HeavyDB: Command '['pip', 'install', 'pymapd']' returned non-zero exit status 1.
2025-05-27 19:49:35,132 - ERROR - Traceback: Traceback (most recent call last):
  File "/srv/samba/shared/scripts/debug_tbs_strike_selection.py", line 126, in query_heavydb_strike_data
    import pymapd
ModuleNotFoundError: No module named 'pymapd'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/srv/samba/shared/scripts/debug_tbs_strike_selection.py", line 131, in query_heavydb_strike_data
    subprocess.check_call(["pip", "install", "pymapd"])
  File "/usr/lib/python3.10/subprocess.py", line 369, in check_call
    raise CalledProcessError(retcode, cmd)
subprocess.CalledProcessError: Command '['pip', 'install', 'pymapd']' returned non-zero exit status 1.

2025-05-27 19:49:35,132 - INFO - Comparing strike selection...
2025-05-27 19:49:35,132 - INFO - Strike selection comparison not fully implemented
2025-05-27 20:28:21,879 - INFO - Starting TBS strike selection debug for date: 20240103
2025-05-27 20:28:21,879 - INFO - Loading Excel config: test_data/tbs/input_portfolio_tbs_1day.xlsx
2025-05-27 20:29:25,386 - INFO - Starting TBS strike selection debug for date: 20240103
2025-05-27 20:29:25,386 - INFO - Loading Excel config: test_data/tbs/input_portfolio_tbs_1day.xlsx
2025-05-27 20:30:23,905 - INFO - Starting TBS strike selection debug for date: 20240103
2025-05-27 20:30:23,905 - INFO - Loading Excel config: test_data/tbs/input_portfolio_tbs_1day.xlsx
2025-05-27 20:30:24,004 - INFO - Using strategy file: test_data/tbs/input_tbs_multi_legs.xlsx
2025-05-27 20:30:24,022 - INFO - Debugging legacy strike selection...
2025-05-27 20:30:24,023 - INFO - Legacy system: Date=20240103, Time=091600
2025-05-27 20:30:24,023 - INFO - Legacy Leg 1: call, Method=atm
2025-05-27 20:30:24,023 - INFO - 
            Legacy Strike Selection Logic for Leg 1:
            1. Get spot price at 091600
            2. Apply strike method 'atm' for call
            3. If ATM:
               - Check config.USE_SYNTHETIC_FUTURE_ATM setting
               - Either use synthetic future or round to nearest strike
            4. If ITM/OTM:
               - Calculate offset from ATM based on strike increments
            
2025-05-27 20:30:24,023 - INFO - Legacy Leg 2: put, Method=atm
2025-05-27 20:30:24,023 - INFO - 
            Legacy Strike Selection Logic for Leg 2:
            1. Get spot price at 091600
            2. Apply strike method 'atm' for put
            3. If ATM:
               - Check config.USE_SYNTHETIC_FUTURE_ATM setting
               - Either use synthetic future or round to nearest strike
            4. If ITM/OTM:
               - Calculate offset from ATM based on strike increments
            
2025-05-27 20:30:24,023 - INFO - Legacy Leg 3: call, Method=atm
2025-05-27 20:30:24,023 - INFO - 
            Legacy Strike Selection Logic for Leg 3:
            1. Get spot price at 091600
            2. Apply strike method 'atm' for call
            3. If ATM:
               - Check config.USE_SYNTHETIC_FUTURE_ATM setting
               - Either use synthetic future or round to nearest strike
            4. If ITM/OTM:
               - Calculate offset from ATM based on strike increments
            
2025-05-27 20:30:24,023 - INFO - Legacy Leg 4: put, Method=atm
2025-05-27 20:30:24,023 - INFO - 
            Legacy Strike Selection Logic for Leg 4:
            1. Get spot price at 091600
            2. Apply strike method 'atm' for put
            3. If ATM:
               - Check config.USE_SYNTHETIC_FUTURE_ATM setting
               - Either use synthetic future or round to nearest strike
            4. If ITM/OTM:
               - Calculate offset from ATM based on strike increments
            
2025-05-27 20:30:24,024 - INFO - Debugging GPU strike selection...
2025-05-27 20:30:24,024 - INFO - GPU system: Date=2024-01-03, Time=091600
2025-05-27 20:30:24,024 - INFO - Connecting to HeavyDB using project utilities...
2025-05-27 20:30:24,025 - WARNING - Partially restored config.py is being imported - Phase 10.
2025-05-27 20:30:24,025 - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - get_table_name active!
2025-05-27 20:30:24,025 - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
2025-05-27 20:30:24,025 - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
2025-05-27 20:30:25,358 - INFO - init
2025-05-27 20:30:25,658 - INFO - cuDF is available - GPU acceleration enabled
2025-05-27 20:30:25,922 - INFO - Using DirectDAL for database access
2025-05-27 20:30:25,943 - ERROR - Failed to import BTRUN config module
2025-05-27 20:30:25,944 - INFO - Using project's HeavyDB connection utilities
2025-05-27 20:30:26,165 - INFO - Successfully connected to HeavyDB at 127.0.0.1:6274/heavyai
2025-05-27 20:30:26,178 - INFO - HeavyDB connection test successful
2025-05-27 20:30:26,178 - INFO - Executing HeavyDB query for 2024-01-03 09:16:00
2025-05-27 20:30:26,890 - INFO - HeavyDB strike data: ATM=21700.0, Spot=21615.75, Strikes=63
2025-05-27 20:30:26,891 - INFO - GPU Leg 1: call, Method=atm
2025-05-27 20:30:26,891 - INFO - Selected strike (ATM): 21700.0
2025-05-27 20:30:26,891 - INFO - 
                    GPU Strike Selection for Leg 1 (ATM):
                    - Pre-computed ATM strike from nifty_option_chain: 21700.0
                    - Underlying price: 21615.75
                    - Calculated using synthetic future method
                    
2025-05-27 20:30:26,891 - INFO - GPU Leg 2: put, Method=atm
2025-05-27 20:30:26,891 - INFO - Selected strike (ATM): 21700.0
2025-05-27 20:30:26,891 - INFO - 
                    GPU Strike Selection for Leg 2 (ATM):
                    - Pre-computed ATM strike from nifty_option_chain: 21700.0
                    - Underlying price: 21615.75
                    - Calculated using synthetic future method
                    
2025-05-27 20:30:26,891 - INFO - GPU Leg 3: call, Method=atm
2025-05-27 20:30:26,892 - INFO - Selected strike (ATM): 21700.0
2025-05-27 20:30:26,892 - INFO - 
                    GPU Strike Selection for Leg 3 (ATM):
                    - Pre-computed ATM strike from nifty_option_chain: 21700.0
                    - Underlying price: 21615.75
                    - Calculated using synthetic future method
                    
2025-05-27 20:30:26,892 - INFO - GPU Leg 4: put, Method=atm
2025-05-27 20:30:26,892 - INFO - Selected strike (ATM): 21700.0
2025-05-27 20:30:26,892 - INFO - 
                    GPU Strike Selection for Leg 4 (ATM):
                    - Pre-computed ATM strike from nifty_option_chain: 21700.0
                    - Underlying price: 21615.75
                    - Calculated using synthetic future method
                    
2025-05-27 20:30:26,892 - INFO - Comparing strike selection...
2025-05-27 20:30:26,892 - INFO - Strike selection comparison not fully implemented
