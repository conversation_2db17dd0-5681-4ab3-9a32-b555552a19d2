STDOUT:
CONFIG.PY: Attempting to load config.py...
CONFIG.PY: Finished loading config.py.
Warning: Could not import required modules: No module named 'bt.backtester_stable.models'


STDERR:
2025-05-30 21:37:19,257 - backtester_stable.BTRUN.core.config - WARNING - Partially restored config.py is being imported - Phase 10.
2025-05-30 21:37:19,257 - backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - get_table_name active!
2025-05-30 21:37:19,257 - backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
2025-05-30 21:37:19,257 - backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
2025-05-30 21:37:20,594 - numba.cuda.cudadrv.driver - INFO - init
2025-05-30 21:37:20,896 - backtester_stable.BTRUN.core.gpu_helpers - INFO - cuDF is available - GPU acceleration enabled
2025-05-30 21:37:21,162 - root - ERROR - Failed to import BTRUN config module
usage: BT_TV_GPU_aggregated_v4.py [-h] [--start-date START_DATE]
                                  [--end-date END_DATE]
                                  [--output-dir OUTPUT_DIR]
                                  [--slippage SLIPPAGE] [--capital CAPITAL]
BT_TV_GPU_aggregated_v4.py: error: unrecognized arguments: --tv-excel /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tv.xlsx --output-path /srv/samba/shared/Trades/tv_cpu_test_20250530_213718.xlsx --workers 4
