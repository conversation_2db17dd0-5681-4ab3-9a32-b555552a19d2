2025-05-21 18:59:52,824 - column_mapping_verify - INFO - Starting column mapping verification...
2025-05-21 18:59:52,824 - column_mapping_verify - INFO - Running backtester with portfolio file: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_fixed.xlsx
2025-05-21 18:59:52,824 - column_mapping_verify - INFO - Running command: python3 /srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py --portfolio-excel /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_fixed.xlsx --output-path /srv/samba/shared/Trades/column_verify_20250521_185952.xlsx
2025-05-21 19:00:00,558 - column_mapping_verify - INFO - Backtester completed successfully: /srv/samba/shared/Trades/column_verify_20250521_185952.xlsx
2025-05-21 19:00:00,559 - column_mapping_verify - INFO - Reading portfolio file: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_fixed.xlsx
2025-05-21 19:00:00,672 - column_mapping_verify - INFO - Found alternative strategy file: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/INPUT TBS.xlsx
2025-05-21 19:00:00,672 - column_mapping_verify - INFO - Strategy file: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/INPUT TBS.xlsx
2025-05-21 19:00:00,693 - column_mapping_verify - INFO - Reading output files: /srv/samba/shared/Trades/column_verify_20250521_185952.xlsx, /srv/samba/shared/Trades/column_verify_20250521_185952.json
2025-05-21 19:00:00,719 - column_mapping_verify - INFO - Found 7 sheets in output Excel
2025-05-21 19:00:00,719 - column_mapping_verify - INFO - Read output JSON file
2025-05-21 19:00:00,720 - column_mapping_verify - INFO - Found 2 trades in PORTFOLIO Trans sheet
2025-05-21 19:00:00,720 - column_mapping_verify - INFO - Verifying portfolio_setting mappings...
2025-05-21 19:00:00,720 - column_mapping_verify - INFO - ✅ portfolio_setting.StartDate -> entry_date: 2025-04-01 ✓ 2025-04-01
2025-05-21 19:00:00,720 - column_mapping_verify - WARNING - ❌ portfolio_setting.EndDate -> exit_date: Expected 2025-04-02, got 2025-04-01
2025-05-21 19:00:00,720 - column_mapping_verify - INFO - ✅ portfolio_setting.PortfolioName -> portfolio_name: NIF0DTE ✓ NIF0DTE
2025-05-21 19:00:00,720 - column_mapping_verify - INFO - portfolio_setting mapping verification: 2/3 verified
2025-05-21 19:00:00,721 - column_mapping_verify - INFO - Verifying strategy_setting mappings...
2025-05-21 19:00:00,721 - column_mapping_verify - INFO - strategy_setting mapping verification: 0/0 verified
2025-05-21 19:00:00,722 - column_mapping_verify - INFO - 
=== Column Mapping Verification Results ===

2025-05-21 19:00:00,722 - column_mapping_verify - INFO - portfolio_setting: 2/3 mappings verified (66.7%) - FAILED
2025-05-21 19:00:00,722 - column_mapping_verify - INFO -   Failed mappings:
2025-05-21 19:00:00,722 - column_mapping_verify - INFO -     EndDate -> exit_date: Expected 2025-04-02, got 2025-04-01
2025-05-21 19:00:00,722 - column_mapping_verify - ERROR - 
❌ SOME VERIFICATIONS FAILED: Some column mappings don't match expected values
2025-05-21 19:00:00,722 - column_mapping_verify - ERROR - Column mapping verification failed
2025-05-21 19:00:00,722 - column_mapping_verify - ERROR - Verification failed
