2025-05-27 19:39:19,091 - INFO - Starting TBS exit time debug for date: 20240103
2025-05-27 19:39:19,091 - INFO - Loading Excel config: test_data/tbs_test_1day.xlsx
2025-05-27 19:39:19,203 - INFO - Debugging risk rule execution...
2025-05-27 19:39:19,204 - INFO - Loading Excel config: test_data/tbs_test_1day.xlsx
2025-05-27 19:39:19,225 - INFO - Expected exit time from Excel: 1200
2025-05-27 19:39:19,225 - INFO - Legacy risk evaluation approach:
2025-05-27 19:39:19,225 - INFO - 
        Legacy Risk Evaluation (Util.py):
        1. Checks exit time: endTime
        2. SL check: slValue based on slType
        3. TP check: tgtValue based on tgtType
        4. Sets exit reason based on hit condition
        5. Force exit at EndTime if no SL/TP hit
        
2025-05-27 19:39:19,225 - INFO - GPU risk evaluation approach:
2025-05-27 19:39:19,226 - WARNING - Partially restored config.py is being imported - Phase 10.
2025-05-27 19:39:19,226 - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - get_table_name active!
2025-05-27 19:39:19,227 - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
2025-05-27 19:39:19,227 - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
2025-05-27 19:39:20,539 - INFO - init
2025-05-27 19:39:20,847 - INFO - cuDF is available - GPU acceleration enabled
2025-05-27 19:39:21,108 - INFO - Using DirectDAL for database access
2025-05-27 19:39:21,130 - ERROR - Failed to import BTRUN config module
2025-05-27 19:39:21,131 - ERROR - Failed to import GPU risk module: Object of type int64 is not JSON serializable
2025-05-27 19:39:21,131 - INFO - Running legacy TBS...
2025-05-27 19:39:21,132 - INFO - Legacy command: cd /srv/samba/shared/bt/archive/backtester_stable/BTRUN && export MYSQL_HOST=localhost && export DEBUG_MODE=true && python BTRunPortfolio.py --excel test_data/tbs_test_1day.xlsx --output debug_output/tbs_exit_time/legacy_tbs_output.xlsx
2025-05-27 19:39:21,133 - ERROR - Legacy TBS failed: Command 'cd /srv/samba/shared/bt/archive/backtester_stable/BTRUN && export MYSQL_HOST=localhost && export DEBUG_MODE=true && python BTRunPortfolio.py --excel test_data/tbs_test_1day.xlsx --output debug_output/tbs_exit_time/legacy_tbs_output.xlsx' returned non-zero exit status 127.
2025-05-27 19:39:21,133 - INFO - Running GPU TBS...
2025-05-27 19:39:21,133 - INFO - GPU command: python -m bt.backtester_stable.BTRUN.BTRunPortfolio_GPU --legacy-excel --portfolio-excel test_data/tbs_test_1day.xlsx --output-path debug_output/tbs_exit_time/gpu_tbs_output.xlsx --debug
2025-05-27 19:39:21,134 - ERROR - GPU TBS failed: [Errno 2] No such file or directory: 'python'
2025-05-27 19:39:21,134 - ERROR - Failed to get exit times
2025-05-27 19:44:39,546 - INFO - Starting TBS exit time debug for date: 20240103
2025-05-27 19:44:39,546 - INFO - Loading Excel config: test_data/tbs_test_1day.xlsx
2025-05-27 19:44:39,657 - INFO - Debugging risk rule execution...
2025-05-27 19:44:39,657 - INFO - Loading Excel config: test_data/tbs_test_1day.xlsx
2025-05-27 19:44:39,681 - INFO - Expected exit time from Excel: 1200
2025-05-27 19:44:39,681 - INFO - Legacy risk evaluation approach:
2025-05-27 19:44:39,682 - INFO - 
        Legacy Risk Evaluation (Util.py):
        1. Checks exit time: endTime
        2. SL check: slValue based on slType
        3. TP check: tgtValue based on tgtType
        4. Sets exit reason based on hit condition
        5. Force exit at EndTime if no SL/TP hit
        
2025-05-27 19:44:39,682 - INFO - GPU risk evaluation approach:
2025-05-27 19:44:39,683 - WARNING - Partially restored config.py is being imported - Phase 10.
2025-05-27 19:44:39,683 - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - get_table_name active!
2025-05-27 19:44:39,683 - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
2025-05-27 19:44:39,683 - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
2025-05-27 19:44:41,026 - INFO - init
2025-05-27 19:44:41,319 - INFO - cuDF is available - GPU acceleration enabled
2025-05-27 19:44:41,595 - INFO - Using DirectDAL for database access
2025-05-27 19:44:41,615 - ERROR - Failed to import BTRUN config module
2025-05-27 19:44:41,617 - ERROR - Failed to import GPU risk module: Object of type int64 is not JSON serializable
2025-05-27 19:44:41,617 - INFO - Running legacy TBS...
2025-05-27 19:44:41,617 - INFO - Legacy command: cd /srv/samba/shared/bt/archive/backtester_stable/BTRUN && export MYSQL_HOST=localhost && export DEBUG_MODE=true && python3 BTRunPortfolio.py --excel test_data/tbs_test_1day.xlsx --output debug_output/tbs_exit_time/legacy_tbs_output.xlsx
2025-05-27 19:44:47,854 - INFO - Legacy backtester completed. Output: debug_output/tbs_exit_time/legacy_tbs_output.xlsx
2025-05-27 19:44:47,855 - ERROR - Failed to extract legacy exit times: [Errno 2] No such file or directory: 'debug_output/tbs_exit_time/legacy_tbs_output.xlsx'
2025-05-27 19:44:47,855 - INFO - Running GPU TBS...
2025-05-27 19:44:47,855 - INFO - GPU command: python3 -m bt.backtester_stable.BTRUN.BTRunPortfolio_GPU --legacy-excel --portfolio-excel test_data/tbs_test_1day.xlsx --output-path debug_output/tbs_exit_time/gpu_tbs_output.xlsx --debug
2025-05-27 19:44:54,415 - INFO - GPU TBS completed. Output: debug_output/tbs_exit_time/gpu_tbs_output.xlsx
2025-05-27 19:44:54,417 - ERROR - Failed to extract GPU exit times: [Errno 2] No such file or directory: 'debug_output/tbs_exit_time/gpu_tbs_output.xlsx'
2025-05-27 19:44:54,417 - ERROR - Failed to get exit times
