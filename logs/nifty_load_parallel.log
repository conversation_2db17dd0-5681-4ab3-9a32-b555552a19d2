nohup: ignoring input
=== Parallel Nifty Option Chain Loader ===
Starting at: 2025-05-16 20:29:36.067209
Processes: 4
Batch size: 5000
Test mode: False
Creating table nifty_option_chain...
Table created successfully
Found 28 CSV files to process
[1/28] Processing file: IV_2023_apr_nifty_futures.csv

Processing IV_2023_apr_nifty_futures.csv with 4 processes...
Total lines in file: 357479
Each process will handle approximately 89369 rows
Started process 1 for rows 1-89369
Started process 2 for rows 89370-178738
Started process 3 for rows 178739-268107
[Process 4106978] Processing IV_2023_apr_nifty_futures.csv rows 178739-268107
SQL command timed out after 300 seconds
[Process 4106978] Batch 1 failed on attempt 1/3. Retrying...
SQL command timed out after 300 seconds
[Process 4106978] Batch 1 failed on attempt 2/3. Retrying...
SQL command timed out after 300 seconds
[Process 4106978] Batch 1 failed on attempt 3/3. Retrying...
[Process 4106978] Batch 1 failed after 3 attempts.
[Process 4106978] Progress: 5.59% - Inserted 0/5000 rows (Batch 1)
SQL command timed out after 300 seconds
[Process 4106978] Batch 2 failed on attempt 1/3. Retrying...
[Process 4106978] Progress: 11.19% - Inserted 5000/10000 rows (Batch 2)
[Process 4106978] Progress: 16.78% - Inserted 10000/15000 rows (Batch 3)
[Process 4106978] Progress: 22.38% - Inserted 15000/20000 rows (Batch 4)
[Process 4106978] Progress: 27.97% - Inserted 20000/25000 rows (Batch 5)
[Process 4106978] Progress: 33.57% - Inserted 25000/30000 rows (Batch 6)
[Process 4106978] Progress: 39.16% - Inserted 30000/35000 rows (Batch 7)
[Process 4106978] Progress: 44.76% - Inserted 35000/40000 rows (Batch 8)
[Process 4106978] Progress: 50.35% - Inserted 40000/45000 rows (Batch 9)
[Process 4106978] Progress: 55.95% - Inserted 45000/50000 rows (Batch 10)
[Process 4106978] Progress: 61.54% - Inserted 50000/55000 rows (Batch 11)
[Process 4106978] Progress: 67.14% - Inserted 55000/60000 rows (Batch 12)
SQL command timed out after 300 seconds
[Process 4106978] Batch 13 failed on attempt 1/3. Retrying...
SQL command timed out after 300 seconds
[Process 4106978] Batch 13 failed on attempt 2/3. Retrying...
SQL command timed out after 300 seconds
[Process 4106978] Batch 13 failed on attempt 3/3. Retrying...
[Process 4106978] Batch 13 failed after 3 attempts.
[Process 4106978] Progress: 72.73% - Inserted 55000/65000 rows (Batch 13)
SQL command timed out after 300 seconds
[Process 4106978] Batch 14 failed on attempt 1/3. Retrying...
SQL command timed out after 300 seconds
[Process 4106978] Batch 14 failed on attempt 2/3. Retrying...
[Process 4106978] Progress: 78.33% - Inserted 60000/70000 rows (Batch 14)
[Process 4106978] Progress: 83.92% - Inserted 65000/75000 rows (Batch 15)
[Process 4106978] Progress: 89.52% - Inserted 70000/80000 rows (Batch 16)
[Process 4106978] Progress: 95.11% - Inserted 75000/85000 rows (Batch 17)
[Process 4106978] Final batch: Inserted 4369 rows
[Process 4106978] Completed chunk. Inserted 79369/89369 rows.
[Process 4106976] Processing IV_2023_apr_nifty_futures.csv rows 1-89369
SQL command timed out after 300 seconds
[Process 4106976] Batch 1 failed on attempt 1/3. Retrying...
SQL command timed out after 300 seconds
[Process 4106976] Batch 1 failed on attempt 2/3. Retrying...
SQL command timed out after 300 seconds
[Process 4106976] Batch 1 failed on attempt 3/3. Retrying...
[Process 4106976] Batch 1 failed after 3 attempts.
[Process 4106976] Progress: 5.59% - Inserted 0/5000 rows (Batch 1)
SQL command timed out after 300 seconds
[Process 4106976] Batch 2 failed on attempt 1/3. Retrying...
[Process 4106976] Progress: 11.19% - Inserted 5000/10000 rows (Batch 2)
[Process 4106976] Progress: 16.78% - Inserted 10000/15000 rows (Batch 3)
[Process 4106976] Progress: 22.38% - Inserted 15000/20000 rows (Batch 4)
[Process 4106976] Progress: 27.97% - Inserted 20000/25000 rows (Batch 5)
[Process 4106976] Progress: 33.57% - Inserted 25000/30000 rows (Batch 6)
[Process 4106976] Progress: 39.16% - Inserted 30000/35000 rows (Batch 7)
[Process 4106976] Progress: 44.76% - Inserted 35000/40000 rows (Batch 8)
[Process 4106976] Progress: 50.35% - Inserted 40000/45000 rows (Batch 9)
[Process 4106976] Progress: 55.95% - Inserted 45000/50000 rows (Batch 10)
[Process 4106976] Progress: 61.54% - Inserted 50000/55000 rows (Batch 11)
[Process 4106976] Progress: 67.14% - Inserted 55000/60000 rows (Batch 12)
SQL command timed out after 300 seconds
[Process 4106976] Batch 13 failed on attempt 1/3. Retrying...
SQL command timed out after 300 seconds
[Process 4106976] Batch 13 failed on attempt 2/3. Retrying...
SQL command timed out after 300 seconds
[Process 4106976] Batch 13 failed on attempt 3/3. Retrying...
[Process 4106976] Batch 13 failed after 3 attempts.
[Process 4106976] Progress: 72.73% - Inserted 55000/65000 rows (Batch 13)
SQL command timed out after 300 seconds
[Process 4106976] Batch 14 failed on attempt 1/3. Retrying...
SQL command timed out after 300 seconds
[Process 4106976] Batch 14 failed on attempt 2/3. Retrying...
[Process 4106976] Progress: 78.33% - Inserted 60000/70000 rows (Batch 14)
[Process 4106976] Progress: 83.92% - Inserted 65000/75000 rows (Batch 15)
[Process 4106976] Progress: 89.52% - Inserted 70000/80000 rows (Batch 16)
[Process 4106976] Progress: 95.11% - Inserted 75000/85000 rows (Batch 17)
[Process 4106976] Final batch: Inserted 4369 rows
[Process 4106976] Completed chunk. Inserted 79369/89369 rows.
[Process 4106977] Processing IV_2023_apr_nifty_futures.csv rows 89370-178738
SQL command timed out after 300 seconds
[Process 4106977] Batch 1 failed on attempt 1/3. Retrying...
SQL command timed out after 300 seconds
[Process 4106977] Batch 1 failed on attempt 2/3. Retrying...
SQL command timed out after 300 seconds
[Process 4106977] Batch 1 failed on attempt 3/3. Retrying...
[Process 4106977] Batch 1 failed after 3 attempts.
[Process 4106977] Progress: 5.59% - Inserted 0/5000 rows (Batch 1)
SQL command timed out after 300 seconds
[Process 4106977] Batch 2 failed on attempt 1/3. Retrying...
[Process 4106977] Progress: 11.19% - Inserted 5000/10000 rows (Batch 2)
[Process 4106977] Progress: 16.78% - Inserted 10000/15000 rows (Batch 3)
[Process 4106977] Progress: 22.38% - Inserted 15000/20000 rows (Batch 4)
[Process 4106977] Progress: 27.97% - Inserted 20000/25000 rows (Batch 5)
[Process 4106977] Progress: 33.57% - Inserted 25000/30000 rows (Batch 6)
[Process 4106977] Progress: 39.16% - Inserted 30000/35000 rows (Batch 7)
[Process 4106977] Progress: 44.76% - Inserted 35000/40000 rows (Batch 8)
[Process 4106977] Progress: 50.35% - Inserted 40000/45000 rows (Batch 9)
[Process 4106977] Progress: 55.95% - Inserted 45000/50000 rows (Batch 10)
[Process 4106977] Progress: 61.54% - Inserted 50000/55000 rows (Batch 11)
[Process 4106977] Progress: 67.14% - Inserted 55000/60000 rows (Batch 12)
SQL command timed out after 300 seconds
[Process 4106977] Batch 13 failed on attempt 1/3. Retrying...
SQL command timed out after 300 seconds
[Process 4106977] Batch 13 failed on attempt 2/3. Retrying...
SQL command timed out after 300 seconds
[Process 4106977] Batch 13 failed on attempt 3/3. Retrying...
[Process 4106977] Batch 13 failed after 3 attempts.
[Process 4106977] Progress: 72.73% - Inserted 55000/65000 rows (Batch 13)
SQL command timed out after 300 seconds
[Process 4106977] Batch 14 failed on attempt 1/3. Retrying...
SQL command timed out after 300 seconds
[Process 4106977] Batch 14 failed on attempt 2/3. Retrying...
[Process 4106977] Progress: 78.33% - Inserted 60000/70000 rows (Batch 14)
[Process 4106977] Progress: 83.92% - Inserted 65000/75000 rows (Batch 15)
[Process 4106977] Progress: 89.52% - Inserted 70000/80000 rows (Batch 16)
[Process 4106977] Progress: 95.11% - Inserted 75000/85000 rows (Batch 17)
[Process 4106977] Final batch: Inserted 4369 rows
[Process 4106977] Completed chunk. Inserted 79369/89369 rows.
[Process 4106979] Processing IV_2023_apr_nifty_futures.csv rows 268108-357478
SQL command timed out after 300 seconds
[Process 4106979] Batch 1 failed on attempt 1/3. Retrying...
SQL command timed out after 300 seconds
[Process 4106979] Batch 1 failed on attempt 2/3. Retrying...
SQL command timed out after 300 seconds
[Process 4106979] Batch 1 failed on attempt 3/3. Retrying...
[Process 4106979] Batch 1 failed after 3 attempts.
[Process 4106979] Progress: 5.59% - Inserted 0/5000 rows (Batch 1)
SQL command timed out after 300 seconds
[Process 4106979] Batch 2 failed on attempt 1/3. Retrying...
[Process 4106979] Progress: 11.19% - Inserted 5000/10000 rows (Batch 2)
[Process 4106979] Progress: 16.78% - Inserted 10000/15000 rows (Batch 3)
[Process 4106979] Progress: 22.38% - Inserted 15000/20000 rows (Batch 4)
[Process 4106979] Progress: 27.97% - Inserted 20000/25000 rows (Batch 5)
[Process 4106979] Progress: 33.57% - Inserted 25000/30000 rows (Batch 6)
[Process 4106979] Progress: 39.16% - Inserted 30000/35000 rows (Batch 7)
[Process 4106979] Progress: 44.76% - Inserted 35000/40000 rows (Batch 8)
[Process 4106979] Progress: 50.35% - Inserted 40000/45000 rows (Batch 9)
[Process 4106979] Progress: 55.95% - Inserted 45000/50000 rows (Batch 10)
[Process 4106979] Progress: 61.54% - Inserted 50000/55000 rows (Batch 11)
[Process 4106979] Progress: 67.14% - Inserted 55000/60000 rows (Batch 12)
SQL command timed out after 300 seconds
[Process 4106979] Batch 13 failed on attempt 1/3. Retrying...
SQL command timed out after 300 seconds
[Process 4106979] Batch 13 failed on attempt 2/3. Retrying...
SQL command timed out after 300 seconds
[Process 4106979] Batch 13 failed on attempt 3/3. Retrying...
[Process 4106979] Batch 13 failed after 3 attempts.
[Process 4106979] Progress: 72.73% - Inserted 55000/65000 rows (Batch 13)
SQL command timed out after 300 seconds
[Process 4106979] Batch 14 failed on attempt 1/3. Retrying...
SQL command timed out after 300 seconds
[Process 4106979] Batch 14 failed on attempt 2/3. Retrying...
[Process 4106979] Progress: 78.33% - Inserted 60000/70000 rows (Batch 14)
[Process 4106979] Progress: 83.92% - Inserted 65000/75000 rows (Batch 15)
[Process 4106979] Progress: 89.51% - Inserted 70000/80000 rows (Batch 16)
[Process 4106979] Progress: 95.11% - Inserted 75000/85000 rows (Batch 17)
[Process 4106979] Final batch: Inserted 4371 rows
[Process 4106979] Completed chunk. Inserted 79371/89371 rows.
/usr/lib/python3.10/multiprocessing/resource_tracker.py:224: UserWarning: resource_tracker: There appear to be 3 leaked semaphore objects to clean up at shutdown
  warnings.warn('resource_tracker: There appear to be %d '
