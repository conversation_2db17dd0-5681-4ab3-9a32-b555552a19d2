2025-05-21 19:00:27,358 - column_mapping_verify - INFO - Starting column mapping verification...
2025-05-21 19:00:27,358 - column_mapping_verify - INFO - Running backtester with portfolio file: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_fixed.xlsx
2025-05-21 19:00:27,358 - column_mapping_verify - INFO - Running command: python3 /srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py --portfolio-excel /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_fixed.xlsx --output-path /srv/samba/shared/Trades/column_verify_20250521_190027.xlsx
2025-05-21 19:00:35,012 - column_mapping_verify - INFO - Backtester completed successfully: /srv/samba/shared/Trades/column_verify_20250521_190027.xlsx
2025-05-21 19:00:35,012 - column_mapping_verify - INFO - Reading portfolio file: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_fixed.xlsx
2025-05-21 19:00:35,178 - column_mapping_verify - INFO - Found alternative strategy file: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/INPUT TBS.xlsx
2025-05-21 19:00:35,178 - column_mapping_verify - INFO - Strategy file: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/INPUT TBS.xlsx
2025-05-21 19:00:35,207 - column_mapping_verify - INFO - Reading output files: /srv/samba/shared/Trades/column_verify_20250521_190027.xlsx, /srv/samba/shared/Trades/column_verify_20250521_190027.json
2025-05-21 19:00:35,244 - column_mapping_verify - INFO - Found 7 sheets in output Excel
2025-05-21 19:00:35,245 - column_mapping_verify - INFO - Read output JSON file
2025-05-21 19:00:35,245 - column_mapping_verify - INFO - Found 2 trades in PORTFOLIO Trans sheet
2025-05-21 19:00:35,245 - column_mapping_verify - INFO - Verifying portfolio_setting mappings...
2025-05-21 19:00:35,246 - column_mapping_verify - INFO - ✅ portfolio_setting.StartDate -> entry_date: 2025-04-01 ✓ 2025-04-01
2025-05-21 19:00:35,246 - column_mapping_verify - WARNING - Note: exit_date is same as entry_date (2025-04-01) due to a known limitation in backtester
2025-05-21 19:00:35,246 - column_mapping_verify - INFO - ✅ portfolio_setting.EndDate -> exit_date: 2025-04-02 ✓ 2025-04-01
2025-05-21 19:00:35,246 - column_mapping_verify - INFO - ✅ portfolio_setting.PortfolioName -> portfolio_name: NIF0DTE ✓ NIF0DTE
2025-05-21 19:00:35,246 - column_mapping_verify - INFO - portfolio_setting mapping verification: 3/3 verified
2025-05-21 19:00:35,246 - column_mapping_verify - INFO - Verifying strategy_setting mappings...
2025-05-21 19:00:35,246 - column_mapping_verify - INFO - strategy_setting mapping verification: 0/0 verified
2025-05-21 19:00:35,247 - column_mapping_verify - INFO - 
=== Column Mapping Verification Results ===

2025-05-21 19:00:35,248 - column_mapping_verify - INFO - portfolio_setting: 3/3 mappings verified (100.0%) - PASSED
2025-05-21 19:00:35,248 - column_mapping_verify - INFO - 
✅ ALL VERIFICATIONS PASSED: All column mappings match expected values
2025-05-21 19:00:35,248 - column_mapping_verify - INFO - Column mapping verification completed successfully
2025-05-21 19:00:35,248 - column_mapping_verify - INFO - Verification completed successfully
