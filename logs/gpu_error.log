STDOUT:
CONFIG.PY: Attempting to load config.py...
CONFIG.PY: Finished loading config.py.


STDERR:
2025-05-30 21:37:21,910 - bt.backtester_stable.BTRUN.core.config - WARNING - Partially restored config.py is being imported - Phase 10.
2025-05-30 21:37:21,910 - bt.backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - get_table_name active!
2025-05-30 21:37:21,910 - bt.backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - Stats constants active!
2025-05-30 21:37:21,910 - bt.backtester_stable.BTRUN.core.config - WARNING - USING PARTIALLY RESTORED DEBUG CONFIG.PY - METRICS_KEY_NAME active!
2025-05-30 21:37:23,247 - numba.cuda.cudadrv.driver - INFO - init
2025-05-30 21:37:23,547 - bt.backtester_stable.BTRUN.core.gpu_helpers - INFO - cuDF is available - GPU acceleration enabled
2025-05-30 21:37:23,795 - bt.backtester_stable.BTRUN.strategies.tv_processor - INFO - Using DirectDAL for database access
2025-05-30 21:37:23,815 - root - ERROR - Failed to import BTRUN config module
usage: BT_TV_GPU_enhanced.py [-h] [--tv-file TV_FILE]
                             [--output-dir OUTPUT_DIR] [--strict] [--debug]
BT_TV_GPU_enhanced.py: error: unrecognized arguments: --tv-excel /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tv.xlsx --output-path /srv/samba/shared/Trades/tv_gpu_test_20250530_213721.xlsx --workers auto --batch-days 7
