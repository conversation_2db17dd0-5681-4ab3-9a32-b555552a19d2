#!/usr/bin/env python3
import os
import sys
import glob
import logging
import pandas as pd
from heavydb import connect

# Configure logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_heavydb_connection():
    """Establishes a connection to the HeavyDB database"""
    try:
        conn = connect(
            host='127.0.0.1',
            port=6274,
            user='admin',
            password='HyperInteractive',
            dbname='heavyai'
        )
        logger.info("Successfully connected to HeavyDB")
        return conn
    except Exception as e:
        logger.error(f"Failed to connect to HeavyDB: {str(e)}")
        return None

def create_nifty_option_chain_table(conn):
    """Creates the nifty_option_chain table"""
    try:
        cursor = conn.cursor()
        
        # Drop existing table if any
        drop_table_sql = "DROP TABLE IF EXISTS nifty_option_chain;"
        cursor.execute(drop_table_sql)
        conn.commit()
        logger.info("Dropped existing table if present")

        # Create new table
        create_table_sql = """CREATE TABLE nifty_option_chain (
            trade_date       DATE ENCODING DAYS(32),
            trade_time       TIME,
            expiry_date      DATE ENCODING DAYS(32),
            index_name       TEXT ENCODING DICT(32),
            spot             DOUBLE,
            atm_strike       DOUBLE,
            strike           DOUBLE,
            dte              INT,
            expiry_bucket    TEXT ENCODING DICT(32),
            zone_id          SMALLINT,
            zone_name        TEXT ENCODING DICT(32),
            call_strike_type TEXT ENCODING DICT(32),
            put_strike_type  TEXT ENCODING DICT(32),

            -- Call metrics
            ce_symbol TEXT ENCODING DICT(32),
            ce_open   DOUBLE,
            ce_high   DOUBLE,
            ce_low    DOUBLE,
            ce_close  DOUBLE,
            ce_volume BIGINT,
            ce_oi     BIGINT,
            ce_coi    BIGINT,
            ce_iv     DOUBLE,
            ce_delta  DOUBLE,
            ce_gamma  DOUBLE,
            ce_theta  DOUBLE,
            ce_vega   DOUBLE,
            ce_rho    DOUBLE,

            -- Put metrics
            pe_symbol TEXT ENCODING DICT(32),
            pe_open   DOUBLE,
            pe_high   DOUBLE,
            pe_low    DOUBLE,
            pe_close  DOUBLE,
            pe_volume BIGINT,
            pe_oi     BIGINT,
            pe_coi    BIGINT,
            pe_iv     DOUBLE,
            pe_delta  DOUBLE,
            pe_gamma  DOUBLE,
            pe_theta  DOUBLE,
            pe_vega   DOUBLE,
            pe_rho    DOUBLE,

            -- Futures join
            future_open   DOUBLE,
            future_high   DOUBLE,
            future_low    DOUBLE,
            future_close  DOUBLE,
            future_volume BIGINT,
            future_oi     BIGINT,
            future_coi    BIGINT
        )
        WITH (fragment_size = 32000000);"""
        
        cursor.execute(create_table_sql)
        conn.commit()
        logger.info("Successfully created nifty_option_chain table")
        return True
    except Exception as e:
        logger.error(f"Failed to create table: {str(e)}")
        return False

def verify_table(conn):
    """Verifies that the table exists and has the correct structure"""
    try:
        cursor = conn.cursor()
        # Try to select 0 rows to verify table exists and structure
        verify_sql = "SELECT * FROM nifty_option_chain LIMIT 0;"
        cursor.execute(verify_sql)
        logger.info("Table verification successful")
        return True
    except Exception as e:
        logger.error(f"Table verification failed: {str(e)}")
        return False

def list_csv_files(directory):
    """List all CSV files in the specified directory"""
    return glob.glob(os.path.join(directory, "*.csv"))

def load_data_from_csv(conn, csv_file):
    """Loads data from a CSV file into the table using COPY FROM"""
    try:
        cursor = conn.cursor()
        
        # Use COPY FROM command for efficient bulk loading
        copy_sql = f"""
        COPY nifty_option_chain FROM '{csv_file}'
        WITH (header='true', delimiter=',');
        """
        
        cursor.execute(copy_sql)
        conn.commit()
        logger.info(f"Successfully loaded data from {os.path.basename(csv_file)}")
        return True
    except Exception as e:
        logger.error(f"Failed to load data from {os.path.basename(csv_file)}: {str(e)}")
        return False

def main():
    # Connect to HeavyDB
    conn = get_heavydb_connection()
    if not conn:
        sys.exit(1)

    # Create the table
    if not create_nifty_option_chain_table(conn):
        conn.close()
        sys.exit(1)

    # Verify table creation
    if not verify_table(conn):
        conn.close()
        sys.exit(1)

    # List CSV files in the data directory
    data_dir = "/srv/samba/shared/market_data/nifty/oc_with_futures"
    csv_files = list_csv_files(data_dir)
    
    if not csv_files:
        logger.error(f"No CSV files found in {data_dir}")
        conn.close()
        sys.exit(1)

    # Sort files to process them in order
    csv_files.sort()
    
    # Load data from each CSV file
    success_count = 0
    total_files = len(csv_files)
    
    for i, csv_file in enumerate(csv_files, 1):
        logger.info(f"Processing file {i}/{total_files}: {os.path.basename(csv_file)}")
        if load_data_from_csv(conn, csv_file):
            success_count += 1

    logger.info(f"Data loading completed. Successfully loaded {success_count} out of {total_files} files.")
    conn.close()

if __name__ == "__main__":
    main() 