#!/usr/bin/env python3
import os
import sys
import logging
import pandas as pd
import subprocess

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("nifty_chain_create.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# HeavyDB connection parameters
HEAVYDB_HOST = "127.0.0.1"
HEAVYDB_PORT = "6274"
HEAVYDB_USER = "admin"
HEAVYDB_PASSWORD = "HyperInteractive"
HEAVYDB_DB = "heavyai"

def run_sql_command(sql_command):
    """Run a SQL command using the HeavyDB command-line interface"""
    logger.info(f"Executing SQL command: {sql_command[:100]}...")
    
    # Create a temporary SQL file
    with open("temp_command.sql", "w") as f:
        f.write(sql_command)
    
    # Execute the command using heavysql
    cmd = f"/opt/heavyai/bin/heavysql -s {HEAVYDB_HOST} --port {HEAVYDB_PORT} -u {HEAVYDB_USER} -p {HEAVYDB_PASSWORD} -d {HEAVYDB_DB} < temp_command.sql"
    
    try:
        result = subprocess.run(cmd, shell=True, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        logger.info(f"Command executed successfully: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed with error: {e.stderr}")
        return False
    finally:
        # Clean up the temporary file
        if os.path.exists("temp_command.sql"):
            os.remove("temp_command.sql")

def create_nifty_option_chain_table():
    """Create the nifty_option_chain table"""
    logger.info("Creating nifty_option_chain table...")
    
    create_table_sql = """
    DROP TABLE IF EXISTS nifty_option_chain;

    CREATE TABLE nifty_option_chain (
        trade_date       DATE ENCODING DAYS(32),
        trade_time       TIME,
        expiry_date      DATE ENCODING DAYS(32),
        index_name       TEXT ENCODING DICT(32),
        spot             DOUBLE,
        atm_strike       DOUBLE,
        strike           DOUBLE,
        dte              INT,
        expiry_bucket    TEXT ENCODING DICT(32),
        zone_id          SMALLINT,
        zone_name        TEXT ENCODING DICT(32),
        call_strike_type TEXT ENCODING DICT(32),
        put_strike_type  TEXT ENCODING DICT(32),

        -- Call metrics
        ce_symbol TEXT ENCODING DICT(32),
        ce_open   DOUBLE,
        ce_high   DOUBLE,
        ce_low    DOUBLE,
        ce_close  DOUBLE,
        ce_volume BIGINT,
        ce_oi     BIGINT,
        ce_coi    BIGINT,
        ce_iv     DOUBLE,
        ce_delta  DOUBLE,
        ce_gamma  DOUBLE,
        ce_theta  DOUBLE,
        ce_vega   DOUBLE,
        ce_rho    DOUBLE,

        -- Put metrics
        pe_symbol TEXT ENCODING DICT(32),
        pe_open   DOUBLE,
        pe_high   DOUBLE,
        pe_low    DOUBLE,
        pe_close  DOUBLE,
        pe_volume BIGINT,
        pe_oi     BIGINT,
        pe_coi    BIGINT,
        pe_iv     DOUBLE,
        pe_delta  DOUBLE,
        pe_gamma  DOUBLE,
        pe_theta  DOUBLE,
        pe_vega   DOUBLE,
        pe_rho    DOUBLE,

        -- Futures join
        future_open   DOUBLE,
        future_high   DOUBLE,
        future_low    DOUBLE,
        future_close  DOUBLE,
        future_volume BIGINT,
        future_oi     BIGINT,
        future_coi    BIGINT
    )
    WITH (fragment_size = 32000000);
    """
    
    return run_sql_command(create_table_sql)

def insert_sample_data():
    """Insert sample data into the nifty_option_chain table"""
    logger.info("Inserting sample data...")
    
    insert_sql = """
    INSERT INTO nifty_option_chain VALUES 
    ('2023-01-02', '09:15:00', '2023-01-05', 'NIFTY', 18168.0, 18150, 18200, 1, 'CW', 1, 'OPEN', 'ATM', 'ATM', 'NIFTY05JAN2318200CE', 45.0, 46.0, 44.0, 45.5, 100000, 1200000, 0, 12.0, 0.75, 0.001, -15.0, 3.0, 70.0, 'NIFTY05JAN2318200PE', 80.0, 82.0, 78.0, 80.5, 90000, 900000, 0, 14.0, -0.25, 0.001, -14.0, 3.0, -15.0, 18218.0, 18250.0, 18100.0, 18200.0, 5000, 10000, -200);

    INSERT INTO nifty_option_chain VALUES 
    ('2023-01-02', '10:00:00', '2023-01-12', 'NIFTY', 18190.0, 18200, 18100, 8, 'NW', 2, 'MID_MORN', 'ITM1', 'OTM1', 'NIFTY12JAN2318100CE', 150.0, 155.0, 148.0, 152.0, 120000, 1500000, 0, 11.0, 0.80, 0.001, -14.0, 2.8, 72.0, 'NIFTY12JAN2318100PE', 60.0, 62.0, 58.0, 59.0, 85000, 850000, 0, 15.0, -0.20, 0.001, -13.0, 2.8, -14.0, 18240.0, 18290.0, 18150.0, 18215.0, 5020, 10100, -199);

    INSERT INTO nifty_option_chain VALUES 
    ('2023-01-02', '15:00:00', '2023-01-25', 'NIFTY', 18210.0, 18200, 18300, 15, 'CM', 5, 'CLOSE', 'OTM1', 'ITM1', 'NIFTY25JAN2318300CE', 120.0, 125.0, 118.0, 122.0, 80000, 900000, 0, 10.0, 0.70, 0.001, -12.0, 4.0, 100.0, 'NIFTY25JAN2318300PE', 100.0, 105.0, 98.0, 102.0, 75000, 750000, 0, 13.0, -0.30, 0.001, -11.0, 4.0, -20.0, 18260.0, 18310.0, 18180.0, 18235.0, 5040, 10200, -198);
    """
    
    return run_sql_command(insert_sql)

def verify_data():
    """Verify that the data was inserted correctly"""
    logger.info("Verifying data...")
    
    verify_sql = "SELECT COUNT(*) FROM nifty_option_chain;"
    
    return run_sql_command(verify_sql)

def main():
    """Main function"""
    logger.info("Starting nifty_option_chain table creation and population...")
    
    # Step 1: Create the table
    if not create_nifty_option_chain_table():
        logger.error("Failed to create table. Exiting.")
        return False
    
    # Step 2: Insert sample data
    if not insert_sample_data():
        logger.error("Failed to insert sample data. Exiting.")
        return False
    
    # Step 3: Verify the data
    if not verify_data():
        logger.error("Failed to verify data. Exiting.")
        return False
    
    logger.info("Successfully created and populated nifty_option_chain table!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 