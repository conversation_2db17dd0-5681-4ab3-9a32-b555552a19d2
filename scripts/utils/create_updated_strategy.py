import pandas as pd
import shutil
import os
from datetime import datetime

# Paths
original_file = "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx"
updated_file = "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_fixed_exits.xlsx"

# First, make a copy of the original file
shutil.copy2(original_file, updated_file)

# Now read and modify the necessary sheets
with pd.ExcelWriter(updated_file, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
    # 1. Update LegParameter sheet to increase SL/TP values so they don't trigger immediately
    leg_params = pd.read_excel(original_file, sheet_name="LegParameter")
    
    # For each leg, update the SL/TP values
    for idx, row in leg_params.iterrows():
        # For legs 1 & 2 (call & put sell), set reasonable stop loss
        if row['Transaction'].lower() == 'sell':
            # High SL value that won't trigger immediately, but provides protection
            leg_params.at[idx, 'SLValue'] = 500  # 500% instead of 100%
            leg_params.at[idx, 'TGTValue'] = 100  # 100% instead of 0%
        # For legs 3 & 4 (call & put buy), set reasonable SL/TP
        else:  # 'buy'
            # High SL value that won't trigger immediately
            leg_params.at[idx, 'SLValue'] = 50   # 50% instead of 0%
            leg_params.at[idx, 'TGTValue'] = 100  # 100% instead of 0%
    
    # Write the updated LegParameter sheet
    leg_params.to_excel(writer, sheet_name="LegParameter", index=False)
    
    # 2. Read and keep GeneralParameter sheet unchanged
    general_params = pd.read_excel(original_file, sheet_name="GeneralParameter")
    
    # Print existing parameters for verification
    print("Current parameters:")
    print(f"StartTime: {general_params['StartTime'].iloc[0]}")
    print(f"LastEntryTime: {general_params['LastEntryTime'].iloc[0]}")
    print(f"EndTime: {general_params['EndTime'].iloc[0]}")
    
    # Note: We're keeping these unchanged as they define the entry window and exit time (120000 = 12:00:00)
    # Update only if change is desired:
    # general_params.at[0, 'EndTime'] = 151500  # Set to 15:15:00 or another desired time
    
    # Write the unchanged GeneralParameter sheet
    general_params.to_excel(writer, sheet_name="GeneralParameter", index=False)

# Output success message
print(f"\nUpdated strategy file created: {updated_file}")
print("Changes made:")
print("1. Sell legs (1 & 2): SL increased to 500%, TP set to 100%")
print("2. Buy legs (3 & 4): SL increased to 50%, TP set to 100%")
print("\nThese changes will prevent SL/TP from triggering immediately,")
print(f"allowing trades to run until the specified exit time: {general_params['EndTime'].iloc[0]} (12:00:00)")
print("\nTo use this strategy, run:")
print(f"python3 bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py --portfolio-excel /srv/samba/shared/input_sheets/input_portfolio.xlsx --output-path /srv/samba/shared/Trades/Updated_Strategy_Output.xlsx") 