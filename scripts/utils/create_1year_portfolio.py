#!/usr/bin/env python3
"""
Create a 1-year portfolio file for 2024 backtest
"""

import pandas as pd
import os
from datetime import datetime

# Read the existing portfolio file
input_file = "/srv/samba/shared/input_portfolio_fixed.xlsx"
output_file = "/srv/samba/shared/input_portfolio_2024_1year.xlsx"

print(f"Creating 1-year portfolio file for 2024...")
print(f"Reading from: {input_file}")

# Read all sheets
with pd.ExcelFile(input_file) as xls:
    sheets = {}
    for sheet_name in xls.sheet_names:
        sheets[sheet_name] = pd.read_excel(xls, sheet_name=sheet_name)
        print(f"  Read sheet: {sheet_name} ({sheets[sheet_name].shape})")

# Update PortfolioSetting dates
if 'PortfolioSetting' in sheets:
    df = sheets['PortfolioSetting']
    
    # Update StartDate to Jan 1, 2024
    if 'StartDate' in df.columns:
        df['StartDate'] = pd.to_datetime('2024-01-01')
        print(f"  Updated StartDate to: 2024-01-01")
    
    # Find the end date column (it might have various names)
    end_date_cols = [col for col in df.columns if '2024' in str(col) or '2025' in str(col)]
    if end_date_cols:
        # Rename the first date column found to represent end date
        old_col = end_date_cols[0]
        df = df.rename(columns={old_col: '31_12_2024'})
        df['31_12_2024'] = pd.to_datetime('2024-12-31')
        print(f"  Updated EndDate to: 2024-12-31")
        sheets['PortfolioSetting'] = df

# Write to new file
with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
    for sheet_name, df in sheets.items():
        df.to_excel(writer, sheet_name=sheet_name, index=False)
        print(f"  Wrote sheet: {sheet_name}")

print(f"\n✅ Created: {output_file}")

# Verify the dates
df = pd.read_excel(output_file, sheet_name='PortfolioSetting')
print(f"\nVerification:")
print(f"  StartDate: {df['StartDate'].iloc[0]}")
if '31_12_2024' in df.columns:
    print(f"  EndDate: {df['31_12_2024'].iloc[0]}")
print(f"  Portfolio: {df['PortfolioName'].iloc[0] if 'PortfolioName' in df.columns else 'Unknown'}")