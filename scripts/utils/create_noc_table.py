import heavyai

# Try to connect to HeavyDB
try:
    conn = heavyai.connect(
        host="127.0.0.1",
        port=6274,
        user="admin",
        password="HyperInteractive",
        dbname="heavyai"
    )
    print("Connected to HeavyDB!")
    
    # Drop table if exists
    try:
        conn.execute("DROP TABLE IF EXISTS nifty_option_chain;")
        print("Dropped table if it existed")
    except Exception as e:
        print(f"Error dropping table: {e}")
    
    # Create table
    create_table_sql = """
    CREATE TABLE nifty_option_chain (
        trade_date       DATE,
        trade_time       TIME,
        expiry_date      DATE,
        index_name       TEXT,
        spot             DOUBLE,
        atm_strike       DOUBLE,
        strike           DOUBLE,
        dte              INT,
        expiry_bucket    TEXT,
        zone_id          SMALLINT,
        zone_name        TEXT,
        call_strike_type TEXT,
        put_strike_type  TEXT,
        ce_symbol        TEXT,
        ce_open          DOUBLE,
        ce_high          DOUBLE,
        ce_low           DOUBLE,
        ce_close         DOUBLE,
        ce_volume        BIGINT,
        ce_oi            BIGINT,
        ce_coi           BIGINT,
        ce_iv            DOUBLE,
        ce_delta         DOUBLE,
        ce_gamma         DOUBLE,
        ce_theta         DOUBLE,
        ce_vega          DOUBLE,
        ce_rho           DOUBLE,
        pe_symbol        TEXT,
        pe_open          DOUBLE,
        pe_high          DOUBLE,
        pe_low           DOUBLE,
        pe_close         DOUBLE,
        pe_volume        BIGINT,
        pe_oi            BIGINT,
        pe_coi           BIGINT,
        pe_iv            DOUBLE,
        pe_delta         DOUBLE,
        pe_gamma         DOUBLE,
        pe_theta         DOUBLE,
        pe_vega          DOUBLE,
        pe_rho           DOUBLE,
        future_open      DOUBLE,
        future_high      DOUBLE,
        future_low       DOUBLE,
        future_close     DOUBLE,
        future_volume    BIGINT,
        future_oi        BIGINT,
        future_coi       BIGINT
    );
    """
    
    try:
        conn.execute(create_table_sql)
        print("Successfully created table nifty_option_chain")
    except Exception as e:
        print(f"Error creating table: {e}")
    
    # Verify table was created
    try:
        tables = conn.execute("SHOW TABLES;").fetchall()
        print("Available tables:")
        for table in tables:
            print(table[0])
        
        columns = conn.execute("DESCRIBE nifty_option_chain;").fetchall()
        print("\nSchema for nifty_option_chain:")
        for col in columns:
            print(f"{col[0]} - {col[1]}")
        
        count = conn.execute("SELECT COUNT(*) FROM nifty_option_chain;").fetchone()[0]
        print(f"\nTable has {count} rows")
    except Exception as e:
        print(f"Error verifying table: {e}")
    
    conn.close()
except Exception as e:
    print(f"Connection error: {e}") 