#!/usr/bin/env python3
"""
Create exact template Excel files for TBS and TV backtesting systems
matching the actual INPUT TBS.xlsx and INPUT TV.xlsx structures
"""

import pandas as pd
import numpy as np
from datetime import datetime

def create_tbs_exact_template():
    """Create exact TBS template matching INPUT TBS.xlsx structure"""
    
    # GeneralParameter sheet - exact columns from INPUT TBS.xlsx
    general_params = {
        'StrategyName': ['ATM_STRADDLE_EXAMPLE', 'IRON_CONDOR_EXAMPLE'],
        'MoveSlToCost': ['NO', 'YES'],
        'Underlying': ['NIFTY', 'BANKNIFTY'],
        'Index': ['NIFTY', 'BANKNIFTY'],
        'Weekdays': ['1,2,3,4,5', '1,2,3,4'],  # Mon-Fri, Mon-Thu
        'DTE': ['0', '1'],  # 0 = same day expiry, 1 = next day
        'StrikeSelectionTime': ['91600', '92000'],  # 9:16:00, 9:20:00
        'StartTime': ['91600', '92000'],
        'LastEntryTime': ['120000', '143000'],  # 12:00:00, 14:30:00
        'EndTime': ['153000', '152000'],  # 15:30:00, 15:20:00
        'StrategyProfit': ['0', '5000'],  # 0 = no limit, 5000 = exit at 5k profit
        'StrategyLoss': ['0', '-3000'],  # 0 = no limit, -3000 = exit at 3k loss
        'StrategyProfitReExecuteNo': ['0', '0'],
        'StrategyLossReExecuteNo': ['0', '0'],
        'StrategyTrailingType': ['NONE', 'TRAIL_PROFIT'],
        'PnLCalTime': ['0', '0'],
        'LockPercent': ['0', '70'],  # Lock 70% profit
        'TrailPercent': ['0', '30'],  # Trail by 30%
        'SqOff1Time': ['0', '143000'],  # Square off 50% at 14:30
        'SqOff1Percent': ['0', '50'],
        'SqOff2Time': ['0', '0'],
        'SqOff2Percent': ['0', '0'],
        'ProfitReaches': ['0', '2000'],  # Activate trailing when profit reaches 2k
        'LockMinProfitAt': ['0', '1500'],
        'IncreaseInProfit': ['0', '500'],
        'TrailMinProfitBy': ['0', '200'],
        'TgtTrackingFrom': ['ENTRY', 'ENTRY'],
        'TgtRegisterPriceFrom': ['ENTRY', 'ENTRY'],
        'SlTrackingFrom': ['ENTRY', 'ENTRY'],
        'SlRegisterPriceFrom': ['ENTRY', 'ENTRY'],
        'PnLCalculationFrom': ['ENTRY', 'STRATEGY'],
        'ConsiderHedgePnLForStgyPnL': ['NO', 'YES'],
        'StoplossCheckingInterval': ['30', '60'],  # Check every 30/60 seconds
        'TargetCheckingInterval': ['30', '60'],
        'ReEntryCheckingInterval': ['30', '300'],  # Re-entry check every 5 min
        'OnExpiryDayTradeNextExpiry': ['NO', 'YES']
    }
    
    # LegParameter sheet - exact columns with multiple leg examples
    leg_params = {
        'StrategyName': [
            'ATM_STRADDLE_EXAMPLE', 'ATM_STRADDLE_EXAMPLE',  # 2 legs for straddle
            'IRON_CONDOR_EXAMPLE', 'IRON_CONDOR_EXAMPLE', 'IRON_CONDOR_EXAMPLE', 'IRON_CONDOR_EXAMPLE'  # 4 legs for IC
        ],
        'LegID': ['1', '2', '1', '2', '3', '4'],
        'Instrument': ['OPTIONS', 'OPTIONS', 'OPTIONS', 'OPTIONS', 'OPTIONS', 'OPTIONS'],
        'Transaction': ['SELL', 'SELL', 'SELL', 'SELL', 'BUY', 'BUY'],
        'Expiry': ['CW', 'CW', 'CW', 'CW', 'CW', 'CW'],  # Current Week
        'W&Type': ['NONE', 'NONE', 'NONE', 'NONE', 'NONE', 'NONE'],
        'W&TValue': ['0', '0', '0', '0', '0', '0'],
        'TrailW&T': ['0', '0', '0', '0', '0', '0'],
        'StrikeMethod': ['ATM', 'ATM', 'OTM1', 'OTM1', 'OTM2', 'OTM2'],
        'MatchPremium': ['0', '0', '0', '0', '0', '0'],
        'StrikeValue': ['0', '0', '1', '1', '2', '2'],  # 0=ATM, 1=1 strike OTM, 2=2 strikes OTM
        'StrikePremiumCondition': ['NONE', 'NONE', 'NONE', 'NONE', 'NONE', 'NONE'],
        'SLType': ['PERCENT', 'PERCENT', 'PERCENT', 'PERCENT', 'NONE', 'NONE'],
        'SLValue': ['100', '100', '50', '50', '0', '0'],  # 100% SL, 50% SL
        'TGTType': ['PERCENT', 'PERCENT', 'PERCENT', 'PERCENT', 'NONE', 'NONE'],
        'TGTValue': ['50', '50', '30', '30', '0', '0'],  # 50% target, 30% target
        'TrailSLType': ['NONE', 'NONE', 'LOCK_PROFIT', 'LOCK_PROFIT', 'NONE', 'NONE'],
        'SL_TrailAt': ['0', '0', '20', '20', '0', '0'],  # Trail when 20% profit
        'SL_TrailBy': ['0', '0', '10', '10', '0', '0'],  # Trail by 10%
        'Lots': ['2', '2', '1', '1', '1', '1'],
        'ReEntryType': ['NONE', 'NONE', 'IMMEDIATE', 'IMMEDIATE', 'NONE', 'NONE'],
        'ReEnteriesCount': ['0', '0', '2', '2', '0', '0'],  # Allow 2 re-entries
        'OnEntry_OpenTradeOn': ['NO', 'NO', 'NO', 'NO', 'NO', 'NO'],
        'OnEntry_SqOffTradeOff': ['NO', 'NO', 'NO', 'NO', 'NO', 'NO'],
        'OnEntry_SqOffAllLegs': ['NO', 'NO', 'NO', 'NO', 'NO', 'NO'],
        'OnEntry_OpenTradeDelay': ['0', '0', '0', '0', '0', '0'],
        'OnEntry_SqOffDelay': ['0', '0', '0', '0', '0', '0'],
        'OnExit_OpenTradeOn': ['NO', 'NO', 'NO', 'NO', 'NO', 'NO'],
        'OnExit_SqOffTradeOff': ['NO', 'NO', 'NO', 'NO', 'NO', 'NO'],
        'OnExit_SqOffAllLegs': ['NO', 'NO', 'YES', 'YES', 'YES', 'YES'],  # Exit all legs together
        'OnExit_OpenAllLegs': ['NO', 'NO', 'NO', 'NO', 'NO', 'NO'],
        'OnExit_OpenTradeDelay': ['0', '0', '0', '0', '0', '0'],
        'OnExit_SqOffDelay': ['0', '0', '0', '0', '0', '0']
    }
    
    # Add database exit timing columns (these would be added for enhanced functionality)
    general_params['UseDbExitTiming'] = ['NO', 'YES']
    general_params['ExitSearchInterval'] = ['5', '10']
    general_params['ExitPriceSource'] = ['SPOT', 'FUTURE']
    
    # Create Excel file
    output_file = '/srv/samba/shared/TBS_EXACT_TEMPLATE.xlsx'
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        pd.DataFrame(general_params).to_excel(writer, sheet_name='GeneralParameter', index=False)
        pd.DataFrame(leg_params).to_excel(writer, sheet_name='LegParameter', index=False)
        
        # Add reference sheet
        reference_data = {
            'Sheet': ['GeneralParameter', 'LegParameter'],
            'Purpose': [
                'Strategy-level settings including timing, P&L limits, and database exit timing',
                'Individual leg configuration for options/futures positions'
            ],
            'Key Fields': [
                'StrategyName, Underlying, DTE, Start/End times, SL/TP settings, UseDbExitTiming',
                'LegID, Transaction (BUY/SELL), StrikeMethod, SL/TGT settings, Lots'
            ]
        }
        pd.DataFrame(reference_data).to_excel(writer, sheet_name='Reference', index=False)
    
    print(f"✅ Created exact TBS template: {output_file}")
    return output_file

def create_tv_exact_template():
    """Create exact TV template matching INPUT TV.xlsx structure"""
    
    # Setting sheet - exact columns from INPUT TV.xlsx with all fields
    tv_settings = {
        'StartDate': ['01_04_2024', '01_04_2024', '01_04_2024'],
        'EndDate': ['30_04_2024', '30_04_2024', '30_04_2024'],
        'SignalDateFormat': ['%d-%m-%Y %H:%M', '%Y%m%d %H%M%S', '%d/%m/%Y %H:%M:%S'],
        'Enabled': ['yes', 'yes', 'no'],
        'TvExitApplicable': ['yes', 'yes', 'no'],
        'ManualTradeEntryTime': ['91600', '92000', '0'],
        'ManualTradeLots': ['2', '1', '0'],
        'IncreaseEntrySignalTimeBy': ['0', '30', '0'],  # Add 30 seconds delay
        'IncreaseExitSignalTimeBy': ['0', '30', '0'],
        'IntradaySqOffApplicable': ['yes', 'yes', 'no'],
        'FirstTradeEntryTime': ['91600', '0', '0'],
        'IntradayExitTime': ['151500', '152000', '0'],
        'ExpiryDayExitTime': ['151500', '150000', '0'],
        'DoRollover': ['no', 'yes', 'no'],
        'RolloverTime': ['0', '151500', '0'],
        'Name': ['NIFTY_TV_STRATEGY', 'BANKNIFTY_TV_STRATEGY', 'DISABLED_EXAMPLE'],
        'SignalFilePath': [
            './signals/nifty_signals.xlsx',
            './signals/banknifty_signals.xlsx',
            './signals/test_signals.xlsx'
        ],
        'LongPortfolioFilePath': [
            './portfolios/nifty_long_portfolio.xlsx',
            './portfolios/banknifty_long_portfolio.xlsx',
            './portfolios/test_long.xlsx'
        ],
        'ShortPortfolioFilePath': [
            './portfolios/nifty_short_portfolio.xlsx',
            './portfolios/banknifty_short_portfolio.xlsx',
            './portfolios/test_short.xlsx'
        ],
        'ManualPortfolioFilePath': [
            './portfolios/nifty_manual_portfolio.xlsx',
            './portfolios/banknifty_manual_portfolio.xlsx',
            './portfolios/test_manual.xlsx'
        ],
        'UseDbExitTiming': ['YES', 'NO', 'NO'],  # Database exit timing
        'ExitSearchInterval': ['5', '10', '5'],
        'ExitPriceSource': ['SPOT', 'FUTURE', 'SPOT']
    }
    
    # Create sample signals sheet
    signals_data = {
        'Trade #': [1, 1, 2, 2, 3, 3, 4, 4, 5],
        'Type': [
            'Entry Long', 'Exit Long',
            'Entry Short', 'Exit Short',
            'Entry Long', 'Exit Long',
            'Entry Short', 'Exit Short',
            'Manual Entry'
        ],
        'Date/Time': [
            '03-04-2024 09:20', '03-04-2024 11:30',
            '03-04-2024 12:00', '03-04-2024 14:45',
            '04-04-2024 09:30', '04-04-2024 12:00',
            '04-04-2024 13:00', '04-04-2024 15:00',
            '05-04-2024 09:16'
        ],
        'Contracts': [2, 2, 3, 3, 2, 2, 1, 1, 4]
    }
    
    # Create Excel file
    output_file = '/srv/samba/shared/TV_EXACT_TEMPLATE.xlsx'
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        pd.DataFrame(tv_settings).to_excel(writer, sheet_name='Setting', index=False)
        pd.DataFrame(signals_data).to_excel(writer, sheet_name='ExampleSignals', index=False)
        
        # Add reference sheet
        reference_data = {
            'Parameter': [
                'Enabled', 'TvExitApplicable', 'UseDbExitTiming', 'SignalDateFormat',
                'Signal Types', 'Portfolio Mapping', 'Time Adjustments'
            ],
            'Description': [
                'Must be "yes" to process this row',
                'Use TV exit signals vs forced intraday exit',
                'Enable database price-based exit timing',
                'Format string matching signal file timestamps',
                'Entry Long/Short, Exit Long/Short, Manual Entry/Exit',
                'Long/Short/Manual portfolio files for different signal types',
                'FirstTradeEntryTime overrides first signal, IncreaseBy adds delay'
            ],
            'Example': [
                'yes/no',
                'yes = use TV exits, no = use IntradayExitTime',
                'YES = search DB for precise exit, NO = use signal time',
                '%d-%m-%Y %H:%M or %Y%m%d %H%M%S',
                'Entry Long → LongPortfolioFilePath',
                'Entry Short → ShortPortfolioFilePath',
                'IncreaseEntrySignalTimeBy=30 adds 30 sec delay'
            ]
        }
        pd.DataFrame(reference_data).to_excel(writer, sheet_name='Reference', index=False)
    
    print(f"✅ Created exact TV template: {output_file}")
    return output_file

def show_yaml_mapping_info():
    """Show how Excel maps to YAML"""
    print("\n📋 EXCEL TO YAML MAPPING:")
    print("="*70)
    print("\nBoth TBS and TV Excel files are converted to YAML for processing:")
    print("\n1. TBS Excel → YAML Structure:")
    print("   - GeneralParameter sheet → general_parameters section")
    print("   - LegParameter sheet → leg_parameters array")
    print("   - Each row in GeneralParameter = one strategy")
    print("   - Related legs grouped by StrategyName")
    print("\n2. TV Excel → YAML Structure:")
    print("   - Setting sheet → tv_settings configuration")
    print("   - Each enabled row = one TV strategy configuration")
    print("   - References portfolio Excel files for actual strategy params")
    print("\n3. Pipeline Location:")
    print("   /srv/samba/shared/excel_to_yaml_pipeline/")
    print("   - Validates Excel structure")
    print("   - Converts to YAML format")
    print("   - Loads into backtester models")

def main():
    """Create exact template files"""
    print("Creating Exact Template Files for TBS and TV Backtesting")
    print("="*70)
    
    # Create TBS template
    print("\n1. Creating TBS Exact Template...")
    tbs_file = create_tbs_exact_template()
    
    # Create TV template  
    print("\n2. Creating TV Exact Template...")
    tv_file = create_tv_exact_template()
    
    # Show YAML mapping info
    show_yaml_mapping_info()
    
    print("\n✅ Templates created successfully!")
    print(f"\nTBS Template: {tbs_file}")
    print("  - Sheets: GeneralParameter, LegParameter")
    print("  - Includes database exit timing columns")
    print("  - Two example strategies: ATM Straddle, Iron Condor")
    
    print(f"\nTV Template: {tv_file}")
    print("  - Sheets: Setting, ExampleSignals")
    print("  - All 23 columns including database exit timing")
    print("  - Three example configurations")
    
    print("\nThese templates match the exact structure of:")
    print("  - INPUT TBS.xlsx")
    print("  - INPUT TV.xlsx")
    print("\nUse these as references for creating your own strategies!")

if __name__ == "__main__":
    main()