#!/usr/bin/env python3
"""
Create standard template Excel files for TBS and TV backtesting systems
with all possible columns and example values for reference.
"""

import pandas as pd
from datetime import datetime, time
import os

def create_tbs_template():
    """Create standard TBS template with all columns"""
    
    # GeneralParameter sheet - All possible columns
    general_params = {
        'StrategyName': ['ExampleStrategy'],
        'MoveSlToCost': ['NO'],
        'Underlying': ['NIFTY'],
        'Index': ['NIFTY'],
        'Weekdays': ['1,2,3,4,5'],
        'DTE': ['0'],
        'StrikeSelectionTime': ['91600'],
        'StartTime': ['91600'],
        'LastEntryTime': ['120000'],
        'EndTime': ['153000'],
        'StrategyProfit': ['0'],
        'StrategyLoss': ['0'],
        'StrategyProfitReExecuteNo': ['0'],
        'StrategyLossReExecuteNo': ['0'],
        'StrategyTrailingType': ['NONE'],
        'PnLCalTime': ['0'],
        'LockPercent': ['0'],
        'TrailPercent': ['0'],
        'SqOff1Time': ['0'],
        'SqOff1Percent': ['0'],
        'SqOff2Time': ['0'],
        'SqOff2Percent': ['0'],
        'ProfitReaches': ['0'],
        'LockMinProfitAt': ['0'],
        'IncreaseInProfit': ['0'],
        'TrailMinProfitBy': ['0'],
        'TgtTrackingFrom': ['ENTRY'],
        'TgtRegisterPriceFrom': ['ENTRY'],
        'SlTrackingFrom': ['ENTRY'],
        'SlRegisterPriceFrom': ['ENTRY'],
        'PnLCalculationFrom': ['ENTRY'],
        'ConsiderHedgePnLForStgyPnL': ['NO'],
        'StoplossCheckingInterval': ['30'],
        'TargetCheckingInterval': ['30'],
        'ReEntryCheckingInterval': ['30'],
        'OnExpiryDayTradeNextExpiry': ['NO'],
        # Additional columns that might be used
        'SlippagePercent': ['0.1'],
        'UseDbExitTiming': ['NO'],  # Database exit timing feature
        'ExitSearchInterval': ['5'],  # Minutes to search around exit signal
        'ExitPriceSource': ['SPOT'],  # SPOT or FUTURE
        'MaxOrderRetries': ['3'],
        'OrderPlacementDelay': ['0'],
        'UseLimitOrders': ['NO'],
        'LimitOrderOffset': ['0']
    }
    
    # LegParameter sheet - All possible columns  
    leg_params = {
        'StrategyName': ['ExampleStrategy', 'ExampleStrategy'],
        'IsIdle': ['NO', 'NO'],
        'LegID': ['1', '2'],
        'Instrument': ['OPTIONS', 'OPTIONS'],
        'Transaction': ['SELL', 'BUY'],
        'Expiry': ['CW', 'CW'],
        'W&Type': ['NONE', 'NONE'],
        'W&TValue': ['0', '0'],
        'TrailW&T': ['0', '0'],
        'StrikeMethod': ['ATM', 'ATM'],
        'MatchPremium': ['0', '0'],
        'StrikeValue': ['0', '2'],
        'StrikePremiumCondition': ['NONE', 'NONE'],
        'SLType': ['PERCENT', 'NONE'],
        'SLValue': ['100', '0'],
        'TGTType': ['PERCENT', 'NONE'],
        'TGTValue': ['50', '0'],
        'TrailSLType': ['NONE', 'NONE'],
        'SL_TrailAt': ['0', '0'],
        'SL_TrailBy': ['0', '0'],
        'Lots': ['2', '1'],
        'ReEntryType': ['NONE', 'NONE'],
        'ReEnteriesCount': ['0', '0'],
        'OnEntry_OpenTradeOn': ['NO', 'NO'],
        'OnEntry_SqOffTradeOff': ['NO', 'NO'],
        'OnEntry_SqOffAllLegs': ['NO', 'NO'],
        'OnEntry_OpenTradeDelay': ['0', '0'],
        'OnEntry_SqOffDelay': ['0', '0'],
        'OnExit_OpenTradeOn': ['NO', 'NO'],
        'OnExit_SqOffTradeOff': ['NO', 'NO'],
        'OnExit_SqOffAllLegs': ['NO', 'NO'],
        'OnExit_OpenAllLegs': ['NO', 'NO'],
        'OnExit_OpenTradeDelay': ['0', '0'],
        'OnExit_SqOffDelay': ['0', '0'],
        'OpenHedge': ['NO', 'NO'],
        'HedgeStrikeMethod': ['NONE', 'NONE'],
        'HedgeStrikeValue': ['0', '0'],
        'HedgeStrikePremiumCondition': ['NONE', 'NONE']
    }
    
    # Create Excel file
    output_file = '/srv/samba/shared/TBS_TEMPLATE_ALL_COLUMNS.xlsx'
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        pd.DataFrame(general_params).to_excel(writer, sheet_name='GeneralParameter', index=False)
        pd.DataFrame(leg_params).to_excel(writer, sheet_name='LegParameter', index=False)
        
        # Add a reference sheet with explanations
        reference_data = {
            'Parameter': [
                'MoveSlToCost', 'Weekdays', 'DTE', 'StrikeMethod', 'SLType', 'TGTType',
                'TrailSLType', 'ReEntryType', 'UseDbExitTiming', 'ExitPriceSource',
                'Transaction', 'Expiry', 'Instrument'
            ],
            'Possible Values': [
                'YES/NO', '1,2,3,4,5 (Mon-Fri)', '0 (same day), 1 (next day), etc.',
                'ATM/ITM1/ITM2/OTM1/OTM2/ATM_WIDTH/PREMIUM', 'NONE/PERCENT/POINTS',
                'NONE/PERCENT/POINTS', 'NONE/LOCK_PROFIT/TRAIL_PROFIT',
                'NONE/IMMEDIATE/MOMENTUM/REVERSAL', 'YES/NO', 'SPOT/FUTURE',
                'BUY/SELL', 'CW/NW/MONTHLY/WEEKLY', 'OPTIONS/FUTURES/SPOT'
            ],
            'Description': [
                'Move stop loss to cost after profit', 'Trading days (1=Mon, 5=Fri)',
                'Days to expiry filter', 'Strike selection method', 'Stop loss type',
                'Target/Take profit type', 'Trailing stop loss type', 'Re-entry type',
                'Use database for precise exit timing', 'Price source for exit detection',
                'Buy or Sell transaction', 'Expiry selection', 'Instrument type'
            ]
        }
        pd.DataFrame(reference_data).to_excel(writer, sheet_name='Reference', index=False)
    
    print(f"✅ Created TBS template: {output_file}")
    return output_file

def create_tv_template():
    """Create standard TV template with all columns"""
    
    # TV Setting sheet - All possible columns
    tv_settings = {
        'StartDate': ['240401'],
        'EndDate': ['240430'],
        'SignalDateFormat': ['%d-%m-%Y %H:%M'],
        'Enabled': ['yes'],
        'TvExitApplicable': ['yes'],
        'ManualTradeEntryTime': ['91600'],
        'ManualTradeLots': ['2'],
        'IncreaseEntrySignalTimeBy': ['0'],
        'IncreaseExitSignalTimeBy': ['0'],
        'IntradaySqOffApplicable': ['yes'],
        'FirstTradeEntryTime': ['91600'],
        'IntradayExitTime': ['151500'],
        'ExpiryDayExitTime': ['151500'],
        'DoRollover': ['no'],
        'RolloverTime': ['151500'],
        'Name': ['ExampleTVStrategy'],
        'SignalFilePath': ['./signals/example_signals.csv'],
        'LongPortfolioFilePath': ['./portfolios/long_portfolio.xlsx'],
        'ShortPortfolioFilePath': ['./portfolios/short_portfolio.xlsx'],
        'ManualPortfolioFilePath': ['./portfolios/manual_portfolio.xlsx'],
        'UseDbExitTiming': ['YES'],
        'ExitSearchInterval': ['5'],
        'ExitPriceSource': ['SPOT'],
        # Additional possible columns
        'SlippagePercent': ['0.1'],
        'MaxPositions': ['4'],
        'PositionSizing': ['FIXED'],
        'RiskPercent': ['1'],
        'UseTrailingStop': ['NO'],
        'TrailingStopPercent': ['0'],
        'UseBreakeven': ['NO'],
        'BreakevenPercent': ['0'],
        'SignalValidityMinutes': ['5'],
        'IgnoreGapSignals': ['NO'],
        'GapThresholdPercent': ['2'],
        'UseMartingale': ['NO'],
        'MartingaleMultiplier': ['1'],
        'MaxMartingaleLevels': ['0'],
        'UseKellyFormula': ['NO'],
        'KellyFraction': ['0.25']
    }
    
    # Signal sheet template
    signal_data = {
        'Trade #': [1, 2, 3, 4, 5, 6],
        'Type': ['Entry Long', 'Exit Long', 'Entry Short', 'Exit Short', 'Entry Long', 'Exit Long'],
        'Date/Time': [
            '03-04-2024 09:20', '03-04-2024 11:30',
            '03-04-2024 12:00', '03-04-2024 14:45',
            '04-04-2024 09:30', '04-04-2024 15:00'
        ],
        'Contracts': [2, 2, 3, 3, 2, 2]
    }
    
    # Create Excel file
    output_file = '/srv/samba/shared/TV_TEMPLATE_ALL_COLUMNS.xlsx'
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        pd.DataFrame(tv_settings).to_excel(writer, sheet_name='Setting', index=False)
        pd.DataFrame(signal_data).to_excel(writer, sheet_name='ExampleSignals', index=False)
        
        # Add reference sheet
        reference_data = {
            'Parameter': [
                'SignalDateFormat', 'Enabled', 'TvExitApplicable', 'UseDbExitTiming',
                'ExitSearchInterval', 'ExitPriceSource', 'IntradaySqOffApplicable',
                'DoRollover', 'PositionSizing', 'Signal Type Values'
            ],
            'Possible Values': [
                '%d-%m-%Y %H:%M, %Y-%m-%d %H:%M:%S, etc.', 'yes/no', 'yes/no',
                'YES/NO', 'Integer (minutes)', 'SPOT/FUTURE', 'yes/no', 'yes/no',
                'FIXED/PERCENT/KELLY', 'Entry Long/Exit Long/Entry Short/Exit Short'
            ],
            'Description': [
                'Date format in signal CSV file', 'Enable/disable this strategy',
                'Use TV exit signals', 'Use database for precise exit timing',
                'Minutes to search around TV exit signal', 'Price data source for exits',
                'Force intraday square off', 'Rollover positions to next expiry',
                'Position sizing method', 'Valid signal types in CSV'
            ]
        }
        pd.DataFrame(reference_data).to_excel(writer, sheet_name='Reference', index=False)
    
    print(f"✅ Created TV template: {output_file}")
    return output_file

def main():
    """Create both template files"""
    print("Creating Standard Template Files for Backtesting")
    print("="*60)
    
    # Create TBS template
    print("\n1. Creating TBS Template...")
    tbs_file = create_tbs_template()
    
    # Create TV template  
    print("\n2. Creating TV Template...")
    tv_file = create_tv_template()
    
    print("\n✅ Templates created successfully!")
    print(f"\nTBS Template: {tbs_file}")
    print(f"TV Template: {tv_file}")
    print("\nThese templates include ALL possible columns with example values.")
    print("Use the 'Reference' sheet in each file for parameter explanations.")

if __name__ == "__main__":
    main()