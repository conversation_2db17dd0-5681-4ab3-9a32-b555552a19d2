#!/usr/bin/env python3
import sys
import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    import heavyai
except ImportError:
    logger.error("heavyai module not found. Please install it with pip install heavyai")
    sys.exit(1)

def main():
    """
    Main function to connect to HeavyDB, create the nifty_option_chain table,
    and insert sample data.
    """
    try:
        # Connect to HeavyDB
        logger.info("Connecting to HeavyDB...")
        conn = heavyai.connect(
            host="127.0.0.1",
            port=6274,
            user="admin",
            password="HyperInteractive",
            dbname="heavyai",
            protocol='binary'
        )
        logger.info("Connected to HeavyDB successfully!")
        
        # Create cursor
        cursor = conn.cursor()
        
        # First, check if the table exists
        logger.info("Checking if table exists...")
        try:
            cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
            result = cursor.fetchone()
            logger.info(f"Table exists with {result[0]} rows.")
            
            # Drop the table if it exists
            logger.info("Dropping existing table...")
            cursor.execute("DROP TABLE IF EXISTS nifty_option_chain")
            logger.info("Table dropped.")
        except Exception as e:
            logger.info(f"Table check error (likely doesn't exist): {e}")
        
        # Create the table
        logger.info("Creating nifty_option_chain table...")
        create_table_sql = """
        CREATE TABLE nifty_option_chain (
            trade_date       DATE ENCODING DAYS(32),
            trade_time       TIME,
            expiry_date      DATE ENCODING DAYS(32),
            index_name       TEXT ENCODING DICT(32),
            spot             DOUBLE,
            atm_strike       DOUBLE,
            strike           DOUBLE,
            dte              INT,
            expiry_bucket    TEXT ENCODING DICT(32),
            zone_id          SMALLINT,
            zone_name        TEXT ENCODING DICT(32),
            call_strike_type TEXT ENCODING DICT(32),
            put_strike_type  TEXT ENCODING DICT(32),

            -- Call metrics
            ce_symbol TEXT ENCODING DICT(32),
            ce_open   DOUBLE,
            ce_high   DOUBLE,
            ce_low    DOUBLE,
            ce_close  DOUBLE,
            ce_volume BIGINT,
            ce_oi     BIGINT,
            ce_coi    BIGINT,
            ce_iv     DOUBLE,
            ce_delta  DOUBLE,
            ce_gamma  DOUBLE,
            ce_theta  DOUBLE,
            ce_vega   DOUBLE,
            ce_rho    DOUBLE,

            -- Put metrics
            pe_symbol TEXT ENCODING DICT(32),
            pe_open   DOUBLE,
            pe_high   DOUBLE,
            pe_low    DOUBLE,
            pe_close  DOUBLE,
            pe_volume BIGINT,
            pe_oi     BIGINT,
            pe_coi    BIGINT,
            pe_iv     DOUBLE,
            pe_delta  DOUBLE,
            pe_gamma  DOUBLE,
            pe_theta  DOUBLE,
            pe_vega   DOUBLE,
            pe_rho    DOUBLE,

            -- Futures join
            future_open   DOUBLE,
            future_high   DOUBLE,
            future_low    DOUBLE,
            future_close  DOUBLE,
            future_volume BIGINT,
            future_oi     BIGINT,
            future_coi    BIGINT
        )
        WITH (fragment_size = 32000000);
        """
        cursor.execute(create_table_sql)
        logger.info("Table created successfully!")
        
        # Insert sample data
        logger.info("Inserting sample data...")
        
        # Sample 1
        insert_sql = """
        INSERT INTO nifty_option_chain VALUES (
            '2023-01-01',
            '09:15:00',
            '2023-01-26',
            'NIFTY',
            18000,
            18000,
            18000,
            25,
            'CW',
            1,
            'OPEN',
            'ATM',
            'ATM',
            'NIFTY26JAN2318000CE',
            100,
            120,
            90,
            110,
            1000,
            5000,
            100,
            15,
            0.5,
            0.05,
            -10,
            10,
            5,
            'NIFTY26JAN2318000PE',
            100,
            120,
            90,
            110,
            1000,
            5000,
            100,
            15,
            -0.5,
            0.05,
            -10,
            10,
            -5,
            18050,
            18100,
            18000,
            18075,
            5000,
            10000,
            200
        );
        """
        cursor.execute(insert_sql)
        
        # Sample 2
        insert_sql = """
        INSERT INTO nifty_option_chain VALUES (
            '2023-01-01',
            '09:15:00',
            '2023-01-26',
            'NIFTY',
            18000,
            18000,
            18050,
            25,
            'CW',
            1,
            'OPEN',
            'OTM1',
            'ITM1',
            'NIFTY26JAN2318050CE',
            90,
            110,
            80,
            100,
            900,
            4500,
            90,
            16,
            0.45,
            0.06,
            -9,
            11,
            4,
            'NIFTY26JAN2318050PE',
            110,
            130,
            100,
            120,
            1100,
            5500,
            110,
            14,
            -0.55,
            0.04,
            -11,
            9,
            -6,
            18050,
            18100,
            18000,
            18075,
            5000,
            10000,
            200
        );
        """
        cursor.execute(insert_sql)
        
        # Sample 3
        insert_sql = """
        INSERT INTO nifty_option_chain VALUES (
            '2023-01-02',
            '10:30:00',
            '2023-01-26',
            'NIFTY',
            18100,
            18100,
            18100,
            24,
            'CW',
            2,
            'MID_MORN',
            'ATM',
            'ATM',
            'NIFTY26JAN2318100CE',
            105,
            125,
            95,
            115,
            1050,
            5200,
            105,
            16,
            0.52,
            0.055,
            -11,
            11,
            5.5,
            'NIFTY26JAN2318100PE',
            105,
            125,
            95,
            115,
            1050,
            5200,
            105,
            16,
            -0.52,
            0.055,
            -11,
            11,
            -5.5,
            18150,
            18200,
            18100,
            18175,
            5100,
            10100,
            210
        );
        """
        cursor.execute(insert_sql)
        
        # Commit changes
        conn.commit()
        logger.info("Sample data inserted successfully!")
        
        # Verify data
        cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
        count = cursor.fetchone()[0]
        logger.info(f"Total rows in the table: {count}")
        
        # Close cursor and connection
        cursor.close()
        conn.close()
        logger.info("Connection closed.")
        
    except Exception as e:
        logger.error(f"Error in main function: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 