#!/usr/bin/env python3
"""Create a visual flow diagram for TV file relationships."""
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
import numpy as np

def create_flow_diagram():
    """Create a visual flow diagram showing TV file relationships."""
    fig, ax = plt.subplots(figsize=(16, 12))
    
    # Define positions for each file
    positions = {
        'input_tv.xlsx': (8, 10),
        'sample_nifty_list_of_trades.xlsx': (2, 7),
        'input_portfolio_long.xlsx': (8, 7),
        'input_portfolio_short.xlsx': (14, 7),
        'input_tbs_long.xlsx': (5, 4),
        'input_tbs_short.xlsx': (11, 4),
    }
    
    # Define box properties
    box_width = 3.5
    box_height = 1.2
    
    # Colors for different file types
    colors = {
        'input_tv.xlsx': '#4CAF50',  # Green - Main TV file
        'sample_nifty_list_of_trades.xlsx': '#2196F3',  # Blue - Signal file
        'input_portfolio_long.xlsx': '#FF9800',  # Orange - Portfolio files
        'input_portfolio_short.xlsx': '#FF9800',
        'input_tbs_long.xlsx': '#9C27B0',  # Purple - TBS files
        'input_tbs_short.xlsx': '#9C27B0',
    }
    
    # Draw boxes for each file
    boxes = {}
    for file_name, (x, y) in positions.items():
        # Create fancy box
        box = FancyBboxPatch(
            (x - box_width/2, y - box_height/2),
            box_width, box_height,
            boxstyle="round,pad=0.1",
            facecolor=colors[file_name],
            edgecolor='black',
            linewidth=2,
            alpha=0.8
        )
        ax.add_patch(box)
        boxes[file_name] = box
        
        # Add file name
        ax.text(x, y + 0.2, file_name.replace('.xlsx', ''), 
                ha='center', va='center', fontsize=10, fontweight='bold')
        
        # Add sheet info
        if file_name == 'input_tv.xlsx':
            ax.text(x, y - 0.2, 'Sheet: Setting', 
                    ha='center', va='center', fontsize=8, style='italic')
        elif 'portfolio' in file_name:
            ax.text(x, y - 0.2, 'Sheets: PortfolioSetting,\nStrategySetting', 
                    ha='center', va='center', fontsize=8, style='italic')
        elif 'tbs' in file_name:
            ax.text(x, y - 0.2, 'Sheets: GeneralParameter,\nLegParameter', 
                    ha='center', va='center', fontsize=8, style='italic')
        elif 'sample_nifty' in file_name:
            ax.text(x, y - 0.2, 'Sheets: Performance, Trades,\nRisk, List of trades', 
                    ha='center', va='center', fontsize=8, style='italic')
    
    # Draw arrows showing relationships
    arrows = [
        # From input_tv to its referenced files
        ('input_tv.xlsx', 'sample_nifty_list_of_trades.xlsx', 'SignalFilePath'),
        ('input_tv.xlsx', 'input_portfolio_long.xlsx', 'LongPortfolioFilePath'),
        ('input_tv.xlsx', 'input_portfolio_short.xlsx', 'ShortPortfolioFilePath'),
        # From portfolio files to TBS files
        ('input_portfolio_long.xlsx', 'input_tbs_long.xlsx', 'StrategyExcelFilePath'),
        ('input_portfolio_short.xlsx', 'input_tbs_short.xlsx', 'StrategyExcelFilePath'),
    ]
    
    for source, target, label in arrows:
        x1, y1 = positions[source]
        x2, y2 = positions[target]
        
        # Calculate arrow position
        if source == 'input_tv.xlsx':
            y1 -= box_height/2
        else:
            y1 -= box_height/2
        
        if target in ['input_tbs_long.xlsx', 'input_tbs_short.xlsx']:
            y2 += box_height/2
        else:
            y2 += box_height/2
        
        # Draw arrow
        arrow = patches.FancyArrowPatch(
            (x1, y1), (x2, y2),
            connectionstyle="arc3,rad=0.1",
            arrowstyle="->",
            mutation_scale=20,
            linewidth=2,
            color='#333333',
            alpha=0.7
        )
        ax.add_patch(arrow)
        
        # Add label
        mid_x = (x1 + x2) / 2
        mid_y = (y1 + y2) / 2
        ax.text(mid_x, mid_y, label, ha='center', va='bottom', 
                fontsize=8, bbox=dict(boxstyle="round,pad=0.3", 
                facecolor='white', alpha=0.8))
    
    # Add title
    ax.text(8, 12, 'TV (TradingView) Input File Structure and Flow', 
            ha='center', va='center', fontsize=16, fontweight='bold')
    
    # Add legend
    legend_elements = [
        patches.Patch(color='#4CAF50', label='Main TV Configuration'),
        patches.Patch(color='#2196F3', label='Signal/Trade Data'),
        patches.Patch(color='#FF9800', label='Portfolio Configuration'),
        patches.Patch(color='#9C27B0', label='TBS Strategy Configuration'),
    ]
    ax.legend(handles=legend_elements, loc='lower center', ncol=4, 
              bbox_to_anchor=(0.5, -0.05))
    
    # Add annotations
    annotations = [
        (2, 1, "Key Points:", 'left', 12, 'bold'),
        (2, 0.5, "1. input_tv.xlsx is the main configuration file", 'left', 10, 'normal'),
        (2, 0.2, "2. It references signal file, long/short portfolio files", 'left', 10, 'normal'),
        (2, -0.1, "3. Portfolio files reference their respective TBS strategy files", 'left', 10, 'normal'),
        (2, -0.4, "4. TBS files contain general parameters and leg parameters", 'left', 10, 'normal'),
        (2, -0.7, "5. Signal file contains actual trade signals from TradingView", 'left', 10, 'normal'),
    ]
    
    for x, y, text, ha, size, weight in annotations:
        ax.text(x, y, text, ha=ha, va='top', fontsize=size, fontweight=weight)
    
    # Set axis properties
    ax.set_xlim(-1, 17)
    ax.set_ylim(-1, 13)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('/srv/samba/shared/tv_file_flow_diagram.png', dpi=300, bbox_inches='tight')
    print("Flow diagram saved to: /srv/samba/shared/tv_file_flow_diagram.png")
    
    # Also create a detailed structure diagram
    create_detailed_structure()

def create_detailed_structure():
    """Create a detailed structure diagram showing file contents."""
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('TV File Detailed Structure', fontsize=16, fontweight='bold')
    
    # Flatten axes for easier iteration
    axes = axes.flatten()
    
    # File structures
    structures = [
        {
            'title': 'input_tv.xlsx',
            'content': [
                'Sheet: Setting',
                '─────────────────',
                '• StartDate, EndDate',
                '• SignalDateFormat',
                '• SignalFilePath → signal file',
                '• LongPortfolioFilePath → long portfolio',
                '• ShortPortfolioFilePath → short portfolio',
                '• ManualPortfolioFilePath',
                '• TvExitApplicable',
                '• IntradayExitTime',
                '• ExpiryDayExitTime',
                '• UseDbExitTiming',
                '• ExitSearchInterval',
                '• SlippagePercent',
            ]
        },
        {
            'title': 'sample_nifty_list_of_trades.xlsx',
            'content': [
                'Sheet: List of trades',
                '─────────────────────',
                '• Trade #',
                '• Type (Entry/Exit)',
                '• Signal (Buy/Sell)',
                '• Date/Time',
                '• Price INR',
                '• Contracts',
                '• Profit INR/% ',
                '• Cumulative profit',
                '',
                'Sheet: Performance',
                '• Net/Gross profit',
                '• Win/Loss stats',
            ]
        },
        {
            'title': 'input_portfolio_long.xlsx',
            'content': [
                'Sheet: PortfolioSetting',
                '────────────────────────',
                '• StartDate, EndDate',
                '• PortfolioName',
                '• PortfolioTarget/Stoploss',
                '• PortfolioTrailingType',
                '• SqOff times & percentages',
                '',
                'Sheet: StrategySetting',
                '• Enabled (YES/NO)',
                '• PortfolioName',
                '• StrategyType',
                '• StrategyExcelFilePath → TBS file',
            ]
        },
        {
            'title': 'input_portfolio_short.xlsx',
            'content': [
                'Sheet: PortfolioSetting',
                '────────────────────────',
                '• Same structure as long portfolio',
                '• Different parameters for short',
                '',
                'Sheet: StrategySetting',
                '• References input_tbs_short.xlsx',
            ]
        },
        {
            'title': 'input_tbs_long.xlsx',
            'content': [
                'Sheet: GeneralParameter',
                '───────────────────────',
                '• StrategyName: "L"',
                '• Underlying, Index',
                '• DTE, Weekdays',
                '• Entry/Exit times',
                '• Profit/Loss settings',
                '• Trailing parameters',
                '',
                'Sheet: LegParameter',
                '• LegID (1-4)',
                '• Instrument (CE/PE)',
                '• Strike selection method',
                '• SL/Target settings',
            ]
        },
        {
            'title': 'input_tbs_short.xlsx',
            'content': [
                'Sheet: GeneralParameter',
                '───────────────────────',
                '• StrategyName: "S"',
                '• Same structure as long TBS',
                '• Different parameters for short',
                '',
                'Sheet: LegParameter',
                '• Same structure as long TBS',
                '• Different leg configurations',
            ]
        },
    ]
    
    # Draw each file structure
    for idx, (ax, struct) in enumerate(zip(axes, structures)):
        ax.text(0.5, 0.95, struct['title'], ha='center', va='top', 
                fontsize=12, fontweight='bold', 
                transform=ax.transAxes,
                bbox=dict(boxstyle="round,pad=0.3", 
                         facecolor='lightblue', alpha=0.8))
        
        y_pos = 0.85
        for line in struct['content']:
            if line.startswith('Sheet:'):
                ax.text(0.05, y_pos, line, ha='left', va='top', 
                       fontsize=10, fontweight='bold',
                       transform=ax.transAxes)
            elif line.startswith('─'):
                ax.text(0.05, y_pos, line, ha='left', va='top', 
                       fontsize=8, transform=ax.transAxes)
            elif line.startswith('•'):
                ax.text(0.1, y_pos, line, ha='left', va='top', 
                       fontsize=9, transform=ax.transAxes)
            else:
                ax.text(0.05, y_pos, line, ha='left', va='top', 
                       fontsize=9, transform=ax.transAxes)
            y_pos -= 0.05
        
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('/srv/samba/shared/tv_file_detailed_structure.png', 
                dpi=300, bbox_inches='tight')
    print("Detailed structure saved to: /srv/samba/shared/tv_file_detailed_structure.png")

if __name__ == "__main__":
    create_flow_diagram()