#!/usr/bin/env python3
"""
Create sample data for nifty_option_chain and futures to test the merging process.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def create_sample_option_chain(filename, num_rows=20):
    """Create a sample option chain CSV with realistic data"""
    # Define base date and timestamps
    base_date = datetime(2022, 1, 3)  # Same as in futures data
    timestamps = [
        "09:15:00", "09:16:00", "09:17:00", "09:18:00", "09:19:00",
        "09:20:00", "09:21:00", "09:22:00", "09:23:00", "09:24:00"
    ]
    
    # Create expiry dates (weekly and monthly)
    expiry_dates = [
        base_date + timedelta(days=3),  # Current week
        base_date + timedelta(days=10),  # Next week
        base_date + timedelta(days=24),  # Current month (assume last Thursday)
        base_date + timedelta(days=52)   # Next month
    ]
    
    # Sample strikes around ATM (17500)
    strikes = [17300, 17350, 17400, 17450, 17500, 17550, 17600, 17650, 17700]
    
    # Create DataFrame rows
    rows = []
    for ts in timestamps:
        for expiry in expiry_dates:
            for strike in strikes:
                # Underlying price with some random variation around 17480
                underlying = 17480 + np.random.randint(-30, 30)
                
                # ATM strike (closest to underlying)
                atm_strike = 17500  # Fixed for example
                
                # Calculate days to expiry
                dte = (expiry - base_date).days
                
                # Determine expiry bucket
                if expiry == expiry_dates[0]:
                    expiry_bucket = "CW"
                elif expiry == expiry_dates[1]:
                    expiry_bucket = "NW"
                elif expiry == expiry_dates[2]:
                    expiry_bucket = "CM"
                else:
                    expiry_bucket = "NM"
                
                # Classify strikes
                if strike == atm_strike:
                    call_strike_type = "ATM"
                    put_strike_type = "ATM"
                else:
                    dist = abs(strike - atm_strike) / 50
                    if strike < atm_strike:
                        call_strike_type = f"ITM{int(dist)}"
                        put_strike_type = f"OTM{int(dist)}"
                    else:
                        call_strike_type = f"OTM{int(dist)}"
                        put_strike_type = f"ITM{int(dist)}"
                
                # Add row
                rows.append({
                    "trade_date": base_date.strftime("%Y-%m-%d"),
                    "trade_time": ts,
                    "expiry_date": expiry.strftime("%Y-%m-%d"),
                    "index_name": "NIFTY",
                    "underlying_price": underlying,
                    "atm_strike": atm_strike,
                    "strike": strike,
                    "dte": dte,
                    "expiry_bucket": expiry_bucket,
                    "zone_id": 1,
                    "zone_name": "OPEN",
                    "call_strike_type": call_strike_type,
                    "put_strike_type": put_strike_type,
                    "ce_symbol": f"NIFTY{expiry.strftime('%d%b%y').upper()}{strike}CE",
                    "ce_open": 300 + np.random.randint(-20, 20),
                    "ce_high": 310 + np.random.randint(-10, 30),
                    "ce_low": 290 + np.random.randint(-20, 10),
                    "ce_close": 305 + np.random.randint(-15, 15),
                    "ce_volume": np.random.randint(1000, 5000),
                    "ce_oi": np.random.randint(50000, 150000),
                    "ce_coi": np.random.randint(-5000, 5000),
                    "ce_iv": 15 + np.random.random() * 5,
                    "ce_delta": 0.5 + np.random.random() * 0.2,
                    "ce_gamma": 0.05 + np.random.random() * 0.02,
                    "ce_theta": -10 - np.random.random() * 5,
                    "ce_vega": 15 + np.random.random() * 5,
                    "ce_rho": 5 + np.random.random() * 2,
                    "pe_symbol": f"NIFTY{expiry.strftime('%d%b%y').upper()}{strike}PE",
                    "pe_open": 280 + np.random.randint(-20, 20),
                    "pe_high": 290 + np.random.randint(-10, 30),
                    "pe_low": 270 + np.random.randint(-20, 10),
                    "pe_close": 285 + np.random.randint(-15, 15),
                    "pe_volume": np.random.randint(1000, 5000),
                    "pe_oi": np.random.randint(50000, 150000),
                    "pe_coi": np.random.randint(-5000, 5000),
                    "pe_iv": 16 + np.random.random() * 5,
                    "pe_delta": -0.5 + np.random.random() * 0.2,
                    "pe_gamma": 0.05 + np.random.random() * 0.02,
                    "pe_theta": -11 - np.random.random() * 5,
                    "pe_vega": 16 + np.random.random() * 5,
                    "pe_rho": -5 - np.random.random() * 2
                })
                
                # Limit rows if needed
                if len(rows) >= num_rows:
                    break
            if len(rows) >= num_rows:
                break
        if len(rows) >= num_rows:
            break
    
    # Create DataFrame and save to CSV
    df = pd.DataFrame(rows[:num_rows])
    df.to_csv(filename, index=False)
    
    print(f"Created sample option chain with {len(df)} rows in {filename}")
    return df

if __name__ == "__main__":
    # Create sample data
    sample_file = "/srv/samba/shared/market_data/nifty/oc/sample_option_chain.csv"
    create_sample_option_chain(sample_file, num_rows=20)
    
    print("\nFirst few rows of generated data:")
    df = pd.read_csv(sample_file)
    
    # Display subset of columns
    display_cols = [
        "trade_date", "trade_time", "expiry_date", 
        "underlying_price", "atm_strike", "strike",
        "call_strike_type", "put_strike_type"
    ]
    print(df[display_cols].head()) 