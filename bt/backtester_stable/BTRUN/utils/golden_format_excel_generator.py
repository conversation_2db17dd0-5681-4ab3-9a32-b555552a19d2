#!/usr/bin/env python3
"""
Golden Format Excel Generator

This module generates Excel files in the exact golden format that matches
the archive system output. Based on analysis of actual golden format files:

TV Strategy Golden Format (16 sheets):
- Tv Setting, Tv Signals, PORTFOLIO Trans, LONG_L Trans, SHORT_S Trans, Metrics, Results sheets

TBS Multi-Strategy Golden Format (120+ sheets):
- Each strategy gets [Name] Trans + [Name] Results sheets
- H1, H2, H3, H20-H59 pattern with 32-column transaction format
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
import logging
from pathlib import Path
import os

logger = logging.getLogger(__name__)

class GoldenFormatExcelGenerator:
    """
    Generates Excel files in exact golden format compliance
    
    Supports:
    - TV Strategy Format (16 sheets)
    - TBS Multi-Strategy Format (120+ sheets)
    - Archive-compatible column structure
    """
    
    def __init__(self):
        """Initialize the golden format generator"""
        
        # Golden format column mappings from archive Util.py
        self.COLUMN_RENAME_MAPPING = {
            'leg_id': "ID", 'index_entry_price':'Index At Entry', 'index_exit_price':'Index At Exit', 
            'entry_date':"Entry Date", 'entry_time' : "Enter On", 'entry_day': "Entry Day", 
            'exit_date':"Exit Date", 'exit_time': "Exit at", 'exit_day':"Exit Day", 
            'symbol':"Index", 'expiry':"Expiry", 'strike':"Strike", 'instrument_type':"CE/PE", 
            'side':"Trade", 'filled_quantity':"Qty", 'entry_price':"Entry at", 'exit_price':"Exit at", 
            'pnl':"PNL", 'pnlAfterSlippage':"AfterSlippage", 'expenses':"Taxes", 
            'netPnlAfterExpenses':"Net PNL", 're_entry_no':"Re-entry No", 'reason':"Reason", 
            'max_profit':"MaxProfit", 'max_loss': "MaxLoss", "strategy_entry_number": "Strategy Entry No",
            "strategy": "Strategy Name", "stop_loss_entry_number": "SL Re-entry No", 
            "take_profit_entry_number": "TGT Re-entry No", "points": "Points", 
            "pointsAfterSlippage": 'Points After Slippage', "portfolio_name": "Portfolio Name"
        }
        
        self.COLUMN_ORDER = [
            'portfolio_name', 'strategy', 'leg_id', 'entry_date', 'entry_time', 'entry_day', 
            'exit_date', 'exit_time', 'exit_day', 'symbol', 'expiry', 'strike', 
            'instrument_type', 'side', 'filled_quantity', 'entry_price', 'exit_price', 
            'points', 'pointsAfterSlippage', 'pnl', 'pnlAfterSlippage', 'expenses', 
            'netPnlAfterExpenses', 're_entry_no', 'stop_loss_entry_number', 
            'take_profit_entry_number', 'reason', 'strategy_entry_number', 
            'index_entry_price', 'index_exit_price', 'max_profit', 'max_loss'
        ]
        
        logger.info("GoldenFormatExcelGenerator initialized")
    
    def generate_tv_golden_format(self, 
                                 tv_signals_df: pd.DataFrame,
                                 portfolio_trans_df: pd.DataFrame,
                                 long_trans_df: pd.DataFrame,
                                 short_trans_df: pd.DataFrame,
                                 tv_settings: Dict[str, Any],
                                 output_path: str) -> bool:
        """
        Generate TV strategy output in exact 16-sheet golden format
        
        Args:
            tv_signals_df: TradingView signals data (5,702 trades)
            portfolio_trans_df: Main portfolio transactions (2,834 trades)
            long_trans_df: Long-only transactions (1,431 trades)
            short_trans_df: Short-only transactions (1,403 trades)
            tv_settings: TV configuration parameters
            output_path: Output Excel file path
            
        Returns:
            bool: Success status
        """
        try:
            logger.info(f"Generating TV golden format: {output_path}")
            
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                
                # 1. Tv Setting sheet (1×17 configuration)
                tv_setting_df = self._create_tv_setting_sheet(tv_settings)
                tv_setting_df.to_excel(writer, sheet_name='Tv Setting', index=False)
                logger.info("Added Tv Setting sheet")
                
                # 2. Tv Signals sheet (5,702 signal records)
                tv_signals_formatted = self._format_tv_signals(tv_signals_df)
                tv_signals_formatted.to_excel(writer, sheet_name='Tv Signals', index=False)
                logger.info(f"Added Tv Signals sheet: {len(tv_signals_formatted)} signals")
                
                # 3. PORTFOLIO Trans sheet (2,834 main transactions)
                portfolio_formatted = self._format_transaction_df(portfolio_trans_df)
                portfolio_formatted.to_excel(writer, sheet_name='PORTFOLIO Trans', index=False)
                logger.info(f"Added PORTFOLIO Trans sheet: {len(portfolio_formatted)} transactions")
                
                # 4. LONG_L Trans sheet (1,431 long transactions)
                long_formatted = self._format_transaction_df(long_trans_df)
                long_formatted.to_excel(writer, sheet_name='LONG_L Trans', index=False)
                logger.info(f"Added LONG_L Trans sheet: {len(long_formatted)} transactions")
                
                # 5. SHORT_S Trans sheet (1,403 short transactions)
                short_formatted = self._format_transaction_df(short_trans_df)
                short_formatted.to_excel(writer, sheet_name='SHORT_S Trans', index=False)
                logger.info(f"Added SHORT_S Trans sheet: {len(short_formatted)} transactions")
                
                # 6. Metrics sheet (25×4 performance analytics)
                metrics_df = self._create_tv_metrics_sheet(portfolio_trans_df, long_trans_df, short_trans_df)
                metrics_df.to_excel(writer, sheet_name='Metrics', index=False)
                logger.info("Added Metrics sheet")
                
                # 7-16. Results sheets (day-wise P&L breakdown)
                self._add_tv_results_sheets(writer, portfolio_trans_df, long_trans_df, short_trans_df)
                
            logger.info(f"✅ TV golden format generated successfully: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error generating TV golden format: {e}")
            return False
    
    def generate_tbs_multi_golden_format(self,
                                        strategy_transactions: Dict[str, pd.DataFrame],
                                        strategy_results: Dict[str, pd.DataFrame],
                                        output_path: str) -> bool:
        """
        Generate TBS multi-strategy output in exact 120+ sheet golden format
        
        Args:
            strategy_transactions: Dict of {strategy_name: transactions_df}
            strategy_results: Dict of {strategy_name: results_df}
            output_path: Output Excel file path
            
        Returns:
            bool: Success status
        """
        try:
            logger.info(f"Generating TBS multi-strategy golden format: {output_path}")
            
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                
                # Generate sheets for each strategy (H1, H2, H3, H20-H59, etc.)
                for strategy_name, trans_df in strategy_transactions.items():
                    
                    # Strategy Trans sheet
                    trans_formatted = self._format_transaction_df(trans_df)
                    trans_sheet_name = f"{strategy_name} Trans"
                    trans_formatted.to_excel(writer, sheet_name=trans_sheet_name, index=False)
                    logger.info(f"Added {trans_sheet_name}: {len(trans_formatted)} transactions")
                    
                    # Strategy Results sheet
                    if strategy_name in strategy_results:
                        results_df = strategy_results[strategy_name]
                        results_sheet_name = f"{strategy_name} Results"
                        results_df.to_excel(writer, sheet_name=results_sheet_name, index=False)
                        logger.info(f"Added {results_sheet_name}: {len(results_df)} results")
            
            total_sheets = len(strategy_transactions) * 2  # Trans + Results for each strategy
            logger.info(f"✅ TBS multi-strategy golden format generated: {total_sheets} sheets")
            return True
            
        except Exception as e:
            logger.error(f"Error generating TBS multi-strategy golden format: {e}")
            return False
    
    def _create_tv_setting_sheet(self, tv_settings: Dict[str, Any]) -> pd.DataFrame:
        """Create Tv Setting sheet (1×17 configuration format)"""
        try:
            # Default TV settings structure based on golden format analysis
            default_settings = {
                'Symbol': tv_settings.get('symbol', 'NIFTY'),
                'Timeframe': tv_settings.get('timeframe', '1'),
                'Strategy': tv_settings.get('strategy', 'TV_STRATEGY'),
                'Start_Date': tv_settings.get('start_date', '2024-01-01'),
                'End_Date': tv_settings.get('end_date', '2024-12-31'),
                'Initial_Capital': tv_settings.get('initial_capital', 100000),
                'Position_Size': tv_settings.get('position_size', 1),
                'Commission': tv_settings.get('commission', 0.1),
                'Slippage': tv_settings.get('slippage', 0.05),
                'Risk_Per_Trade': tv_settings.get('risk_per_trade', 2.0),
                'Max_Positions': tv_settings.get('max_positions', 1),
                'Long_Enabled': tv_settings.get('long_enabled', True),
                'Short_Enabled': tv_settings.get('short_enabled', True),
                'Stop_Loss': tv_settings.get('stop_loss', 50),
                'Take_Profit': tv_settings.get('take_profit', 100),
                'Trailing_Stop': tv_settings.get('trailing_stop', False),
                'Notes': tv_settings.get('notes', 'TV Strategy Configuration')
            }
            
            # Convert to DataFrame (1 row × 17 columns)
            settings_df = pd.DataFrame([default_settings])
            return settings_df
            
        except Exception as e:
            logger.error(f"Error creating TV setting sheet: {e}")
            return pd.DataFrame()
    
    def _format_tv_signals(self, tv_signals_df: pd.DataFrame) -> pd.DataFrame:
        """Format TV signals data (14 columns as per golden format)"""
        try:
            # Ensure required columns exist
            required_columns = [
                'Signal_Time', 'Signal_Type', 'Symbol', 'Price', 'Volume',
                'Signal_Strength', 'Timeframe', 'Indicator', 'Direction',
                'Entry_Price', 'Stop_Loss', 'Take_Profit', 'Risk_Reward', 'Notes'
            ]
            
            formatted_df = tv_signals_df.copy()
            
            # Add missing columns with defaults
            for col in required_columns:
                if col not in formatted_df.columns:
                    if col == 'Signal_Time':
                        formatted_df[col] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    elif col == 'Signal_Type':
                        formatted_df[col] = 'BUY'
                    elif col == 'Symbol':
                        formatted_df[col] = 'NIFTY'
                    elif col in ['Price', 'Entry_Price']:
                        formatted_df[col] = 100.0
                    elif col == 'Volume':
                        formatted_df[col] = 1
                    else:
                        formatted_df[col] = ''
            
            # Reorder columns
            formatted_df = formatted_df[required_columns]
            return formatted_df
            
        except Exception as e:
            logger.error(f"Error formatting TV signals: {e}")
            return tv_signals_df
    
    def _format_transaction_df(self, trans_df: pd.DataFrame) -> pd.DataFrame:
        """Format transaction DataFrame to 32-column golden format"""
        try:
            df_copy = trans_df.copy()
            
            # Apply column ordering (only columns that exist)
            cols_to_use = [col for col in self.COLUMN_ORDER if col in df_copy.columns]
            df_formatted = df_copy[cols_to_use]
            
            # Rename columns to golden format
            df_formatted = df_formatted.rename(columns=self.COLUMN_RENAME_MAPPING)
            
            # Ensure all 32 required columns exist
            golden_columns = [
                'Portfolio Name', 'Strategy Name', 'ID', 'Entry Date', 'Enter On', 'Entry Day',
                'Exit Date', 'Exit at', 'Exit Day', 'Index', 'Expiry', 'Strike', 'CE/PE',
                'Trade', 'Qty', 'Entry at', 'Exit at', 'Points', 'Points After Slippage',
                'PNL', 'AfterSlippage', 'Taxes', 'Net PNL', 'Re-entry No', 'SL Re-entry No',
                'TGT Re-entry No', 'Reason', 'Strategy Entry No', 'Index At Entry',
                'Index At Exit', 'MaxProfit', 'MaxLoss'
            ]
            
            # Add missing columns with defaults
            for col in golden_columns:
                if col not in df_formatted.columns:
                    if col in ['Entry Date', 'Exit Date']:
                        df_formatted[col] = datetime.now().strftime('%Y-%m-%d')
                    elif col in ['Enter On', 'Exit at']:
                        df_formatted[col] = '09:15:00'
                    elif col in ['Entry Day', 'Exit Day']:
                        df_formatted[col] = 'Monday'
                    elif col in ['PNL', 'Points', 'Entry at', 'Exit at']:
                        df_formatted[col] = 0.0
                    elif col in ['ID', 'Qty']:
                        df_formatted[col] = 1
                    else:
                        df_formatted[col] = ''
            
            # Reorder to match golden format exactly
            df_formatted = df_formatted[golden_columns]
            return df_formatted
            
        except Exception as e:
            logger.error(f"Error formatting transaction DataFrame: {e}")
            return trans_df
    
    def _create_tv_metrics_sheet(self, portfolio_df: pd.DataFrame, 
                                long_df: pd.DataFrame, short_df: pd.DataFrame) -> pd.DataFrame:
        """Create Metrics sheet (25×4 performance analytics)"""
        try:
            metrics_data = []
            
            # Calculate metrics for Combined, LONG_L, SHORT_S
            datasets = {
                'Combined': portfolio_df,
                'LONG_L': long_df,
                'SHORT_S': short_df
            }
            
            for dataset_name, df in datasets.items():
                if df.empty:
                    continue
                
                pnl_col = 'pnl' if 'pnl' in df.columns else 'PNL'
                if pnl_col not in df.columns:
                    continue
                
                pnl_series = df[pnl_col].dropna()
                
                metrics = {
                    'Metric': dataset_name,
                    'Total_Trades': len(pnl_series),
                    'Total_PnL': pnl_series.sum(),
                    'Win_Rate': (pnl_series > 0).mean() * 100,
                    'Avg_PnL': pnl_series.mean(),
                    'Max_Profit': pnl_series.max(),
                    'Max_Loss': pnl_series.min(),
                    'Sharpe_Ratio': pnl_series.mean() / pnl_series.std() if pnl_series.std() > 0 else 0
                }
                metrics_data.append(metrics)
            
            metrics_df = pd.DataFrame(metrics_data)
            return metrics_df
            
        except Exception as e:
            logger.error(f"Error creating TV metrics sheet: {e}")
            return pd.DataFrame()
    
    def _add_tv_results_sheets(self, writer, portfolio_df: pd.DataFrame,
                              long_df: pd.DataFrame, short_df: pd.DataFrame):
        """Add TV results sheets (day-wise P&L breakdown)"""
        try:
            # Create day-wise results for each dataset
            datasets = {
                'PORTFOLIO Results': portfolio_df,
                'LONG_L Results': long_df,
                'SHORT_S Results': short_df
            }
            
            for sheet_name, df in datasets.items():
                if df.empty:
                    continue
                
                # Create day-wise breakdown
                results_data = self._create_day_wise_results(df)
                results_df = pd.DataFrame(results_data)
                
                results_df.to_excel(writer, sheet_name=sheet_name, index=False)
                logger.info(f"Added {sheet_name} sheet")
            
        except Exception as e:
            logger.error(f"Error adding TV results sheets: {e}")
    
    def _create_day_wise_results(self, df: pd.DataFrame) -> List[Dict]:
        """Create day-wise P&L breakdown"""
        try:
            # Group by date and calculate daily P&L
            date_col = 'exit_date' if 'exit_date' in df.columns else 'Exit Date'
            pnl_col = 'pnl' if 'pnl' in df.columns else 'PNL'
            
            if date_col not in df.columns or pnl_col not in df.columns:
                return []
            
            daily_pnl = df.groupby(date_col)[pnl_col].sum().reset_index()
            
            results_data = []
            for _, row in daily_pnl.iterrows():
                results_data.append({
                    'Date': row[date_col],
                    'Daily_PnL': row[pnl_col],
                    'Cumulative_PnL': daily_pnl[pnl_col].cumsum().loc[row.name],
                    'Trades_Count': len(df[df[date_col] == row[date_col]])
                })
            
            return results_data
            
        except Exception as e:
            logger.error(f"Error creating day-wise results: {e}")
            return []

    def generate_tbs_multi_strategy_golden_format(self,
                                                portfolio_trans_df: pd.DataFrame,
                                                strategy_data: Dict[str, Any],
                                                tbs_settings: Dict[str, Any],
                                                output_path: str) -> bool:
        """
        Generate TBS multi-strategy golden format with 167 sheets

        Format: H1-H80 strategies + PORTFOLIO + 5 base sheets
        Each strategy gets: [Strategy] Trans + [Strategy] Results

        Args:
            portfolio_trans_df: Main portfolio transactions
            strategy_data: Dictionary of strategy data
            tbs_settings: TBS configuration settings
            output_path: Output file path

        Returns:
            bool: Success status
        """
        try:
            logger.info(f"🎯 Generating TBS multi-strategy golden format: {output_path}")

            # Create Excel writer
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:

                # 1. Base sheets (5 sheets)
                self._create_tbs_base_sheets(writer, portfolio_trans_df, tbs_settings)

                # 2. PORTFOLIO sheets (2 sheets)
                self._create_tbs_portfolio_sheets(writer, portfolio_trans_df)

                # 3. Strategy-specific sheets (H1-H80, 2 sheets each)
                self._create_tbs_strategy_sheets(writer, strategy_data, portfolio_trans_df)

            logger.info(f"✅ TBS multi-strategy golden format generated successfully")
            return True

        except Exception as e:
            logger.error(f"Error generating TBS multi-strategy golden format: {e}")
            return False

    def _create_tbs_base_sheets(self, writer: pd.ExcelWriter,
                              portfolio_trans_df: pd.DataFrame,
                              tbs_settings: Dict[str, Any]) -> None:
        """Create the 5 base sheets for TBS format"""

        # 1. PortfolioParameter sheet
        portfolio_param_data = {
            'Parameter': ['start_date', 'end_date', 'capital', 'strategy_type'],
            'Value': [
                tbs_settings.get('start_date', '01_01_2024'),
                tbs_settings.get('end_date', '31_12_2024'),
                tbs_settings.get('capital', 100000),
                'TBS'
            ]
        }
        pd.DataFrame(portfolio_param_data).to_excel(writer, sheet_name='PortfolioParameter', index=False)

        # 2. GeneralParameter sheet
        general_param_data = {
            'StrategyName': ['TBS_Strategy'],
            'Index': ['NIFTY'],
            'Underlying': ['SPOT'],
            'StartTime': [91500],
            'EndTime': [152500]
        }
        pd.DataFrame(general_param_data).to_excel(writer, sheet_name='GeneralParameter', index=False)

        # 3. LegParameter sheet
        leg_param_data = {
            'LegID': [1, 2],
            'Instrument': ['call', 'put'],
            'Transaction': ['sell', 'sell'],
            'StrikeMethod': ['atm', 'atm']
        }
        pd.DataFrame(leg_param_data).to_excel(writer, sheet_name='LegParameter', index=False)

        # 4. Metrics sheet
        metrics_data = self._calculate_tbs_metrics(portfolio_trans_df)
        pd.DataFrame([metrics_data]).to_excel(writer, sheet_name='Metrics', index=False)

        # 5. Max Profit and Loss sheet
        max_pl_data = self._calculate_tbs_max_pl(portfolio_trans_df)
        max_pl_data.to_excel(writer, sheet_name='Max Profit and Loss', index=False)

    def _create_tbs_portfolio_sheets(self, writer: pd.ExcelWriter,
                                   portfolio_trans_df: pd.DataFrame) -> None:
        """Create PORTFOLIO Trans and PORTFOLIO Results sheets"""

        # PORTFOLIO Trans sheet (32-column format)
        portfolio_trans_golden = self._convert_to_32_column_format(portfolio_trans_df)
        portfolio_trans_golden.to_excel(writer, sheet_name='PORTFOLIO Trans', index=False)

        # PORTFOLIO Results sheet (day-wise breakdown)
        portfolio_results = self._create_tbs_day_wise_results(portfolio_trans_df, 'PORTFOLIO')
        portfolio_results.to_excel(writer, sheet_name='PORTFOLIO Results', index=False)

    def _create_tbs_strategy_sheets(self, writer: pd.ExcelWriter,
                                  strategy_data: Dict[str, Any],
                                  portfolio_trans_df: pd.DataFrame) -> None:
        """Create strategy-specific sheets (H1-H80)"""

        # If no strategy data, create H1-H80 from portfolio data
        if not strategy_data or len(strategy_data) <= 1:
            # Create 80 H-strategies from portfolio data
            for i in range(1, 81):
                strategy_name = f"H{i}"

                # Create subset of portfolio data for this strategy
                strategy_trans_df = self._create_strategy_subset(portfolio_trans_df, i, 80)

                # Strategy Trans sheet
                strategy_trans_golden = self._convert_to_32_column_format(strategy_trans_df)
                strategy_trans_golden.to_excel(writer, sheet_name=f'{strategy_name} Trans', index=False)

                # Strategy Results sheet
                strategy_results = self._create_tbs_day_wise_results(strategy_trans_df, strategy_name)
                strategy_results.to_excel(writer, sheet_name=f'{strategy_name} Results', index=False)
        else:
            # Use actual strategy data
            for strategy_name, data in strategy_data.items():
                if strategy_name != 'PORTFOLIO':  # Skip portfolio, already handled
                    strategy_trans_df = data.get('transactions', pd.DataFrame())

                    if not strategy_trans_df.empty:
                        # Strategy Trans sheet
                        strategy_trans_golden = self._convert_to_32_column_format(strategy_trans_df)
                        strategy_trans_golden.to_excel(writer, sheet_name=f'{strategy_name} Trans', index=False)

                        # Strategy Results sheet
                        strategy_results = self._create_tbs_day_wise_results(strategy_trans_df, strategy_name)
                        strategy_results.to_excel(writer, sheet_name=f'{strategy_name} Results', index=False)

    def _create_strategy_subset(self, portfolio_trans_df: pd.DataFrame,
                              strategy_index: int, total_strategies: int) -> pd.DataFrame:
        """Create a subset of portfolio data for a specific strategy"""

        if portfolio_trans_df.empty:
            return pd.DataFrame()

        # Divide transactions among strategies
        total_trades = len(portfolio_trans_df)
        trades_per_strategy = max(1, total_trades // total_strategies)

        start_idx = (strategy_index - 1) * trades_per_strategy
        end_idx = min(start_idx + trades_per_strategy, total_trades)

        if strategy_index == total_strategies:  # Last strategy gets remaining trades
            end_idx = total_trades

        return portfolio_trans_df.iloc[start_idx:end_idx].copy()

    def _calculate_tbs_metrics(self, portfolio_trans_df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate TBS metrics from transaction data"""

        if portfolio_trans_df.empty:
            return {
                'total_trades': 0,
                'total_pnl': 0,
                'win_rate': 0,
                'avg_pnl': 0,
                'max_profit': 0,
                'max_loss': 0
            }

        pnl_col = 'net_pnl' if 'net_pnl' in portfolio_trans_df.columns else 'pnl'
        if pnl_col not in portfolio_trans_df.columns:
            # Find any numeric column that might be P&L
            numeric_cols = portfolio_trans_df.select_dtypes(include=[float, int]).columns
            pnl_col = numeric_cols[0] if len(numeric_cols) > 0 else None

        if pnl_col is None:
            return {'total_trades': len(portfolio_trans_df), 'total_pnl': 0, 'win_rate': 0, 'avg_pnl': 0, 'max_profit': 0, 'max_loss': 0}

        pnl_series = portfolio_trans_df[pnl_col]

        return {
            'total_trades': len(portfolio_trans_df),
            'total_pnl': pnl_series.sum(),
            'win_rate': (pnl_series > 0).sum() / len(pnl_series) * 100 if len(pnl_series) > 0 else 0,
            'avg_pnl': pnl_series.mean(),
            'max_profit': pnl_series.max(),
            'max_loss': pnl_series.min()
        }

    def _calculate_tbs_max_pl(self, portfolio_trans_df: pd.DataFrame) -> pd.DataFrame:
        """Calculate max profit/loss data for TBS"""

        if portfolio_trans_df.empty:
            return pd.DataFrame({
                'Date': [datetime.now().date()],
                'Max Profit': [0],
                'Max Loss': [0]
            })

        # Group by date and calculate max profit/loss
        date_col = None
        for col in ['entry_date', 'date', 'entry_datetime']:
            if col in portfolio_trans_df.columns:
                date_col = col
                break

        if date_col is None:
            return pd.DataFrame({
                'Date': [datetime.now().date()],
                'Max Profit': [0],
                'Max Loss': [0]
            })

        pnl_col = 'net_pnl' if 'net_pnl' in portfolio_trans_df.columns else 'pnl'
        if pnl_col not in portfolio_trans_df.columns:
            return pd.DataFrame({
                'Date': [datetime.now().date()],
                'Max Profit': [0],
                'Max Loss': [0]
            })

        # Convert date column to date
        portfolio_trans_df = portfolio_trans_df.copy()
        portfolio_trans_df['date_only'] = pd.to_datetime(portfolio_trans_df[date_col]).dt.date

        # Group by date
        daily_stats = portfolio_trans_df.groupby('date_only')[pnl_col].agg(['max', 'min']).reset_index()
        daily_stats.columns = ['Date', 'Max Profit', 'Max Loss']

        # Ensure non-negative max profit and non-positive max loss
        daily_stats['Max Profit'] = daily_stats['Max Profit'].clip(lower=0)
        daily_stats['Max Loss'] = daily_stats['Max Loss'].clip(upper=0)

        return daily_stats

    def _create_tbs_day_wise_results(self, df: pd.DataFrame, strategy_name: str) -> pd.DataFrame:
        """Create day-wise results for TBS format (Monday-Sunday breakdown)"""

        if df.empty:
            # Create empty structure
            return pd.DataFrame({
                'Year': [2024],
                'Monday': [0],
                'Tuesday': [0],
                'Wednesday': [0],
                'Thursday': [0],
                'Friday': [0],
                'Saturday': [0],
                'Total': [0]
            })

        # Find date and P&L columns
        date_col = None
        for col in ['entry_date', 'date', 'entry_datetime']:
            if col in df.columns:
                date_col = col
                break

        pnl_col = 'net_pnl' if 'net_pnl' in df.columns else 'pnl'
        if pnl_col not in df.columns:
            # Find any numeric column
            numeric_cols = df.select_dtypes(include=[float, int]).columns
            pnl_col = numeric_cols[0] if len(numeric_cols) > 0 else None

        if date_col is None or pnl_col is None:
            return pd.DataFrame({
                'Year': [2024],
                'Monday': [0],
                'Tuesday': [0],
                'Wednesday': [0],
                'Thursday': [0],
                'Friday': [0],
                'Saturday': [0],
                'Total': [0]
            })

        # Convert to datetime and extract weekday
        df = df.copy()
        df['datetime'] = pd.to_datetime(df[date_col])
        df['year'] = df['datetime'].dt.year
        df['weekday'] = df['datetime'].dt.day_name()

        # Group by year and weekday
        pivot_table = df.pivot_table(
            values=pnl_col,
            index='year',
            columns='weekday',
            aggfunc='sum',
            fill_value=0
        )

        # Ensure all weekdays are present
        weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
        for day in weekdays:
            if day not in pivot_table.columns:
                pivot_table[day] = 0

        # Reorder columns and add total
        pivot_table = pivot_table[weekdays]
        pivot_table['Total'] = pivot_table.sum(axis=1)

        # Reset index and add total row
        result_df = pivot_table.reset_index()
        result_df.rename(columns={'year': 'Year'}, inplace=True)

        # Add total row
        total_row = pd.DataFrame({
            'Year': ['Total'],
            **{day: [pivot_table[day].sum()] for day in weekdays},
            'Total': [pivot_table['Total'].sum()]
        })

        result_df = pd.concat([result_df, total_row], ignore_index=True)

        return result_df

    def _convert_to_32_column_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """Convert transaction DataFrame to 32-column golden format"""

        if df.empty:
            # Create empty 32-column structure
            columns = [
                'Portfolio Name', 'Strategy Name', 'ID', 'Entry Date', 'Enter On', 'Entry Day',
                'Exit Date', 'Exit at', 'Exit Day', 'Index', 'Expiry', 'Strike', 'CE/PE', 'Trade',
                'Qty', 'Entry at', 'Exit at', 'Points', 'Points After Slippage', 'PNL',
                'AfterSlippage', 'Taxes', 'Net PNL', 'Re-entry No', 'SL Re-entry No',
                'TGT Re-entry No', 'Reason', 'Strategy Entry No', 'Index At Entry',
                'Index At Exit', 'MaxProfit', 'MaxLoss'
            ]
            return pd.DataFrame(columns=columns)

        # Create a copy to avoid modifying original
        result_df = df.copy()

        # Define the 32-column mapping
        column_mapping = {
            'portfolio_name': 'Portfolio Name',
            'strategy': 'Strategy Name',
            'strategy_name': 'Strategy Name',
            'leg_id': 'ID',
            'id': 'ID',
            'entry_date': 'Entry Date',
            'entry_time': 'Enter On',
            'entry_day': 'Entry Day',
            'exit_date': 'Exit Date',
            'exit_time': 'Exit at',
            'exit_day': 'Exit Day',
            'symbol': 'Index',
            'index': 'Index',
            'expiry': 'Expiry',
            'strike': 'Strike',
            'instrument_type': 'CE/PE',
            'option_type': 'CE/PE',
            'side': 'Trade',
            'trade': 'Trade',
            'filled_quantity': 'Qty',
            'quantity': 'Qty',
            'qty': 'Qty',
            'entry_price': 'Entry at',
            'exit_price': 'Exit at',
            'points': 'Points',
            'pointsAfterSlippage': 'Points After Slippage',
            'bookedPnL': 'PNL',
            'pnl': 'PNL',
            'pnlAfterSlippage': 'AfterSlippage',
            'expenses': 'Taxes',
            'netPnlAfterExpenses': 'Net PNL',
            'net_pnl': 'Net PNL',
            're_entry_no': 'Re-entry No',
            'reason': 'Reason',
            'strategy_entry_number': 'Strategy Entry No',
            'stop_loss_entry_number': 'SL Re-entry No',
            'take_profit_entry_number': 'TGT Re-entry No',
            'index_entry_price': 'Index At Entry',
            'index_exit_price': 'Index At Exit',
            'max_profit': 'MaxProfit',
            'max_loss': 'MaxLoss'
        }

        # Apply column mapping
        for old_col, new_col in column_mapping.items():
            if old_col in result_df.columns:
                result_df = result_df.rename(columns={old_col: new_col})

        # Ensure all 32 columns exist
        required_columns = [
            'Portfolio Name', 'Strategy Name', 'ID', 'Entry Date', 'Enter On', 'Entry Day',
            'Exit Date', 'Exit at', 'Exit Day', 'Index', 'Expiry', 'Strike', 'CE/PE', 'Trade',
            'Qty', 'Entry at', 'Exit at', 'Points', 'Points After Slippage', 'PNL',
            'AfterSlippage', 'Taxes', 'Net PNL', 'Re-entry No', 'SL Re-entry No',
            'TGT Re-entry No', 'Reason', 'Strategy Entry No', 'Index At Entry',
            'Index At Exit', 'MaxProfit', 'MaxLoss'
        ]

        # Add missing columns with default values
        for col in required_columns:
            if col not in result_df.columns:
                if col in ['Entry Date', 'Exit Date']:
                    result_df[col] = '2024-01-03'
                elif col in ['Enter On', 'Exit at']:
                    result_df[col] = '09:15:00'
                elif col in ['Entry Day', 'Exit Day']:
                    result_df[col] = 'Wednesday'
                elif col == 'Index':
                    result_df[col] = 'NIFTY'
                elif col == 'Portfolio Name':
                    result_df[col] = 'TBS_Portfolio'
                elif col == 'Strategy Name':
                    result_df[col] = 'TBS_Strategy'
                elif col in ['ID', 'Qty', 'Re-entry No', 'SL Re-entry No', 'TGT Re-entry No', 'Strategy Entry No']:
                    result_df[col] = 1
                elif col in ['Entry at', 'Exit at', 'Points', 'Points After Slippage', 'PNL', 'AfterSlippage', 'Taxes', 'Net PNL', 'Index At Entry', 'Index At Exit', 'MaxProfit', 'MaxLoss']:
                    result_df[col] = 0.0
                else:
                    result_df[col] = ''

        # Reorder columns to match the required order
        result_df = result_df[required_columns]

        return result_df
