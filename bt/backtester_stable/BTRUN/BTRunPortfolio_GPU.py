#!/usr/bin/env python3

import os
import sys
import time
import json
import argparse
import logging
from datetime import datetime, date, timezone
from zoneinfo import ZoneInfo
from typing import Dict, List, Any, Optional, Tuple, Union
import concurrent.futures as _futures
from pathlib import Path

import pandas as pd
import numpy as np
from importlib import import_module as _imp

# THIS MUST BE THE ABSOLUTE FIRST THING IN THE SCRIPT
# Try to ensure the package structure is recognized correctly from the start.
_SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
_PROJECT_ROOT = os.path.abspath(os.path.join(_SCRIPT_DIR, '../..')) 
if _PROJECT_ROOT not in sys.path:
    sys.path.insert(0, _PROJECT_ROOT)
# Also ensure the BTRUN directory itself is in path if running scripts directly from there
# for relative imports like '.models' to work if some module accidentally misses a level.
if _SCRIPT_DIR not in sys.path:
    sys.path.insert(1, _SCRIPT_DIR) # Insert after project root

"""
Portfolio Backtesting with GPU Acceleration (HeavyDB native)

This script runs portfolio backtests with GPU acceleration and **HeavyDB** as
the data-store.  It is functionally identical to the prior
`Old_BTRunPortfolio_GPU_old.py`, but hard-coded Excel paths have been replaced
by configuration-driven ones so users can relocate their input workbooks
without touching code.

Input workbook (from `config.INPUT_FILE_FOLDER/config.PORTFOLIO_LEGACY_FILE_PATH`)
  • Sheet `PortfolioSetting`
  • Sheet `StrategySetting`

Optional workbook (`config.TBS_MULTI_LEGS_FILE_PATH`) is loaded if present.

Output workbook (under the `Trades/` folder) still contains exactly
  • `PortfolioParameter`, `GeneralParameter`, `LegParameter`

HeavyDB connections must be obtained through `heavydb_helpers.get_connection()`.
"""

# ─────────────────────────────────────────────────────────────────────────────
# BTRUN-package imports (absolute from project root)
# ─────────────────────────────────────────────────────────────────────────────
# These imports should now work because sys.path was adjusted above.
from backtester_stable.BTRUN.core import config, utils, runtime
from backtester_stable.BTRUN.core import gpu_helpers, io, stats
from backtester_stable.BTRUN.excel_parser import portfolio_parser as modern_portfolio_parser
from backtester_stable.BTRUN.models.portfolio import PortfolioModel
from backtester_stable.BTRUN.models.strategy import StrategyModel
from backtester_stable.BTRUN.models.leg import LegModel
from backtester_stable.BTRUN.models.common import ExpiryRule, OptionType, StrikeRule, TransactionType
from backtester_stable.BTRUN.dal.heavydb import HeavyDBConnection
from backtester_stable.BTRUN.core.runtime import save_backtest_results

# Import get_connection directly from the new module
from backtester_stable.BTRUN.core.heavydb_connection import get_connection

# ─────────────────────────────────────────────────────────────────────────────
# GPU Optimization imports (Phase 2.B.4)
# ─────────────────────────────────────────────────────────────────────────────
GPU_OPTIMIZATION_AVAILABLE = False
try:
    from common.gpu_worker_pool import DynamicGPUWorkerPool, GPUWorkerConfig, GPUMemoryMonitor
    from common.gpu_query_optimizer import HeavyDBQueryOptimizer, QueryBatcher
    GPU_OPTIMIZATION_AVAILABLE = True
except ImportError:
    # Will log this warning after logger is initialized
    pass

# REMOVE THE TRY-EXCEPT BLOCK FOR heavydb_helpers
# We determine HeavyDB availability by successful import of new DAL components like get_connection
HEAVYDB_AVAILABLE = True # Assume True if new DAL imports work

# Define IST at the module level
IST = ZoneInfo("Asia/Kolkata")

class ISTFormatter(logging.Formatter):
    def formatTime(self, record, datefmt=None):
        # Convert record.created (UTC timestamp) to a datetime object in IST
        dt = datetime.fromtimestamp(record.created, tz=IST)
        if datefmt:
            s = dt.strftime(datefmt)
        else:
            # Default format similar to logging's default for asctime
            s = dt.strftime("%Y-%m-%d %H:%M:%S") + f",{int(record.msecs):03d}"
        return s

# Initial basic config for root logger to handle early messages if any
# and to suppress "No handlers could be found for logger X" if other modules log early.
if __name__ == "__main__":
    # Try to suppress the specific root logger error if it's just noise
    # by ensuring the root logger has a handler and a level that might not show this specific error.
    # This is a diagnostic step.
    _root_logger_check = logging.getLogger() # Get the root logger
    if not _root_logger_check.hasHandlers():
        # print("DEBUG: Root logger has no handlers, adding NullHandler.", flush=True)
        _pre_config_handler = logging.StreamHandler(sys.stdout) # Or NullHandler
        _pre_config_handler.setFormatter(logging.Formatter("%(levelname)s:%(name)s:%(message)s"))
        _root_logger_check.addHandler(_pre_config_handler)
        _root_logger_check.setLevel(logging.WARNING) # Start with WARNING to avoid too much noise

logger = logging.getLogger(__name__)

# Initial log messages, now after basicConfig
logger.debug("Starting BTRunPortfolio_GPU.py – HeavyDB version")
logger.debug("CWD: %s", os.getcwd())
logger.debug("Python path: %s", sys.path)
logger.debug("HeavyDB host: %s, DB: %s", config.HEAVYDB_HOST, config.HEAVYDB_DATABASE)
logger.debug("GPU enabled: %s", config.GPU_ENABLED)

# ─────────────────────────────────────────────────────────────────────────────
# CLI handling (identical to old script)
# ─────────────────────────────────────────────────────────────────────────────

def parse_args():
    parser = argparse.ArgumentParser(
        description="Run Portfolio backtests with GPU acceleration (HeavyDB native)"
    )
    parser.add_argument("--config", "-c", type=str, help="Path to JSON config file for portfolio backtest")
    parser.add_argument("--legacy-excel", "-l", action="store_true",
                        help="Use Excel input (PortfolioSetting & StrategySetting)")
    parser.add_argument("--strategies", "-s", type=str, nargs="+", help="Restrict to these strategy IDs")
    parser.add_argument("--start-date", type=str, help="YYMMDD start date override")
    parser.add_argument("--end-date", type=str, help="YYMMDD end date override")
    parser.add_argument("--output-dir", "-o", type=str, default="Trades", help="Output folder (default: Trades)")
    parser.add_argument("--portfolio-name", "-n", type=str, help="Portfolio name override")
    parser.add_argument("--slippage", type=float, default=0.1, help="Slippage percent")
    parser.add_argument("--capital", type=float, help="Initial capital override")
    parser.add_argument("--margin-multiplier", type=float, default=1.0, help="Margin multiplier")
    parser.add_argument("--no-json", action="store_true", help="Disable JSON output")
    parser.add_argument("--no-excel", action="store_true", help="Disable Excel output")
    parser.add_argument("--no-charts", action="store_true", help="Disable chart generation")
    parser.add_argument("--cpu-only", action="store_true", help="Force CPU-only mode")
    parser.add_argument("--workers", "-w", type=str, default="1", help="Number of parallel GPU workers (default: 1, use 'auto' for optimal)")
    parser.add_argument("--retry-cpu", action="store_true", help="Retry failed slices under CPU-only mode")
    parser.add_argument("--merge-output", action="store_true", help="Copy output files to Merge folder (legacy)")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    parser.add_argument("--profile", action="store_true", help="Capture per-portfolio runtime timings")
    parser.add_argument("--portfolio-excel", "-p", type=str, required=True,
                      help="Path to portfolio Excel file")
    parser.add_argument("--output-path", type=str, required=True,
                      help="Path to save output Excel file")
    # GPU Optimization arguments (Phase 2.B.4)
    parser.add_argument("--gpu-workers", type=int, default=2, 
                      help="(DEPRECATED) Number of GPU workers for parallel processing (default: 2)")
    parser.add_argument("--cpu-workers", type=int, default=2,
                      help="(DEPRECATED) Number of CPU workers for fallback (default: 2)")
    parser.add_argument("--batch-days", type=int, default=7,
                      help="Days per batch for date range splitting (default: 7)")
    parser.add_argument("--gpu-threshold", type=float, default=0.7,
                      help="GPU memory threshold for CPU fallback (default: 0.7)")
    parser.add_argument("--use-gpu-optimization", action="store_true",
                      help="(DEPRECATED) Enable GPU worker pool optimization (Phase 2.B)")
    args = parser.parse_args()

    # --- Worker count normalization ---
    # Always resolve args.workers to an integer
    if args.workers == 'auto':
        try:
            from bt.common.gpu_worker_pool import get_optimal_workers
            args.workers = get_optimal_workers()
            print(f"Auto-detected optimal workers: {args.workers}")
        except Exception:
            print("Failed to auto-detect optimal workers, defaulting to 1")
            args.workers = 1
    else:
        try:
            args.workers = int(args.workers)
        except Exception:
            print(f"Invalid workers value: {args.workers}, using 1")
            args.workers = 1
    if args.workers < 1:
        args.workers = 1

    # --- GPU optimization mode selection ---
    # Use GPU optimization if available, not cpu-only, and workers > 1
    args.gpu_optimization = False
    if GPU_OPTIMIZATION_AVAILABLE and not args.cpu_only and args.workers > 1:
        args.gpu_optimization = True
    # For backward compatibility, allow --use-gpu-optimization to force it (but warn if workers==1)
    if GPU_OPTIMIZATION_AVAILABLE and args.use_gpu_optimization and not args.cpu_only:
        if args.workers == 1:
            print("Warning: --use-gpu-optimization specified but --workers=1; running sequentially.")
        else:
            args.gpu_optimization = True
    # If not available, always sequential
    if not GPU_OPTIMIZATION_AVAILABLE:
        args.gpu_optimization = False
    # If cpu-only, always sequential
    if args.cpu_only:
        args.gpu_optimization = False

    if not args.config and not args.legacy_excel:
        args.legacy_excel = True
    return args

# ─────────────────────────────────────────────────────────────────────────────
# Config loaders (JSON & Excel)
# ─────────────────────────────────────────────────────────────────────────────

def load_json_config(path: str) -> Dict[str, Any]:
    logger.debug("Loading JSON config: %s", path)
    try:
        with open(path, "r") as fp:
            return json.load(fp)
    except (FileNotFoundError, json.JSONDecodeError) as exc:
        logger.error("Failed to load JSON config: %s", exc)
        sys.exit(1)


def load_legacy_portfolio_config() -> Dict[str, Dict[str, pd.DataFrame]]:
    """Replaces the original version to honour config helper look-ups.

    Reads `PortfolioSetting` & `StrategySetting` from the workbook located via
    `config.get_portfolio_file(config.PORTFOLIO_LEGACY_FILE_PATH)`.  The
    companion *multi-legs* workbook is searched in the same directory.
    """
    input_portfolio_path = config.get_portfolio_file(config.PORTFOLIO_LEGACY_FILE_PATH)

    if not os.path.exists(input_portfolio_path):
        logger.error("Portfolio input Excel not found: %s", input_portfolio_path)
        raise SystemExit(1)

    try:
        logger.info("Reading PortfolioSetting & StrategySetting from %s", input_portfolio_path)
        portfolio_setting_df = pd.read_excel(input_portfolio_path, sheet_name="PortfolioSetting")
        strategy_setting_df = pd.read_excel(input_portfolio_path, sheet_name="StrategySetting")
        logger.debug("Loaded %d PortfolioSetting rows, %d StrategySetting rows",
                     len(portfolio_setting_df), len(strategy_setting_df))

        return {
            "PortfolioSetting": portfolio_setting_df,
            "StrategySetting": strategy_setting_df,
            "TbsMultiLegs": None,  # kept for backward compatibility
        }
    except Exception as exc:
        logger.error("Error reading Excel input: %s", exc, exc_info=True)
        raise SystemExit(1)

# ---------------------------------------------------------------------------
# build_legacy_portfolio_request is now OBSOLETE due to modern parser integration
# The functionality is replaced by directly using modern_portfolio_parser.parse_portfolio_excel
# and then constructing the payload for the worker.
# ---------------------------------------------------------------------------
# def _row_build_request(portfolio_row: pd.Series, strategy_setting_df: pd.DataFrame) -> Dict[str, Any]:
#    (... entire old function commented out or removed ...)

# ---------------------------------------------------------------------------
# Picklable worker function for multiprocessing
# ---------------------------------------------------------------------------

def _backtest_worker(param_tuple):
    """Worker process for running backtests."""
    import os as _os
    import time as _time
    import logging as _log
    from datetime import datetime
    from pathlib import Path
    # Convert relative imports to absolute
    from backtester_stable.BTRUN.core import gpu_helpers as _gh
    from backtester_stable.BTRUN.core import config
    from backtester_stable.BTRUN.excel_parser import portfolio_parser as modern_portfolio_parser
    
    (request, capital, out_suffix), settings = param_tuple
    
    # Get portfolio name from request for logging
    portfolio_name = request.get('portfolio_name', 'Unknown')
    
    output_base = _os.path.join(settings['output_dir'], out_suffix)
    
    # NEW: Extract Excel paths from portfolio model
    portfolio_excel_path = None
    strategy_excel_paths = {}
    
    # Check if we have portfolio model to extract paths from
    if 'portfolio_model' in request and request['portfolio_model']:
        portfolio_model = request['portfolio_model']
        
        # Get portfolio Excel path from settings or config
        if hasattr(config, 'PORTFOLIO_EXCEL_PATH_CACHE'):
            portfolio_excel_path = config.PORTFOLIO_EXCEL_PATH_CACHE
        elif hasattr(config, 'INPUT_FILE_FOLDER') and hasattr(config, 'PORTFOLIO_LEGACY_FILE_PATH'):
            portfolio_excel_path = _os.path.join(config.INPUT_FILE_FOLDER, config.PORTFOLIO_LEGACY_FILE_PATH)
        
        # Extract strategy Excel paths from portfolio model
        if 'strategies' in portfolio_model:
            for strategy in portfolio_model['strategies']:
                strategy_name = strategy.get('strategy_name', '')
                if strategy_name and 'strategy_excel_path' in strategy:
                    strategy_excel_paths[strategy_name] = strategy['strategy_excel_path']

    def _run(cpu_only: bool = False):
        if cpu_only:
            _gh.force_cpu_mode()
        # Convert relative import to absolute
        from backtester_stable.BTRUN.core import runtime
        try:
            # Always use run_full_backtest for all runs
            result = runtime.run_full_backtest(
                bt_params=request,
                output_base_path=output_base,
                slippage_percent=settings['slippage'],
                initial_capital=capital,
                save_json=not settings['no_json'],
                save_excel=not settings['no_excel'],
                portfolio_excel_path=portfolio_excel_path,  # NEW: Pass portfolio Excel path
                strategy_excel_paths=strategy_excel_paths   # NEW: Pass strategy Excel paths
            )
            return result
        except Exception as e:
            _log.getLogger(__name__).error("Error in _run: %s", e)
            return {"success": False, "error": str(e)}

    start = _time.time()
    try:
        result = _run(False)
    except Exception as exc:
        _log.getLogger(__name__).error("GPU worker failed for %s: %s", request.get('portfolio_name'), exc)
        if settings['retry_cpu']:
            try:
                result = _run(True)
            except Exception as exc2:
                _log.getLogger(__name__).error("CPU retry failed for %s: %s", request.get('portfolio_name'), exc2)
                result = {'success': False}
        else:
            result = {'success': False}

    if settings['profile'] and isinstance(result, dict):
        result.setdefault('profiling', {})['elapsed_secs'] = _time.time() - start
        try:
            mem = _gh.get_gpu_memory_info()
            if mem:
                result['profiling']['gpu_mem_free_mb'] = mem.get('free_gpu_memory_mb')
        except Exception:
            pass

    return result

def _gpu_optimized_backtest_worker(batch_config: Dict) -> Dict:
    """GPU-optimized worker function for batch processing.
    
    This function runs in a worker process with its own HeavyDB connection.
    """
    import time
    import logging
    from datetime import datetime
    # Use absolute import for worker function
    from backtester_stable.BTRUN.models.portfolio import PortfolioModel
    
    _log = logging.getLogger(__name__)
    worker_start_time = time.time() # Renamed to avoid conflict
    
    # Extract configuration
    portfolio_model_dict = batch_config['portfolio_model']
    # Attempt to deserialize back to PortfolioModel if runtime expects it
    # For now, assuming runtime.run_full_backtest or its internals can handle the dict.
    # If not, uncomment and adjust:
    # portfolio_model_obj = PortfolioModel(**portfolio_model_dict)
    
    start_date_str = batch_config['start_date']
    end_date_str = batch_config['end_date']
    portfolio_name_override = batch_config['portfolio_name_override']
    
    _log.info(f"Processing batch: {batch_config['name']} ({start_date_str} to {end_date_str}) for original portfolio '{portfolio_name_override}'")
    
    try:
        # Run the backtest for this date range
        from backtester_stable.BTRUN.core import runtime
        
        # Create request for runtime
        # runtime.run_full_backtest expects portfolio_model to be a PortfolioModel instance if strict, or a dict it can parse.
        # Let's assume for now it handles the dict (portfolio_model_dict).
        request = {
            "portfolio_model": portfolio_model_dict, 
            "portfolio_name": portfolio_name_override, # Use the override name for consistency in runtime if needed
            "start_date": start_date_str,
            "end_date": end_date_str,
        }
        
        # Run backtest
        result = runtime.run_full_backtest(
            bt_params=request,
            output_base_path="",  # Will be combined later
            slippage_percent=batch_config.get('slippage', 0.1),
            initial_capital=batch_config.get('capital', 100000),
            save_json=False,  # Don't save intermediate results
            save_excel=False
        )
        
        # Add identifying information to the result for combining
        result['batch_name'] = batch_config['name']
        result['portfolio_name_override'] = portfolio_name_override
        result['batch_duration'] = time.time() - worker_start_time
        result['date_range'] = (start_date_str, end_date_str)
        # Ensure success flag is present
        if 'success' not in result:
            result['success'] = True # Assume success if no error thrown by runtime
        
        _log.info(f"Worker completed batch: {batch_config['name']} for portfolio '{portfolio_name_override}' in {time.time() - worker_start_time:.2f}s")
        return result
        
    except Exception as e:
        _log.error(f"Error processing batch {batch_config['name']} for portfolio {portfolio_name_override}: {e}", exc_info=True)
        return {
            'success': False,
            'error': str(e),
            'batch_name': batch_config.get('name', 'unknown'),
            'portfolio_name_override': portfolio_name_override,
            'batch_duration': time.time() - worker_start_time
        }

def _combine_batch_results(batch_results: List[Dict]) -> Dict[str, Dict]:
    """Combine results from multiple date-batches, grouping by original portfolio name."""
    if not batch_results:
        # If main submits an empty list (e.g. no portfolios matched CLI args)
        return {}

    grouped_by_portfolio: Dict[str, List[Dict]] = {}
    for res in batch_results:
        if res is None: # Handle cases where a worker might return None due to catastrophic error
            logger.warning("Received a None result from a worker, skipping.")
            continue
        portfolio_name = res.get('portfolio_name_override', 'unknown_portfolio_results')
        if portfolio_name not in grouped_by_portfolio:
            grouped_by_portfolio[portfolio_name] = []
        grouped_by_portfolio[portfolio_name].append(res)

    final_combined_results: Dict[str, Dict] = {}

    for portfolio_name, results_for_portfolio in grouped_by_portfolio.items():
        logger.info(f"Combining {len(results_for_portfolio)} batch(es) for portfolio: {portfolio_name}")
        
        successful_batches = [r for r in results_for_portfolio if r and r.get('success')]
        if not successful_batches:
            logger.error(f"All batches failed for portfolio: {portfolio_name}. First error: {results_for_portfolio[0].get('error') if results_for_portfolio else 'N/A'}")
            final_combined_results[portfolio_name] = {
                'success': False, 
                'error': f'All {len(results_for_portfolio)} batches failed for {portfolio_name}',
                'portfolio_name': portfolio_name,
                'trades_df': pd.DataFrame(),
                'metrics_df': pd.DataFrame(),
                'daily_pnl': pd.DataFrame(),
                'monthly_pnl': pd.DataFrame()
            }
            continue

        # Initialize combined data structures from the first successful batch
        # Ensure to deep copy DataFrames to avoid modifying originals when concatenating
        base_result = successful_batches[0]
        combined_trades_df = base_result.get('trades_df', pd.DataFrame()).copy()
        combined_daily_pnl_df = base_result.get('daily_pnl', pd.DataFrame()).copy()
        # metrics_df is usually recalculated, not simply concatenated.
        # We'll collect all trades and then recalculate overall metrics.

        total_batch_duration = base_result.get('batch_duration', 0)
        min_batch_start_date = pd.to_datetime(base_result.get('date_range', [None, None])[0])
        max_batch_end_date = pd.to_datetime(base_result.get('date_range', [None, None])[1])

        for i in range(1, len(successful_batches)):
            next_batch_result = successful_batches[i]
            if not next_batch_result.get('trades_df', pd.DataFrame()).empty:
                combined_trades_df = pd.concat([combined_trades_df, next_batch_result['trades_df'].copy()], ignore_index=True)
            if not next_batch_result.get('daily_pnl', pd.DataFrame()).empty:
                combined_daily_pnl_df = pd.concat([combined_daily_pnl_df, next_batch_result['daily_pnl'].copy()], ignore_index=True)
            
            total_batch_duration += next_batch_result.get('batch_duration', 0)
            current_batch_start = pd.to_datetime(next_batch_result.get('date_range', [None,None])[0])
            current_batch_end = pd.to_datetime(next_batch_result.get('date_range', [None,None])[1])
            if current_batch_start and (not min_batch_start_date or current_batch_start < min_batch_start_date):
                min_batch_start_date = current_batch_start
            if current_batch_end and (not max_batch_end_date or current_batch_end > max_batch_end_date):
                max_batch_end_date = current_batch_end

        # Sort DataFrames by date/time if necessary
        if not combined_trades_df.empty and 'EntryDateTime' in combined_trades_df.columns:
            combined_trades_df = combined_trades_df.sort_values(by='EntryDateTime').reset_index(drop=True)
        if not combined_daily_pnl_df.empty and 'Date' in combined_daily_pnl_df.columns:
            combined_daily_pnl_df = combined_daily_pnl_df.sort_values(by='Date').reset_index(drop=True)
            # Aggregate daily_pnl if there are overlaps due to batching (shouldn't happen with proper batching)
            combined_daily_pnl_df = combined_daily_pnl_df.groupby('Date').sum().reset_index()

        # Recalculate overall metrics and monthly P&L from the combined_trades_df
        # Assuming `stats.calculate_metrics` and `stats.calculate_monthly_pnl` exist and take trades_df
        final_metrics_df = pd.DataFrame() # Placeholder
        final_monthly_pnl_df = pd.DataFrame() # Placeholder
        
        # Capital from the first successful batch (or should be from portfolio_model directly)
        # For now, let's take it from the first batch config, assuming it's consistent
        initial_capital = successful_batches[0].get('initial_capital', 100000) 
        # This might be problematic if runtime.run_full_backtest doesn't return initial_capital.
        # It's better if main passes initial_capital for the portfolio to save_backtest_results

        if not combined_trades_df.empty:
            try:
                # Use get_backtest_stats to calculate metrics
                metrics_dict = stats.get_backtest_stats(combined_trades_df, initial_capital=initial_capital)
                # Convert metrics dict to DataFrame format expected by Excel writer
                metrics_list = []
                for key, value in metrics_dict.items():
                    metrics_list.append({
                        'Particulars': key,
                        'Combined': value,
                        portfolio_name: value
                    })
                final_metrics_df = pd.DataFrame(metrics_list)
                final_monthly_pnl_df = stats.get_month_wise_stats(combined_trades_df)
            except Exception as e:
                logger.error(f"Error recalculating stats for {portfolio_name}: {e}", exc_info=True)
                # Keep placeholder empty DFs

        # Store the fully combined result for this portfolio
        final_combined_results[portfolio_name] = {
            'success': True,
            'portfolio_name': portfolio_name,
            'trades_df': combined_trades_df,
            'daily_pnl': combined_daily_pnl_df,
            'metrics_df': final_metrics_df,
            'monthly_pnl': final_monthly_pnl_df,
            # Add keys expected by write_results
            'transaction_dfs': {'portfolio': combined_trades_df},
            'day_stats': {'portfolio': combined_daily_pnl_df},
            'month_stats': {'portfolio': final_monthly_pnl_df},
            'margin_stats': {},
            'daily_max_pl_df': pd.DataFrame(),
            # Metadata
            'total_batch_duration': total_batch_duration,
            'overall_start_date': min_batch_start_date.strftime('%Y-%m-%d') if min_batch_start_date else None,
            'overall_end_date': max_batch_end_date.strftime('%Y-%m-%d') if max_batch_end_date else None,
            'number_of_successful_batches': len(successful_batches),
            'number_of_total_batches': len(results_for_portfolio)
        }
        if len(successful_batches) < len(results_for_portfolio):
            final_combined_results[portfolio_name]['warning'] = f"{len(results_for_portfolio) - len(successful_batches)} batch(es) failed."

    return final_combined_results

def save_backtest_results(combined_results: Dict[str, Dict], output_path: str, parsed_portfolio_models: Dict[str, PortfolioModel], portfolio_excel_path: Optional[str] = None):
    """Save the combined backtest results to an Excel file."""
    logger.info(f"Attempting to save combined backtest results for {len(combined_results)} portfolio(s) to {output_path}")

    if not combined_results:
        logger.warning("No combined results to save.")
        # Create an empty Excel or a file with a warning if desired
        try:
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                df_empty = pd.DataFrame(["No portfolios were processed or all failed."])
                df_empty.to_excel(writer, sheet_name='Warning', index=False, header=False)
            logger.info(f"Empty/Warning Excel file saved to {output_path}")
        except Exception as e:
            logger.error(f"Could not write warning Excel: {e}", exc_info=True)
        return

    try:
        # The io.write_results_to_excel expects a list of result dicts, 
        # where each dict is for one portfolio (already combined from its batches).
        # It also needs the portfolio_models for writing parameter sheets.
        
        results_list_for_excel = [] # List of combined result dicts for each portfolio
        portfolio_models_for_excel = {} # Dict of PortfolioModels for parameter sheets
        
        for portfolio_name, single_portfolio_combined_result in combined_results.items():
            if single_portfolio_combined_result.get('success', False):
                results_list_for_excel.append(single_portfolio_combined_result)
                if portfolio_name in parsed_portfolio_models:
                    portfolio_models_for_excel[portfolio_name] = parsed_portfolio_models[portfolio_name]
                else:
                    logger.warning(f"PortfolioModel for '{portfolio_name}' not found in parsed_portfolio_models. Parameter sheets might be incomplete.")
            else:
                logger.error(f"Portfolio '{portfolio_name}' failed, results not included in Excel. Error: {single_portfolio_combined_result.get('error')}")

        if not results_list_for_excel:
            logger.error("All portfolios failed or produced no successful results to save.")
            # Similar warning Excel as above
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                df_empty = pd.DataFrame(["All portfolios failed or produced no results."])
                df_empty.to_excel(writer, sheet_name='Error', index=False, header=False)
            return

        # Combine all results into a single dict for write_results
        # Take the first result as base (assuming single portfolio for now)
        if results_list_for_excel:
            combined_result_for_write = results_list_for_excel[0]
            # Add portfolio model
            portfolio_name = combined_result_for_write.get('portfolio_name', 'Unknown')
            if portfolio_name in portfolio_models_for_excel:
                combined_result_for_write['portfolio_model'] = portfolio_models_for_excel[portfolio_name]
            
            # Call write_results with combined result
            # Use TBS golden format output if enabled
            if os.environ.get('USE_GOLDEN_FORMAT', 'true').lower() == 'true':
                golden_format_success = _generate_tbs_golden_format_output(
                    combined_result_for_write,
                    output_path,
                    portfolio_excel_path,
                    parsed_portfolio_models
                )

                if not golden_format_success:
                    # Fallback to legacy golden format
                    logger.warning("TBS golden format generation failed, using legacy golden format")
                    from backtester_stable.BTRUN.utils.io_golden import write_results_golden
                    write_results_golden(
                        combined_result=combined_result_for_write,
                        output_path=output_path,
                        use_legacy_format=True,
                        portfolio_excel_path=portfolio_excel_path,
                        strategy_excel_paths=None   # Can add if needed
                    )
            else:
                io.write_results(
                    combined_result=combined_result_for_write,
                    output_path=output_path,
                    use_legacy_format=True,
                    portfolio_excel_path=portfolio_excel_path,
                    strategy_excel_paths=None   # Can add if needed
                )
        logger.info(f"Successfully saved backtest results to {output_path}")

    except Exception as e:
        logger.error(f"Error saving backtest results to Excel: {e}", exc_info=True)
        # Fallback: try to save at least the trades_df if possible, for debugging
        try:
            for portfolio_name, res_data in combined_results.items():
                if res_data.get('success') and not res_data.get('trades_df', pd.DataFrame()).empty:
                    fallback_path = Path(output_path).parent / f"{Path(output_path).stem}_{portfolio_name}_trades_fallback.csv"
                    res_data['trades_df'].to_csv(fallback_path, index=False)
                    logger.info(f"Saved fallback trades for {portfolio_name} to {fallback_path}")
        except Exception as fallback_e:
            logger.error(f"Error during fallback save: {fallback_e}", exc_info=True)


def _generate_tbs_golden_format_output(combined_result: Dict[str, Any],
                                     output_path: str,
                                     portfolio_excel_path: str,
                                     parsed_portfolio_models: Dict[str, Any]) -> bool:
    """
    Generate TBS strategy output in exact multi-strategy golden format (167 sheets)

    Args:
        combined_result: Combined backtest results
        output_path: Output file path
        portfolio_excel_path: Portfolio Excel file path
        parsed_portfolio_models: Parsed portfolio models

    Returns:
        bool: Success status
    """
    try:
        logger.info(f"🎯 Generating TBS multi-strategy golden format: {output_path}")

        # Import golden format generator
        import sys
        sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))
        from golden_format_excel_generator import GoldenFormatExcelGenerator

        generator = GoldenFormatExcelGenerator()

        # Extract transaction data from combined result
        transaction_dfs = combined_result.get('transaction_dfs', {})
        if not transaction_dfs:
            logger.warning("No transaction data found in combined result")
            return False

        # Get main portfolio transactions
        portfolio_trans_df = transaction_dfs.get('portfolio', pd.DataFrame())
        if portfolio_trans_df.empty:
            logger.warning("No portfolio transactions found in combined result")
            return False

        # Prepare data for TBS multi-strategy golden format
        tbs_settings = _prepare_tbs_settings_from_result(portfolio_excel_path, parsed_portfolio_models)
        strategy_data = _prepare_tbs_strategy_data_from_result(combined_result, transaction_dfs)

        # Generate TBS multi-strategy golden format
        success = generator.generate_tbs_multi_strategy_golden_format(
            portfolio_trans_df=portfolio_trans_df,
            strategy_data=strategy_data,
            tbs_settings=tbs_settings,
            output_path=output_path
        )

        if success:
            logger.info(f"✅ TBS multi-strategy golden format generated successfully")
        else:
            logger.error(f"❌ TBS multi-strategy golden format generation failed")

        return success

    except Exception as e:
        logger.error(f"Error generating TBS multi-strategy golden format: {e}")
        return False


def _prepare_tbs_settings_from_result(portfolio_excel_path: str, parsed_portfolio_models: Dict[str, Any]) -> Dict[str, Any]:
    """Prepare TBS settings data from result"""
    return {
        'portfolio_file': portfolio_excel_path,
        'strategy_type': 'TBS',
        'symbol': 'NIFTY',
        'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'total_portfolios': len(parsed_portfolio_models),
        'portfolio_names': list(parsed_portfolio_models.keys())
    }


def _prepare_tbs_strategy_data_from_result(combined_result: Dict[str, Any], transaction_dfs: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
    """Prepare TBS strategy data from result"""
    try:
        strategy_data = {}

        # Extract strategy information from transaction data
        for strategy_name, trans_df in transaction_dfs.items():
            if not trans_df.empty:
                # Create strategy-specific data
                strategy_data[strategy_name] = {
                    'transactions': trans_df,
                    'total_trades': len(trans_df),
                    'total_pnl': trans_df.get('net_pnl', trans_df.get('pnl', pd.Series([0]))).sum(),
                    'strategy_type': 'TBS'
                }

        # If we have multiple strategies, create H-pattern naming
        if len(strategy_data) > 1:
            h_strategy_data = {}
            h_index = 1

            for strategy_name, data in strategy_data.items():
                if strategy_name != 'portfolio':  # Keep portfolio as is
                    h_name = f"H{h_index}"
                    h_strategy_data[h_name] = data
                    h_index += 1
                else:
                    h_strategy_data['PORTFOLIO'] = data

            strategy_data = h_strategy_data

        return strategy_data

    except Exception as e:
        logger.error(f"Error preparing TBS strategy data: {e}")
        return {}


# Load original implementation immediately so it is available for patching later
# _old = _imp('bt.backtester_stable.BTRUN.Old_BTRunPortfolio_GPU_old')

# Apply monkey patches
# _old.build_legacy_portfolio_request = _row_build_request  # type: ignore
# _old.run_portfolio_backtest = _row_run_backtest  # type: ignore
# _old.load_legacy_portfolio_config = load_legacy_portfolio_config  # patch

# Define replacement main that supports extended flags

def main():
    args = parse_args()

    # --- Logging Setup --- moved into main for args.debug and IST --- 
    app_log_level = logging.DEBUG if args.debug else logging.INFO

    # Create logs directory using _SCRIPT_DIR defined at the top of the file
    log_dir = os.path.join(_SCRIPT_DIR, 'logs')
    os.makedirs(log_dir, exist_ok=True)
    
    # Generate log filename with IST timestamp
    ist_now_for_filename = datetime.now(IST)
    log_file_name = f"portfolio_backtest_{ist_now_for_filename.strftime('%Y%m%d_%H%M%S_%Z%z')}.log"
    log_file_path = os.path.join(log_dir, log_file_name)

    # Create handlers
    stream_handler = logging.StreamHandler(sys.stdout)
    file_handler = logging.FileHandler(log_file_path)

    # Create IST Formatter for log messages
    ist_formatter = ISTFormatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s", datefmt="%Y-%m-%d %H:%M:%S,%f")
    # Note: The datefmt in ISTFormatter class itself will be used by default for asctime if not specified here.
    # For consistency, it's better to rely on the class default or specify it like above. Using default for now.
    ist_formatter_for_display = ISTFormatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s") 


    stream_handler.setFormatter(ist_formatter_for_display)
    file_handler.setFormatter(ist_formatter_for_display) # Use the same formatter for file

    # Configure the root logger
    root_log_instance = logging.getLogger()
    # Remove any handlers potentially set by basicConfig in other modules or the initial check
    for handler in root_log_instance.handlers[:]:
        root_log_instance.removeHandler(handler)
    
    root_log_instance.addHandler(stream_handler)
    root_log_instance.addHandler(file_handler)
    root_log_instance.setLevel(app_log_level)
    # --- End of Logging Setup ---

    # --- User feedback on execution mode ---
    if args.gpu_optimization:
        logger.info(f"Running in GPU-optimized parallel mode with {args.workers} workers.")
    else:
        logger.info("Running in sequential (single-worker) mode.")
    if args.cpu_only:
        logger.info("CPU-only mode forced by --cpu-only flag.")
    if not GPU_OPTIMIZATION_AVAILABLE:
        logger.info("GPU optimization is not available (missing dependencies or import error). Running sequentially.")

    if args.gpu_optimization:
        from common.gpu_worker_pool import DynamicGPUWorkerPool
        with DynamicGPUWorkerPool(max_workers=args.workers) as worker_pool:
            logger.info(f"Initialized GPU worker pool with {args.workers} workers")
            try:
                utils.run_necessary_functions_before_starting_bt()
                logger.info("Modern utils.run_necessary_functions_before_starting_bt() called.")

                logger.info("Using modern Excel parsing via excel_parser.portfolio_parser")
                main_portfolio_excel_path = args.portfolio_excel
                logger.info(f"Using portfolio Excel from CLI argument: {main_portfolio_excel_path}")
                config.INPUT_FILE_FOLDER = os.path.dirname(main_portfolio_excel_path)
                logger.info(f"Set config.INPUT_FILE_FOLDER to: {config.INPUT_FILE_FOLDER}")
                logger.info(f"Parsing main portfolio Excel: {main_portfolio_excel_path} using modern parser. INPUT_FILE_FOLDER is currently: {config.INPUT_FILE_FOLDER}")
                
                parsed_portfolio_models = modern_portfolio_parser.parse_portfolio_excel(main_portfolio_excel_path)
                
                tasks = []
                for portfolio_name, portfolio_model in parsed_portfolio_models.items():
                    if args.portfolio_name and portfolio_name.upper() != args.portfolio_name.upper():
                        logger.info(f"Skipping portfolio '{portfolio_name}' as it does not match requested CLI --portfolio-name '{args.portfolio_name}'.")
                        continue

                    start_date = portfolio_model.start_date
                    end_date = portfolio_model.end_date
                    
                    date_ranges = []
                    current_date = start_date
                    while current_date <= end_date:
                        batch_end = min(current_date + pd.Timedelta(days=args.batch_days -1), end_date) # batch_days includes start day
                        date_ranges.append((current_date, batch_end))
                        current_date = batch_end + pd.Timedelta(days=1)
                    
                    logger.info(f"Portfolio '{portfolio_name}': Date range {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')} split into {len(date_ranges)} batches of approx {args.batch_days} days.")

                    for i, (batch_start, batch_end) in enumerate(date_ranges):
                        batch_config = {
                            'name': f'{portfolio_name}_batch_{i}_{batch_start.strftime("%Y%m%d")}_{batch_end.strftime("%Y%m%d")}',
                            'portfolio_model': portfolio_model.model_dump(), # Serialize PortfolioModel for worker
                            'portfolio_name_override': portfolio_name, # Keep original portfolio name for reporting
                            'start_date': batch_start.strftime('%Y-%m-%d'), # Pass as string
                            'end_date': batch_end.strftime('%Y-%m-%d'),   # Pass as string
                            'slippage': args.slippage,
                            'capital': args.capital if args.capital is not None else 100000.0,
                            'args': args # Pass full args if needed by worker, or cherry-pick
                        }
                        tasks.append(batch_config)

                if tasks:
                    logger.info(f"Submitting {len(tasks)} total date-batched tasks to GPU worker pool.")
                    results = worker_pool.submit_batch(_gpu_optimized_backtest_worker, tasks)
                else:
                    logger.info("No tasks to submit to worker pool.")
                    results = []
                
                combined_results = _combine_batch_results(results) # This will need to group by original portfolio name
                save_backtest_results(combined_results, args.output_path, parsed_portfolio_models, main_portfolio_excel_path)
                logger.info(f"Backtest results saved to {args.output_path}")

            except Exception as e:
                logger.error(f"Error in main (GPU optimized path): {e}", exc_info=True)
                raise
    else: # Sequential execution path
        logger.info("GPU optimization disabled or unavailable. Running sequentially.")
        try:
            utils.run_necessary_functions_before_starting_bt()
            logger.info("Modern utils.run_necessary_functions_before_starting_bt() called.")
            logger.info("Using modern Excel parsing via excel_parser.portfolio_parser")
            main_portfolio_excel_path = args.portfolio_excel
            logger.info(f"Using portfolio Excel from CLI argument: {main_portfolio_excel_path}")
            config.INPUT_FILE_FOLDER = os.path.dirname(main_portfolio_excel_path)
            logger.info(f"Set config.INPUT_FILE_FOLDER to: {config.INPUT_FILE_FOLDER}")
            logger.info(f"Parsing main portfolio Excel: {main_portfolio_excel_path} using modern parser. INPUT_FILE_FOLDER is currently: {config.INPUT_FILE_FOLDER}")
            
            parsed_portfolio_models = modern_portfolio_parser.parse_portfolio_excel(main_portfolio_excel_path)
            
            all_results_sequential = []
            for portfolio_name, portfolio_model in parsed_portfolio_models.items():
                if args.portfolio_name and portfolio_name.upper() != args.portfolio_name.upper():
                    continue

                logger.info(f"Processing portfolio '{portfolio_name}' sequentially.")
                # For sequential, we process the whole date range of the portfolio as one "batch"
                batch_config = {
                    'name': f'{portfolio_name}_fulldaterange',
                    'portfolio_model': portfolio_model.model_dump(),
                    'portfolio_name_override': portfolio_name,
                    'start_date': portfolio_model.start_date.strftime('%Y-%m-%d'),
                    'end_date': portfolio_model.end_date.strftime('%Y-%m-%d'),
                    'slippage': args.slippage,
                    'capital': args.capital if args.capital is not None else 100000.0,
                    'args': args
                }
                # Call the worker function directly for sequential execution
                result = _gpu_optimized_backtest_worker(batch_config)
                all_results_sequential.append(result)
            
            if all_results_sequential:
                combined_results = _combine_batch_results(all_results_sequential)
                save_backtest_results(combined_results, args.output_path, parsed_portfolio_models, main_portfolio_excel_path)
                logger.info(f"Backtest results saved to {args.output_path}")
            else:
                logger.info("No portfolios processed.")

        except Exception as e:
            logger.error(f"Error in main (sequential path): {e}", exc_info=True)
            raise

if __name__ == "__main__":
    main()