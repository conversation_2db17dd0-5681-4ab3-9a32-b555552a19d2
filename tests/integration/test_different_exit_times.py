#!/usr/bin/env python3
"""
Test Different Exit Times

This script tests the backtester with different exit times to ensure
our fix works for various exit times, not just 12:00.
"""

import os
import sys
import subprocess
import pandas as pd
import logging
from datetime import datetime
import shutil

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('exit_time_test')

# Constants
BT_PATH = '/srv/samba/shared/bt/backtester_stable/BTRUN'
PORTFOLIO_FILE = os.path.join(BT_PATH, 'input_sheets/input_portfolio_fixed.xlsx')
STRATEGY_FILE = os.path.join(BT_PATH, 'input_sheets/input_tbs_fixed_exits.xlsx')
OUTPUT_BASE = '/srv/samba/shared/Trades/exit_time_test'

# Different exit times to test (in HHMMSS format)
EXIT_TIMES = [
    '100000',  # 10:00:00
    '120000',  # 12:00:00 (original)
    '140000',  # 14:00:00
    '153000'   # 15:30:00 (market close)
]

def modify_exit_time(exit_time):
    """Create a copy of the strategy file with the specified exit time"""
    try:
        # Create a copy of the strategy file
        new_strategy_file = os.path.join(BT_PATH, f'input_sheets/input_tbs_exit_{exit_time}.xlsx')
        shutil.copy2(STRATEGY_FILE, new_strategy_file)
        
        # Read the file using pandas
        df = pd.read_excel(new_strategy_file, sheet_name='GeneralParameter')
        
        # Find the EndTime column and update it
        if 'EndTime' in df.columns:
            # Update the EndTime for all rows
            df['EndTime'] = int(exit_time)
            
            # Save the modified file
            with pd.ExcelWriter(new_strategy_file, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='GeneralParameter', index=False)
                
                # Copy other sheets without modification
                original_df = pd.read_excel(STRATEGY_FILE, sheet_name=None)
                for sheet_name, sheet_df in original_df.items():
                    if sheet_name != 'GeneralParameter':
                        sheet_df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            # Format the exit time for logging
            formatted_time = f"{exit_time[:2]}:{exit_time[2:4]}:{exit_time[4:]}"
            logger.info(f"Created strategy file with exit time {formatted_time}: {new_strategy_file}")
            
            return new_strategy_file
        else:
            logger.error("EndTime column not found in the strategy file")
            return None
    
    except Exception as e:
        logger.error(f"Error modifying exit time: {e}")
        return None

def create_portfolio_file(strategy_file):
    """Create a portfolio file that references the modified strategy file"""
    try:
        # Extract the exit time from the strategy file name
        exit_time = os.path.basename(strategy_file).split('_')[-1].split('.')[0]
        
        # Create a new portfolio file
        new_portfolio_file = os.path.join(BT_PATH, f'input_sheets/input_portfolio_exit_{exit_time}.xlsx')
        
        # Read the original portfolio file
        df = pd.read_excel(PORTFOLIO_FILE, sheet_name='StrategySetting')
        
        # Update the StrategyExcelFilePath to point to the new strategy file
        df['StrategyExcelFilePath'] = strategy_file
        
        # Save the modified file
        with pd.ExcelWriter(new_portfolio_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='StrategySetting', index=False)
            
            # Copy other sheets without modification
            original_df = pd.read_excel(PORTFOLIO_FILE, sheet_name=None)
            for sheet_name, sheet_df in original_df.items():
                if sheet_name != 'StrategySetting':
                    sheet_df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        logger.info(f"Created portfolio file referencing the modified strategy: {new_portfolio_file}")
        
        return new_portfolio_file
    
    except Exception as e:
        logger.error(f"Error creating portfolio file: {e}")
        return None

def run_backtester(portfolio_file, exit_time):
    """Run the backtester with the modified portfolio file"""
    try:
        output_path = f"{OUTPUT_BASE}_{exit_time}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        cmd = [
            "python3", 
            os.path.join(BT_PATH, "BTRunPortfolio_GPU.py"),
            "--portfolio-excel", portfolio_file,
            "--output-path", output_path
        ]
        
        # Format the exit time for logging
        formatted_time = f"{exit_time[:2]}:{exit_time[2:4]}:{exit_time[4:]}"
        logger.info(f"Running backtester with exit time {formatted_time}")
        
        # Run the command
        process = subprocess.run(cmd, capture_output=True, text=True)
        
        if process.returncode == 0:
            logger.info(f"Backtester completed successfully. Output: {output_path}")
            return output_path
        else:
            logger.error(f"Backtester failed with return code {process.returncode}")
            logger.error(f"STDOUT: {process.stdout}")
            logger.error(f"STDERR: {process.stderr}")
            return None
    
    except Exception as e:
        logger.error(f"Error running backtester: {e}")
        return None

def verify_output(output_path, expected_exit_time):
    """Verify that all trades exited at the expected time"""
    try:
        if not os.path.exists(output_path):
            logger.error(f"Output file not found: {output_path}")
            return False
        
        # Read the transaction sheet
        df = pd.read_excel(output_path, sheet_name='PORTFOLIO Trans')
        
        if df.empty:
            logger.error(f"No trades found in the output file")
            return False
        
        # Format the expected exit time for comparison
        expected_time = f"{expected_exit_time[:2]}:{expected_exit_time[2:4]}:{expected_exit_time[4:]}"
        
        # Check if all exit times match the expected time
        exit_times = df['exit_time'].unique()
        
        if len(exit_times) == 1 and exit_times[0] == expected_time:
            logger.info(f"✅ All trades exited at the expected time: {expected_time}")
            
            # Check if all exit reasons are "Exit Time Hit"
            exit_reasons = df['reason'].unique()
            if len(exit_reasons) == 1 and exit_reasons[0] == 'Exit Time Hit':
                logger.info(f"✅ All trades show the correct exit reason: Exit Time Hit")
                return True
            else:
                logger.error(f"❌ Unexpected exit reasons: {', '.join(exit_reasons)}")
                return False
        else:
            logger.error(f"❌ Unexpected exit times: {', '.join(exit_times)}")
            logger.error(f"Expected: {expected_time}")
            return False
    
    except Exception as e:
        logger.error(f"Error verifying output: {e}")
        return False

def test_exit_time(exit_time):
    """Test the backtester with a specific exit time"""
    logger.info(f"\n=== Testing Exit Time: {exit_time} ===\n")
    
    # Step 1: Modify the exit time in the strategy file
    modified_strategy_file = modify_exit_time(exit_time)
    if not modified_strategy_file:
        return False
    
    # Step 2: Create a portfolio file referencing the modified strategy
    modified_portfolio_file = create_portfolio_file(modified_strategy_file)
    if not modified_portfolio_file:
        return False
    
    # Step 3: Run the backtester
    output_path = run_backtester(modified_portfolio_file, exit_time)
    if not output_path:
        return False
    
    # Step 4: Verify the output
    return verify_output(output_path, exit_time)

def main():
    """Test all exit times"""
    logger.info("=== Testing Multiple Exit Times ===")
    
    results = {}
    
    for exit_time in EXIT_TIMES:
        result = test_exit_time(exit_time)
        results[exit_time] = result
    
    # Print summary
    logger.info("\n=== Summary ===\n")
    
    all_passed = True
    for exit_time, result in results.items():
        formatted_time = f"{exit_time[:2]}:{exit_time[2:4]}:{exit_time[4:]}"
        status = "PASSED" if result else "FAILED"
        logger.info(f"Exit Time {formatted_time}: {status}")
        
        if not result:
            all_passed = False
    
    if all_passed:
        logger.info("\n✅ ALL TESTS PASSED: Fix works for all exit times!")
    else:
        logger.error("\n❌ SOME TESTS FAILED: Fix may not work for all exit times")

if __name__ == "__main__":
    main() 