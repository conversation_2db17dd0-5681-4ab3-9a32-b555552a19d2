#!/usr/bin/env python3
"""
Run the new system backtester directly for comparison
Using the actual TBS implementation
"""
import sys
import os
import json
import pandas as pd
from datetime import datetime, date

# Add path for new system modules
sys.path.insert(0, '/srv/samba/shared')

from config.database import get_db_connection, DatabaseConfig
from services.strategy_executor import StrategyExecutor
from api.v2.contracts import BacktestRequest, StrategyType, TestMode

# Create output directory
output_dir = "/srv/samba/shared/new_system_output"
os.makedirs(output_dir, exist_ok=True)

print(f"Running new system backtester for {DatabaseConfig.TEST_DATE}")
print("Using HeavyDB with synthetic future ATM calculation")

# Create test request
request = BacktestRequest(
    strategy_type=StrategyType.TBS,
    test_mode=TestMode.TEST,
    files={
        "portfolio": "/srv/samba/shared/bt/archive/backtester_stable/BTRUN/INPUT SHEETS/INPUT PORTFOLIO.xlsx",
        "strategy": "/srv/samba/shared/bt/archive/backtester_stable/BTRUN/INPUT SHEETS/INPUT TBS MULTI LEGS.xlsx"
    }
)

# Run backtest
executor = StrategyExecutor()
job_id = f"test_job_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

print(f"Executing backtest with job_id: {job_id}")

# Execute synchronously
import asyncio

async def run_backtest():
    try:
        result = await executor.execute_strategy(job_id, request)
        return result
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return None

# Run the async function
result = asyncio.run(run_backtest())

if result:
    print("\nBacktest completed!")
    print(f"Execution time: {result.get('execution_time_seconds', 0)} seconds")
    print(f"Output files: {json.dumps(result.get('output_files', {}), indent=2)}")
    print(f"Summary: {json.dumps(result.get('summary', {}), indent=2)}")
    
    # Save result
    result_file = os.path.join(output_dir, f"new_system_result_{DatabaseConfig.TEST_DATE}.json")
    with open(result_file, "w") as f:
        json.dump(result, f, indent=2)
    print(f"\nResult saved to: {result_file}")
else:
    print("Backtest failed!")

# Also check the actual data being used
print("\nChecking HeavyDB data for comparison:")
with get_db_connection(test_mode=True) as db:
    query = f"""
    SELECT 
        COUNT(*) as total_rows,
        COUNT(DISTINCT strike) as unique_strikes,
        MIN(spot) as min_spot,
        MAX(spot) as max_spot,
        MIN(trade_time) as start_time,
        MAX(trade_time) as end_time
    FROM {DatabaseConfig.OPTION_CHAIN_TABLE}
    WHERE {DatabaseConfig.TEST_DATE_FILTER}
    """
    cursor = db.execute(query)
    result = cursor.fetchone()
    print(f"Total rows: {result[0]:,}")
    print(f"Unique strikes: {result[1]}")
    print(f"Spot range: {result[2]} - {result[3]}")
    print(f"Time range: {result[4]} - {result[5]}")