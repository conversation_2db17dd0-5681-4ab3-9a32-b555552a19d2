#!/usr/bin/env python3
"""
Test script to load a small sample of data first
"""

import os
import sys
import pandas as pd
from datetime import datetime

sys.path.append('/srv/samba/shared')

# Import the ETL class
from optimized_nifty_etl import OptimizedNiftyETL

def test_sample_load():
    """Test loading with a small sample file"""
    
    # Create ETL instance
    etl = OptimizedNiftyETL()
    
    try:
        # Connect to database
        etl.connect()
        
        # Create table
        print("Creating optimized table...")
        etl.create_table()
        
        # Get smallest CSV file for testing
        data_dir = "/srv/samba/shared/market_data/nifty/oc_with_futures"
        csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv') and not f.endswith('_sorted.csv')]
        
        # Sort by file size and pick smallest
        csv_files_with_size = [(f, os.path.getsize(os.path.join(data_dir, f))) for f in csv_files]
        csv_files_with_size.sort(key=lambda x: x[1])
        
        if csv_files_with_size:
            test_file = os.path.join(data_dir, csv_files_with_size[0][0])
            file_size_mb = csv_files_with_size[0][1] / (1024 * 1024)
            print(f"\nTesting with smallest file: {csv_files_with_size[0][0]} ({file_size_mb:.2f} MB)")
            
            # Process just first 1000 rows
            print("Reading first 1000 rows...")
            df = pd.read_csv(test_file, nrows=1000)
            
            # Show data types
            print("\nData types in CSV:")
            print(df.dtypes)
            
            print("\nSample data:")
            print(df.head())
            
            # Process the sample
            print("\nProcessing data...")
            processed_df = etl.process_csv_file(test_file)
            
            # Take only first 1000 rows for test
            test_df = processed_df.head(1000)
            
            print(f"\nLoading {len(test_df)} rows to HeavyDB...")
            etl.load_dataframe_to_heavydb(test_df)
            
            # Verify
            etl.verify_load()
            
            print("\nTest completed successfully!")
            
        else:
            print("No CSV files found in directory")
            
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if etl.cursor:
            etl.cursor.close()
        if etl.conn:
            etl.conn.close()

if __name__ == "__main__":
    test_sample_load()