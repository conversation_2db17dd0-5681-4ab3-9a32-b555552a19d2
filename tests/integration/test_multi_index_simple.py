#!/usr/bin/env python3
"""
Simple test for multi-index backtesting without complex imports
"""

import sys
import os
from datetime import datetime
import pandas as pd
from heavydb import connect

# Test configuration without imports
INDICES_CONFIG = {
    'NIFTY': {
        'lot_size': 50,
        'strike_increment': 50,
        'margin_pct': 12.0
    },
    'BANKNIFTY': {
        'lot_size': 25,
        'strike_increment': 100,
        'margin_pct': 12.0
    },
    'MIDCAPNIFTY': {
        'lot_size': 75,
        'strike_increment': 25,
        'margin_pct': 12.0
    },
    'SENSEX': {
        'lot_size': 10,
        'strike_increment': 100,
        'margin_pct': 12.0
    }
}

def test_simple_backtest():
    """Run a simple backtest test for each index"""
    print("=" * 80)
    print("SIMPLE MULTI-INDEX BACKTEST TEST")
    print("=" * 80)
    
    conn = connect(
        host='localhost',
        port=6274,
        user='admin',
        password='HyperInteractive',
        dbname='heavyai'
    )
    cursor = conn.cursor()
    
    # Test a simple straddle for each index
    test_date = '2024-01-15'
    test_time = '09:20:00'
    
    for index, config in INDICES_CONFIG.items():
        print(f"\n{index} Straddle Test:")
        print("-" * 40)
        
        # Get ATM strike
        query = f"""
        SELECT 
            spot,
            atm_strike,
            ce_close as ce_price,
            pe_close as pe_price
        FROM nifty_option_chain
        WHERE index_name = '{index}'
        AND trade_date = '{test_date}'
        AND trade_time = '{test_time}'
        AND strike = atm_strike
        LIMIT 1
        """
        
        try:
            cursor.execute(query)
            result = cursor.fetchone()
            
            if result:
                spot, atm_strike, ce_price, pe_price = result
                
                # Calculate straddle details
                straddle_premium = ce_price + pe_price
                lot_size = config['lot_size']
                total_premium = straddle_premium * lot_size
                margin_required = spot * lot_size * (config['margin_pct'] / 100) * 2  # For both legs
                
                print(f"  Spot Price: {spot:.2f}")
                print(f"  ATM Strike: {atm_strike}")
                print(f"  CE Price: {ce_price:.2f}")
                print(f"  PE Price: {pe_price:.2f}")
                print(f"  Straddle Premium: {straddle_premium:.2f}")
                print(f"  Lot Size: {lot_size}")
                print(f"  Total Premium Collected: ₹{total_premium:,.2f}")
                print(f"  Margin Required: ₹{margin_required:,.2f}")
                print(f"  Premium as % of Margin: {(total_premium/margin_required*100):.2f}%")
                
                # Get exit prices at 15:20
                exit_query = f"""
                SELECT 
                    ce_close as ce_exit,
                    pe_close as pe_exit
                FROM nifty_option_chain
                WHERE index_name = '{index}'
                AND trade_date = '{test_date}'
                AND trade_time = '15:20:00'
                AND strike = {atm_strike}
                LIMIT 1
                """
                
                cursor.execute(exit_query)
                exit_result = cursor.fetchone()
                
                if exit_result:
                    ce_exit, pe_exit = exit_result
                    exit_cost = ce_exit + pe_exit
                    pnl = (straddle_premium - exit_cost) * lot_size
                    pnl_pct = (pnl / margin_required) * 100
                    
                    print(f"\n  Exit Prices (15:20):")
                    print(f"    CE Exit: {ce_exit:.2f}")
                    print(f"    PE Exit: {pe_exit:.2f}")
                    print(f"    Total Exit Cost: {exit_cost:.2f}")
                    print(f"    P&L: ₹{pnl:,.2f} ({pnl_pct:.2f}% of margin)")
                    
            else:
                print(f"  No data available for {test_date}")
                
        except Exception as e:
            print(f"  Error: {e}")
    
    cursor.close()
    conn.close()

def test_data_summary():
    """Show data summary for all indices"""
    print("\n" + "=" * 80)
    print("DATA SUMMARY BY INDEX")
    print("=" * 80)
    
    conn = connect(
        host='localhost',
        port=6274,
        user='admin',
        password='HyperInteractive',
        dbname='heavyai'
    )
    cursor = conn.cursor()
    
    # Get summary for each index
    query = """
    SELECT 
        index_name,
        COUNT(DISTINCT trade_date) as days,
        MIN(trade_date) as start_date,
        MAX(trade_date) as end_date,
        COUNT(*) as total_rows,
        COUNT(DISTINCT strike) as strikes,
        MIN(spot) as min_spot,
        MAX(spot) as max_spot
    FROM nifty_option_chain
    GROUP BY index_name
    ORDER BY index_name
    """
    
    cursor.execute(query)
    results = cursor.fetchall()
    
    print(f"\n{'Index':<12} {'Days':>6} {'Start Date':<12} {'End Date':<12} {'Rows':>12} {'Strikes':>8} {'Spot Range':<20}")
    print("-" * 100)
    
    for row in results:
        index, days, start, end, rows, strikes, min_spot, max_spot = row
        spot_range = f"{min_spot:.0f}-{max_spot:.0f}"
        print(f"{index:<12} {days:>6} {start:<12} {end:<12} {rows:>12,} {strikes:>8} {spot_range:<20}")
    
    cursor.close()
    conn.close()

def test_expiry_distribution():
    """Test expiry type distribution"""
    print("\n" + "=" * 80)
    print("EXPIRY TYPE DISTRIBUTION")
    print("=" * 80)
    
    conn = connect(
        host='localhost',
        port=6274,
        user='admin',
        password='HyperInteractive',
        dbname='heavyai'
    )
    cursor = conn.cursor()
    
    query = """
    SELECT 
        index_name,
        expiry_bucket,
        COUNT(DISTINCT trade_date) as days,
        COUNT(*) as row_count
    FROM nifty_option_chain
    GROUP BY index_name, expiry_bucket
    ORDER BY index_name, expiry_bucket
    """
    
    cursor.execute(query)
    results = cursor.fetchall()
    
    current_index = None
    for row in results:
        index, expiry, days, rows = row
        if index != current_index:
            print(f"\n{index}:")
            current_index = index
        print(f"  {expiry}: {days} days, {rows:,} rows")
    
    cursor.close()
    conn.close()

if __name__ == "__main__":
    print("Multi-Index Backtesting Simple Test")
    print("=" * 80)
    print(f"Test Time: {datetime.now()}")
    
    test_data_summary()
    test_expiry_distribution()
    test_simple_backtest()
    
    print("\n" + "=" * 80)
    print("Test completed!")