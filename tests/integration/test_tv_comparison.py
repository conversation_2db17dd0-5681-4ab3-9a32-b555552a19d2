#!/usr/bin/env python3
"""
Script to test and compare CPU-based and GPU-enhanced TV backtesters
"""

import subprocess
import pandas as pd
import os
import sys
from datetime import datetime

def run_cpu_version():
    """Run the CPU-based TV backtester"""
    print("\n" + "="*80)
    print("Running CPU-based TV Backtester (BT_TV_GPU_aggregated_v4.py)")
    print("="*80)
    
    output_file = f"/srv/samba/shared/Trades/tv_cpu_output_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    cmd = [
        "python3",
        "/srv/samba/shared/bt/backtester_stable/BTRUN/BT_TV_GPU_aggregated_v4.py",
        "--tv-excel", "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tv.xlsx",
        "--output-path", output_file,
        "--workers", "4"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, cwd="/srv/samba/shared")
        print("STDOUT:", result.stdout)
        print("STDERR:", result.stderr)
        print(f"Return code: {result.returncode}")
        
        if result.returncode == 0 and os.path.exists(output_file):
            print(f"✅ CPU version completed successfully")
            print(f"Output file: {output_file}")
            return output_file
        else:
            print(f"❌ CPU version failed")
            return None
            
    except Exception as e:
        print(f"Error running CPU version: {e}")
        return None

def run_gpu_version():
    """Run the GPU-enhanced TV backtester"""
    print("\n" + "="*80)
    print("Running GPU-enhanced TV Backtester (BT_TV_GPU_enhanced.py)")
    print("="*80)
    
    output_file = f"/srv/samba/shared/Trades/tv_gpu_output_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    cmd = [
        "python3",
        "/srv/samba/shared/bt/backtester_stable/BTRUN/BT_TV_GPU_enhanced.py",
        "--tv-excel", "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tv.xlsx",
        "--output-path", output_file,
        "--workers", "auto",
        "--batch-days", "7"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, cwd="/srv/samba/shared")
        print("STDOUT:", result.stdout)
        print("STDERR:", result.stderr)
        print(f"Return code: {result.returncode}")
        
        if result.returncode == 0 and os.path.exists(output_file):
            print(f"✅ GPU version completed successfully")
            print(f"Output file: {output_file}")
            return output_file
        else:
            print(f"❌ GPU version failed")
            return None
            
    except Exception as e:
        print(f"Error running GPU version: {e}")
        return None

def compare_outputs(cpu_file, gpu_file):
    """Compare the outputs from both versions"""
    print("\n" + "="*80)
    print("Comparing Outputs")
    print("="*80)
    
    if not cpu_file or not gpu_file:
        print("❌ Cannot compare - one or both files missing")
        return
    
    try:
        # Read key sheets from both files
        sheets_to_compare = ['PORTFOLIO Trans', 'Metrics', 'Max Profit and Loss']
        
        for sheet in sheets_to_compare:
            print(f"\n--- Comparing {sheet} sheet ---")
            
            try:
                cpu_df = pd.read_excel(cpu_file, sheet_name=sheet)
                gpu_df = pd.read_excel(gpu_file, sheet_name=sheet)
                
                print(f"CPU shape: {cpu_df.shape}")
                print(f"GPU shape: {gpu_df.shape}")
                
                if cpu_df.shape != gpu_df.shape:
                    print(f"⚠️  Different shapes in {sheet}")
                    continue
                
                # Compare key numeric columns
                if sheet == 'PORTFOLIO Trans':
                    numeric_cols = ['Points', 'Points After Slippage', 'Net PNL', 'MaxProfit', 'MaxLoss']
                    for col in numeric_cols:
                        if col in cpu_df.columns and col in gpu_df.columns:
                            diff = (cpu_df[col] - gpu_df[col]).abs().max()
                            if diff > 0.01:
                                print(f"⚠️  {col} max difference: {diff}")
                            else:
                                print(f"✅ {col} matches (max diff: {diff})")
                
                elif sheet == 'Metrics':
                    # Compare overall metrics
                    print("CPU Metrics sample:")
                    print(cpu_df.head())
                    print("\nGPU Metrics sample:")
                    print(gpu_df.head())
                    
            except Exception as e:
                print(f"Error comparing {sheet}: {e}")
                
    except Exception as e:
        print(f"Error in comparison: {e}")

def main():
    """Main execution"""
    print("TV Backtester Comparison Test")
    print(f"Started at: {datetime.now()}")
    
    # Run both versions
    cpu_output = run_cpu_version()
    gpu_output = run_gpu_version()
    
    # Compare results
    compare_outputs(cpu_output, gpu_output)
    
    print(f"\nCompleted at: {datetime.now()}")

if __name__ == "__main__":
    main()