#!/usr/bin/env python3
import os
import sys
import subprocess
import time

def run_sql(sql_cmd):
    print(f"Running SQL: {sql_cmd}")
    cmd = ["/opt/heavyai/bin/heavysql", "-s", "127.0.0.1", "--port", "6274", 
           "-u", "admin", "-p", "HyperInteractive", "-d", "heavyai"]
    
    process = subprocess.Popen(cmd, stdin=subprocess.PIPE, 
                           stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                           text=True)
    
    stdout, stderr = process.communicate(input=sql_cmd)
    
    print("STDOUT:")
    print(stdout)
    
    if stderr:
        print("STDERR:")
        print(stderr)
    
    return stdout, stderr

def main():
    print("=== Simple Test Loader ===")
    
    # Step 1: Create a simple test table
    create_sql = """
    DROP TABLE IF EXISTS test_table;
    CREATE TABLE test_table (id INT, name TEXT);
    """
    run_sql(create_sql)
    
    # Step 2: Insert a test row
    insert_sql = """
    INSERT INTO test_table VALUES (1, 'test');
    """
    run_sql(insert_sql)
    
    # Step 3: Verify the data
    verify_sql = """
    SELECT * FROM test_table;
    """
    run_sql(verify_sql)
    
    print("Test completed.")

if __name__ == "__main__":
    main() 