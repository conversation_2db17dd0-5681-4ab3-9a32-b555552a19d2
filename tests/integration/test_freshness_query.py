#!/usr/bin/env python3
"""Test the freshness query directly"""

from heavydb import connect

try:
    # Connect to HeavyDB
    conn = connect(
        host='localhost',
        port=6274,
        user='admin',
        password='HyperInteractive',
        dbname='heavyai'
    )
    print("Connected to HeavyDB successfully")
    
    # Test simple query first
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM nifty_option_chain LIMIT 1")
    result = cursor.fetchone()
    print(f"Total rows in table: {result[0]}")
    
    # Test index-specific query
    indices = ['NIFTY', 'BANKNIFTY', 'MIDCAPNIFTY', 'SENSEX']
    
    for index in indices:
        try:
            print(f"\nChecking {index}...")
            cursor.execute(f"""
                SELECT 
                    COUNT(*) as row_count
                FROM nifty_option_chain
                WHERE index_name = '{index}'
                LIMIT 1
            """)
            result = cursor.fetchone()
            print(f"  Row count: {result[0]}")
            
            # Now try to get date info
            if result[0] > 0:
                cursor.execute(f"""
                    SELECT 
                        MAX(trade_date) as latest_date,
                        MIN(trade_date) as earliest_date
                    FROM nifty_option_chain
                    WHERE index_name = '{index}'
                    LIMIT 1
                """)
                date_result = cursor.fetchone()
                print(f"  Latest date: {date_result[0]}")
                print(f"  Earliest date: {date_result[1]}")
                
        except Exception as e:
            print(f"  Error: {e}")
            
except Exception as e:
    print(f"Connection error: {e}")