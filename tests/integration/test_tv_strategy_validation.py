#!/usr/bin/env python3
"""
Comprehensive TV Strategy Validation Test
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
import subprocess
import json

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TVStrategyValidation:
    def __init__(self):
        self.test_dir = '/srv/samba/shared/test_results/tv_validation'
        os.makedirs(self.test_dir, exist_ok=True)
        
    def create_tv_test_signals(self):
        """Create comprehensive TV test signals."""
        
        logger.info("=== Creating TV Test Signals ===")
        
        # Create different signal patterns
        signals = []
        
        # Pattern 1: Simple Buy/Sell signals
        base_date = datetime(2024, 4, 1, 9, 30)
        
        # Day 1: Simple long entry and exit
        signals.extend([
            {
                'Trade #': 1,
                'Type': 'Entry long',
                'Signal': 'Buy_Entry',
                'Date/Time': base_date.strftime('%Y-%m-%d %H:%M:%S'),
                'Price INR': 22500,
                'Contracts': 2,
                'Profit INR': 0,
                'Profit %': 0,
                'Cumulative profit INR': 0,
                'Cumulative profit %': 0,
                'Run-up INR': 0,
                'Run-up %': 0,
                'Drawdown INR': 0,
                'Drawdown %': 0
            },
            {
                'Trade #': 1,
                'Type': 'Exit long',
                'Signal': 'Sell_Exit',
                'Date/Time': (base_date + timedelta(hours=2)).strftime('%Y-%m-%d %H:%M:%S'),
                'Price INR': 22550,
                'Contracts': 2,
                'Profit INR': 100,
                'Profit %': 0.44,
                'Cumulative profit INR': 100,
                'Cumulative profit %': 0.44,
                'Run-up INR': 100,
                'Run-up %': 0.44,
                'Drawdown INR': 0,
                'Drawdown %': 0
            }
        ])
        
        # Day 2: Short entry and exit
        base_date = datetime(2024, 4, 2, 10, 0)
        signals.extend([
            {
                'Trade #': 2,
                'Type': 'Entry short',
                'Signal': 'Sell_Entry',
                'Date/Time': base_date.strftime('%Y-%m-%d %H:%M:%S'),
                'Price INR': 22600,
                'Contracts': 2,
                'Profit INR': 0,
                'Profit %': 0,
                'Cumulative profit INR': 100,
                'Cumulative profit %': 0.44,
                'Run-up INR': 0,
                'Run-up %': 0,
                'Drawdown INR': 0,
                'Drawdown %': 0
            },
            {
                'Trade #': 2,
                'Type': 'Exit short',
                'Signal': 'Buy_Exit',
                'Date/Time': (base_date + timedelta(hours=3)).strftime('%Y-%m-%d %H:%M:%S'),
                'Price INR': 22550,
                'Contracts': 2,
                'Profit INR': 100,
                'Profit %': 0.44,
                'Cumulative profit INR': 200,
                'Cumulative profit %': 0.88,
                'Run-up INR': 100,
                'Run-up %': 0.44,
                'Drawdown INR': 0,
                'Drawdown %': 0
            }
        ])
        
        # Day 3: Multiple signals (pyramiding)
        base_date = datetime(2024, 4, 3, 9, 20)
        signals.extend([
            {
                'Trade #': 3,
                'Type': 'Entry long',
                'Signal': 'Buy_Entry',
                'Date/Time': base_date.strftime('%Y-%m-%d %H:%M:%S'),
                'Price INR': 22480,
                'Contracts': 1,
                'Profit INR': 0,
                'Profit %': 0,
                'Cumulative profit INR': 200,
                'Cumulative profit %': 0.88,
                'Run-up INR': 0,
                'Run-up %': 0,
                'Drawdown INR': 0,
                'Drawdown %': 0
            },
            {
                'Trade #': 4,
                'Type': 'Entry long',
                'Signal': 'Buy_Entry',
                'Date/Time': (base_date + timedelta(hours=1)).strftime('%Y-%m-%d %H:%M:%S'),
                'Price INR': 22500,
                'Contracts': 1,
                'Profit INR': 0,
                'Profit %': 0,
                'Cumulative profit INR': 200,
                'Cumulative profit %': 0.88,
                'Run-up INR': 0,
                'Run-up %': 0,
                'Drawdown INR': 0,
                'Drawdown %': 0
            },
            {
                'Trade #': 3,
                'Type': 'Exit long',
                'Signal': 'Sell_Exit',
                'Date/Time': (base_date + timedelta(hours=4)).strftime('%Y-%m-%d %H:%M:%S'),
                'Price INR': 22520,
                'Contracts': 1,
                'Profit INR': 40,
                'Profit %': 0.18,
                'Cumulative profit INR': 240,
                'Cumulative profit %': 1.06,
                'Run-up INR': 40,
                'Run-up %': 0.18,
                'Drawdown INR': 0,
                'Drawdown %': 0
            },
            {
                'Trade #': 4,
                'Type': 'Exit long',
                'Signal': 'Sell_Exit',
                'Date/Time': (base_date + timedelta(hours=4, minutes=30)).strftime('%Y-%m-%d %H:%M:%S'),
                'Price INR': 22520,
                'Contracts': 1,
                'Profit INR': 20,
                'Profit %': 0.09,
                'Cumulative profit INR': 260,
                'Cumulative profit %': 1.15,
                'Run-up INR': 20,
                'Run-up %': 0.09,
                'Drawdown INR': 0,
                'Drawdown %': 0
            }
        ])
        
        # Day 4: Loss trade
        base_date = datetime(2024, 4, 4, 11, 0)
        signals.extend([
            {
                'Trade #': 5,
                'Type': 'Entry long',
                'Signal': 'Buy_Entry',
                'Date/Time': base_date.strftime('%Y-%m-%d %H:%M:%S'),
                'Price INR': 22550,
                'Contracts': 2,
                'Profit INR': 0,
                'Profit %': 0,
                'Cumulative profit INR': 260,
                'Cumulative profit %': 1.15,
                'Run-up INR': 0,
                'Run-up %': 0,
                'Drawdown INR': 0,
                'Drawdown %': 0
            },
            {
                'Trade #': 5,
                'Type': 'Exit long',
                'Signal': 'Sell_Exit',
                'Date/Time': (base_date + timedelta(hours=2)).strftime('%Y-%m-%d %H:%M:%S'),
                'Price INR': 22500,
                'Contracts': 2,
                'Profit INR': -100,
                'Profit %': -0.44,
                'Cumulative profit INR': 160,
                'Cumulative profit %': 0.71,
                'Run-up INR': 0,
                'Run-up %': 0,
                'Drawdown INR': 100,
                'Drawdown %': 0.44
            }
        ])
        
        # Day 5: Close signal (end of day)
        base_date = datetime(2024, 4, 5, 9, 30)
        signals.extend([
            {
                'Trade #': 6,
                'Type': 'Entry short',
                'Signal': 'Sell_Entry',
                'Date/Time': base_date.strftime('%Y-%m-%d %H:%M:%S'),
                'Price INR': 22500,
                'Contracts': 1,
                'Profit INR': 0,
                'Profit %': 0,
                'Cumulative profit INR': 160,
                'Cumulative profit %': 0.71,
                'Run-up INR': 0,
                'Run-up %': 0,
                'Drawdown INR': 0,
                'Drawdown %': 0
            },
            {
                'Trade #': 6,
                'Type': 'Exit short',
                'Signal': 'Buy_Close',
                'Date/Time': datetime(2024, 4, 5, 15, 25).strftime('%Y-%m-%d %H:%M:%S'),
                'Price INR': 22480,
                'Contracts': 1,
                'Profit INR': 20,
                'Profit %': 0.09,
                'Cumulative profit INR': 180,
                'Cumulative profit %': 0.80,
                'Run-up INR': 20,
                'Run-up %': 0.09,
                'Drawdown INR': 0,
                'Drawdown %': 0
            }
        ])
        
        # Create DataFrame
        signals_df = pd.DataFrame(signals)
        
        # Create signal file
        signal_file = os.path.join(self.test_dir, 'test_tv_signals.xlsx')
        with pd.ExcelWriter(signal_file) as writer:
            # Create sheets matching TradingView format
            # Performance sheet
            performance_data = {
                'Metric': ['Net profit', 'Total trades', 'Win rate', 'Profit factor'],
                'Value': [180, 6, '66.67%', 2.8]
            }
            pd.DataFrame(performance_data).to_excel(writer, sheet_name='Performance', index=False)
            
            # Trades analysis
            pd.DataFrame({'Analysis': ['Placeholder']}).to_excel(writer, sheet_name='Trades analysis', index=False)
            
            # Risk performance ratios
            pd.DataFrame({'Ratios': ['Placeholder']}).to_excel(writer, sheet_name='Risk performance ratios', index=False)
            
            # List of trades (main data)
            signals_df.to_excel(writer, sheet_name='List of trades', index=False)
            
            # Properties
            pd.DataFrame({'Properties': ['Test signals']}).to_excel(writer, sheet_name='Properties', index=False)
            
        logger.info(f"Created test signal file: {signal_file}")
        
        # Create portfolio files
        self.create_tv_portfolio_files()
        
        return signal_file
        
    def create_tv_portfolio_files(self):
        """Create TV portfolio files for long and short strategies."""
        
        # Long portfolio
        long_portfolio = {
            'GeneralParameter': pd.DataFrame([{
                'StrategyName': 'TV_LONG',
                'Symbol': 'NIFTY',
                'InstrumentName': 'NIFTY',
                'DTE': 0,
                'StartTime': 91500,
                'EndTime': 152500,
                'Weekdays': '1,2,3,4,5',
                'PositionSizing': 'LotsSize',
                'LotsSize': 1,
                'Expiry': 'current'
            }]),
            'LegParameter': pd.DataFrame([{
                'StrategyName': 'TV_LONG',
                'LegID': 1,
                'Instrument': 'atm',
                'Transaction': 'buy',
                'StrikeMethod': 'atm',
                'StrikeValue': 0,
                'SLType': 'percentage',
                'SLValue': 0,
                'TGTType': 'percentage',
                'TGTValue': 0,
                'Lots': 1
            }])
        }
        
        long_file = os.path.join(self.test_dir, 'input_portfolio_long.xlsx')
        with pd.ExcelWriter(long_file) as writer:
            for sheet, df in long_portfolio.items():
                df.to_excel(writer, sheet_name=sheet, index=False)
                
        # Short portfolio
        short_portfolio = {
            'GeneralParameter': pd.DataFrame([{
                'StrategyName': 'TV_SHORT',
                'Symbol': 'NIFTY',
                'InstrumentName': 'NIFTY',
                'DTE': 0,
                'StartTime': 91500,
                'EndTime': 152500,
                'Weekdays': '1,2,3,4,5',
                'PositionSizing': 'LotsSize',
                'LotsSize': 1,
                'Expiry': 'current'
            }]),
            'LegParameter': pd.DataFrame([{
                'StrategyName': 'TV_SHORT',
                'LegID': 1,
                'Instrument': 'atm',
                'Transaction': 'sell',
                'StrikeMethod': 'atm',
                'StrikeValue': 0,
                'SLType': 'percentage',
                'SLValue': 0,
                'TGTType': 'percentage',
                'TGTValue': 0,
                'Lots': 1
            }])
        }
        
        short_file = os.path.join(self.test_dir, 'input_portfolio_short.xlsx')
        with pd.ExcelWriter(short_file) as writer:
            for sheet, df in short_portfolio.items():
                df.to_excel(writer, sheet_name=sheet, index=False)
                
        logger.info(f"Created portfolio files: {long_file}, {short_file}")
        
    def create_tv_setting_file(self, signal_file):
        """Create TV setting file."""
        
        setting_data = {
            'StartDate': '01_04_2024',
            'EndDate': '05_04_2024',
            'SignalDateFormat': '%Y-%m-%d %H:%M:%S',
            'Enabled': 'yes',
            'TvExitApplicable': 'yes',
            'ManualTradeEntryTime': 0,
            'ManualTradeLots': 0,
            'IncreaseEntrySignalTimeBy': 0,
            'IncreaseExitSignalTimeBy': 0,
            'IntradaySqOffApplicable': 'no',
            'FirstTradeEntryTime': 91500,
            'IntradayExitTime': 152500,
            'ExpiryDayExitTime': 152500,
            'DoRollover': 'no',
            'RolloverTime': 0,
            'Name': 'TV_TEST',
            'SignalFilePath': signal_file,
            'LongPortfolioFilePath': os.path.join(self.test_dir, 'input_portfolio_long.xlsx'),
            'ShortPortfolioFilePath': os.path.join(self.test_dir, 'input_portfolio_short.xlsx'),
            'ManualPortfolioFilePath': '',
            'UseDbExitTiming': 'no',
            'ExitSearchInterval': 1,
            'ExitPriceSource': 'SPOT',
            'SlippagePercent': 0.05
        }
        
        setting_file = os.path.join(self.test_dir, 'input_tv.xlsx')
        with pd.ExcelWriter(setting_file) as writer:
            pd.DataFrame([setting_data]).to_excel(writer, sheet_name='Setting', index=False)
            
        logger.info(f"Created TV setting file: {setting_file}")
        return setting_file
        
    def run_tv_backtest(self, setting_file):
        """Run TV backtest using GPU system."""
        
        logger.info("\n=== Running TV Backtest ===")
        
        output_path = os.path.join(self.test_dir, 'tv_output_golden.xlsx')
        
        # Set environment for golden format
        env = os.environ.copy()
        env['USE_GOLDEN_FORMAT'] = 'true'
        
        # Command for TV backtest
        cmd = [
            'python3',
            '/srv/samba/shared/bt/backtester_stable/BTRUN/BT_TV_GPU.py',
            '--tv-excel', setting_file,
            '--output-path', output_path,
            '--debug'
        ]
        
        logger.info(f"Running: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, env=env, timeout=300)
            
            if result.returncode == 0:
                logger.info("✅ TV backtest completed successfully")
                return output_path
            else:
                logger.error(f"❌ TV backtest failed")
                if result.stderr:
                    logger.error(f"Error: {result.stderr[-1000:]}")
                return None
                
        except subprocess.TimeoutExpired:
            logger.error("TV backtest timed out")
            return None
        except Exception as e:
            logger.error(f"Error running TV backtest: {e}")
            return None
            
    def analyze_tv_output(self, output_path):
        """Analyze TV backtest output."""
        
        logger.info("\n=== Analyzing TV Output ===")
        
        if not output_path or not os.path.exists(output_path):
            logger.error("Output file not found")
            return
            
        try:
            xl = pd.ExcelFile(output_path)
            logger.info(f"Sheets: {xl.sheet_names}")
            
            if 'PORTFOLIO Trans' in xl.sheet_names:
                df = pd.read_excel(xl, 'PORTFOLIO Trans')
                logger.info(f"\nTotal trades: {len(df)}")
                
                if len(df) > 0:
                    logger.info("\nTrade Summary:")
                    logger.info(f"- Columns: {len(df.columns)}")
                    
                    if 'Reason' in df.columns:
                        logger.info("\nExit Reasons:")
                        for reason, count in df['Reason'].value_counts().items():
                            logger.info(f"  {reason}: {count}")
                            
                    if 'Net PNL' in df.columns:
                        logger.info(f"\nP&L Summary:")
                        logger.info(f"  Total P&L: {df['Net PNL'].sum():.2f}")
                        logger.info(f"  Win Rate: {(df['Net PNL'] > 0).sum() / len(df) * 100:.1f}%")
                        
                    # Show sample trades
                    cols_to_show = ['Strategy Name', 'Entry Date', 'Exit Date', 'Strike', 'CE/PE', 'Trade', 'Net PNL', 'Reason']
                    available_cols = [c for c in cols_to_show if c in df.columns]
                    if available_cols:
                        logger.info("\nSample trades:")
                        print(df[available_cols].head())
                        
        except Exception as e:
            logger.error(f"Error analyzing output: {e}")
            import traceback
            traceback.print_exc()
            
    def validate_signal_execution(self, signal_file, output_path):
        """Validate that signals were executed correctly."""
        
        logger.info("\n=== Validating Signal Execution ===")
        
        # Load signals
        signals_df = pd.read_excel(signal_file, 'List of trades')
        entry_signals = signals_df[signals_df['Type'].str.contains('Entry')]
        
        # Load output
        if os.path.exists(output_path):
            xl = pd.ExcelFile(output_path)
            if 'PORTFOLIO Trans' in xl.sheet_names:
                trades_df = pd.read_excel(xl, 'PORTFOLIO Trans')
                
                logger.info(f"Signals: {len(entry_signals)} entries")
                logger.info(f"Executed trades: {len(trades_df)}")
                
                # Check signal mapping
                if len(trades_df) > 0:
                    logger.info("✅ Signals were executed")
                else:
                    logger.warning("❌ No trades executed from signals")
                    
    def run_validation(self):
        """Run complete TV validation."""
        
        logger.info("="*80)
        logger.info("TV STRATEGY VALIDATION")
        logger.info("="*80)
        
        # Create test signals
        signal_file = self.create_tv_test_signals()
        
        # Create TV setting file
        setting_file = self.create_tv_setting_file(signal_file)
        
        # Run TV backtest
        output_path = self.run_tv_backtest(setting_file)
        
        # Analyze output
        if output_path:
            self.analyze_tv_output(output_path)
            self.validate_signal_execution(signal_file, output_path)
            
            # Generate summary report
            self.generate_validation_report(signal_file, output_path)
        else:
            logger.error("TV backtest did not produce output")
            
    def generate_validation_report(self, signal_file, output_path):
        """Generate TV validation report."""
        
        report_path = os.path.join(self.test_dir, 'TV_VALIDATION_REPORT.md')
        
        with open(report_path, 'w') as f:
            f.write("# TV Strategy Validation Report\n\n")
            f.write(f"**Date**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## Test Configuration\n\n")
            f.write(f"- Signal file: `{signal_file}`\n")
            f.write(f"- Output file: `{output_path}`\n")
            f.write("- Date range: 01-04-2024 to 05-04-2024\n")
            f.write("- Golden format: ENABLED\n\n")
            
            f.write("## Signal Summary\n\n")
            f.write("- Entry signals: 6\n")
            f.write("- Exit signals: 6\n")
            f.write("- Signal types: Buy_Entry, Sell_Entry, Buy_Exit, Sell_Exit, Buy_Close\n\n")
            
            f.write("## Validation Status\n\n")
            if output_path and os.path.exists(output_path):
                f.write("✅ TV backtest executed successfully\n")
                f.write("✅ Golden format output generated\n")
                
                # Check if trades were executed
                xl = pd.ExcelFile(output_path)
                if 'PORTFOLIO Trans' in xl.sheet_names:
                    df = pd.read_excel(xl, 'PORTFOLIO Trans')
                    if len(df) > 0:
                        f.write(f"✅ {len(df)} trades executed from signals\n")
                    else:
                        f.write("❌ No trades executed\n")
                        
                    # Check golden format
                    if len(df.columns) == 32:
                        f.write("✅ Golden format validated (32 columns)\n")
                    else:
                        f.write(f"❌ Column count mismatch: {len(df.columns)} vs 32 expected\n")
            else:
                f.write("❌ TV backtest failed to produce output\n")
                
        logger.info(f"\nValidation report saved to: {report_path}")


if __name__ == "__main__":
    validator = TVStrategyValidation()
    validator.run_validation()