#!/usr/bin/env python3
"""
Direct comparison of Archive vs New system using actual backtester modules
Testing all strategies: TBS, TV, ORB, OI with synthetic future ATM
"""
import os
import sys
import subprocess
import pandas as pd
import json
from datetime import datetime
import shutil

# Add backtester paths
sys.path.append('/srv/samba/shared/bt/backtester_stable/BTRUN')
sys.path.append('/srv/samba/shared/bt/archive/backtester_stable/BTRUN')

# Test configuration
TEST_DATE = "2024-04-01"
STRATEGIES = ["TBS", "TV", "ORB", "OI"]
OUTPUT_DIR = "/srv/samba/shared/direct_comparison_results"
os.makedirs(OUTPUT_DIR, exist_ok=True)

print("="*80)
print("DIRECT BACKTEST COMPARISON - ARCHIVE vs NEW SYSTEM")
print(f"Test Date: {TEST_DATE}")
print(f"Strategies: {', '.join(STRATEGIES)}")
print("Both systems using synthetic future ATM")
print("="*80)

results = {}

# Input file paths
ARCHIVE_INPUT = "/srv/samba/shared/bt/archive/backtester_stable/BTRUN/INPUT SHEETS"
NEW_INPUT = "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets"

for strategy in STRATEGIES:
    print(f"\n{'='*60}")
    print(f"Testing {strategy} Strategy")
    print(f"{'='*60}")
    
    results[strategy] = {
        'archive': {'success': False},
        'new': {'success': False},
        'comparison': {}
    }
    
    # 1. Run Archive System
    print(f"\n1. Running Archive System for {strategy}...")
    
    # Change to archive directory and run
    archive_cmd = f"""
cd /srv/samba/shared/bt/archive/backtester_stable/BTRUN
export USE_SYNTHETIC_FUTURE_ATM=true
export TEST_DATE={TEST_DATE}
export STRATEGY_TYPE={strategy}
python3 BTRunPortfolio.py 2>&1 | tee archive_{strategy}_run.log
"""
    
    archive_start = datetime.now()
    result = subprocess.run(['bash', '-c', archive_cmd], capture_output=True, text=True)
    archive_duration = (datetime.now() - archive_start).total_seconds()
    
    # Check for output
    archive_trades = "/srv/samba/shared/bt/archive/backtester_stable/BTRUN/Trades"
    if os.path.exists(archive_trades):
        files = [f for f in os.listdir(archive_trades) if f.endswith('.xlsx')]
        if files:
            latest = sorted(files)[-1]
            archive_output = os.path.join(archive_trades, latest)
            archive_copy = os.path.join(OUTPUT_DIR, f"archive_{strategy}_{TEST_DATE}.xlsx")
            shutil.copy(archive_output, archive_copy)
            print(f"  ✓ Archive output: {archive_copy}")
            print(f"  Duration: {archive_duration:.2f} seconds")
            results[strategy]['archive'] = {
                'file': archive_copy,
                'duration': archive_duration,
                'success': True
            }
    
    # 2. Run New System with actual backtester
    print(f"\n2. Running New System for {strategy}...")
    
    # Import the actual backtester modules
    try:
        if strategy == "TBS":
            from backtester_v2.strategies.tbs.strategy import TBSStrategy
            print("  ✓ TBS module loaded")
        elif strategy == "TV":
            from backtester_v2.strategies.tv.strategy import TVStrategy
            print("  ✓ TV module loaded")
        elif strategy == "ORB":
            from backtester_v2.strategies.orb.strategy import ORBStrategy
            print("  ✓ ORB module loaded")
        elif strategy == "OI":
            from backtester_v2.strategies.oi.strategy import OIStrategy
            print("  ✓ OI module loaded")
            
        # Run backtest using the module
        new_start = datetime.now()
        
        # Create a simple test runner
        new_cmd = f"""
cd /srv/samba/shared/bt/backtester_stable/BTRUN
export USE_HEAVYDB=true
export TEST_DATE={TEST_DATE}
export STRATEGY_TYPE={strategy}
python3 -c "
import sys
sys.path.append('.')
from backtester_v2.run_backtest import run_strategy
result = run_strategy('{strategy.lower()}', '{TEST_DATE}')
print(f'Result: {{result}}')
" 2>&1 | tee new_{strategy}_run.log
"""
        
        result = subprocess.run(['bash', '-c', new_cmd], capture_output=True, text=True)
        new_duration = (datetime.now() - new_start).total_seconds()
        
        # Look for output
        output_files = [f for f in os.listdir('/tmp') if f.startswith(f'output_{strategy.lower()}_')]
        if output_files:
            latest = sorted(output_files)[-1]
            new_output = os.path.join('/tmp', latest)
            new_copy = os.path.join(OUTPUT_DIR, f"new_{strategy}_{TEST_DATE}.xlsx")
            shutil.copy(new_output, new_copy)
            print(f"  ✓ New system output: {new_copy}")
            print(f"  Duration: {new_duration:.2f} seconds")
            results[strategy]['new'] = {
                'file': new_copy,
                'duration': new_duration,
                'success': True
            }
            
    except ImportError as e:
        print(f"  ✗ Could not load {strategy} module: {e}")
        
    # 3. Compare Results
    if results[strategy]['archive']['success'] and results[strategy]['new']['success']:
        print(f"\n3. Comparing {strategy} Results...")
        
        try:
            archive_df = pd.read_excel(results[strategy]['archive']['file'], sheet_name='Metrics')
            new_df = pd.read_excel(results[strategy]['new']['file'], sheet_name='Metrics')
            
            # Compare key metrics
            print("  Key Metrics Comparison:")
            metrics_to_compare = ['Gross P&L', 'Net P&L', 'Total Trading Days', 'Win Rate %']
            
            for metric in metrics_to_compare:
                archive_val = archive_df[archive_df['Particulars'] == metric]['Combined'].values
                new_val = new_df[new_df['Particulars'] == metric]['Combined'].values
                
                if len(archive_val) > 0 and len(new_val) > 0:
                    print(f"    {metric}: Archive={archive_val[0]}, New={new_val[0]}")
                    
        except Exception as e:
            print(f"  ✗ Comparison failed: {e}")

# Summary
print(f"\n{'='*80}")
print("SUMMARY")
print(f"{'='*80}")

summary_file = os.path.join(OUTPUT_DIR, f"direct_comparison_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
with open(summary_file, 'w') as f:
    json.dump(results, f, indent=2, default=str)

print(f"\nResults saved to: {OUTPUT_DIR}")
print(f"Summary: {summary_file}")

# Print summary table
print("\nExecution Summary:")
print(f"{'Strategy':<10} {'Archive':<15} {'New System':<15} {'Match':<10}")
print("-"*50)
for strategy in STRATEGIES:
    archive_status = "✓" if results[strategy]['archive']['success'] else "✗"
    new_status = "✓" if results[strategy]['new']['success'] else "✗"
    match = "✓" if archive_status == "✓" and new_status == "✓" else "✗"
    print(f"{strategy:<10} {archive_status:<15} {new_status:<15} {match:<10}")

print("\nNote: Both systems configured for synthetic future ATM calculation")