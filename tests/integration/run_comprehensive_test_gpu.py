#!/usr/bin/env python3
"""
Run comprehensive test with maximum GPU processing and verification
"""

import os
import sys
import subprocess
from datetime import datetime
import time
import multiprocessing

def check_system_readiness():
    """Check if system is ready for backtest"""
    
    print("="*80)
    print("SYSTEM READINESS CHECK")
    print("="*80)
    
    # Check GPU
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ GPU detected")
            # Extract GPU memory
            for line in result.stdout.split('\n'):
                if 'MiB' in line and '/' in line:
                    print(f"   {line.strip()}")
        else:
            print("❌ GPU not detected")
    except:
        print("❌ nvidia-smi not available")
    
    # Check CPU cores
    cpu_count = multiprocessing.cpu_count()
    print(f"\n✅ CPU cores available: {cpu_count}")
    
    # Check Python GPU modules
    gpu_modules = ['cudf', 'cupy', 'numba']
    gpu_available = True
    print("\nGPU Modules:")
    for module in gpu_modules:
        try:
            __import__(module)
            print(f"  ✅ {module}")
        except ImportError:
            print(f"  ❌ {module}")
            gpu_available = False
    
    # Check HeavyDB
    try:
        result = subprocess.run(['systemctl', 'is-active', 'heavydb'], 
                              capture_output=True, text=True)
        if result.stdout.strip() == 'active':
            print("\n✅ HeavyDB is running")
        else:
            print("\n❌ HeavyDB is not running")
            print("   Run: sudo systemctl start heavydb")
    except:
        print("\n⚠️  Could not check HeavyDB status")
    
    return gpu_available, cpu_count

def run_comprehensive_backtest():
    """Run the comprehensive test with maximum optimization"""
    
    print("\n" + "="*80)
    print("COMPREHENSIVE TBS COLUMN TEST - GPU OPTIMIZED")
    print("="*80)
    print(f"Start time: {datetime.now()}")
    
    # Check system
    gpu_available, cpu_count = check_system_readiness()
    
    # File paths
    portfolio_file = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/comprehensive_tests/MASTER_COMPREHENSIVE_TEST_PORTFOLIO_FIXED.xlsx'
    output_file = f'/srv/samba/shared/comprehensive_test_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
    log_file = f'/srv/samba/shared/comprehensive_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
    
    # Build command with maximum optimization
    cmd = [
        'python3',
        '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py',
        '--portfolio-excel', portfolio_file,
        '--output-path', output_file,
        '--workers', 'auto',
        '--cpu-workers', str(cpu_count),
        '--batch-days', '7',  # Weekly batches for memory efficiency
        '--merge-output',
        '--debug'  # Enable debug for detailed logs
    ]
    
    # Add GPU flags if available
    if gpu_available:
        cmd.extend([
            '--use-gpu-optimization',
            '--gpu-workers', '4',
            '--gpu-threshold', '1000'
        ])
        print("\n✅ GPU optimization enabled")
    else:
        print("\n⚠️  Running without GPU optimization")
    
    # Set environment for GPU
    env = os.environ.copy()
    env['USE_GPU'] = '1'
    env['PYTHONUNBUFFERED'] = '1'
    
    print("\nCommand:")
    print(' '.join(cmd))
    
    print("\n" + "-"*80)
    print("Running comprehensive backtest...")
    print("-"*80)
    
    start_time = time.time()
    
    # Run with output capture
    with open(log_file, 'w') as log:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1,
            env=env
        )
        
        # Print and log output
        for line in process.stdout:
            print(line, end='')
            log.write(line)
            log.flush()
        
        return_code = process.wait()
    
    end_time = time.time()
    duration = end_time - start_time
    
    print("\n" + "="*80)
    print("BACKTEST COMPLETED")
    print("="*80)
    print(f"Return code: {return_code}")
    print(f"Duration: {duration:.2f} seconds ({duration/60:.2f} minutes)")
    print(f"Output file: {output_file}")
    print(f"Log file: {log_file}")
    
    if return_code == 0:
        print("\n✅ Backtest completed successfully!")
        return output_file, log_file
    else:
        print("\n❌ Backtest failed!")
        print(f"Check log file: {log_file}")
        return None, log_file

def create_verification_script(output_file):
    """Create verification script for results"""
    
    verify_script = f'''#!/usr/bin/env python3
"""
Verify comprehensive test results
"""

import pandas as pd
import os

def verify_results():
    output_file = '{output_file}'
    
    if not os.path.exists(output_file):
        print("❌ Output file not found!")
        return
    
    print("="*80)
    print("COMPREHENSIVE TEST VERIFICATION")
    print("="*80)
    
    # Read Excel file
    xls = pd.ExcelFile(output_file)
    print(f"\\nSheets available: {{xls.sheet_names}}")
    
    # Check each strategy
    strategy_results = {{}}
    
    for sheet in xls.sheet_names:
        if sheet.endswith('Trans'):
            df = pd.read_excel(output_file, sheet_name=sheet)
            if len(df) > 0:
                strategy_name = sheet.replace(' Trans', '')
                strategy_results[strategy_name] = {{
                    'trades': len(df),
                    'exit_reasons': df['Reason'].value_counts() if 'Reason' in df.columns else {{}},
                    'total_pnl': df['Netpnlafterexpenses'].sum() if 'Netpnlafterexpenses' in df.columns else 0
                }}
    
    # Print verification results
    print("\\n1. STRATEGY EXECUTION SUMMARY:")
    print("-"*60)
    for strategy, results in strategy_results.items():
        print(f"\\n{{strategy}}:")
        print(f"  Trades: {{results['trades']}}")
        print(f"  Total P&L: ₹{{results['total_pnl']:,.2f}}")
        print("  Exit reasons:")
        for reason, count in results['exit_reasons'].items():
            print(f"    - {{reason}}: {{count}}")
    
    # Check specific features
    print("\\n2. FEATURE VERIFICATION:")
    print("-"*60)
    
    # Check SL/TP exits
    sl_hits = sum(1 for s, r in strategy_results.items() 
                  for reason in r['exit_reasons'] if 'SL' in str(reason))
    tp_hits = sum(1 for s, r in strategy_results.items() 
                  for reason in r['exit_reasons'] if 'TGT' in str(reason))
    
    print(f"✓ Stop Loss hits: {{sl_hits}}")
    print(f"✓ Target hits: {{tp_hits}}")
    
    # Check re-entries
    if 'PORTFOLIO Trans' in xls.sheet_names:
        portfolio_df = pd.read_excel(output_file, sheet_name='PORTFOLIO Trans')
        if 'Re_Entry_No' in portfolio_df.columns:
            max_reentry = portfolio_df['Re_Entry_No'].max()
            reentry_trades = len(portfolio_df[portfolio_df['Re_Entry_No'] > 0])
            print(f"✓ Re-entries detected: {{reentry_trades}} trades")
            print(f"✓ Maximum re-entry number: {{max_reentry}}")
    
    print("\\n✅ Verification complete!")

if __name__ == "__main__":
    verify_results()
'''
    
    verify_file = '/srv/samba/shared/verify_comprehensive_test.py'
    with open(verify_file, 'w') as f:
        f.write(verify_script)
    
    os.chmod(verify_file, 0o755)
    print(f"\n✅ Created verification script: {verify_file}")
    
    return verify_file

def main():
    """Main execution"""
    
    print("COMPREHENSIVE TBS COLUMN TEST")
    print("Testing all column functionalities with GPU optimization")
    print("")
    
    # Run backtest
    output_file, log_file = run_comprehensive_backtest()
    
    if output_file:
        # Create verification script
        verify_script = create_verification_script(output_file)
        
        print("\n" + "="*80)
        print("NEXT STEPS")
        print("="*80)
        print("\n1. Verify results:")
        print(f"   python3 {verify_script}")
        
        print("\n2. Check detailed logs:")
        print(f"   less {log_file}")
        
        print("\n3. Open Excel output:")
        print(f"   {output_file}")
        
        print("\n4. Manual verification checklist:")
        print("   - Check if all 18 strategies executed")
        print("   - Verify strike selection worked for all methods")
        print("   - Check SL/TP exits occurred")
        print("   - Verify re-entries happened where configured")
        print("   - Check partial exits (SqOff) at specified times")
        print("   - Verify strategy-level risk management")
        
        # Run verification automatically
        print("\n" + "="*80)
        print("RUNNING AUTOMATIC VERIFICATION")
        print("="*80)
        subprocess.run(['python3', verify_script])

if __name__ == "__main__":
    main()