#!/usr/bin/env python3
"""
Test TBS output format with updated output generator
"""
import requests
import json
import pandas as pd
import os
import time

print("="*80)
print("TESTING TBS OUTPUT FORMAT WITH STRATEGY-SPECIFIC SHEETS")
print("="*80)

# Server URL
SERVER_URL = "http://localhost:8000"

# 1. Get auth token
print("\n1. Getting auth token...")
auth_response = requests.post(
    f"{SERVER_URL}/api/v1/auth/send-otp",
    json={"phone": "9876543210"}
)
if auth_response.status_code == 200:
    verify_response = requests.post(
        f"{SERVER_URL}/api/v1/auth/verify-otp",
        json={"phone": "9876543210", "otp": "123456"}
    )
    if verify_response.status_code == 200:
        token = verify_response.json()["access_token"]
        print("✓ Authentication successful")
    else:
        print(f"✗ OTP verification failed: {verify_response.text}")
        exit(1)
else:
    print(f"✗ Send OTP failed: {auth_response.text}")
    exit(1)

headers = {"Authorization": f"Bearer {token}"}

# 2. Create TBS backtest
print("\n2. Creating TBS backtest...")
backtest_data = {
    "strategy_type": "TBS",
    "portfolio_params": {
        "start_date": "2024-04-01",
        "end_date": "2024-04-01",
        "capital": 1000000,
        "data_source": "HeavyDB",
        "use_synthetic_future_atm": True
    }
}

response = requests.post(
    f"{SERVER_URL}/api/v1/backtest/create",
    json=backtest_data,
    headers=headers
)

if response.status_code == 200:
    backtest_id = response.json()["backtest_id"]
    print(f"✓ Backtest created: {backtest_id}")
else:
    print(f"✗ Failed to create backtest: {response.text}")
    exit(1)

# 3. Wait for completion and download
print("\n3. Waiting for backtest to complete...")
max_attempts = 30
for i in range(max_attempts):
    status_response = requests.get(
        f"{SERVER_URL}/api/v1/backtest/{backtest_id}/status",
        headers=headers
    )
    if status_response.status_code == 200:
        status = status_response.json()["status"]
        if status == "completed":
            print("✓ Backtest completed")
            break
        elif status == "failed":
            print("✗ Backtest failed")
            exit(1)
    time.sleep(2)
    print(f"  Status: {status} (attempt {i+1}/{max_attempts})")

# 4. Download output
print("\n4. Downloading output...")
download_response = requests.get(
    f"{SERVER_URL}/api/v1/backtest/{backtest_id}/download",
    headers=headers
)

if download_response.status_code == 200:
    output_path = f"/srv/samba/shared/test_outputs/new_TBS_test_{int(time.time())}.xlsx"
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    with open(output_path, 'wb') as f:
        f.write(download_response.content)
    print(f"✓ Output saved to: {output_path}")
    
    # 5. Analyze the output
    print("\n5. Analyzing output structure...")
    xl = pd.ExcelFile(output_path)
    sheets = xl.sheet_names
    print(f"Total sheets: {len(sheets)}")
    print("Sheet names:")
    for i, sheet in enumerate(sheets, 1):
        print(f"  {i}. {sheet}")
    
    # Check for strategy-specific sheets
    strategy_trans_sheets = [s for s in sheets if 'Trans' in s and s != 'PORTFOLIO Trans']
    strategy_results_sheets = [s for s in sheets if 'Results' in s and s != 'PORTFOLIO Results']
    
    print(f"\nStrategy-specific Trans sheets: {len(strategy_trans_sheets)}")
    if strategy_trans_sheets:
        for sheet in strategy_trans_sheets:
            print(f"  - {sheet}")
    
    print(f"\nStrategy-specific Results sheets: {len(strategy_results_sheets)}")
    if strategy_results_sheets:
        for sheet in strategy_results_sheets:
            print(f"  - {sheet}")
    
    # Compare with expected structure
    expected_sheets = [
        'PortfolioParameter',
        'GeneralParameter',
        'LegParameter',
        'Metrics',
        'Max Profit and Loss',
        'PORTFOLIO Trans',
        'PORTFOLIO Results',
        'RS,916-1200,ATM-SELL,OTM2-BUY WITH 100%SL Trans',
        'RS,916-1200,ATM-SELL,OTM2-BUY Results'  # Truncated due to Excel limit
    ]
    
    print("\n6. COMPARISON WITH EXPECTED FORMAT")
    print("-"*40)
    
    missing_sheets = []
    for expected in expected_sheets:
        # Check if sheet exists (accounting for truncation)
        found = False
        for actual in sheets:
            if expected.startswith(actual) or actual.startswith(expected[:31]):
                found = True
                break
        if not found:
            missing_sheets.append(expected)
    
    if missing_sheets:
        print(f"❌ Missing sheets: {missing_sheets}")
    else:
        print("✅ All expected sheets present!")
        
    # Check metrics sheet
    print("\n7. Checking Metrics sheet columns...")
    metrics_df = pd.read_excel(output_path, sheet_name='Metrics')
    print(f"Metrics columns: {list(metrics_df.columns)}")
    
else:
    print(f"✗ Failed to download output: {download_response.text}")

print("\n" + "="*80)
print("TEST COMPLETE")
print("="*80)