#!/usr/bin/env python3
"""
Run contract tests to verify module interfaces
"""
import subprocess
import sys
import os

def run_contract_tests():
    """Run contract tests with pytest"""
    print("=" * 60)
    print("RUNNING CONTRACT TESTS")
    print("=" * 60)
    
    # Ensure we're in the right directory
    os.chdir("/srv/samba/shared")
    
    # Run pytest with verbose output
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/test_contracts.py",
        "-v",
        "--tb=short",
        "-s"  # Show print statements
    ]
    
    print(f"\nRunning: {' '.join(cmd)}\n")
    
    result = subprocess.run(cmd, capture_output=False)
    
    if result.returncode == 0:
        print("\n✅ All contract tests passed!")
    else:
        print("\n❌ Some contract tests failed")
        print("This is expected if HeavyDB is not fully configured")
    
    return result.returncode

if __name__ == "__main__":
    exit_code = run_contract_tests()