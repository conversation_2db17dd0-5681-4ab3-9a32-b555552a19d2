#!/usr/bin/env python3
"""
Test actual system execution with a small subset of comprehensive test cases.
This will attempt to run the real Archive and GPU systems to validate actual ATM differences.
"""

import os
import sys
import subprocess
import pandas as pd
import json
import logging
from datetime import datetime
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ActualSystemTester:
    """Tests actual Archive and GPU systems with real input files."""
    
    def __init__(self):
        self.base_dir = Path("/srv/samba/shared")
        self.test_inputs_dir = self.base_dir / "test_inputs_comprehensive"
        self.actual_results_dir = self.base_dir / "actual_system_results"
        self.actual_results_dir.mkdir(exist_ok=True)
        
        # Create simple test files for actual execution
        self.create_simple_test_files()
        
    def create_simple_test_files(self):
        """Create simple test files that should work with both systems."""
        logger.info("Creating simple test files for actual execution...")
        
        # Simple Portfolio
        portfolio_data = [{
            "portfolio_name": "ACTUAL_TEST",
            "symbol": "NIFTY",
            "start_date": "2024-01-01",
            "end_date": "2024-01-02",  # Just 2 days for quick test
            "lot_size": 50,
            "capital": 1000000
        }]
        
        strategy_data = [{
            "strategy_name": "SIMPLE_ATM_TEST",
            "portfolio_name": "ACTUAL_TEST",
            "strategy_enabled": "YES",
            "max_trades_per_day": 1,
            "description": "Simple ATM test for actual execution"
        }]
        
        # Simple TBS parameters
        general_data = [{
            "strategy_name": "SIMPLE_ATM_TEST",
            "symbol": "NIFTY",
            "product_type": "MIS",
            "dte": 0,
            "start_time": "09:15:00",
            "end_time": "15:29:00",
            "last_entry_time": "14:00:00",
            "strike_selection_time": "09:20:00",
            "re_entry": "NO",
            "max_re_entry": 0,
            "weekdays": "1,2,3,4,5",
            "on_expiry_day_trade_next_expiry": "NO",
            "sq_off_1_time": "",
            "sq_off_1_percent": 0,
            "sq_off_2_time": "",
            "sq_off_2_percent": 0,
            "comments": "Simple ATM test"
        }]
        
        leg_data = [
            {
                "strategy_name": "SIMPLE_ATM_TEST",
                "leg_id": 1,
                "option_type": "CE",
                "action": "SELL",
                "quantity": 1,
                "strike_selection": "ATM",
                "strike_value": "",
                "premium_condition": "",
                "premium_value": "",
                "delta_value": "",
                "sl_type": "PERCENTAGE",
                "sl_value": 500,
                "tgt_type": "PERCENTAGE",
                "tgt_value": 50,
                "trail_sl": "NO",
                "trail_sl_type": "",
                "trail_sl_value": "",
                "comments": "Simple CE sell"
            },
            {
                "strategy_name": "SIMPLE_ATM_TEST",
                "leg_id": 2,
                "option_type": "PE",
                "action": "SELL",
                "quantity": 1,
                "strike_selection": "ATM",
                "strike_value": "",
                "premium_condition": "",
                "premium_value": "",
                "delta_value": "",
                "sl_type": "PERCENTAGE",
                "sl_value": 500,
                "tgt_type": "PERCENTAGE",
                "tgt_value": 50,
                "trail_sl": "NO",
                "trail_sl_type": "",
                "trail_sl_value": "",
                "comments": "Simple PE sell"
            }
        ]
        
        # Save files
        self.simple_portfolio_file = self.actual_results_dir / "SIMPLE_TEST_PORTFOLIO.xlsx"
        with pd.ExcelWriter(self.simple_portfolio_file, engine='openpyxl') as writer:
            pd.DataFrame(portfolio_data).to_excel(writer, sheet_name='PortfolioSetting', index=False)
            pd.DataFrame(strategy_data).to_excel(writer, sheet_name='StrategySetting', index=False)
        
        self.simple_tbs_file = self.actual_results_dir / "SIMPLE_TEST_TBS.xlsx"
        with pd.ExcelWriter(self.simple_tbs_file, engine='openpyxl') as writer:
            pd.DataFrame(general_data).to_excel(writer, sheet_name='GeneralParameter', index=False)
            pd.DataFrame(leg_data).to_excel(writer, sheet_name='LegParameter', index=False)
        
        logger.info(f"Created simple test files: {self.simple_portfolio_file}, {self.simple_tbs_file}")
    
    def test_gpu_system_actual(self):
        """Test actual GPU system execution."""
        logger.info("Testing actual GPU system execution...")
        
        try:
            # Change to the BTRUN directory
            btrun_dir = "/srv/samba/shared/bt/backtester_stable/BTRUN"
            os.chdir(btrun_dir)
            
            # Try different GPU backtester commands
            gpu_commands = [
                # Command 1: Direct TBS GPU backtester
                [
                    "python3", "BT_TBS_GPU.py",
                    "--portfolio-excel", str(self.simple_portfolio_file),
                    "--tbs-excel", str(self.simple_tbs_file),
                    "--symbol", "NIFTY",
                    "--start-date", "240101",
                    "--end-date", "240102",
                    "--output-dir", str(self.actual_results_dir / "gpu_output")
                ],
                # Command 2: Main portfolio runner
                [
                    "python3", "BTRunPortfolio_GPU.py",
                    "--portfolio", str(self.simple_portfolio_file),
                    "--strategy", "TBS",
                    "--output", str(self.actual_results_dir / "gpu_main_output")
                ]
            ]
            
            for i, cmd in enumerate(gpu_commands, 1):
                logger.info(f"Trying GPU command {i}: {' '.join(cmd)}")
                
                try:
                    result = subprocess.run(
                        cmd,
                        capture_output=True,
                        text=True,
                        timeout=300,  # 5 minute timeout
                        cwd=btrun_dir
                    )
                    
                    if result.returncode == 0:
                        logger.info(f"✅ GPU system execution successful with command {i}")
                        logger.info(f"Output: {result.stdout[:500]}...")
                        return {
                            "status": "success",
                            "command": cmd,
                            "stdout": result.stdout,
                            "stderr": result.stderr,
                            "execution_time": "unknown"
                        }
                    else:
                        logger.warning(f"⚠️ GPU command {i} failed with return code {result.returncode}")
                        logger.warning(f"Error: {result.stderr[:500]}...")
                        
                except subprocess.TimeoutExpired:
                    logger.warning(f"⚠️ GPU command {i} timed out after 5 minutes")
                except Exception as e:
                    logger.warning(f"⚠️ GPU command {i} failed with exception: {str(e)}")
            
            # If all commands failed
            return {
                "status": "failed",
                "error": "All GPU commands failed",
                "last_stderr": result.stderr if 'result' in locals() else "No output"
            }
            
        except Exception as e:
            logger.error(f"❌ GPU system test failed: {str(e)}")
            return {
                "status": "failed",
                "error": str(e)
            }
    
    def test_archive_system_actual(self):
        """Test actual Archive system execution."""
        logger.info("Testing actual Archive system execution...")
        
        try:
            # Look for archive system files
            archive_candidates = [
                "/srv/samba/shared/BTRunPortfolio.py",
                "/srv/samba/shared/bt/archive/BTRunPortfolio.py",
                "/srv/samba/shared/legacy/BTRunPortfolio.py"
            ]
            
            archive_file = None
            for candidate in archive_candidates:
                if os.path.exists(candidate):
                    archive_file = candidate
                    break
            
            if not archive_file:
                logger.warning("⚠️ No Archive system file found")
                return {
                    "status": "failed",
                    "error": "Archive system file not found"
                }
            
            logger.info(f"Found archive system: {archive_file}")
            
            # Try to run archive system
            archive_cmd = [
                "python3", archive_file,
                "--portfolio", str(self.simple_portfolio_file),
                "--output", str(self.actual_results_dir / "archive_output")
            ]
            
            logger.info(f"Running archive command: {' '.join(archive_cmd)}")
            
            result = subprocess.run(
                archive_cmd,
                capture_output=True,
                text=True,
                timeout=300,  # 5 minute timeout
                cwd="/srv/samba/shared"
            )
            
            if result.returncode == 0:
                logger.info("✅ Archive system execution successful")
                logger.info(f"Output: {result.stdout[:500]}...")
                return {
                    "status": "success",
                    "command": archive_cmd,
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                    "execution_time": "unknown"
                }
            else:
                logger.warning(f"⚠️ Archive system failed with return code {result.returncode}")
                logger.warning(f"Error: {result.stderr[:500]}...")
                return {
                    "status": "failed",
                    "error": result.stderr,
                    "stdout": result.stdout
                }
                
        except subprocess.TimeoutExpired:
            logger.warning("⚠️ Archive system timed out after 5 minutes")
            return {
                "status": "failed",
                "error": "Timeout after 5 minutes"
            }
        except Exception as e:
            logger.error(f"❌ Archive system test failed: {str(e)}")
            return {
                "status": "failed",
                "error": str(e)
            }
    
    def analyze_outputs(self, gpu_result, archive_result):
        """Analyze outputs from both systems to find actual ATM differences."""
        logger.info("Analyzing outputs for ATM differences...")
        
        analysis = {
            "gpu_status": gpu_result.get("status", "unknown"),
            "archive_status": archive_result.get("status", "unknown"),
            "timestamp": datetime.now().isoformat()
        }
        
        # Look for output files
        gpu_output_dir = self.actual_results_dir / "gpu_output"
        archive_output_dir = self.actual_results_dir / "archive_output"
        
        # Check for Excel output files
        gpu_files = list(gpu_output_dir.glob("*.xlsx")) if gpu_output_dir.exists() else []
        archive_files = list(archive_output_dir.glob("*.xlsx")) if archive_output_dir.exists() else []
        
        analysis["gpu_output_files"] = [str(f) for f in gpu_files]
        analysis["archive_output_files"] = [str(f) for f in archive_files]
        
        # Try to extract strike information from stdout
        if gpu_result.get("stdout"):
            analysis["gpu_stdout_sample"] = gpu_result["stdout"][:1000]
        
        if archive_result.get("stdout"):
            analysis["archive_stdout_sample"] = archive_result["stdout"][:1000]
        
        # If both succeeded, try to compare actual output files
        if gpu_files and archive_files:
            try:
                gpu_df = pd.read_excel(gpu_files[0], sheet_name=0)
                archive_df = pd.read_excel(archive_files[0], sheet_name=0)
                
                analysis["gpu_columns"] = list(gpu_df.columns)
                analysis["archive_columns"] = list(archive_df.columns)
                analysis["gpu_trades"] = len(gpu_df)
                analysis["archive_trades"] = len(archive_df)
                
                # Look for strike columns
                if 'strike' in gpu_df.columns and 'strike' in archive_df.columns:
                    gpu_strikes = gpu_df['strike'].unique()
                    archive_strikes = archive_df['strike'].unique()
                    
                    analysis["gpu_strikes"] = list(gpu_strikes)
                    analysis["archive_strikes"] = list(archive_strikes)
                    analysis["strike_difference_found"] = len(set(gpu_strikes) - set(archive_strikes)) > 0
                
            except Exception as e:
                analysis["file_analysis_error"] = str(e)
        
        return analysis
    
    def run_actual_test(self):
        """Run actual system test with both Archive and GPU systems."""
        logger.info("🚀 Starting actual system execution test...")
        
        # Test GPU system
        gpu_result = self.test_gpu_system_actual()
        
        # Test Archive system
        archive_result = self.test_archive_system_actual()
        
        # Analyze results
        analysis = self.analyze_outputs(gpu_result, archive_result)
        
        # Save results
        results = {
            "gpu_result": gpu_result,
            "archive_result": archive_result,
            "analysis": analysis,
            "test_timestamp": datetime.now().isoformat()
        }
        
        results_file = self.actual_results_dir / "actual_system_test_results.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        # Generate report
        self.generate_actual_test_report(results)
        
        return results
    
    def generate_actual_test_report(self, results):
        """Generate actual test execution report."""
        
        gpu_status = results["gpu_result"]["status"]
        archive_status = results["archive_result"]["status"]
        analysis = results["analysis"]
        
        report = f"""# Actual System Execution Test Report

**Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 🎯 EXECUTION SUMMARY

### GPU System (New)
- **Status**: {gpu_status.upper()}
- **Output Files**: {len(analysis.get('gpu_output_files', []))}

### Archive System (Legacy)  
- **Status**: {archive_status.upper()}
- **Output Files**: {len(analysis.get('archive_output_files', []))}

## 📊 DETAILED RESULTS

### GPU System Details
"""
        
        if gpu_status == "success":
            report += f"""
✅ **GPU system executed successfully**
- Command used: {' '.join(results['gpu_result'].get('command', []))}
- Output files generated: {analysis.get('gpu_output_files', [])}
"""
        else:
            report += f"""
❌ **GPU system failed**
- Error: {results['gpu_result'].get('error', 'Unknown error')}
- Last stderr: {results['gpu_result'].get('stderr', 'No stderr')[:500]}...
"""
        
        report += f"""
### Archive System Details
"""
        
        if archive_status == "success":
            report += f"""
✅ **Archive system executed successfully**
- Command used: {' '.join(results['archive_result'].get('command', []))}
- Output files generated: {analysis.get('archive_output_files', [])}
"""
        else:
            report += f"""
❌ **Archive system failed**
- Error: {results['archive_result'].get('error', 'Unknown error')}
- Last stderr: {results['archive_result'].get('stderr', 'No stderr')[:500]}...
"""
        
        # Strike analysis if available
        if analysis.get('gpu_strikes') and analysis.get('archive_strikes'):
            report += f"""
## ⚡ STRIKE ANALYSIS FOUND

### GPU System Strikes
{analysis['gpu_strikes']}

### Archive System Strikes  
{analysis['archive_strikes']}

### Strike Difference Detection
- **Different strikes found**: {analysis.get('strike_difference_found', False)}
- **This confirms ATM calculation differences**: {'YES' if analysis.get('strike_difference_found') else 'NO'}
"""
        
        report += f"""
## 🔍 NEXT STEPS

### If Both Systems Succeeded
1. ✅ Analyze actual strike differences in output files
2. ✅ Validate ATM calculation methods
3. ✅ Compare trade-by-trade results
4. ✅ Measure actual performance differences

### If Systems Failed
1. ❌ Fix configuration issues identified
2. ❌ Re-run with corrected settings
3. ❌ Use simulation results for validation

## 📁 FILES GENERATED

- Test input files: `{self.simple_portfolio_file.name}`, `{self.simple_tbs_file.name}`
- Results file: `actual_system_test_results.json`
- GPU outputs: `gpu_output/` directory
- Archive outputs: `archive_output/` directory

**Location**: `/srv/samba/shared/actual_system_results/`
"""
        
        # Save report
        report_file = self.actual_results_dir / "ACTUAL_SYSTEM_TEST_REPORT.md"
        with open(report_file, 'w') as f:
            f.write(report)
        
        logger.info(f"📋 Actual test report generated: {report_file}")
        
        # Print summary
        print(f"\n{'='*60}")
        print(f"🔬 ACTUAL SYSTEM EXECUTION TEST COMPLETED")
        print(f"{'='*60}")
        print(f"🖥️ GPU System: {gpu_status.upper()}")
        print(f"🗃️ Archive System: {archive_status.upper()}")
        if analysis.get('strike_difference_found'):
            print(f"⚡ Strike differences detected in actual execution!")
        print(f"📋 Report: {report_file}")
        print(f"📊 Results: {self.actual_results_dir}/actual_system_test_results.json")
        print(f"{'='*60}")

def main():
    """Run actual system execution test."""
    tester = ActualSystemTester()
    results = tester.run_actual_test()
    
    # Return results for further analysis
    return results

if __name__ == "__main__":
    main()