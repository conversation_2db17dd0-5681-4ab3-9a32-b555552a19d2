#!/usr/bin/env python3
"""
Test script to verify exit time handling in the backtester.

This script creates sample trade data and simulates the risk evaluation process
to identify why trades are exiting at entry time (9:16) instead of the specified
exit time (12:00).
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Import modules from the backtester
from bt.backtester_stable.BTRUN.models.risk import RiskRule, RiskRuleType, NumberType, evaluate_risk_rule
from bt.backtester_stable.BTRUN.trade_builder import build_trade_record
from bt.backtester_stable.BTRUN.models.common import OptionType, TransactionType
from bt.backtester_stable.BTRUN.models.leg import LegModel

# Create mock data
def create_sample_tick_data(entry_time="09:16:00", exit_time="12:00:00", entry_price=100.0, underlying=22000.0):
    """Create sample tick data for testing risk evaluation."""
    
    # Convert times to datetime
    entry_dt = datetime.strptime(f"2025-04-01 {entry_time}", "%Y-%m-%d %H:%M:%S")
    exit_dt = datetime.strptime(f"2025-04-01 {exit_time}", "%Y-%m-%d %H:%M:%S")
    
    # Create a time range from entry to exit with 1-minute intervals
    time_range = pd.date_range(entry_dt, exit_dt, freq='1min')
    
    # Create tick data DataFrame
    tick_df = pd.DataFrame({
        'datetime': time_range,
        'high': [entry_price * (1 + 0.01 * i) for i in range(len(time_range))],  # Increasing high prices
        'low': [entry_price * (1 - 0.005 * i) for i in range(len(time_range))],   # Decreasing low prices
        'close': [entry_price * (1 + 0.008 * i) for i in range(len(time_range))], # Slightly increasing close prices
        'ce_high': [entry_price * (1 + 0.01 * i) for i in range(len(time_range))],
        'ce_low': [entry_price * (1 - 0.005 * i) for i in range(len(time_range))],
        'ce_close': [entry_price * (1 + 0.008 * i) for i in range(len(time_range))],
        'pe_high': [entry_price * (1 + 0.01 * i) for i in range(len(time_range))],
        'pe_low': [entry_price * (1 - 0.005 * i) for i in range(len(time_range))],
        'pe_close': [entry_price * (1 + 0.008 * i) for i in range(len(time_range))],
        'underlying_price': [underlying] * len(time_range),
        'trade_date': [datetime.strptime("2025-04-01", "%Y-%m-%d").date()] * len(time_range),
        'trade_time': [int(dt.strftime("%H%M%S")) for dt in time_range],
        'strike': [22000.0] * len(time_range),
    })
    
    return tick_df

def create_entry_exit_rows(entry_time="09:16:00", exit_time="12:00:00", entry_price=100.0, underlying=22000.0):
    """Create sample entry and exit rows for trade building."""
    
    # Convert times to datetime
    entry_dt = datetime.strptime(f"2025-04-01 {entry_time}", "%Y-%m-%d %H:%M:%S")
    exit_dt = datetime.strptime(f"2025-04-01 {exit_time}", "%Y-%m-%d %H:%M:%S")
    
    # Create entry row
    entry_row = pd.Series({
        'datetime': entry_dt,
        'trade_date': entry_dt.date(),
        'trade_time': int(entry_dt.strftime("%H%M%S")),
        'ce_close': entry_price,
        'pe_close': entry_price,
        'underlying_price': underlying,
        'strike': 22000.0,
        'expiry_date': (entry_dt + timedelta(days=3)).date(),
    })
    
    # Create exit row
    exit_row = pd.Series({
        'datetime': exit_dt,
        'trade_date': exit_dt.date(),
        'trade_time': int(exit_dt.strftime("%H%M%S")),
        'ce_close': entry_price * 1.05,  # Slight profit
        'pe_close': entry_price * 0.95,  # Slight loss
        'underlying_price': underlying,
        'strike': 22000.0,
    })
    
    return entry_row, exit_row

def test_sl_values():
    """Test different SL values and their impact on risk evaluation."""
    print("\n=== Testing SL Values ===")
    
    # Create sample tick data
    tick_df = create_sample_tick_data()
    
    # Test different SL values for SELL option
    sl_values = [0, 10, 50, 100, 150, 200, 500]
    for sl_value in sl_values:
        sl_rule = RiskRule(
            rule_type=RiskRuleType.STOPLOSS,
            number_type=NumberType.PERCENTAGE,
            value=sl_value,
            params={'start_time': '091600'}  # Add start_time to skip first candle
        )
        
        # Test for SELL (is_long=False)
        exit_price, exit_time, triggered, reason = evaluate_risk_rule(
            tick_df=tick_df, 
            risk_rule=sl_rule,
            entry_price=100.0,
            is_long=False  # SELL
        )
        
        if triggered:
            print(f"SELL SL={sl_value}%: Triggered at {exit_time}, price={exit_price:.2f}, reason='{reason}'")
        else:
            print(f"SELL SL={sl_value}%: Not triggered")
    
    print("\n---")
    
    # Test different SL values for BUY option
    for sl_value in sl_values:
        sl_rule = RiskRule(
            rule_type=RiskRuleType.STOPLOSS,
            number_type=NumberType.PERCENTAGE,
            value=sl_value,
            params={'start_time': '091600'}  # Add start_time to skip first candle
        )
        
        # Test for BUY (is_long=True)
        exit_price, exit_time, triggered, reason = evaluate_risk_rule(
            tick_df=tick_df, 
            risk_rule=sl_rule,
            entry_price=100.0,
            is_long=True  # BUY
        )
        
        if triggered:
            print(f"BUY SL={sl_value}%: Triggered at {exit_time}, price={exit_price:.2f}, reason='{reason}'")
        else:
            print(f"BUY SL={sl_value}%: Not triggered")

def test_tp_values():
    """Test different TP values and their impact on risk evaluation."""
    print("\n=== Testing TP Values ===")
    
    # Create sample tick data
    tick_df = create_sample_tick_data()
    
    # Test different TP values
    tp_values = [0, 10, 50, 100, 150, 200]
    for tp_value in tp_values:
        tp_rule = RiskRule(
            rule_type=RiskRuleType.TAKEPROFIT,
            number_type=NumberType.PERCENTAGE,
            value=tp_value,
            params={'start_time': '091600'}  # Add start_time to skip first candle
        )
        
        # Test for SELL (is_long=False)
        exit_price, exit_time, triggered, reason = evaluate_risk_rule(
            tick_df=tick_df, 
            risk_rule=tp_rule,
            entry_price=100.0,
            is_long=False  # SELL
        )
        
        if triggered:
            print(f"SELL TP={tp_value}%: Triggered at {exit_time}, price={exit_price:.2f}, reason='{reason}'")
        else:
            print(f"SELL TP={tp_value}%: Not triggered")
    
    print("\n---")
    
    # Test different TP values for BUY option
    for tp_value in tp_values:
        tp_rule = RiskRule(
            rule_type=RiskRuleType.TAKEPROFIT,
            number_type=NumberType.PERCENTAGE,
            value=tp_value,
            params={'start_time': '091600'}  # Add start_time to skip first candle
        )
        
        # Test for BUY (is_long=True)
        exit_price, exit_time, triggered, reason = evaluate_risk_rule(
            tick_df=tick_df, 
            risk_rule=tp_rule,
            entry_price=100.0,
            is_long=True  # BUY
        )
        
        if triggered:
            print(f"BUY TP={tp_value}%: Triggered at {exit_time}, price={exit_price:.2f}, reason='{reason}'")
        else:
            print(f"BUY TP={tp_value}%: Not triggered")

def test_trade_builder():
    """Test the trade builder's exit time handling."""
    print("\n=== Testing Trade Builder Exit Time Handling ===")
    
    # Create sample entry and exit rows
    entry_row, exit_row = create_entry_exit_rows(entry_time="09:16:00", exit_time="12:00:00")
    
    # Set up a mock leg
    leg = LegModel(
        leg_id="1",
        index="NIFTY",
        option_type=OptionType.CALL,
        transaction=TransactionType.SELL,  # SELL option
        lots=1,
        entry_time="091600",
        exit_time="120000",
        risk_rules=[
            {
                "rule_type": "STOPLOSS",
                "number_type": "PERCENTAGE",
                "value": 100.0  # This triggers immediately for SELL
            }
        ]
    )
    
    # Add exit_reason to exit_row to simulate SL hit
    exit_row['exit_reason'] = "Stop Loss Hit"
    exit_row['trade_time'] = 91700  # 9:17 AM (shortly after entry)
    
    # Build trade record
    trade_record = build_trade_record(
        portfolio_name="TestPortfolio",
        strategy_name="TestStrategy",
        leg=leg,
        entry_row=entry_row,
        exit_row=exit_row,
        slippage_pct=0.1
    )
    
    # Check exit time
    print(f"Entry Time: {trade_record['entry_time']}")
    print(f"Exit Time: {trade_record['exit_time']}")
    print(f"Exit Reason: {trade_record['reason']}")
    print(f"Exit Datetime: {trade_record['exit_datetime']}")
    
    # Test with different exit reasons
    exit_reasons = ["Stop Loss Hit", "Target Hit", "Exit Time Hit", "Trail SL Hit"]
    for reason in exit_reasons:
        exit_row['exit_reason'] = reason
        trade_record = build_trade_record(
            portfolio_name="TestPortfolio",
            strategy_name="TestStrategy",
            leg=leg,
            entry_row=entry_row,
            exit_row=exit_row,
            slippage_pct=0.1
        )
        print(f"\nExit Reason '{reason}':")
        print(f"  Exit Time: {trade_record['exit_time']}")
        print(f"  Reported Reason: {trade_record['reason']}")
        print(f"  Exit Datetime: {trade_record['exit_datetime']}")

def recommend_fixes():
    """Recommend fixes for the exit time issue."""
    print("\n=== Recommended Fixes ===")
    print("1. In risk.py -> evaluate_risk_rule():")
    print("   - Adjust SL values to prevent immediate triggering:")
    print("     - For SELL legs: Use SL=500% instead of 100%")
    print("     - For BUY legs: Use SL=50% instead of 0%")
    print("   - Skip the first entry candle to prevent immediate triggering")
    print("   - Add start_time param to risk rules to filter candles before start time")
    
    print("\n2. In heavydb_trade_processing.py -> get_trades_for_portfolio():")
    print("   - Enforce the strategy exit time for SL/TP exits")
    print("   - Override risk_exit_row['trade_time'] with strategy_exit_time_int")
    print("   - Change exit_reason to 'Exit Time Hit' for overridden exits")
    
    print("\n3. In trade_builder.py -> build_trade_record():")
    print("   - Always override early exits (within first few minutes) with scheduled exit time")
    print("   - Update exit_row['datetime'] to match the modified exit time")
    print("   - Ensure exit_datetime field is consistent with exit_time")

def main():
    """Main test function."""
    print("=== Exit Time Issue Test Script ===")
    
    # Test SL and TP values
    test_sl_values()
    test_tp_values()
    
    # Test trade builder
    test_trade_builder()
    
    # Recommend fixes
    recommend_fixes()

if __name__ == "__main__":
    main() 