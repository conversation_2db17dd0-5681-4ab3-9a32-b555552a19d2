#!/usr/bin/env python3
"""
Manual test for TBS refactored implementation with actual input sheets
"""

import os
import sys
import pandas as pd
from datetime import datetime

# Add the backtester_v2 directory to Python path
sys.path.insert(0, '/srv/samba/shared/bt/backtester_stable/BTRUN/backtester_v2')

from strategies.tbs.strategy import TBSStrategy
from strategies.tbs.parser import T<PERSON><PERSON>ars<PERSON>

def test_tbs_with_actual_files():
    """Test TBS refactoring with actual input sheets"""
    
    print("="*60)
    print("TESTING TBS REFACTORING WITH ACTUAL INPUT SHEETS")
    print("="*60)
    
    # Define input files
    portfolio_file = '/srv/samba/shared/input_portfolio.xlsx'
    tbs_file = '/srv/samba/shared/input_tbs_multi_legs.xlsx'
    
    # Test 1: Parser functionality
    print("\n1. Testing Parser...")
    parser = TBSParser()
    
    try:
        # Parse portfolio
        portfolio_data = parser.parse_portfolio_excel(portfolio_file)
        print(f"✅ Portfolio parsed successfully")
        print(f"   - Portfolio name: {portfolio_data['portfolio']['portfolio_name']}")
        print(f"   - Date range: {portfolio_data['portfolio']['start_date']} to {portfolio_data['portfolio']['end_date']}")
        print(f"   - Total strategies: {len(portfolio_data['strategies'])}")
        
        # Find enabled TBS strategies
        enabled_tbs = [s for s in portfolio_data['strategies'] 
                      if s.get('enabled') and s.get('strategytype', '').upper() == 'TBS']
        print(f"   - Enabled TBS strategies: {len(enabled_tbs)}")
        
        # Parse TBS multi-leg file
        tbs_data = parser.parse_multi_leg_excel(tbs_file)
        print(f"\n✅ TBS file parsed successfully")
        print(f"   - Total strategies in file: {len(tbs_data['general_parameters'])}")
        print(f"   - Total legs: {len(tbs_data['leg_parameters'])}")
        
        # Show strategy details
        if tbs_data['general_parameters']:
            first_strategy = tbs_data['general_parameters'][0]
            print(f"\n   First Strategy Details:")
            print(f"   - Name: {first_strategy.get('strategy_name', 'N/A')}")
            print(f"   - Index: {first_strategy.get('index', 'N/A')}")
            print(f"   - Legs: {first_strategy.get('no_of_legs', 0)}")
            
            # Find legs for this strategy
            strategy_legs = [leg for leg in tbs_data['leg_parameters'] 
                           if leg.get('strategy_name') == first_strategy.get('strategy_name')]
            print(f"   - Found {len(strategy_legs)} legs for this strategy")
            
    except Exception as e:
        print(f"❌ Parser test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test 2: Manual merge to fix the linking issue
    print("\n2. Testing Manual Strategy Linking...")
    try:
        # For each enabled TBS strategy, find corresponding strategy in TBS file
        merged_strategies = []
        
        for portfolio_strategy in enabled_tbs:
            excel_path = portfolio_strategy.get('strategyexcelfilepath', '')
            
            # Check if this matches our TBS file
            if os.path.basename(excel_path) == os.path.basename(tbs_file):
                # Since we don't have a direct link, we'll use the first strategy
                # In a real implementation, we'd need a StrategyName field
                if tbs_data['general_parameters']:
                    tbs_strategy = tbs_data['general_parameters'][0].copy()
                    
                    # Merge portfolio settings into strategy
                    tbs_strategy['portfolio_name'] = portfolio_data['portfolio']['portfolio_name']
                    tbs_strategy['enabled'] = portfolio_strategy.get('enabled')
                    
                    # Find legs for this strategy
                    strategy_legs = [leg for leg in tbs_data['leg_parameters'] 
                                   if leg.get('strategy_name') == tbs_strategy.get('strategy_name')]
                    
                    # Add to merged strategies
                    merged_strategies.append({
                        'strategy': tbs_strategy,
                        'legs': strategy_legs
                    })
                    
                    print(f"✅ Linked strategy: {tbs_strategy.get('strategy_name')}")
                    print(f"   - Portfolio: {tbs_strategy.get('portfolio_name')}")
                    print(f"   - Legs: {len(strategy_legs)}")
        
        if not merged_strategies:
            print("❌ No strategies could be linked")
            return False
            
    except Exception as e:
        print(f"❌ Manual linking failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test 3: Query Generation (without database)
    print("\n3. Testing Query Generation...")
    try:
        from strategies.tbs.query_builder import TBSQueryBuilder
        
        builder = TBSQueryBuilder()
        
        # Use the first merged strategy
        if merged_strategies:
            test_strategy = merged_strategies[0]
            
            # Create a simplified params structure
            params = {
                'portfolio_settings': portfolio_data['portfolio'],
                'strategies': [{
                    **test_strategy['strategy'],
                    'legs': test_strategy['legs']
                }]
            }
            
            queries = builder.build_queries(params)
            print(f"✅ Generated {len(queries)} queries")
            
            # Show first 200 chars of first query
            if queries:
                print(f"\n   First query preview:")
                print(f"   {queries[0][:200]}...")
                
    except Exception as e:
        print(f"❌ Query generation failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test 4: Full Strategy Integration (without database execution)
    print("\n4. Testing Full Strategy Integration...")
    try:
        strategy = TBSStrategy()
        
        # Create input data with fixed linking
        input_data = {
            'portfolio_excel': portfolio_file,
            'tbs_excel': tbs_file,
            # Add a manual strategy name mapping for now
            'strategy_name_mapping': {
                'Strategy_14': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL'
            }
        }
        
        # This will likely fail due to validation, but let's see the error
        try:
            parsed_params = strategy.parse_input(input_data)
            print("✅ Strategy parsed successfully")
        except ValueError as ve:
            print(f"⚠️  Validation error (expected): {ve}")
            # This is expected due to the linking issue
            
    except Exception as e:
        print(f"❌ Strategy integration failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    print("✅ Parser: Working correctly")
    print("✅ Manual Linking: Demonstrates the issue and solution")
    print("✅ Query Builder: Working correctly")
    print("⚠️  Strategy Integration: Needs linking mechanism fix")
    
    print("\n📋 RECOMMENDATIONS:")
    print("1. Add 'StrategyName' column to portfolio Excel")
    print("2. Update parser to use explicit strategy name linking")
    print("3. Add validation for orphaned strategies")
    print("4. Update documentation with new linking requirements")
    
    return True

if __name__ == "__main__":
    test_tbs_with_actual_files()