#!/usr/bin/env python3
import os
import sys
import logging
import pandas as pd
import subprocess
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_test_data():
    """Generate a small test dataset for a single month"""
    logger.info("Generating test data for a single month")
    
    # Generate trading dates (just 5 days)
    start_date = datetime(2023, 1, 2)  # Monday
    trade_dates = [start_date + timedelta(days=i) for i in range(5)]
    
    # Generate expiry dates (weekly Thursday expiries for next 4 weeks)
    expiry_dates = []
    
    # Find next Thursday
    days_until_thursday = (3 - start_date.weekday()) % 7
    next_thursday = start_date + timedelta(days=days_until_thursday)
    
    # Generate 4 weekly expiries
    for i in range(4):
        expiry_dates.append(next_thursday + timedelta(weeks=i))
    
    # Trading times - Just 3 times to keep it small
    trade_times = ['09:15:00', '12:00:00', '15:00:00']
    
    # Create sample data
    data = []
    
    # Generate 100 sample rows (5 days x 20 rows per day)
    for trade_date in trade_dates:
        for i in range(20):
            # Pick a time and expiry
            time_idx = i % len(trade_times)
            expiry_idx = i % len(expiry_dates)
            
            # Basic fields
            spot_base = 18000 + (i % 200)
            strike_base = 17800 + (i % 5) * 50
            
            row = {
                'trade_date': trade_date.strftime('%Y-%m-%d'),
                'trade_time': trade_times[time_idx],
                'expiry_date': expiry_dates[expiry_idx].strftime('%Y-%m-%d'),
                'index_name': 'NIFTY',
                'spot': spot_base,
                'atm_strike': spot_base // 50 * 50,
                'strike': strike_base,
                'dte': (expiry_dates[expiry_idx] - trade_date).days,
                'expiry_bucket': ['CW', 'NW', 'CM', 'NM'][i % 4],
                'zone_id': (time_idx % 5) + 1,
                'zone_name': ['OPEN', 'MID_MORN', 'AFTERNOON'][time_idx],
                'call_strike_type': ['ATM', 'ITM1', 'OTM1'][i % 3],
                'put_strike_type': ['ATM', 'OTM1', 'ITM1'][i % 3],
            }
            
            # Call option fields
            row.update({
                'ce_symbol': f"NIFTY{expiry_dates[expiry_idx].strftime('%d%b%y').upper()}{strike_base}CE",
                'ce_open': 100 + (i % 50),
                'ce_high': 120 + (i % 50),
                'ce_low': 90 + (i % 50),
                'ce_close': 110 + (i % 50),
                'ce_volume': 1000 + i * 10,
                'ce_oi': 5000 + i * 50,
                'ce_coi': (i % 200) - 100,
                'ce_iv': 15 + (i % 10),
                'ce_delta': 0.5 + (i % 50) / 100,
                'ce_gamma': 0.05 + (i % 10) / 100,
                'ce_theta': -10 - (i % 5),
                'ce_vega': 10 + (i % 5),
                'ce_rho': 5 + (i % 3),
            })
            
            # Put option fields
            row.update({
                'pe_symbol': f"NIFTY{expiry_dates[expiry_idx].strftime('%d%b%y').upper()}{strike_base}PE",
                'pe_open': 100 + (i % 50),
                'pe_high': 120 + (i % 50),
                'pe_low': 90 + (i % 50),
                'pe_close': 110 + (i % 50),
                'pe_volume': 1000 + i * 10,
                'pe_oi': 5000 + i * 50,
                'pe_coi': (i % 200) - 100,
                'pe_iv': 15 + (i % 10),
                'pe_delta': -0.5 - (i % 50) / 100,
                'pe_gamma': 0.05 + (i % 10) / 100,
                'pe_theta': -10 - (i % 5),
                'pe_vega': 10 + (i % 5),
                'pe_rho': -5 - (i % 3),
            })
            
            # Future fields
            row.update({
                'future_open': spot_base + 50 + (i % 50),
                'future_high': spot_base + 100 + (i % 50),
                'future_low': spot_base + (i % 50),
                'future_close': spot_base + 75 + (i % 50),
                'future_volume': 5000 + i * 20,
                'future_oi': 10000 + i * 100,
                'future_coi': (i % 400) - 200,
            })
            
            data.append(row)
    
    logger.info(f"Generated {len(data)} test rows")
    return data

def generate_insert_sql(data, output_file="test_insert.sql"):
    """Generate SQL INSERT statements from data rows"""
    logger.info(f"Generating INSERT statements for {len(data)} rows to {output_file}")
    
    with open(output_file, 'w') as f:
        # Generate INSERT statements for each row
        for i, row in enumerate(data):
            # Start the INSERT statement
            f.write("INSERT INTO nifty_option_chain VALUES (\n")
            
            # Add values
            values = []
            for field in [
                'trade_date', 'trade_time', 'expiry_date', 'index_name', 'spot', 
                'atm_strike', 'strike', 'dte', 'expiry_bucket', 'zone_id', 
                'zone_name', 'call_strike_type', 'put_strike_type', 'ce_symbol', 
                'ce_open', 'ce_high', 'ce_low', 'ce_close', 'ce_volume', 
                'ce_oi', 'ce_coi', 'ce_iv', 'ce_delta', 'ce_gamma', 
                'ce_theta', 'ce_vega', 'ce_rho', 'pe_symbol', 'pe_open', 
                'pe_high', 'pe_low', 'pe_close', 'pe_volume', 'pe_oi', 
                'pe_coi', 'pe_iv', 'pe_delta', 'pe_gamma', 'pe_theta', 
                'pe_vega', 'pe_rho', 'future_open', 'future_high', 'future_low', 
                'future_close', 'future_volume', 'future_oi', 'future_coi'
            ]:
                value = row[field]
                if isinstance(value, str):
                    values.append(f"'{value}'")
                else:
                    values.append(str(value))
            
            # Join values with commas
            f.write("    " + ",\n    ".join(values))
            
            # End the statement
            f.write("\n);\n")
    
    logger.info(f"SQL statements written to {output_file}")
    return output_file

def execute_sql_file(sql_file):
    """Execute a SQL file using heavysql command line"""
    logger.info(f"Executing SQL file: {sql_file}")
    cmd = f"/opt/heavyai/bin/heavysql -s 127.0.0.1 --port 6274 -u admin -p HyperInteractive -d heavyai < {sql_file}"
    
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        logger.info(f"SQL execution successful")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"SQL execution failed: {e}")
        logger.error(f"Error output: {e.stderr}")
        return False

def main():
    try:
        # Generate test data
        test_data = generate_test_data()
        
        # Generate SQL file
        sql_file = "test_batch.sql"
        generate_insert_sql(test_data, sql_file)
        
        # Execute SQL file
        if execute_sql_file(sql_file):
            logger.info("Test data loaded successfully")
        else:
            logger.error("Failed to load test data")
        
        # Verify data in table
        verify_cmd = "python3 check_table.py"
        subprocess.run(verify_cmd, shell=True)
            
    except Exception as e:
        logger.error(f"Error in main process: {str(e)}")

if __name__ == "__main__":
    main() 