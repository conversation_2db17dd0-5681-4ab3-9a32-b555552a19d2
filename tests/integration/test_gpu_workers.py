#!/usr/bin/env python3
"""
Test GPU worker functionality with different configurations
"""

import os
import sys
import subprocess
import time
import pandas as pd
from datetime import datetime

def run_test(workers, test_name):
    """Run GPU enhanced backtester with specific worker count"""
    print(f"\n{'='*80}")
    print(f"Testing: {test_name}")
    print(f"Workers: {workers}")
    print(f"{'='*80}")
    
    output_dir = f"/srv/samba/shared/Trades/gpu_worker_test_{workers}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    cmd = [
        sys.executable,
        "/srv/samba/shared/bt/backtester_stable/BTRUN/BT_TV_GPU_enhanced.py",
        "--tv-file", "input_tv.xlsx",
        "--output-dir", output_dir,
        "--workers", str(workers),
        "--debug"
    ]
    
    start_time = time.time()
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        elapsed = time.time() - start_time
        
        print(f"Completed in: {elapsed:.2f} seconds")
        
        # Check for output
        output_files = []
        if os.path.exists(output_dir):
            output_files = [f for f in os.listdir(output_dir) if f.endswith('.xlsx')]
            
        if output_files:
            print(f"✅ Success - Generated {len(output_files)} output files")
            
            # Check the output
            output_file = os.path.join(output_dir, output_files[0])
            df = pd.read_excel(output_file, sheet_name='All Trades')
            print(f"   Total trades: {len(df)}")
            print(f"   Total P&L: {df['pnl'].sum():.2f}")
            
            # Look for worker info in logs
            if "Using" in result.stdout:
                for line in result.stdout.split('\n'):
                    if "Using" in line and "workers" in line:
                        print(f"   {line.strip()}")
                        break
                        
            return elapsed, len(df)
        else:
            print(f"❌ Failed - No output files generated")
            if result.stderr:
                print(f"   Error: {result.stderr[:200]}")
            return elapsed, 0
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return 0, 0

def main():
    """Run worker tests"""
    print("GPU Worker Functionality Test")
    print("="*80)
    
    # Test configurations
    tests = [
        (1, "Single Worker (Sequential)"),
        (4, "4 Workers"),
        (8, "8 Workers"),
        ("auto", "Auto Workers (CPU-based)"),
    ]
    
    results = []
    
    for workers, name in tests:
        elapsed, trades = run_test(workers, name)
        results.append({
            'Workers': workers,
            'Name': name,
            'Time (s)': elapsed,
            'Trades': trades,
            'Trades/sec': trades / elapsed if elapsed > 0 else 0
        })
        
        # Small delay between tests
        time.sleep(2)
    
    # Summary
    print(f"\n{'='*80}")
    print("SUMMARY")
    print(f"{'='*80}")
    
    df = pd.DataFrame(results)
    print(df.to_string(index=False))
    
    # Calculate speedup
    if len(results) > 0 and results[0]['Time (s)'] > 0:
        base_time = results[0]['Time (s)']
        print(f"\nSpeedup compared to single worker:")
        for r in results[1:]:
            if r['Time (s)'] > 0:
                speedup = base_time / r['Time (s)']
                print(f"  {r['Name']}: {speedup:.2f}x")

if __name__ == "__main__":
    main()