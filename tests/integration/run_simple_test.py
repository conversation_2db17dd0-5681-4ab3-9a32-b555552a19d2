#!/usr/bin/env python3
"""
Simple test runner to validate comprehensive test files work
"""

import os
import subprocess
import json
from datetime import datetime

def run_oi_test():
    """Run a simple OI test"""
    print("\n" + "="*60)
    print("Running OI Test")
    print("="*60)
    
    # Change to the correct directory
    os.chdir("/srv/samba/shared/bt/backtester_stable/BTRUN")
    
    # Run the OI backtester directly
    cmd = [
        "python3", "BT_OI_GPU.py",
        "--portfolio-excel", "input_sheets/comprehensive_tests/oi/comprehensive_oi_test_all_columns.xlsx",
        "--start-date", "240401",
        "--end-date", "240405"
    ]
    
    print(f"Command: {' '.join(cmd)}")
    print(f"Working directory: {os.getcwd()}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ OI test completed successfully")
            # Look for output file
            import glob
            output_files = glob.glob("*.xlsx")
            if output_files:
                print(f"Output file created: {output_files[0]}")
        else:
            print("❌ OI test failed")
            print(f"Error: {result.stderr}")
            
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error running OI test: {e}")
        return False

def run_tbs_test():
    """Run a simple TBS test"""
    print("\n" + "="*60)
    print("Running TBS Test")
    print("="*60)
    
    os.chdir("/srv/samba/shared/bt/backtester_stable/BTRUN")
    
    cmd = [
        "python3", "BTRunPortfolio.py",
        "--input", "input_sheets/comprehensive_tests/tbs/comprehensive_tbs_portfolio.xlsx",
        "--start", "01-04-2024",
        "--end", "05-04-2024"
    ]
    
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ TBS test completed successfully")
        else:
            print("❌ TBS test failed")
            print(f"Error: {result.stderr}")
            
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error running TBS test: {e}")
        return False

def main():
    """Run simple tests"""
    
    print(f"Starting simple test run at {datetime.now()}")
    
    results = {
        'oi': run_oi_test(),
        'tbs': run_tbs_test()
    }
    
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    for strategy, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{strategy.upper()}: {status}")
    
    # Save results
    with open('/srv/samba/shared/simple_test_results.json', 'w') as f:
        json.dump({
            'timestamp': datetime.now().isoformat(),
            'results': results
        }, f, indent=2)

if __name__ == "__main__":
    main()