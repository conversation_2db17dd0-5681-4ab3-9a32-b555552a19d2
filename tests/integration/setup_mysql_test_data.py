#!/usr/bin/env python3
"""
Setup MySQL test data for archive system comparison
"""
import mysql.connector
import pandas as pd
from datetime import datetime
import os

print("Setting up MySQL test environment...")

# Try to connect to MySQL
mysql_configs = [
    # Try different possible configurations
    {'host': 'localhost', 'user': 'mahesh', 'password': 'mahesh_123', 'database': 'historicaldb'},
    {'host': 'localhost', 'user': 'root', 'password': 'password', 'database': 'NSEFO'},
    {'host': 'localhost', 'user': 'root', 'password': '', 'database': 'NSEFO'},
]

conn = None
for config in mysql_configs:
    try:
        conn = mysql.connector.connect(**config)
        print(f"✓ Connected to MySQL with config: {config['user']}@{config['host']}")
        break
    except Exception as e:
        print(f"✗ Failed with {config['user']}@{config['host']}: {e}")

if not conn:
    print("\nMySQL not accessible. We'll use HeavyDB data for both systems.")
    print("This is acceptable as both will use the same data source.")
else:
    cursor = conn.cursor()
    
    # Check what tables exist
    cursor.execute("SHOW TABLES")
    tables = cursor.fetchall()
    print(f"\nAvailable tables: {[t[0] for t in tables]}")
    
    # Check for NIFTY data
    nifty_tables = [t[0] for t in tables if 'nifty' in t[0].lower()]
    print(f"NIFTY tables: {nifty_tables}")
    
    cursor.close()
    conn.close()

# Since MySQL might not have the data, let's set up a conversion from HeavyDB
print("\nExtracting test data from HeavyDB for comparison...")

from heavydb import connect

# Connect to HeavyDB
heavydb_conn = connect(
    host='localhost',
    port=6274,
    user='admin',
    password='HyperInteractive',
    dbname='heavyai'
)

# Get sample data for April 1, 2024
query = """
SELECT 
    trade_date,
    trade_time,
    expiry_date,
    strike,
    option_type,
    spot,
    futures,
    ce_bid_price,
    ce_ask_price,
    ce_ltp,
    ce_volume,
    ce_oi,
    pe_bid_price,
    pe_ask_price,
    pe_ltp,
    pe_volume,
    pe_oi
FROM nifty_option_chain
WHERE trade_date = DATE '2024-04-01'
AND trade_time >= '09:15:00' 
AND trade_time <= '15:30:00'
ORDER BY trade_time, strike
LIMIT 1000
"""

print("Fetching sample data from HeavyDB...")
df = pd.read_sql(query, heavydb_conn)
print(f"Fetched {len(df)} rows")

# Save as CSV for reference
csv_file = "/srv/samba/shared/test_data_sample.csv"
df.to_csv(csv_file, index=False)
print(f"Sample data saved to: {csv_file}")

# Calculate ATM strikes using both methods
print("\nComparing ATM calculations:")
spot_price = df['spot'].iloc[0]
futures_price = df['futures'].iloc[0]

# Spot-based ATM
spot_atm = round(spot_price / 50) * 50

# Synthetic future-based ATM (simplified - actual calculation is more complex)
synthetic_future = spot_price * 1.0001  # Simplified for demonstration
synthetic_atm = round(synthetic_future / 50) * 50

print(f"Spot Price: {spot_price}")
print(f"Futures Price: {futures_price}")
print(f"Spot-based ATM: {spot_atm}")
print(f"Synthetic Future-based ATM: {synthetic_atm}")
print(f"Difference: {abs(spot_atm - synthetic_atm)} points")

heavydb_conn.close()

print("\nTest data setup complete!")