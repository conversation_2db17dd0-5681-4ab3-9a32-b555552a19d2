#!/usr/bin/env python3
"""
Simple test for ORB parser with actual input sheet
"""

import sys
import pandas as pd

# Add path
sys.path.append('/srv/samba/shared/bt/backtester_stable/BTRUN/backtester_v2')

from strategies.orb.parser import <PERSON><PERSON><PERSON><PERSON><PERSON>

def test_orb_parser():
    """Test ORB parser with actual input file"""
    parser = ORBParser()
    input_file = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/orb/input_orb.xlsx'
    
    print(f"Testing ORB parser with: {input_file}")
    
    try:
        # First, let's check what sheets are in the Excel file
        xl_file = pd.ExcelFile(input_file)
        print(f"\nSheets in file: {xl_file.sheet_names}")
        
        # Parse the file
        result = parser.parse_orb_excel(input_file)
        
        if result and 'strategies' in result:
            strategies = result['strategies']
            print(f"\nSuccessfully parsed {len(strategies)} strategies")
            
            # Display each strategy
            for i, strategy in enumerate(strategies):
                print(f"\n--- Strategy {i+1} ---")
                print(f"Name: {strategy.get('strategy_name', 'Unknown')}")
                print(f"Index: {strategy.get('index', 'Unknown')}")
                print(f"Underlying: {strategy.get('underlying', 'Unknown')}")
                print(f"DTE: {strategy.get('dte', 'Unknown')}")
                print(f"ORB Range Start: {strategy.get('orb_range_start', 'N/A')}")
                print(f"ORB Range End: {strategy.get('orb_range_end', 'N/A')}")
                print(f"Last Entry Time: {strategy.get('last_entry_time', 'N/A')}")
                print(f"End Time: {strategy.get('end_time', 'N/A')}")
                print(f"Number of legs: {len(strategy.get('legs', []))}")
                
                # Display leg details
                for j, leg in enumerate(strategy.get('legs', [])):
                    print(f"\n  Leg {j+1}:")
                    print(f"    ID: {leg.get('leg_id', 'Unknown')}")
                    print(f"    Instrument: {leg.get('instrument', 'Unknown')}")
                    print(f"    Transaction: {leg.get('transaction', 'Unknown')}")
                    print(f"    Strike Method: {leg.get('strike_method', 'Unknown')}")
                    print(f"    Strike Value: {leg.get('strike_value', 'Unknown')}")
                    print(f"    Expiry: {leg.get('expiry', 'Unknown')}")
                    print(f"    Lots: {leg.get('lots', 'Unknown')}")
        else:
            print("Parser returned invalid result")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_orb_parser()