#!/usr/bin/env python3
"""
Optimized Backtest Runner with Golden Format Output

Features:
1. GPU query optimization
2. Golden format Excel output
3. Comprehensive logging and analysis
4. Performance monitoring
"""

import sys
import os
import argparse
import logging
import time
from datetime import datetime
import traceback

# Add path
sys.path.insert(0, '/srv/samba/shared')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"optimized_backtest_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger(__name__)

def run_optimized_backtest(args):
    """Run optimized backtest with all enhancements."""
    
    start_time = time.time()
    logger.info("="*80)
    logger.info("OPTIMIZED BACKTEST WITH GOLDEN FORMAT")
    logger.info("="*80)
    
    try:
        # Import modules after path setup
        from bt.backtester_stable.BTRUN.core import config
        from bt.backtester_stable.BTRUN.core.heavydb_connection import get_connection
        from bt.backtester_stable.BTRUN.strategies.gpu_query_optimizer import GPUQueryOptimizer
        from bt.backtester_stable.BTRUN.core.io_golden import write_results_golden_format
        from bt.backtester_stable.BTRUN.excel_parser import portfolio_parser
        
        # Parse portfolio
        logger.info(f"Loading portfolio from: {args.portfolio}")
        portfolios = portfolio_parser.parse_portfolio_excel(args.portfolio)
        
        if not portfolios:
            logger.error("No portfolios found in Excel file")
            return 1
        
        # Get first portfolio
        portfolio_name = list(portfolios.keys())[0]
        portfolio_model = portfolios[portfolio_name]
        
        logger.info(f"Portfolio: {portfolio_name}")
        logger.info(f"Strategies: {len(portfolio_model.strategies)}")
        
        # Initialize GPU optimizer
        connection = get_connection()
        gpu_optimizer = GPUQueryOptimizer(
            connection=connection,
            max_workers=args.gpu_workers,
            cache_ttl=300
        )
        
        # Import the actual backtester
        from bt.backtester_stable.BTRUN.strategies.heavydb_trade_processing import get_trades_for_portfolio
        from bt.backtester_stable.BTRUN.core.runtime import process_backtest_results
        
        # Prepare backtest parameters
        bt_params = {
            "portfolio_model": portfolio_model.model_dump(),
            "portfolio_name": portfolio_name,
            "start_date": args.start_date,
            "end_date": args.end_date,
            "gpu_optimizer": gpu_optimizer  # Pass optimizer
        }
        
        logger.info(f"Running backtest from {args.start_date} to {args.end_date}")
        
        # Run backtest with GPU optimization
        bt_start = time.time()
        bt_response = get_trades_for_portfolio(bt_params)
        bt_elapsed = time.time() - bt_start
        
        logger.info(f"Backtest query completed in {bt_elapsed:.2f}s")
        
        if not bt_response or 'data' not in bt_response:
            logger.error("No trades generated")
            return 1
        
        # Process results
        logger.info("Processing backtest results...")
        process_start = time.time()
        
        order_df, metrics_df, trans_dfs, day_stats, month_stats, margin_pct_stats, daily_max_pl_df = process_backtest_results(
            bt_response=bt_response,
            slippage_percent=args.slippage / 100.0,
            initial_capital=args.capital
        )
        
        process_elapsed = time.time() - process_start
        logger.info(f"Result processing completed in {process_elapsed:.2f}s")
        
        # Prepare combined result
        combined_result = {
            'order_df': order_df,
            'metrics_df': metrics_df,
            'transaction_dfs': trans_dfs,
            'day_stats': day_stats,
            'month_stats': month_stats,
            'margin_stats': margin_pct_stats,
            'daily_max_pl_df': daily_max_pl_df,
            'portfolio_model': portfolio_model
        }
        
        # Write results in golden format
        logger.info(f"Writing golden format output to: {args.output}")
        write_start = time.time()
        
        write_results_golden_format(
            combined_result=combined_result,
            output_path=args.output,
            portfolio_excel_path=args.portfolio
        )
        
        write_elapsed = time.time() - write_start
        logger.info(f"Excel writing completed in {write_elapsed:.2f}s")
        
        # Get optimization statistics
        opt_stats = gpu_optimizer.get_optimization_stats()
        logger.info("\nOPTIMIZATION STATISTICS:")
        logger.info("-" * 40)
        logger.info(f"Cache hit rate: {opt_stats['cache_stats']['hit_rate']:.2%}")
        logger.info(f"Cache hits: {opt_stats['cache_stats']['hit_count']}")
        logger.info(f"Cache misses: {opt_stats['cache_stats']['miss_count']}")
        
        # Query statistics
        if opt_stats['query_stats']:
            logger.info("\nQuery Performance:")
            for symbol, stats in opt_stats['query_stats'].items():
                logger.info(f"  {symbol}: {stats['count']} queries, avg {stats['avg_time']:.3f}s")
        
        # Summary statistics
        total_elapsed = time.time() - start_time
        logger.info("\nPERFORMANCE SUMMARY:")
        logger.info("-" * 40)
        logger.info(f"Total execution time: {total_elapsed:.2f}s")
        logger.info(f"  - Backtest queries: {bt_elapsed:.2f}s ({bt_elapsed/total_elapsed*100:.1f}%)")
        logger.info(f"  - Result processing: {process_elapsed:.2f}s ({process_elapsed/total_elapsed*100:.1f}%)")
        logger.info(f"  - Excel writing: {write_elapsed:.2f}s ({write_elapsed/total_elapsed*100:.1f}%)")
        
        # Trade statistics
        if 'portfolio' in trans_dfs:
            portfolio_df = trans_dfs['portfolio']
            logger.info(f"\nTrades processed: {len(portfolio_df)}")
            
            if 'netPnlAfterExpenses' in portfolio_df.columns:
                total_pnl = portfolio_df['netPnlAfterExpenses'].sum()
                winning_trades = (portfolio_df['netPnlAfterExpenses'] > 0).sum()
                losing_trades = (portfolio_df['netPnlAfterExpenses'] < 0).sum()
                win_rate = winning_trades / len(portfolio_df) * 100 if len(portfolio_df) > 0 else 0
                
                logger.info(f"Total P&L: ₹{total_pnl:,.2f}")
                logger.info(f"Win rate: {win_rate:.1f}% ({winning_trades}W / {losing_trades}L)")
        
        # Shutdown optimizer
        gpu_optimizer.shutdown()
        
        logger.info("\n✅ Backtest completed successfully!")
        logger.info(f"Output saved to: {args.output}")
        
        return 0
        
    except Exception as e:
        logger.error(f"Backtest failed: {e}")
        logger.error(traceback.format_exc())
        return 1

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Run optimized backtest with golden format output"
    )
    
    parser.add_argument(
        "--portfolio", "-p",
        type=str,
        required=True,
        help="Path to portfolio Excel file"
    )
    
    parser.add_argument(
        "--output", "-o",
        type=str,
        default=f"optimized_golden_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
        help="Output Excel file path"
    )
    
    parser.add_argument(
        "--start-date",
        type=str,
        default="240401",
        help="Start date (YYMMDD format)"
    )
    
    parser.add_argument(
        "--end-date",
        type=str,
        default="240430",
        help="End date (YYMMDD format)"
    )
    
    parser.add_argument(
        "--slippage",
        type=float,
        default=0.1,
        help="Slippage percentage"
    )
    
    parser.add_argument(
        "--capital",
        type=float,
        default=100000.0,
        help="Initial capital"
    )
    
    parser.add_argument(
        "--gpu-workers",
        type=int,
        default=4,
        help="Number of GPU query workers"
    )
    
    args = parser.parse_args()
    
    # Run backtest
    return run_optimized_backtest(args)

if __name__ == "__main__":
    sys.exit(main())