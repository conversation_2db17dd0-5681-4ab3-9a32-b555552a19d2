#!/usr/bin/env python3
"""
Test TV Multi-File Upload Functionality
Tests the 6-file hierarchy for TradingView backtesting
"""

import os
import pandas as pd
from datetime import datetime, timedelta
import random

# Create test directory
TEST_DIR = "/srv/samba/shared/test_tv_files"
os.makedirs(TEST_DIR, exist_ok=True)

def create_tv_signal_file():
    """Create a sample TV signal file with entry/exit pairs"""
    signals = []
    
    # Generate 10 trades for testing
    base_date = datetime(2024, 1, 15, 9, 30, 0)
    
    for i in range(1, 11):
        # Entry signal
        entry_time = base_date + timedelta(days=i-1, minutes=random.randint(0, 300))
        signals.append({
            'Trade #': f'Trade_{i}',
            'Type': 'Entry Long' if i % 2 == 0 else 'Entry Short',
            'Date/Time': entry_time.strftime('%Y%m%d %H%M%S'),
            'Contracts': random.choice([1, 2, 3, 5])
        })
        
        # Exit signal (30-120 minutes later)
        exit_time = entry_time + timedelta(minutes=random.randint(30, 120))
        signals.append({
            'Trade #': f'Trade_{i}',
            'Type': 'Exit Long' if i % 2 == 0 else 'Exit Short',
            'Date/Time': exit_time.strftime('%Y%m%d %H%M%S'),
            'Contracts': signals[-1]['Contracts']
        })
    
    df = pd.DataFrame(signals)
    filepath = os.path.join(TEST_DIR, 'tv_signals.xlsx')
    df.to_excel(filepath, index=False, sheet_name='Signals')
    print(f"Created signal file: {filepath}")
    return filepath

def create_tv_settings_file(signal_path, long_portfolio_path, short_portfolio_path):
    """Create the main TV settings file"""
    settings = [{
        'Name': 'Test_TV_MultiIndex',
        'Enabled': 'YES',
        'SignalFilePath': signal_path,
        'StartDate': '15_01_2024',
        'EndDate': '25_01_2024',
        'SignalDateFormat': '%Y%m%d %H%M%S',
        'IntradaySqOffApplicable': 'YES',
        'IntradayExitTime': '153000',
        'TvExitApplicable': 'YES',
        'DoRollover': 'NO',
        'RolloverTime': '153000',
        'ManualTradeEntryTime': '091500',
        'ManualTradeLots': 1,
        'FirstTradeEntryTime': '091500',
        'IncreaseEntrySignalTimeBy': 0,
        'IncreaseExitSignalTimeBy': 0,
        'ExpiryDayExitTime': '152000',
        'SlippagePercent': 0.1,
        'LongPortfolioFilePath': long_portfolio_path,
        'ShortPortfolioFilePath': short_portfolio_path,
        'ManualPortfolioFilePath': '',
        'UseDbExitTiming': 'YES',
        'ExitSearchInterval': 5,
        'ExitPriceSource': 'SPOT'
    }]
    
    df = pd.DataFrame(settings)
    filepath = os.path.join(TEST_DIR, 'input_tv.xlsx')
    df.to_excel(filepath, index=False, sheet_name='TV Setting')
    print(f"Created TV settings file: {filepath}")
    return filepath

def create_portfolio_file(portfolio_type, tbs_strategy_path):
    """Create a portfolio file (LONG or SHORT)"""
    portfolio = [{
        'StartDate': '01_01_2024',
        'EndDate': '31_12_2024',
        'IsTickBT': 'no',
        'Enabled': 'YES',
        'PortfolioName': f'{portfolio_type}_Portfolio',
        'PortfolioTarget': 0,
        'PortfolioStoploss': 0,
        'PortfolioTrailingType': 'portfolio lock trail',
        'PnLCalTime': 233000,
        'LockPercent': 0,
        'TrailPercent': 0,
        'SqOff1Time': 233000,
        'SqOff1Percent': 0,
        'SqOff2Time': 233000,
        'SqOff2Percent': 0,
        'ProfitReaches': 0,
        'LockMinProfitAt': 0,
        'IncreaseInProfit': 0,
        'TrailMinProfitBy': 0,
        'Multiplier': 1,
        'SlippagePercent': 0.1
    }]
    
    strategies = [{
        'Enabled': 'YES',
        'PortfolioName': f'{portfolio_type}_Portfolio',
        'StrategyType': 'TBS',
        'StrategyExcelFilePath': tbs_strategy_path
    }]
    
    filepath = os.path.join(TEST_DIR, f'portfolio_{portfolio_type.lower()}.xlsx')
    
    with pd.ExcelWriter(filepath) as writer:
        pd.DataFrame(portfolio).to_excel(writer, sheet_name='PortfolioSetting', index=False)
        pd.DataFrame(strategies).to_excel(writer, sheet_name='StrategySetting', index=False)
    
    print(f"Created {portfolio_type} portfolio file: {filepath}")
    return filepath

def create_tbs_strategy_file(strategy_type):
    """Create a TBS strategy file"""
    general_params = [{
        'Strategy_Name': f'{strategy_type}_Straddle',
        'Trade_Start_Date': '15_01_2024',
        'Trade_End_Date': '25_01_2024',
        'Start_Time': '091500',
        'Exit_Time': '152000',
        'Select_Instrument': 'OPT',
        'Take_Trade_Option': 'Both Days',
        'SL Type': 'Fix Point SL',
        'TGT Type': 'No Target',
        'MTM SL Type': 'Fix Point MTM',
        'TSL Type': 'Fix Point TSL',
        'Max_Allowed_Trades': 1,
        'Do_Rollover': 'NO',
        'Trade_Qty': 1,
        'Trade_Type': 'MIS',
        'Buy_Sell': 'Buy',
        'Opt_Type': 'Combined',
        'Leg_Count': 2,
        'Entry_Condition': 'At Time',
        'Entry_Time': '091500',
        'Exit_Condition': 'At Time',
        'Exit_Time': '152000',
        'Risk_Management_Type': 'TBS',
        'Max_Risk': 10000,
        'Max_Profit': 10000,
        'Max_Lots': 10,
        'Index_Name': 'NIFTY'  # This will be overridden by UI selection
    }]
    
    leg_params = [
        {
            'Leg_No': 'Leg_1',
            'Buy_Sell': 'Sell',
            'Trade_Qty': 1,
            'Opt_Type': 'CE',
            'Strike_Selection_1': 'Strike_Type',
            'Strike_Type': 'ATM',
            'ITM_OTM': 0,
            'Expiry_Selection': 'Monthly_0',
            'Stop_Loss': 50,
            'Profit_Target': 0,
            'MTM_Stop_Loss': 0,
            'Trailing_Stop_Loss': 0,
            'Lock_Profit_At': 0,
            'Lock_Profit_Amount': 0,
            'Trail_Profit_By': 0,
            'Re_Entry': 'NO'
        },
        {
            'Leg_No': 'Leg_2',
            'Buy_Sell': 'Sell',
            'Trade_Qty': 1,
            'Opt_Type': 'PE',
            'Strike_Selection_1': 'Strike_Type',
            'Strike_Type': 'ATM',
            'ITM_OTM': 0,
            'Expiry_Selection': 'Monthly_0',
            'Stop_Loss': 50,
            'Profit_Target': 0,
            'MTM_Stop_Loss': 0,
            'Trailing_Stop_Loss': 0,
            'Lock_Profit_At': 0,
            'Lock_Profit_Amount': 0,
            'Trail_Profit_By': 0,
            'Re_Entry': 'NO'
        }
    ]
    
    filepath = os.path.join(TEST_DIR, f'tbs_{strategy_type.lower()}.xlsx')
    
    with pd.ExcelWriter(filepath) as writer:
        pd.DataFrame(general_params).to_excel(writer, sheet_name='GeneralParameter', index=False)
        pd.DataFrame(leg_params).to_excel(writer, sheet_name='LegParameter', index=False)
    
    print(f"Created {strategy_type} TBS strategy file: {filepath}")
    return filepath

def create_complete_tv_file_set():
    """Create a complete set of 6 TV files"""
    print("Creating TV Multi-File Test Set")
    print("=" * 50)
    
    # Create TBS strategy files (files 5 & 6)
    tbs_long_path = create_tbs_strategy_file('LONG')
    tbs_short_path = create_tbs_strategy_file('SHORT')
    
    # Create portfolio files (files 3 & 4)
    portfolio_long_path = create_portfolio_file('LONG', tbs_long_path)
    portfolio_short_path = create_portfolio_file('SHORT', tbs_short_path)
    
    # Create signal file (file 2)
    signal_path = create_tv_signal_file()
    
    # Create main TV settings file (file 1)
    tv_settings_path = create_tv_settings_file(signal_path, portfolio_long_path, portfolio_short_path)
    
    print("\n" + "=" * 50)
    print("TV File Hierarchy Created:")
    print(f"1. Main TV Settings: {tv_settings_path}")
    print(f"   └── 2. Signals: {signal_path}")
    print(f"   └── 3. Long Portfolio: {portfolio_long_path}")
    print(f"       └── 5. Long TBS Strategy: {tbs_long_path}")
    print(f"   └── 4. Short Portfolio: {portfolio_short_path}")
    print(f"       └── 6. Short TBS Strategy: {tbs_short_path}")
    
    return {
        'tv_settings': tv_settings_path,
        'signals': signal_path,
        'portfolio_long': portfolio_long_path,
        'portfolio_short': portfolio_short_path,
        'tbs_long': tbs_long_path,
        'tbs_short': tbs_short_path
    }

def test_file_relationships(file_paths):
    """Test that file relationships are correctly set up"""
    print("\n" + "=" * 50)
    print("Testing File Relationships:")
    print("-" * 50)
    
    # Read TV settings
    tv_df = pd.read_excel(file_paths['tv_settings'], sheet_name='TV Setting')
    
    # Check signal file path
    signal_path = tv_df['SignalFilePath'].iloc[0]
    if os.path.exists(signal_path):
        print(f"✓ Signal file path is valid: {signal_path}")
        signals_df = pd.read_excel(signal_path, sheet_name='Signals')
        print(f"  - Contains {len(signals_df)} signals")
        print(f"  - Trade types: {signals_df['Type'].unique()}")
    else:
        print(f"✗ Signal file path is invalid: {signal_path}")
    
    # Check portfolio file paths
    for portfolio_type in ['Long', 'Short']:
        portfolio_path = tv_df[f'{portfolio_type}PortfolioFilePath'].iloc[0]
        if os.path.exists(portfolio_path):
            print(f"✓ {portfolio_type} portfolio path is valid: {portfolio_path}")
            
            # Check TBS strategy path within portfolio
            strategies_df = pd.read_excel(portfolio_path, sheet_name='StrategySetting')
            tbs_path = strategies_df['StrategyExcelFilePath'].iloc[0]
            if os.path.exists(tbs_path):
                print(f"  ✓ TBS strategy path is valid: {tbs_path}")
            else:
                print(f"  ✗ TBS strategy path is invalid: {tbs_path}")
        else:
            print(f"✗ {portfolio_type} portfolio path is invalid: {portfolio_path}")

def create_multi_index_test_files():
    """Create test files for multiple indices"""
    indices = ['NIFTY', 'BANKNIFTY', 'SENSEX']
    
    for index in indices:
        print(f"\nCreating test files for {index}...")
        test_dir = f"/srv/samba/shared/test_tv_files/{index}"
        os.makedirs(test_dir, exist_ok=True)
        
        # Modify the global TEST_DIR for this index
        global TEST_DIR
        TEST_DIR = test_dir
        
        # Create file set
        file_paths = create_complete_tv_file_set()
        
        # Update TBS files with correct index
        for strategy_type in ['long', 'short']:
            tbs_path = file_paths[f'tbs_{strategy_type}']
            df = pd.read_excel(tbs_path, sheet_name='GeneralParameter')
            df['Index_Name'] = index
            
            with pd.ExcelWriter(tbs_path, mode='a', if_sheet_exists='overlay') as writer:
                df.to_excel(writer, sheet_name='GeneralParameter', index=False)
        
        print(f"Created {index} test files in: {test_dir}")

if __name__ == "__main__":
    # Create single test set
    file_paths = create_complete_tv_file_set()
    
    # Test relationships
    test_file_relationships(file_paths)
    
    # Create multi-index test sets
    print("\n" + "=" * 70)
    print("Creating Multi-Index Test Files")
    print("=" * 70)
    create_multi_index_test_files()
    
    print("\n" + "=" * 50)
    print("TV Multi-File Test Complete!")
    print("Files created in: /srv/samba/shared/test_tv_files/")
    print("\nFor testing:")
    print("1. Upload input_tv.xlsx and portfolio files to test the 6-file hierarchy")
    print("2. The system should automatically load all referenced files")
    print("3. Test with different indices (NIFTY, BANKNIFTY, SENSEX)")