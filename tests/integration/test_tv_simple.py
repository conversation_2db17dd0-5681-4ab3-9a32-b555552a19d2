#!/usr/bin/env python3
"""Simple test to verify TV backtest functionality"""

import os
import sys
import pandas as pd

# Set up paths
sys.path.insert(0, "/srv/samba/shared/bt")

# Test 1: Check if TV input file exists and is readable
tv_file = "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tv.xlsx"
print(f"1. Checking TV input file: {tv_file}")
print(f"   File exists: {os.path.exists(tv_file)}")

# Test 2: Read TV settings
try:
    df_settings = pd.read_excel(tv_file, sheet_name="Setting")
    print(f"\n2. TV Settings loaded successfully")
    print(f"   Total rows: {len(df_settings)}")
    
    # Find enabled settings
    enabled_mask = df_settings['Enabled'].astype(str).str.upper() == 'YES'
    enabled_settings = df_settings[enabled_mask]
    print(f"   Enabled settings: {len(enabled_settings)}")
    
    if len(enabled_settings) > 0:
        for idx, row in enabled_settings.iterrows():
            print(f"\n   Setting: {row['Name']}")
            print(f"   - Signal file: {row['SignalFilePath']}")
            print(f"   - Date range: {row['StartDate']} to {row['EndDate']}")
            
            # Test 3: Check signal file
            signal_file = row['SignalFilePath']
            if os.path.exists(signal_file):
                print(f"   - Signal file exists: YES")
                try:
                    df_signals = pd.read_excel(signal_file, sheet_name="List of trades")
                    print(f"   - Signals loaded: {len(df_signals)} trades")
                    
                    # Filter signals by date range
                    # Parse dates
                    from datetime import datetime
                    start_date = datetime.strptime(row['StartDate'], "%d_%m_%Y")
                    end_date = datetime.strptime(row['EndDate'], "%d_%m_%Y")
                    
                    # Count signals in date range
                    signal_count = 0
                    for _, signal in df_signals.iterrows():
                        try:
                            signal_date = datetime.strptime(str(signal['Date/Time']), row['SignalDateFormat'])
                            if start_date <= signal_date <= end_date:
                                signal_count += 1
                        except:
                            pass
                    
                    print(f"   - Signals in date range: {signal_count}")
                    
                except Exception as e:
                    print(f"   - Error loading signals: {e}")
            else:
                print(f"   - Signal file exists: NO")
                
            # Test 4: Check portfolio files
            for portfolio_type in ['LongPortfolioFilePath', 'ShortPortfolioFilePath', 'ManualPortfolioFilePath']:
                portfolio_path = row.get(portfolio_type)
                if pd.notna(portfolio_path):
                    exists = os.path.exists(portfolio_path)
                    print(f"   - {portfolio_type}: {'EXISTS' if exists else 'NOT FOUND'}")
                    
except Exception as e:
    print(f"\nError reading TV settings: {e}")
    import traceback
    traceback.print_exc()

# Test 5: Try importing TV models
print("\n3. Testing TV model imports:")
try:
    from bt.backtester_stable.models.tv_models import TvSettingModel, RawTvSignalModel, ProcessedTvSignalModel
    print("   - TV models imported successfully")
except Exception as e:
    print(f"   - Error importing TV models: {e}")

# Test 6: Try importing TV processor
print("\n4. Testing TV processor import:")
try:
    from bt.backtester_stable.BTRUN.strategies.tv_processor import process_tv_signals
    print("   - TV processor imported successfully")
except Exception as e:
    print(f"   - Error importing TV processor: {e}")

print("\n" + "="*60)
print("TV backtest infrastructure test complete") 