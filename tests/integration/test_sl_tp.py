#!/usr/bin/env python3
"""
Test script to verify that wider SL/TP values prevent premature exit.
"""

import os
import sys
import pandas as pd
from datetime import datetime, date, time

print("Starting SL/TP exit time test...")

# Create example tick data with price movements (2% range)
today = date.today()
tick_data = pd.DataFrame({
    'datetime': [
        datetime.combine(today, time(9, 16, 0)),
        datetime.combine(today, time(9, 30, 0)),
        datetime.combine(today, time(10, 0, 0)),
        datetime.combine(today, time(10, 30, 0)),
        datetime.combine(today, time(11, 0, 0)),
        datetime.combine(today, time(11, 30, 0)),
        datetime.combine(today, time(12, 0, 0))
    ],
    'close': [100.0, 101.0, 99.0, 102.0, 98.0, 103.0, 97.0],
    'high': [100.5, 101.5, 100.0, 102.5, 99.0, 104.0, 98.0],
    'low': [99.5, 100.5, 98.0, 101.0, 97.0, 102.0, 96.0],
})

# Simple function to check if SL or TP would be triggered
def check_exit(entry_price, is_long, sl_percent, tp_percent):
    """
    Check if a trade would exit before the scheduled exit time.
    """
    # Calculate SL and TP prices
    if is_long:
        sl_price = entry_price * (1 - sl_percent/100)
        tp_price = entry_price * (1 + tp_percent/100)
    else:
        sl_price = entry_price * (1 + sl_percent/100)
        tp_price = entry_price * (1 - tp_percent/100)
    
    # Check each tick for SL/TP triggers
    exit_time = None
    exit_price = None
    exit_reason = None
    
    for _, row in tick_data.iterrows():
        tick_time = row['datetime'].time()
        tick_high = row['high']
        tick_low = row['low']
        
        # Check for SL trigger
        if is_long and tick_low <= sl_price:
            exit_time = tick_time
            exit_price = sl_price
            exit_reason = "Stop Loss Hit"
            break
        elif not is_long and tick_high >= sl_price:
            exit_time = tick_time
            exit_price = sl_price
            exit_reason = "Stop Loss Hit"
            break
        
        # Check for TP trigger
        if is_long and tick_high >= tp_price:
            exit_time = tick_time
            exit_price = tp_price
            exit_reason = "Target Hit"
            break
        elif not is_long and tick_low <= tp_price:
            exit_time = tick_time
            exit_price = tp_price
            exit_reason = "Target Hit"
            break
        
        # Check if we've reached the exit time (12:00)
        if tick_time.hour == 12 and tick_time.minute == 0:
            exit_time = tick_time
            exit_price = row['close']
            exit_reason = "Exit Time Hit"
            break
    
    return exit_time, exit_price, exit_reason

# Test cases
test_cases = [
    # Description, Long/Short, SL%, TP%
    ("SELL leg with tight SL (original)", False, 2, 0),
    ("SELL leg with wide SL (fixed)", False, 500, 100),
    ("BUY leg with tight SL", True, 2, 0),
    ("BUY leg with wide SL (fixed)", True, 50, 100),
]

# Run the tests
for desc, is_long, sl_percent, tp_percent in test_cases:
    exit_time, exit_price, reason = check_exit(100.0, is_long, sl_percent, tp_percent)
    
    print(f"\nTest: {desc}")
    print(f"SL: {sl_percent}%, TP: {tp_percent}%")
    print(f"Exit time: {exit_time}, Price: {exit_price:.2f}, Reason: {reason}")
    
    if reason == "Exit Time Hit":
        print("✅ SUCCESS: Trade exited at scheduled time (12:00)")
    else:
        print(f"❌ EARLY EXIT: Trade exited early due to {reason}")

print("\nTest completed!") 