#!/usr/bin/env python3
"""
Test SL/TP Combinations

This script tests various combinations of stop loss and take profit values
to ensure the backtester correctly handles them with the fixed exit time logic.
"""

import os
import sys
import subprocess
import pandas as pd
import logging
import shutil
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('sl_tp_test')

# Constants
BT_PATH = '/srv/samba/shared/bt/backtester_stable/BTRUN'
PORTFOLIO_FILE = os.path.join(BT_PATH, 'input_sheets/input_portfolio_fixed.xlsx')
STRATEGY_FILE = os.path.join(BT_PATH, 'input_sheets/input_tbs_fixed_exits.xlsx')
OUTPUT_BASE = '/srv/samba/shared/Trades/sl_tp_test'
GOLDEN_FILE = '/srv/samba/shared/bt/archive/backtester_stable/BTRUN/golden_file.xlsx'

# Define SL/TP combinations to test - format: (name, sell_sl, sell_tp, buy_sl, buy_tp)
SL_TP_COMBINATIONS = [
    ('tight', 200, 50, 20, 50),          # Tight SL/TP (should not trigger with skip first candle)
    ('standard', 500, 100, 50, 100),      # Standard (recommended settings)
    ('aggressive', 1000, 200, 100, 200),  # Aggressive SL/TP (very unlikely to trigger)
    ('mixed', 300, 150, 80, 50)           # Mixed (different values for testing)
]

def create_test_strategy(combo_name, sell_sl, sell_tp, buy_sl, buy_tp):
    """Create a test strategy file with the specified SL/TP values"""
    try:
        # Create a new strategy file
        new_strategy_file = os.path.join(BT_PATH, f'input_sheets/input_tbs_sl_tp_{combo_name}.xlsx')
        shutil.copy2(STRATEGY_FILE, new_strategy_file)
        
        # Read the LegParameter sheet to modify SL/TP values
        leg_df = pd.read_excel(new_strategy_file, sheet_name='LegParameter')
        
        # Modify SL/TP values based on transaction type
        for idx, row in leg_df.iterrows():
            transaction = row.get('Transaction', '').upper()
            
            if transaction == 'SELL':
                # Update SL/TP for SELL legs
                leg_df.at[idx, 'SLValue'] = sell_sl
                leg_df.at[idx, 'TGTValue'] = sell_tp
                logger.info(f"Setting SELL leg {row.get('LegID', idx)}: SL={sell_sl}%, TP={sell_tp}%")
            elif transaction == 'BUY':
                # Update SL/TP for BUY legs
                leg_df.at[idx, 'SLValue'] = buy_sl
                leg_df.at[idx, 'TGTValue'] = buy_tp
                logger.info(f"Setting BUY leg {row.get('LegID', idx)}: SL={buy_sl}%, TP={buy_tp}%")
        
        # Save the modified file
        with pd.ExcelWriter(new_strategy_file, engine='openpyxl') as writer:
            # Save modified LegParameter sheet
            leg_df.to_excel(writer, sheet_name='LegParameter', index=False)
            
            # Copy other sheets without modification
            original_df = pd.read_excel(STRATEGY_FILE, sheet_name=None)
            for sheet_name, sheet_df in original_df.items():
                if sheet_name != 'LegParameter':
                    sheet_df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        logger.info(f"Created strategy file with {combo_name} SL/TP values: {new_strategy_file}")
        return new_strategy_file
    
    except Exception as e:
        logger.error(f"Error creating test strategy file: {e}")
        return None

def create_portfolio_file(strategy_file, combo_name):
    """Create a portfolio file that references the test strategy file"""
    try:
        # Create a new portfolio file
        new_portfolio_file = os.path.join(BT_PATH, f'input_sheets/input_portfolio_sl_tp_{combo_name}.xlsx')
        
        # Read the StrategySetting sheet
        df = pd.read_excel(PORTFOLIO_FILE, sheet_name='StrategySetting')
        
        # Update the StrategyExcelFilePath to point to the new strategy file
        df['StrategyExcelFilePath'] = strategy_file
        
        # Save the modified file
        with pd.ExcelWriter(new_portfolio_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='StrategySetting', index=False)
            
            # Copy other sheets without modification
            original_df = pd.read_excel(PORTFOLIO_FILE, sheet_name=None)
            for sheet_name, sheet_df in original_df.items():
                if sheet_name != 'StrategySetting':
                    sheet_df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        logger.info(f"Created portfolio file for {combo_name} SL/TP test: {new_portfolio_file}")
        return new_portfolio_file
    
    except Exception as e:
        logger.error(f"Error creating portfolio file: {e}")
        return None

def run_backtester(portfolio_file, combo_name):
    """Run the backtester with the test portfolio file"""
    try:
        # Create timestamp for unique output file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = f"{OUTPUT_BASE}_{combo_name}_{timestamp}.xlsx"
        
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Build command
        cmd = [
            "python3",
            os.path.join(BT_PATH, "BTRunPortfolio_GPU.py"),
            "--portfolio-excel", portfolio_file,
            "--output-path", output_path
        ]
        
        logger.info(f"Running backtester for {combo_name} SL/TP test")
        logger.info(f"Command: {' '.join(cmd)}")
        
        # Run the command
        process = subprocess.run(cmd, capture_output=True, text=True)
        
        if process.returncode == 0:
            logger.info(f"Backtester completed successfully. Output: {output_path}")
            return output_path
        else:
            logger.error(f"Backtester failed with return code {process.returncode}")
            logger.error(f"STDOUT: {process.stdout}")
            logger.error(f"STDERR: {process.stderr}")
            return None
    
    except Exception as e:
        logger.error(f"Error running backtester: {e}")
        return None

def verify_sheet_structure(output_path):
    """Verify that the output file has the expected sheet structure"""
    try:
        # Load output file and golden file
        output_sheets = pd.ExcelFile(output_path).sheet_names
        
        # Check for expected sheets
        expected_sheets = ['PORTFOLIO Trans', 'PORTFOLIO Results']
        missing_sheets = [sheet for sheet in expected_sheets if sheet not in output_sheets]
        
        if missing_sheets:
            logger.error(f"Missing expected sheets: {', '.join(missing_sheets)}")
            return False
        
        # Load PORTFOLIO Trans sheet from output file
        trans_df = pd.read_excel(output_path, sheet_name='PORTFOLIO Trans')
        
        # Check for expected columns
        expected_columns = ['entry_time', 'exit_time', 'exit_datetime', 'reason', 'pnl', 'side', 'instrument_type']
        missing_columns = [col for col in expected_columns if col not in trans_df.columns]
        
        if missing_columns:
            logger.error(f"Missing expected columns in PORTFOLIO Trans: {', '.join(missing_columns)}")
            return False
        
        # Check for exit time and reason consistency
        if 'exit_time' in trans_df.columns and 'reason' in trans_df.columns:
            exit_times = trans_df['exit_time'].unique()
            exit_reasons = trans_df['reason'].unique()
            
            # All exit times should be the same (12:00:00)
            if len(exit_times) != 1 or exit_times[0] != '12:00:00':
                logger.error(f"Unexpected exit times: {', '.join(exit_times)}")
                logger.error(f"Expected all exits at 12:00:00")
                return False
            
            # All exit reasons should be "Exit Time Hit"
            if len(exit_reasons) != 1 or exit_reasons[0] != 'Exit Time Hit':
                logger.error(f"Unexpected exit reasons: {', '.join(exit_reasons)}")
                logger.error(f"Expected all reasons to be 'Exit Time Hit'")
                return False
        
        # Load PORTFOLIO Results sheet from output file
        results_df = pd.read_excel(output_path, sheet_name='PORTFOLIO Results')
        
        # Check for expected columns in Results
        expected_result_columns = ['profits', 'losses', 'fees', 'net_pnl']
        missing_result_columns = [col for col in expected_result_columns if col not in results_df.columns]
        
        if missing_result_columns:
            logger.error(f"Missing expected columns in PORTFOLIO Results: {', '.join(missing_result_columns)}")
            return False
        
        # All checks passed
        logger.info(f"✅ Output file has the expected structure and content")
        return True
    
    except Exception as e:
        logger.error(f"Error verifying output file: {e}")
        return False

def test_sl_tp_combination(combo_name, sell_sl, sell_tp, buy_sl, buy_tp):
    """Test a specific SL/TP combination"""
    logger.info(f"\n=== Testing SL/TP Combination: {combo_name} ===")
    logger.info(f"SELL legs: SL={sell_sl}%, TP={sell_tp}%")
    logger.info(f"BUY legs: SL={buy_sl}%, TP={buy_tp}%\n")
    
    # Step 1: Create test strategy file
    strategy_file = create_test_strategy(combo_name, sell_sl, sell_tp, buy_sl, buy_tp)
    if not strategy_file:
        return False
    
    # Step 2: Create portfolio file
    portfolio_file = create_portfolio_file(strategy_file, combo_name)
    if not portfolio_file:
        return False
    
    # Step 3: Run backtester
    output_path = run_backtester(portfolio_file, combo_name)
    if not output_path:
        return False
    
    # Step 4: Verify output
    return verify_sheet_structure(output_path)

def check_archive_code_logic():
    """Check the archive code logic for SL/TP handling"""
    logger.info("\n=== Checking Archive Code Logic ===\n")
    
    # Look for checkSLTarget method in archive code
    try:
        archive_path = '/srv/samba/shared/bt/archive/backtester_stable/BTRUN/Util.py'
        if not os.path.exists(archive_path):
            logger.warning(f"Archive file not found: {archive_path}")
            return
        
        with open(archive_path, 'r') as f:
            content = f.read()
        
        # Find relevant sections
        sl_section_start = content.find("def checkSLTarget")
        if sl_section_start != -1:
            # Extract a reasonable chunk of code
            sl_code = content[sl_section_start:sl_section_start + 1000]
            sl_code_lines = sl_code.split('\n')[:20]  # First 20 lines
            
            logger.info("Archive SL/TP handling (first 20 lines):")
            for line in sl_code_lines:
                logger.info(f"  {line}")
        else:
            logger.warning("Could not find checkSLTarget method in archive code")
    
    except Exception as e:
        logger.error(f"Error checking archive code: {e}")

def update_python_refactor_plan():
    """Update the python_refactor_plan.md file with progress"""
    try:
        plan_path = '/srv/samba/shared/bt/memory-bank/python_refactor_plan.md'
        
        if not os.path.exists(plan_path):
            logger.warning(f"Refactor plan file not found: {plan_path}")
            return False
        
        # Read current plan
        with open(plan_path, 'r') as f:
            plan_content = f.read()
        
        # Update the progress on Phase 1.K to 100%
        updated_content = plan_content.replace("Phase 1.K **90%**", "Phase 1.K **100%**")
        
        # Check if anything changed
        if updated_content == plan_content:
            logger.warning("Could not update progress in refactor plan")
            return False
        
        # Write updated plan
        with open(plan_path, 'w') as f:
            f.write(updated_content)
        
        logger.info("✅ Updated python_refactor_plan.md with progress")
        return True
    
    except Exception as e:
        logger.error(f"Error updating python refactor plan: {e}")
        return False

def main():
    """Test all SL/TP combinations and verify outputs"""
    logger.info("=== Starting SL/TP Combination Tests ===")
    
    # Check archive code logic for reference
    check_archive_code_logic()
    
    # Test each combination
    results = {}
    for combo_name, sell_sl, sell_tp, buy_sl, buy_tp in SL_TP_COMBINATIONS:
        result = test_sl_tp_combination(combo_name, sell_sl, sell_tp, buy_sl, buy_tp)
        results[combo_name] = result
    
    # Print summary
    logger.info("\n=== Summary ===\n")
    
    all_passed = True
    for combo_name, result in results.items():
        status = "PASSED" if result else "FAILED"
        logger.info(f"SL/TP Combination '{combo_name}': {status}")
        
        if not result:
            all_passed = False
    
    if all_passed:
        logger.info("\n✅ ALL TESTS PASSED: SL/TP combinations work correctly with fixed exit time logic!")
        
        # Update progress in python_refactor_plan.md
        update_python_refactor_plan()
    else:
        logger.error("\n❌ SOME TESTS FAILED: SL/TP combinations may not work correctly")

if __name__ == "__main__":
    main() 