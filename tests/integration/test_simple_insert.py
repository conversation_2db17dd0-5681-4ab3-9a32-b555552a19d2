#!/usr/bin/env python3
"""
Test simple insert with proper data formatting
"""

import os
import sys
import pandas as pd
from datetime import datetime

sys.path.append('/srv/samba/shared')

try:
    from bt.dal.heavydb_conn import get_conn
    
    # Get connection
    conn = get_conn()
    cursor = conn.cursor()
    
    print("Connected to HeavyDB")
    
    # Check table structure
    cursor.execute("SHOW CREATE TABLE nifty_option_chain")
    result = cursor.fetchone()
    print("Table structure verified")
    
    # Create a simple test row
    test_sql = """
    INSERT INTO nifty_option_chain (
        trade_date, trade_time, trade_ts, expiry_date, index_name,
        spot, atm_strike, strike, dte, expiry_bucket,
        zone_id, zone_name, call_strike_type, put_strike_type,
        ce_symbol, ce_open, ce_high, ce_low, ce_close,
        ce_volume, ce_oi, ce_coi, ce_iv, ce_delta,
        ce_gamma, ce_theta, ce_vega, ce_rho,
        pe_symbol, pe_open, pe_high, pe_low, pe_close,
        pe_volume, pe_oi, pe_coi, pe_iv, pe_delta,
        pe_gamma, pe_theta, pe_vega, pe_rho,
        future_open, future_high, future_low, future_close,
        future_volume, future_oi, future_coi
    ) VALUES (
        '2025-04-01', '09:15:00', '2025-04-01 09:15:00', '2025-04-03', 'NIFTY',
        21453.6, 21450, 21450, 2, 'WEEKLY',
        1, 'OPEN', 'ATM', 'ATM',
        'NIFTY03APR2521450CE', 134.3, 140.0, 125.0, 130.5,
        1000, 50000, 5000, 25.5, 0.5,
        0.02, -50.5, 25.5, 0.8,
        'NIFTY03APR2521450PE', 125.5, 130.0, 120.0, 128.0,
        800, 45000, 4000, 24.8, -0.5,
        0.02, -48.5, 24.5, -0.8,
        21460.0, 21480.0, 21440.0, 21470.0,
        200000, 12600000, -160000
    )
    """
    
    print("\nInserting test row...")
    cursor.execute(test_sql)
    
    # Verify insert
    cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
    count = cursor.fetchone()[0]
    print(f"Rows after insert: {count}")
    
    # Test query
    cursor.execute("SELECT trade_date, strike, ce_symbol, ce_close FROM nifty_option_chain LIMIT 5")
    rows = cursor.fetchall()
    print("\nSample data:")
    for row in rows:
        print(row)
    
    cursor.close()
    conn.close()
    
    print("\nTest completed successfully!")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()