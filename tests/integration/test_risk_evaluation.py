#!/usr/bin/env python3
"""
Unit tests for the risk evaluation logic in bt.backtester_stable.BTRUN.models.risk

Tests the full range of risk rule types, number types, and scenarios to ensure
the risk evaluation logic works correctly, with special focus on the SL/TP 
exit time handling fixes.
"""

import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os
import logging

# Configure test logging
logging.basicConfig(level=logging.ERROR)

# Add project root to path for imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Import the risk module
from bt.backtester_stable.BTRUN.models.risk import (
    RiskRule, RiskRuleType, NumberType, evaluate_risk_rule
)

class TestRiskEvaluation(unittest.TestCase):
    """Test suite for risk evaluation logic"""

    def setUp(self):
        """Create sample data for tests"""
        # Create sample tick data
        self.entry_time = "09:16:00"
        self.exit_time = "12:00:00"
        self.trade_date = "2025-04-01" 
        self.entry_price = 100.0
        self.underlying = 22000.0

        # Create a time range from entry to exit with 1-minute intervals
        entry_dt = datetime.strptime(f"{self.trade_date} {self.entry_time}", "%Y-%m-%d %H:%M:%S")
        exit_dt = datetime.strptime(f"{self.trade_date} {self.exit_time}", "%Y-%m-%d %H:%M:%S")
        time_range = pd.date_range(entry_dt, exit_dt, freq='1min')
        
        # Create tick data DataFrame with realistic price patterns
        # Create tick data with more extreme price changes to ensure SL/TP triggers properly
        # For testing purposes, we need clearer price movements
        self.tick_df = pd.DataFrame({
            'datetime': time_range,
            'high': [self.entry_price * (1 + 0.15 * i) for i in range(len(time_range))],  # 15% increase per minute
            'low': [self.entry_price * (1 - 0.08 * i) for i in range(len(time_range))],   # 8% decrease per minute
            'close': [self.entry_price * (1 + 0.10 * i) for i in range(len(time_range))], # 10% increase per minute
            'ce_high': [self.entry_price * (1 + 0.15 * i) for i in range(len(time_range))],
            'ce_low': [self.entry_price * (1 - 0.08 * i) for i in range(len(time_range))],
            'ce_close': [self.entry_price * (1 + 0.10 * i) for i in range(len(time_range))],
            'pe_high': [self.entry_price * (1 + 0.15 * i) for i in range(len(time_range))],
            'pe_low': [self.entry_price * (1 - 0.08 * i) for i in range(len(time_range))],
            'pe_close': [self.entry_price * (1 + 0.10 * i) for i in range(len(time_range))],
            'underlying_price': [self.underlying] * len(time_range),
            'trade_date': [datetime.strptime(self.trade_date, "%Y-%m-%d").date()] * len(time_range),
            'trade_time': [int(dt.strftime("%H%M%S")) for dt in time_range],
            'strike': [self.underlying] * len(time_range),
        })
        
        # Create an extreme version of the tick_df for tests requiring large jumps
        self.extreme_tick_df = pd.DataFrame({
            'datetime': time_range,
            'high': [self.entry_price * (1 + 0.50 * i) for i in range(len(time_range))],  # 50% increase per minute
            'low': [self.entry_price * (1 - 0.30 * i) for i in range(len(time_range))],   # 30% decrease per minute
            'close': [self.entry_price * (1 + 0.40 * i) for i in range(len(time_range))], # 40% increase per minute
            'ce_high': [self.entry_price * (1 + 0.50 * i) for i in range(len(time_range))],
            'ce_low': [self.entry_price * (1 - 0.30 * i) for i in range(len(time_range))],
            'ce_close': [self.entry_price * (1 + 0.40 * i) for i in range(len(time_range))],
            'pe_high': [self.entry_price * (1 + 0.50 * i) for i in range(len(time_range))],
            'pe_low': [self.entry_price * (1 - 0.30 * i) for i in range(len(time_range))],
            'pe_close': [self.entry_price * (1 + 0.40 * i) for i in range(len(time_range))],
            'underlying_price': [self.underlying] * len(time_range),
            'trade_date': [datetime.strptime(self.trade_date, "%Y-%m-%d").date()] * len(time_range),
            'trade_time': [int(dt.strftime("%H%M%S")) for dt in time_range],
            'strike': [self.underlying] * len(time_range),
        })

    def test_stoploss_sell_percentage(self):
        """Test STOPLOSS rule with PERCENTAGE type for SELL (short) positions"""
        # Test cases for different SL percentages
        test_cases = [
            # (sl_value, should_trigger, reason)
            (50, False, None),    # Too small, should be auto-adjusted to 500
            (100, False, None),   # Should be auto-adjusted to 500
            (300, False, None),   # Should be auto-adjusted to 500
            (1000, True, "Stop Loss Hit"), # Large enough to be triggered with more extreme price changes
        ]
        
        for sl_value, should_trigger, expected_reason in test_cases:
            with self.subTest(sl_value=sl_value):
                sl_rule = RiskRule(
                    rule_type=RiskRuleType.STOPLOSS,
                    number_type=NumberType.PERCENTAGE,
                    value=sl_value,
                    params={'start_time': '091600'}  # Skip first candle
                )
                
                # Create appropriate test data for different cases
                if sl_value < 500:
                    # For SL values that should be auto-adjusted to 500%, 
                    # create data where price increases are less than 500% to avoid trigger
                    entry_dt = datetime.strptime(f"{self.trade_date} {self.entry_time}", "%Y-%m-%d %H:%M:%S")
                    exit_dt = datetime.strptime(f"{self.trade_date} {self.exit_time}", "%Y-%m-%d %H:%M:%S")
                    time_range = pd.date_range(entry_dt, exit_dt, freq='1min')
                    
                    # Create tick data where only up to 450% increase (below adjusted 500% SL)
                    test_df = pd.DataFrame({
                        'datetime': time_range,
                        'high': [self.entry_price * (1 + 4.5 * (i/len(time_range))) for i in range(len(time_range))],
                        'low': [self.entry_price * (1 - 0.2 * i/len(time_range)) for i in range(len(time_range))],
                        'close': [self.entry_price * (1 + 4.0 * (i/len(time_range))) for i in range(len(time_range))],
                        'underlying_price': [self.underlying] * len(time_range),
                        'trade_date': [datetime.strptime(self.trade_date, "%Y-%m-%d").date()] * len(time_range),
                        'trade_time': [int(dt.strftime("%H%M%S")) for dt in time_range],
                        'strike': [self.underlying] * len(time_range),
                    })
                else:
                    # For SL values high enough to trigger
                    test_df = self.extreme_tick_df
                
                exit_price, exit_time, triggered, reason = evaluate_risk_rule(
                    tick_df=test_df,
                    risk_rule=sl_rule,
                    entry_price=self.entry_price, 
                    is_long=False  # SELL position
                )
                
                self.assertEqual(triggered, should_trigger, 
                                f"SELL SL={sl_value}% triggered={triggered}, expected={should_trigger}")
                
                if should_trigger:
                    self.assertEqual(reason, expected_reason)
                    self.assertIsNotNone(exit_price)
                    self.assertIsNotNone(exit_time)

    def test_stoploss_buy_percentage(self):
        """Test STOPLOSS rule with PERCENTAGE type for BUY (long) positions"""
        # Test cases for different SL percentages
        test_cases = [
            # (sl_value, should_trigger, reason)
            (0, False, None),   # Zero SL should not trigger due to auto-adjustment to 50
            (10, False, None),  # Too small, should be auto-adjusted to 50
            (40, False, None),  # Too small, should be auto-adjusted to 50
            (80, True, "Stop Loss Hit"), # Large enough to be triggered with more extreme price changes
        ]
        
        # Create a modified buy-specific tick DataFrame with controlled downward movement
        # This ensures we can test SL auto-adjustment properly
        entry_dt = datetime.strptime(f"{self.trade_date} {self.entry_time}", "%Y-%m-%d %H:%M:%S")
        exit_dt = datetime.strptime(f"{self.trade_date} {self.exit_time}", "%Y-%m-%d %H:%M:%S")
        time_range = pd.date_range(entry_dt, exit_dt, freq='1min')
        
        buy_tick_df = pd.DataFrame({
            'datetime': time_range,
            'high': [self.entry_price * (1 + 0.01 * i) for i in range(len(time_range))],  # Small increase 
            'low': [self.entry_price * (1 - 0.25 * i) for i in range(len(time_range))],   # 25% decrease per minute
            'close': [self.entry_price * (1 - 0.20 * i) for i in range(len(time_range))], # 20% decrease per minute
            'ce_high': [self.entry_price * (1 + 0.01 * i) for i in range(len(time_range))],
            'ce_low': [self.entry_price * (1 - 0.25 * i) for i in range(len(time_range))],
            'ce_close': [self.entry_price * (1 - 0.20 * i) for i in range(len(time_range))],
            'pe_high': [self.entry_price * (1 + 0.01 * i) for i in range(len(time_range))],
            'pe_low': [self.entry_price * (1 - 0.25 * i) for i in range(len(time_range))],
            'pe_close': [self.entry_price * (1 - 0.20 * i) for i in range(len(time_range))],
            'underlying_price': [self.underlying] * len(time_range),
            'trade_date': [datetime.strptime(self.trade_date, "%Y-%m-%d").date()] * len(time_range),
            'trade_time': [int(dt.strftime("%H%M%S")) for dt in time_range],
            'strike': [self.underlying] * len(time_range),
        })
        
        for sl_value, should_trigger, expected_reason in test_cases:
            with self.subTest(sl_value=sl_value):
                sl_rule = RiskRule(
                    rule_type=RiskRuleType.STOPLOSS,
                    number_type=NumberType.PERCENTAGE,
                    value=sl_value,
                    params={'start_time': '091600'}  # Skip first candle
                )
                
                # For low SL values that get adjusted, check they don't trigger
                # For high SL values, check they do trigger
                test_df = None
                if sl_value < 50:
                    # Use controlled tick data where 49% drop won't happen
                    test_df = pd.DataFrame({
                        'datetime': time_range[:5],  # Just a few candles
                        'high': [self.entry_price] * 5,
                        'low': [self.entry_price * 0.60] * 5,  # Only 40% drop (less than 50% SL)
                        'close': [self.entry_price * 0.65] * 5,
                        'underlying_price': [self.underlying] * 5,
                        'trade_date': [datetime.strptime(self.trade_date, "%Y-%m-%d").date()] * 5,
                        'trade_time': [int(dt.strftime("%H%M%S")) for dt in time_range[:5]],
                        'strike': [self.underlying] * 5,
                    })
                else:
                    # Use tick data with big drops for testing larger SL values
                    test_df = buy_tick_df
                
                exit_price, exit_time, triggered, reason = evaluate_risk_rule(
                    tick_df=test_df,
                    risk_rule=sl_rule,
                    entry_price=self.entry_price, 
                    is_long=True  # BUY position
                )
                
                self.assertEqual(triggered, should_trigger, 
                                f"BUY SL={sl_value}% triggered={triggered}, expected={should_trigger}")
                
                if should_trigger:
                    self.assertEqual(reason, expected_reason)
                    self.assertIsNotNone(exit_price)
                    self.assertIsNotNone(exit_time)
    
    def test_stoploss_point(self):
        """Test STOPLOSS rule with POINT type for both BUY and SELL positions"""
        # Test POINT type for SELL position (short)
        sl_rule_sell = RiskRule(
            rule_type=RiskRuleType.STOPLOSS,
            number_type=NumberType.POINT,
            value=20.0,  # 20 point SL
            params={'start_time': '091600'}  # Skip first candle
        )
        
        exit_price_sell, exit_time_sell, triggered_sell, reason_sell = evaluate_risk_rule(
            tick_df=self.tick_df,
            risk_rule=sl_rule_sell,
            entry_price=self.entry_price, 
            is_long=False  # SELL position
        )
        
        # The ticker increases by 1% per minute, so a 20 point SL (20% of entry_price) 
        # should be hit within 20 minutes
        self.assertTrue(triggered_sell, "SELL POINT SL should trigger")
        self.assertEqual(reason_sell, "Stop Loss Hit")
        
        # Test POINT type for BUY position (long)
        sl_rule_buy = RiskRule(
            rule_type=RiskRuleType.STOPLOSS,
            number_type=NumberType.POINT,
            value=10.0,  # 10 point SL
            params={'start_time': '091600'}  # Skip first candle
        )
        
        exit_price_buy, exit_time_buy, triggered_buy, reason_buy = evaluate_risk_rule(
            tick_df=self.tick_df,
            risk_rule=sl_rule_buy,
            entry_price=self.entry_price, 
            is_long=True  # BUY position
        )
        
        # The ticker decreases by 0.5% per minute at the low, so a 10 point SL (10% of entry_price)
        # should be hit within 20 minutes
        self.assertTrue(triggered_buy, "BUY POINT SL should trigger")
        self.assertEqual(reason_buy, "Stop Loss Hit")
    
    def test_takeprofit_percentage(self):
        """Test TAKEPROFIT rule with PERCENTAGE type for both BUY and SELL positions"""
        # Test TP for BUY position (long) - increasing price should hit TP
        tp_rule_buy = RiskRule(
            rule_type=RiskRuleType.TAKEPROFIT,
            number_type=NumberType.PERCENTAGE,
            value=50.0,  # 50% profit target
            params={'start_time': '091600'}  # Skip first candle
        )
        
        exit_price_buy, exit_time_buy, triggered_buy, reason_buy = evaluate_risk_rule(
            tick_df=self.tick_df,
            risk_rule=tp_rule_buy,
            entry_price=self.entry_price, 
            is_long=True  # BUY position
        )
        
        # Price increases by 1% per minute at high, so 50% should be hit
        self.assertTrue(triggered_buy, "BUY PERCENTAGE TP should trigger")
        self.assertEqual(reason_buy, "Target Hit")
        
        # Test TP for SELL position (short) - decreasing price should hit TP
        tp_rule_sell = RiskRule(
            rule_type=RiskRuleType.TAKEPROFIT,
            number_type=NumberType.PERCENTAGE,
            value=30.0,  # 30% profit target
            params={'start_time': '091600'}  # Skip first candle
        )
        
        exit_price_sell, exit_time_sell, triggered_sell, reason_sell = evaluate_risk_rule(
            tick_df=self.tick_df,
            risk_rule=tp_rule_sell,
            entry_price=self.entry_price, 
            is_long=False  # SELL position
        )
        
        # Price decreases by 0.5% per minute at low, so 30% should be hit
        self.assertTrue(triggered_sell, "SELL PERCENTAGE TP should trigger")
        self.assertEqual(reason_sell, "Target Hit")
    
    def test_zero_tp_value(self):
        """Test TAKEPROFIT rule with zero value should not trigger"""
        tp_rule = RiskRule(
            rule_type=RiskRuleType.TAKEPROFIT,
            number_type=NumberType.PERCENTAGE,
            value=0.0,  # 0% profit target (disabled)
            params={'start_time': '091600'}  # Skip first candle
        )
        
        # Test for both BUY and SELL
        for is_long in [True, False]:
            with self.subTest(is_long=is_long):
                exit_price, exit_time, triggered, reason = evaluate_risk_rule(
                    tick_df=self.tick_df,
                    risk_rule=tp_rule,
                    entry_price=self.entry_price, 
                    is_long=is_long
                )
                
                # Zero TP should never trigger
                self.assertFalse(triggered, f"Zero TP value should not trigger for {'BUY' if is_long else 'SELL'}")
                self.assertIsNone(reason)
    
    def test_trailing_stop(self):
        """Test TRAIL rule for both BUY and SELL positions"""
        # Test trailing stop for BUY (long)
        trail_rule_buy = RiskRule(
            rule_type=RiskRuleType.TRAIL,
            number_type=NumberType.PERCENTAGE,
            value=10.0,  # 10% trailing stop
            params={'start_time': '091600', 'trail_by': 5.0}  # Skip first candle, trail by 5%
        )
        
        exit_price_buy, exit_time_buy, triggered_buy, reason_buy = evaluate_risk_rule(
            tick_df=self.tick_df,
            risk_rule=trail_rule_buy,
            entry_price=self.entry_price, 
            is_long=True  # BUY position
        )
        
        # Price first increases then decreases, should trigger trailing stop
        self.assertTrue(triggered_buy, "BUY trailing stop should trigger")
        self.assertEqual(reason_buy, "Trail SL Hit")
        
        # Test trailing stop for SELL (short)
        trail_rule_sell = RiskRule(
            rule_type=RiskRuleType.TRAIL,
            number_type=NumberType.PERCENTAGE,
            value=15.0,  # 15% trailing stop
            params={'start_time': '091600', 'trail_by': 7.5}  # Skip first candle, trail by 7.5%
        )
        
        exit_price_sell, exit_time_sell, triggered_sell, reason_sell = evaluate_risk_rule(
            tick_df=self.tick_df,
            risk_rule=trail_rule_sell,
            entry_price=self.entry_price, 
            is_long=False  # SELL position
        )
        
        # Price first decreases then increases, should trigger trailing stop
        self.assertTrue(triggered_sell, "SELL trailing stop should trigger")
        self.assertEqual(reason_sell, "Trail SL Hit")
    
    def test_first_candle_skipping(self):
        """Test that the first candle is properly skipped"""
        # Create a controlled test environment with precise timing
        # The key is to make sure the first candle has a time EQUAL TO the start_time
        # and the second candle is AFTER the start_time
        
        # Define a start time (9:16:00)
        start_time = "09:16:00"
        start_time_seconds = 91600
        
        # Create datetime objects for entry, start, and exit times
        entry_dt = datetime.strptime(f"{self.trade_date} 09:15:00", "%Y-%m-%d %H:%M:%S")  # One minute before start
        start_dt = datetime.strptime(f"{self.trade_date} {start_time}", "%Y-%m-%d %H:%M:%S")  # Exact start time
        second_dt = datetime.strptime(f"{self.trade_date} 09:17:00", "%Y-%m-%d %H:%M:%S")  # One minute after start
        exit_dt = datetime.strptime(f"{self.trade_date} {self.exit_time}", "%Y-%m-%d %H:%M:%S")  # Exit time
        
        # Create a time range with exactly 3 points: before start, at start, after start
        time_points = [entry_dt, start_dt, second_dt]
        
        # Create extremely controlled tick data with just 3 candles
        mod_tick_df = pd.DataFrame({
            'datetime': time_points,
            'high': [
                self.entry_price * 10.0,  # First candle - extreme high (would trigger SELL SL)
                self.entry_price * 10.0,  # Second candle (at start time) - should be skipped 
                self.entry_price * 1.05   # Third candle - normal
            ],
            'low': [
                self.entry_price * 0.1,   # First candle - extreme low (would trigger BUY SL)
                self.entry_price * 0.1,   # Second candle (at start time) - should be skipped
                self.entry_price * 0.95   # Third candle - normal
            ],
            'close': [
                self.entry_price * 8.0,   # First candle
                self.entry_price * 8.0,   # Second candle (at start time)
                self.entry_price * 1.03   # Third candle
            ],
            'ce_high': [self.entry_price * 10.0, self.entry_price * 10.0, self.entry_price * 1.05],
            'ce_low': [self.entry_price * 0.1, self.entry_price * 0.1, self.entry_price * 0.95],
            'ce_close': [self.entry_price * 8.0, self.entry_price * 8.0, self.entry_price * 1.03],
            'pe_high': [self.entry_price * 10.0, self.entry_price * 10.0, self.entry_price * 1.05],
            'pe_low': [self.entry_price * 0.1, self.entry_price * 0.1, self.entry_price * 0.95],
            'pe_close': [self.entry_price * 8.0, self.entry_price * 8.0, self.entry_price * 1.03],
            'underlying_price': [self.underlying] * 3,
            'trade_date': [datetime.strptime(self.trade_date, "%Y-%m-%d").date()] * 3,
            'trade_time': [91500, 91600, 91700],  # 9:15, 9:16, 9:17
            'strike': [self.underlying] * 3
        })
        
        # Test for SELL position with extremely low SL that would trigger on first two candles
        sl_rule_sell = RiskRule(
            rule_type=RiskRuleType.STOPLOSS,
            number_type=NumberType.PERCENTAGE,
            value=50.0,  # Very low SL that would trigger on first candles (10x entry price is 900% increase)
            params={'start_time': start_time_seconds}  # Set start time to 9:16
        )
        
        exit_price_sell, exit_time_sell, triggered_sell, reason_sell = evaluate_risk_rule(
            tick_df=mod_tick_df,
            risk_rule=sl_rule_sell,
            entry_price=self.entry_price, 
            is_long=False  # SELL position
        )
        
        # Should not trigger because first candle (9:15) is before start time,
        # second candle (9:16) is the start time and should be skipped,
        # and third candle (9:17) does not trigger
        self.assertFalse(triggered_sell, "First and start time candles should be skipped for SELL SL")
        
        # Test for BUY position with extremely low SL that would trigger on first two candles
        sl_rule_buy = RiskRule(
            rule_type=RiskRuleType.STOPLOSS,
            number_type=NumberType.PERCENTAGE,
            value=50.0,  # This would trigger on first two candles (90% decrease)
            params={'start_time': start_time_seconds}  # Set start time to 9:16
        )
        
        exit_price_buy, exit_time_buy, triggered_buy, reason_buy = evaluate_risk_rule(
            tick_df=mod_tick_df,
            risk_rule=sl_rule_buy,
            entry_price=self.entry_price, 
            is_long=True  # BUY position
        )
        
        # Same reason as SELL test
        self.assertFalse(triggered_buy, "First and start time candles should be skipped for BUY SL")
    
    def test_start_time_filtering(self):
        """Test that start_time parameter correctly filters candles"""
        # Create tick data with a later start time
        later_start = "10:30:00"
        
        sl_rule = RiskRule(
            rule_type=RiskRuleType.STOPLOSS,
            number_type=NumberType.PERCENTAGE,
            value=1000.0,  # High enough to trigger
            params={'start_time': later_start}  # Skip all candles before 10:30
        )
        
        # Get the number of candles before 10:30
        entry_dt = datetime.strptime(f"{self.trade_date} {self.entry_time}", "%Y-%m-%d %H:%M:%S")
        later_dt = datetime.strptime(f"{self.trade_date} {later_start}", "%Y-%m-%d %H:%M:%S")
        candles_to_skip = int((later_dt - entry_dt).total_seconds() / 60)
        
        # Modify the first candle after 10:30 to have extreme values
        mod_tick_df = self.tick_df.copy()
        mod_tick_df.loc[candles_to_skip + 1, 'high'] = self.entry_price * 20.0  # Would trigger SELL SL
        
        exit_price, exit_time, triggered, reason = evaluate_risk_rule(
            tick_df=mod_tick_df,
            risk_rule=sl_rule,
            entry_price=self.entry_price, 
            is_long=False  # SELL position
        )
        
        # Should trigger on the modified candle after the start_time
        self.assertTrue(triggered, "SL should trigger after start_time")
        self.assertEqual(reason, "Stop Loss Hit")

    def test_empty_tick_data(self):
        """Test that empty tick data is handled properly"""
        empty_df = pd.DataFrame()
        
        sl_rule = RiskRule(
            rule_type=RiskRuleType.STOPLOSS,
            number_type=NumberType.PERCENTAGE,
            value=100.0,
            params={'start_time': '091600'}
        )
        
        exit_price, exit_time, triggered, reason = evaluate_risk_rule(
            tick_df=empty_df,
            risk_rule=sl_rule,
            entry_price=self.entry_price, 
            is_long=False
        )
        
        # Should not trigger and return None values
        self.assertFalse(triggered)
        self.assertIsNone(exit_price)
        self.assertIsNone(exit_time)
        self.assertIsNone(reason)

    def test_none_entry_price(self):
        """Test that None entry price is handled properly"""
        sl_rule = RiskRule(
            rule_type=RiskRuleType.STOPLOSS,
            number_type=NumberType.PERCENTAGE,
            value=100.0,
            params={'start_time': '091600'}
        )
        
        exit_price, exit_time, triggered, reason = evaluate_risk_rule(
            tick_df=self.tick_df,
            risk_rule=sl_rule,
            entry_price=None, 
            is_long=False
        )
        
        # Should not trigger and return None values
        self.assertFalse(triggered)
        self.assertIsNone(exit_price)
        self.assertIsNone(exit_time)
        self.assertIsNone(reason)

    def test_other_number_types(self):
        """Test other number types like INDEX_POINTS, INDEX_PERCENTAGE, etc."""
        # Create a tick dataframe with more extreme price movements to ensure triggers
        
        # Test cases for different number types with values high enough to definitely trigger
        test_cases = [
            (NumberType.INDEX_POINTS, 50.0, True),          # 50 index points
            (NumberType.INDEX_PERCENTAGE, 20.0, True),      # 20% of index
            (NumberType.PREMIUM, 150.0, True),              # Fixed premium level
            (NumberType.ABSOLUTE_DELTA, 50.0, True),        # 50 delta points
        ]
        
        for number_type, value, should_trigger in test_cases:
            with self.subTest(number_type=number_type):
                sl_rule = RiskRule(
                    rule_type=RiskRuleType.STOPLOSS,
                    number_type=number_type,
                    value=value,
                    params={'start_time': '091600'}
                )
                
                # Use the extreme tick_df to ensure SL is triggered for these tests
                exit_price, exit_time, triggered, reason = evaluate_risk_rule(
                    tick_df=self.extreme_tick_df,
                    risk_rule=sl_rule,
                    entry_price=self.entry_price, 
                    is_long=False  # SELL position
                )
                
                # All of these should trigger since we're using extreme tick data
                self.assertEqual(triggered, should_trigger, 
                                f"{number_type} SL triggered={triggered}, expected={should_trigger}")

if __name__ == "__main__":
    unittest.main() 