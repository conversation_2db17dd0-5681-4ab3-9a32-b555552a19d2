#!/usr/bin/env python3
"""
Complete API Test for Backtesting - Enterprise GPU Backtester
Created: June 6, 2025

This script demonstrates the correct way to use the V2 API for backtesting
"""

import requests
import json
import time
from datetime import datetime
from pathlib import Path

BASE_URL = "http://**************:8000"
API_V2 = f"{BASE_URL}/api/v2"

class BacktestAPITester:
    def __init__(self):
        self.results = []
        self.session = requests.Session()
        
    def check_api_status(self):
        """Check if API is operational"""
        print("🔍 Checking API status...")
        try:
            response = self.session.get(f"{API_V2}/status")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ API Status: {data.get('status')}")
                print(f"   Version: {data.get('version')}")
                print(f"   Strategies: {', '.join(data.get('supported_strategies', []))}")
                return True
            else:
                print(f"❌ API returned status code: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ API error: {e}")
            return False
    
    def submit_backtest(self, strategy_type, config):
        """Submit a backtest request"""
        print(f"\n🚀 Submitting {strategy_type} backtest...")
        
        # Build request payload based on strategy type
        payload = {
            "index": config.get("index", "NIFTY"),
            "start_date": config.get("start_date", "2024-01-01"),
            "end_date": config.get("end_date", "2024-12-31"),
            "gpu_workers": config.get("gpu_workers", 8),
            "max_gpu": True
        }
        
        # Add strategy-specific parameters
        if strategy_type == "TBS":
            payload.update({
                "portfolio_settings": {
                    "file": config["portfolio"],
                    "type": "excel"
                },
                "strategy_params": {
                    "file": config["strategy"],
                    "type": "tbs"
                }
            })
            endpoint = f"{API_V2}/strategies/tbs/backtest"
        elif strategy_type == "TV":
            payload.update({
                "portfolio_settings": {
                    "file": config["portfolio"],
                    "type": "excel"
                },
                "tv_signals": {
                    "file": config["strategy"],
                    "type": "tv"
                }
            })
            endpoint = f"{API_V2}/strategies/tv/backtest"
        else:
            # For ORB and OI, use generic endpoint
            endpoint = f"{API_V2}/backtest/submit"
            payload["strategy_type"] = strategy_type
        
        # Submit backtest
        try:
            response = self.session.post(endpoint, json=payload)
            if response.status_code == 200:
                result = response.json()
                backtest_id = result.get("backtest_id")
                print(f"✅ Backtest submitted successfully")
                print(f"   Backtest ID: {backtest_id}")
                print(f"   Status: {result.get('status')}")
                return backtest_id
            else:
                print(f"❌ Submission failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return None
        except Exception as e:
            print(f"❌ Submission error: {e}")
            return None
    
    def monitor_backtest(self, backtest_id, timeout=300):
        """Monitor backtest progress"""
        print(f"\n📊 Monitoring backtest {backtest_id}...")
        
        start_time = time.time()
        last_progress = 0
        
        while time.time() - start_time < timeout:
            try:
                response = self.session.get(f"{API_V2}/backtest/status/{backtest_id}")
                if response.status_code == 200:
                    status_data = response.json()
                    status = status_data.get("status")
                    progress = status_data.get("progress", 0)
                    
                    # Print progress update if changed
                    if progress > last_progress:
                        print(f"   Progress: {progress}% - {status}")
                        last_progress = progress
                    
                    # Check if completed
                    if status in ["completed", "failed", "error"]:
                        print(f"\n{'✅' if status == 'completed' else '❌'} Backtest {status}")
                        return status_data
                else:
                    print(f"❌ Status check failed: {response.status_code}")
                    return None
                    
            except Exception as e:
                print(f"❌ Monitoring error: {e}")
                return None
            
            time.sleep(2)  # Check every 2 seconds
        
        print(f"⏱️ Timeout reached after {timeout} seconds")
        return None
    
    def get_results(self, backtest_id):
        """Get backtest results"""
        print(f"\n📈 Getting results for backtest {backtest_id}...")
        
        try:
            response = self.session.get(f"{API_V2}/backtest/result/{backtest_id}")
            if response.status_code == 200:
                results = response.json()
                print(f"✅ Results retrieved successfully")
                
                # Display summary
                if "summary" in results:
                    summary = results["summary"]
                    print(f"\n   Summary:")
                    print(f"   - Total Trades: {summary.get('total_trades', 0)}")
                    print(f"   - Win Rate: {summary.get('win_rate', 0):.2f}%")
                    print(f"   - Total P&L: ₹{summary.get('total_pnl', 0):,.2f}")
                    print(f"   - Sharpe Ratio: {summary.get('sharpe_ratio', 0):.2f}")
                
                return results
            else:
                print(f"❌ Results retrieval failed: {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ Results error: {e}")
            return None
    
    def download_excel(self, backtest_id, output_path):
        """Download Excel results"""
        print(f"\n💾 Downloading Excel results...")
        
        try:
            response = self.session.get(f"{API_V2}/backtest/download/{backtest_id}")
            if response.status_code == 200:
                with open(output_path, 'wb') as f:
                    f.write(response.content)
                print(f"✅ Excel saved to: {output_path}")
                return True
            else:
                print(f"❌ Download failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Download error: {e}")
            return False
    
    def run_complete_test(self, strategy_type, config):
        """Run a complete backtest test"""
        print(f"\n{'='*60}")
        print(f"Testing {strategy_type} Strategy via API")
        print(f"{'='*60}")
        
        # Submit backtest
        backtest_id = self.submit_backtest(strategy_type, config)
        if not backtest_id:
            return None
        
        # Monitor progress
        final_status = self.monitor_backtest(backtest_id)
        if not final_status or final_status.get("status") != "completed":
            return None
        
        # Get results
        results = self.get_results(backtest_id)
        
        # Download Excel
        output_path = f"/srv/samba/shared/test_results_{strategy_type}_{backtest_id}.xlsx"
        self.download_excel(backtest_id, output_path)
        
        return {
            "strategy_type": strategy_type,
            "backtest_id": backtest_id,
            "status": final_status,
            "results": results,
            "output_file": output_path
        }

def main():
    print("🧪 Enterprise GPU Backtester - Complete API Test")
    print(f"📅 Start Time: {datetime.now()}")
    print("=" * 60)
    
    tester = BacktestAPITester()
    
    # Check API status
    if not tester.check_api_status():
        print("❌ API is not operational. Exiting.")
        return
    
    # Test configurations
    test_configs = {
        "TBS": {
            "portfolio": "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tbs/input_portfolio.xlsx",
            "strategy": "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tbs/input_tbs_portfolio.xlsx",
            "index": "NIFTY",
            "gpu_workers": 8
        },
        "TV": {
            "portfolio": "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tv/input_portfolio_long.xlsx",
            "strategy": "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tv/input_tv.xlsx",
            "index": "NIFTY",
            "gpu_workers": 8
        }
    }
    
    # Run tests
    results = []
    for strategy_type, config in test_configs.items():
        result = tester.run_complete_test(strategy_type, config)
        if result:
            results.append(result)
        time.sleep(5)  # Pause between tests
    
    # Generate summary report
    print(f"\n{'='*60}")
    print("Test Summary")
    print(f"{'='*60}")
    print(f"Total Tests: {len(results)}")
    print(f"Successful: {sum(1 for r in results if r['status'].get('status') == 'completed')}")
    
    # Save detailed report
    report = {
        "test_date": datetime.now().isoformat(),
        "api_url": BASE_URL,
        "test_results": results
    }
    
    report_path = Path("/srv/samba/shared/docs/api_test_report.json")
    with open(report_path, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📊 Detailed report saved to: {report_path}")
    print("✅ API testing complete!")

if __name__ == "__main__":
    main()