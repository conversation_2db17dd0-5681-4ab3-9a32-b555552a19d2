#!/usr/bin/env python3
"""
Simple API test for all strategies
"""
import requests
import json
import time

BASE_URL = "http://173.208.247.17:8000"

def test_api_health():
    """Test if API is running"""
    print("🔍 Testing API health...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ API is healthy")
            return True
    except:
        pass
    
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            print("✅ API is running")
            return True
    except Exception as e:
        print(f"❌ API error: {e}")
        return False

def test_v2_endpoints():
    """Test V2 API endpoints"""
    print("\n🔍 Testing V2 endpoints...")
    
    # Test backtest status endpoint
    try:
        response = requests.get(f"{BASE_URL}/api/v2/backtest/test-123/status")
        print(f"📊 Status endpoint: {response.status_code}")
        if response.status_code in [200, 404]:  # 404 is expected for non-existent backtest
            print("✅ Status endpoint working")
    except Exception as e:
        print(f"❌ Status endpoint error: {e}")
    
    # Test GPU status
    try:
        response = requests.get(f"{BASE_URL}/api/v2/gpu/status")
        print(f"🖥️ GPU status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ GPU status: {data.get('status', 'unknown')}")
    except Exception as e:
        print(f"❌ GPU status error: {e}")

def test_logs_api():
    """Test logs API"""
    print("\n🔍 Testing Logs API...")
    try:
        response = requests.get(f"{BASE_URL}/api/v1/logs/")
        if response.status_code == 200:
            logs = response.json()
            print(f"✅ Logs API working - Found {len(logs)} log files")
            return True
        else:
            print(f"❌ Logs API returned: {response.status_code}")
    except Exception as e:
        print(f"❌ Logs API error: {e}")
    return False

def test_templates():
    """Test template download"""
    print("\n🔍 Testing Templates...")
    templates = ["portfolio", "tbs", "tv", "orb", "oi"]
    
    for template in templates:
        try:
            response = requests.get(f"{BASE_URL}/api/v1/templates/{template}")
            if response.status_code == 200:
                print(f"✅ {template.upper()} template available")
            else:
                print(f"❌ {template.upper()} template: {response.status_code}")
        except Exception as e:
            print(f"❌ {template.upper()} template error: {e}")

def main():
    """Run all tests"""
    print("🧪 Enterprise GPU Backtester - Simple API Test")
    print("=" * 60)
    
    # Test API health
    if not test_api_health():
        print("❌ API is not running!")
        return
    
    # Test V2 endpoints
    test_v2_endpoints()
    
    # Test logs API
    test_logs_api()
    
    # Test templates
    test_templates()
    
    print("\n" + "=" * 60)
    print("✅ Basic API tests completed")
    print("\nNote: For full backtest testing, use the UI or provide actual test files")

if __name__ == "__main__":
    main()