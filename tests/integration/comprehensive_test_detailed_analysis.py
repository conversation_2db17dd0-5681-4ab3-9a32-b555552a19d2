#!/usr/bin/env python3
"""
Detailed analysis of comprehensive test results with focus on strategy performance,
risk management effectiveness, and trade execution quality.
"""

import pandas as pd
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def create_visualizations(df, output_prefix):
    """Create visualization plots for the analysis."""
    
    # Set up the plotting style
    plt.style.use('ggplot')
    fig_size = (12, 8)
    
    # 1. Strategy Performance Comparison
    plt.figure(figsize=fig_size)
    strategy_pnl = df.groupby('Strategy')['Netpnlafterexpenses'].agg(['sum', 'mean', 'count'])
    strategy_pnl = strategy_pnl.sort_values('sum', ascending=True)
    
    ax = strategy_pnl['sum'].plot(kind='barh', color='skyblue', alpha=0.8)
    ax.set_xlabel('Total P&L (₹)')
    ax.set_ylabel('Strategy')
    ax.set_title('Total P&L by Strategy', fontsize=14, fontweight='bold')
    ax.axvline(x=0, color='red', linestyle='--', alpha=0.5)
    
    # Add value labels
    for i, (idx, row) in enumerate(strategy_pnl.iterrows()):
        ax.text(row['sum'], i, f' ₹{row["sum"]:,.0f} ({row["count"]} trades)', 
                va='center', fontsize=9)
    
    plt.tight_layout()
    plt.savefig(f'{output_prefix}_strategy_pnl.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. Win Rate by Strategy
    plt.figure(figsize=fig_size)
    win_rates = []
    strategies = []
    
    for strategy in df['Strategy'].unique():
        strategy_df = df[df['Strategy'] == strategy]
        win_rate = (strategy_df['Netpnlafterexpenses'] > 0).sum() / len(strategy_df) * 100
        win_rates.append(win_rate)
        strategies.append(strategy)
    
    win_rate_df = pd.DataFrame({'Strategy': strategies, 'Win_Rate': win_rates})
    win_rate_df = win_rate_df.sort_values('Win_Rate', ascending=True)
    
    ax = win_rate_df.plot(x='Strategy', y='Win_Rate', kind='barh', 
                          color='lightgreen', alpha=0.8, legend=False)
    ax.set_xlabel('Win Rate (%)')
    ax.set_ylabel('Strategy')
    ax.set_title('Win Rate by Strategy', fontsize=14, fontweight='bold')
    ax.axvline(x=50, color='red', linestyle='--', alpha=0.5, label='50% threshold')
    
    # Add value labels
    for i, (idx, row) in enumerate(win_rate_df.iterrows()):
        ax.text(row['Win_Rate'], i, f' {row["Win_Rate"]:.1f}%', va='center', fontsize=9)
    
    plt.tight_layout()
    plt.savefig(f'{output_prefix}_strategy_winrate.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. P&L Distribution
    plt.figure(figsize=fig_size)
    plt.subplot(2, 1, 1)
    df['Netpnlafterexpenses'].hist(bins=50, alpha=0.7, color='blue', edgecolor='black')
    plt.axvline(x=0, color='red', linestyle='--', alpha=0.5)
    plt.xlabel('P&L (₹)')
    plt.ylabel('Frequency')
    plt.title('P&L Distribution - All Trades', fontsize=12)
    
    plt.subplot(2, 1, 2)
    df.boxplot(column='Netpnlafterexpenses', by='Strategy', rot=90)
    plt.suptitle('')
    plt.title('P&L Distribution by Strategy', fontsize=12)
    plt.ylabel('P&L (₹)')
    
    plt.tight_layout()
    plt.savefig(f'{output_prefix}_pnl_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 4. Cumulative P&L Chart
    plt.figure(figsize=(14, 8))
    
    # Sort by datetime for proper cumulative calculation
    df_sorted = df.sort_values('Entry_Datetime')
    df_sorted['Cumulative_PNL'] = df_sorted['Netpnlafterexpenses'].cumsum()
    
    plt.plot(df_sorted.index, df_sorted['Cumulative_PNL'], linewidth=2, color='darkblue')
    plt.fill_between(df_sorted.index, 0, df_sorted['Cumulative_PNL'], 
                     where=df_sorted['Cumulative_PNL'] >= 0, alpha=0.3, color='green', label='Profit')
    plt.fill_between(df_sorted.index, 0, df_sorted['Cumulative_PNL'], 
                     where=df_sorted['Cumulative_PNL'] < 0, alpha=0.3, color='red', label='Loss')
    
    plt.xlabel('Trade Number')
    plt.ylabel('Cumulative P&L (₹)')
    plt.title('Cumulative P&L Over Time', fontsize=14, fontweight='bold')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # Add max drawdown annotation
    cumulative_pnl = df_sorted['Cumulative_PNL']
    running_max = cumulative_pnl.expanding().max()
    drawdown = cumulative_pnl - running_max
    max_dd_idx = drawdown.idxmin()
    
    plt.annotate(f'Max Drawdown: ₹{drawdown.min():,.0f}', 
                xy=(max_dd_idx, cumulative_pnl[max_dd_idx]), 
                xytext=(max_dd_idx + 10, cumulative_pnl[max_dd_idx] - 5000),
                arrowprops=dict(arrowstyle='->', color='red', lw=2),
                fontsize=10, color='red')
    
    plt.tight_layout()
    plt.savefig(f'{output_prefix}_cumulative_pnl.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 5. Strike Selection Analysis
    plt.figure(figsize=(14, 10))
    
    # Create strike category if not exists
    if 'Strike_Category' not in df.columns:
        df['Moneyness'] = df['Strike'] - df['Index_Entry_Price']
        
        def categorize_strike(row):
            if row['Instrument_Type'] == 'CE':
                if row['Moneyness'] < -100:
                    return 'Deep ITM'
                elif row['Moneyness'] < 0:
                    return 'ITM'
                elif row['Moneyness'] < 100:
                    return 'ATM'
                elif row['Moneyness'] < 200:
                    return 'OTM'
                else:
                    return 'Deep OTM'
            else:  # PE
                if row['Moneyness'] > 100:
                    return 'Deep ITM'
                elif row['Moneyness'] > 0:
                    return 'ITM'
                elif row['Moneyness'] > -100:
                    return 'ATM'
                elif row['Moneyness'] > -200:
                    return 'OTM'
                else:
                    return 'Deep OTM'
        
        df['Strike_Category'] = df.apply(categorize_strike, axis=1)
    
    strike_summary = df.groupby(['Instrument_Type', 'Strike_Category']).agg({
        'Netpnlafterexpenses': ['count', 'sum', 'mean']
    }).reset_index()
    
    # Flatten column names
    strike_summary.columns = ['Instrument_Type', 'Strike_Category', 'Count', 'Total_PNL', 'Avg_PNL']
    
    # Create subplots for CE and PE
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
    
    # CE Analysis
    ce_data = strike_summary[strike_summary['Instrument_Type'] == 'CE']
    if not ce_data.empty:
        ce_data.plot(x='Strike_Category', y='Total_PNL', kind='bar', ax=ax1, 
                     color='blue', alpha=0.7, label='Total P&L')
        ax1.set_title('Call Option (CE) Performance by Strike Category')
        ax1.set_xlabel('Strike Category')
        ax1.set_ylabel('Total P&L (₹)')
        ax1.axhline(y=0, color='red', linestyle='--', alpha=0.5)
        ax1.legend()
    
    # PE Analysis
    pe_data = strike_summary[strike_summary['Instrument_Type'] == 'PE']
    if not pe_data.empty:
        pe_data.plot(x='Strike_Category', y='Total_PNL', kind='bar', ax=ax2, 
                     color='green', alpha=0.7, label='Total P&L')
        ax2.set_title('Put Option (PE) Performance by Strike Category')
        ax2.set_xlabel('Strike Category')
        ax2.set_ylabel('Total P&L (₹)')
        ax2.axhline(y=0, color='red', linestyle='--', alpha=0.5)
        ax2.legend()
    
    plt.tight_layout()
    plt.savefig(f'{output_prefix}_strike_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"\nVisualizations saved with prefix: {output_prefix}")

def analyze_risk_management_effectiveness(df):
    """Analyze the effectiveness of risk management features."""
    
    print("\n" + "="*80)
    print("RISK MANAGEMENT EFFECTIVENESS ANALYSIS")
    print("="*80)
    
    # Analyze strategies with risk management features
    risk_strategies = ['ALL_TARGET_TYPES', 'ALL_SL_TYPES', 'STRATEGY_LEVEL_RISK', 
                      'TRAILING_SL_ALL_TYPES', 'MOVE_SL_TO_COST']
    
    risk_df = df[df['Strategy'].isin(risk_strategies)]
    non_risk_df = df[~df['Strategy'].isin(risk_strategies)]
    
    print(f"\nRisk Management Strategies Performance:")
    print(f"Total trades: {len(risk_df)}")
    print(f"Total P&L: ₹{risk_df['Netpnlafterexpenses'].sum():,.2f}")
    print(f"Win Rate: {(risk_df['Netpnlafterexpenses'] > 0).sum() / len(risk_df) * 100:.2f}%")
    print(f"Average P&L per trade: ₹{risk_df['Netpnlafterexpenses'].mean():,.2f}")
    
    print(f"\nNon-Risk Management Strategies Performance:")
    print(f"Total trades: {len(non_risk_df)}")
    print(f"Total P&L: ₹{non_risk_df['Netpnlafterexpenses'].sum():,.2f}")
    print(f"Win Rate: {(non_risk_df['Netpnlafterexpenses'] > 0).sum() / len(non_risk_df) * 100:.2f}%")
    print(f"Average P&L per trade: ₹{non_risk_df['Netpnlafterexpenses'].mean():,.2f}")
    
    # Calculate risk metrics
    risk_daily_pnl = risk_df.groupby('Entry_Date')['Netpnlafterexpenses'].sum()
    non_risk_daily_pnl = non_risk_df.groupby('Entry_Date')['Netpnlafterexpenses'].sum()
    
    risk_sharpe = (risk_daily_pnl.mean() / risk_daily_pnl.std() * np.sqrt(252)) if risk_daily_pnl.std() > 0 else 0
    non_risk_sharpe = (non_risk_daily_pnl.mean() / non_risk_daily_pnl.std() * np.sqrt(252)) if non_risk_daily_pnl.std() > 0 else 0
    
    print(f"\nRisk-Adjusted Performance:")
    print(f"Risk Strategies Sharpe Ratio: {risk_sharpe:.2f}")
    print(f"Non-Risk Strategies Sharpe Ratio: {non_risk_sharpe:.2f}")
    
    # Max loss comparison
    print(f"\nMax Loss Comparison:")
    print(f"Risk Strategies Max Loss: ₹{risk_df['Netpnlafterexpenses'].min():,.2f}")
    print(f"Non-Risk Strategies Max Loss: ₹{non_risk_df['Netpnlafterexpenses'].min():,.2f}")

def analyze_execution_quality(df):
    """Analyze trade execution quality and slippage impact."""
    
    print("\n" + "="*80)
    print("EXECUTION QUALITY ANALYSIS")
    print("="*80)
    
    # Calculate slippage metrics
    df['entry_slippage_pct'] = abs(df['Entry_Price'] - df['Entry_Price_Slippage']) / df['Entry_Price'] * 100
    df['exit_slippage_pct'] = abs(df['Exit_Price'] - df['Exit_Price_Slippage']) / df['Exit_Price'] * 100
    
    print(f"\nSlippage Statistics:")
    print(f"Average entry slippage: {df['entry_slippage_pct'].mean():.3f}%")
    print(f"Average exit slippage: {df['exit_slippage_pct'].mean():.3f}%")
    print(f"Max entry slippage: {df['entry_slippage_pct'].max():.3f}%")
    print(f"Max exit slippage: {df['exit_slippage_pct'].max():.3f}%")
    
    # Impact on P&L
    df['pnl_without_slippage'] = df['Pnl']
    df['pnl_impact'] = df['Pnl'] - df['Pnlafterslippage']
    
    print(f"\nSlippage Impact on P&L:")
    print(f"Total P&L impact: ₹{df['pnl_impact'].sum():,.2f}")
    print(f"Average P&L impact per trade: ₹{df['pnl_impact'].mean():,.2f}")
    print(f"P&L impact as % of gross P&L: {abs(df['pnl_impact'].sum() / df['Pnl'].sum() * 100):.2f}%")
    
    # Slippage by instrument type
    print("\nSlippage by Instrument Type:")
    slippage_by_type = df.groupby(['Instrument_Type', 'Side']).agg({
        'entry_slippage_pct': 'mean',
        'exit_slippage_pct': 'mean',
        'pnl_impact': 'sum'
    }).round(3)
    print(slippage_by_type)

def generate_comprehensive_report(df, output_file):
    """Generate a comprehensive HTML report."""
    
    html_content = f"""
    <html>
    <head>
        <title>Comprehensive Test Results Analysis Report</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            h1 {{ color: #333; border-bottom: 2px solid #333; padding-bottom: 10px; }}
            h2 {{ color: #666; margin-top: 30px; }}
            table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; font-weight: bold; }}
            .positive {{ color: green; font-weight: bold; }}
            .negative {{ color: red; font-weight: bold; }}
            .summary-box {{ background-color: #f9f9f9; padding: 15px; margin: 20px 0; border-radius: 5px; }}
        </style>
    </head>
    <body>
        <h1>Comprehensive Test Results Analysis Report</h1>
        <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        
        <div class="summary-box">
            <h2>Executive Summary</h2>
            <ul>
                <li>Total Trades: {len(df)}</li>
                <li>Total P&L: <span class="{'positive' if df['Netpnlafterexpenses'].sum() > 0 else 'negative'}">₹{df['Netpnlafterexpenses'].sum():,.2f}</span></li>
                <li>Win Rate: {(df['Netpnlafterexpenses'] > 0).sum() / len(df) * 100:.2f}%</li>
                <li>Average P&L per Trade: ₹{df['Netpnlafterexpenses'].mean():,.2f}</li>
                <li>Strategies Tested: {len(df['Strategy'].unique())}</li>
            </ul>
        </div>
        
        <h2>Strategy Performance Summary</h2>
        <table>
            <tr>
                <th>Strategy</th>
                <th>Trades</th>
                <th>Total P&L</th>
                <th>Win Rate</th>
                <th>Avg P&L</th>
                <th>Max Win</th>
                <th>Max Loss</th>
            </tr>
    """
    
    # Add strategy rows
    for strategy in df['Strategy'].unique():
        strategy_df = df[df['Strategy'] == strategy]
        total_pnl = strategy_df['Netpnlafterexpenses'].sum()
        win_rate = (strategy_df['Netpnlafterexpenses'] > 0).sum() / len(strategy_df) * 100
        avg_pnl = strategy_df['Netpnlafterexpenses'].mean()
        max_win = strategy_df['Netpnlafterexpenses'].max()
        max_loss = strategy_df['Netpnlafterexpenses'].min()
        
        html_content += f"""
            <tr>
                <td>{strategy}</td>
                <td>{len(strategy_df)}</td>
                <td class="{'positive' if total_pnl > 0 else 'negative'}">₹{total_pnl:,.2f}</td>
                <td>{win_rate:.1f}%</td>
                <td>₹{avg_pnl:,.2f}</td>
                <td class="positive">₹{max_win:,.2f}</td>
                <td class="negative">₹{max_loss:,.2f}</td>
            </tr>
        """
    
    html_content += """
        </table>
        
        <h2>Risk Metrics</h2>
        <div class="summary-box">
            <ul>
    """
    
    # Calculate risk metrics
    daily_pnl = df.groupby('Entry_Date')['Netpnlafterexpenses'].sum()
    sharpe_ratio = (daily_pnl.mean() / daily_pnl.std() * np.sqrt(252)) if daily_pnl.std() > 0 else 0
    
    cumulative_pnl = daily_pnl.cumsum()
    running_max = cumulative_pnl.expanding().max()
    drawdown = cumulative_pnl - running_max
    max_drawdown = drawdown.min()
    
    html_content += f"""
                <li>Sharpe Ratio (Annualized): {sharpe_ratio:.2f}</li>
                <li>Maximum Drawdown: <span class="negative">₹{max_drawdown:,.2f}</span></li>
                <li>Daily P&L Std Dev: ₹{daily_pnl.std():,.2f}</li>
                <li>Best Day: <span class="positive">₹{daily_pnl.max():,.2f}</span></li>
                <li>Worst Day: <span class="negative">₹{daily_pnl.min():,.2f}</span></li>
            </ul>
        </div>
        
        <p>For detailed visualizations, please refer to the PNG files generated alongside this report.</p>
    </body>
    </html>
    """
    
    with open(output_file, 'w') as f:
        f.write(html_content)
    
    print(f"\nHTML report saved to: {output_file}")

def main():
    """Main analysis function."""
    
    file_path = "/srv/samba/shared/comprehensive_test_results_20250530_091230.xlsx"
    output_prefix = file_path.replace('.xlsx', '')
    
    print(f"Starting comprehensive analysis of: {file_path}")
    
    # Load data
    df = pd.read_excel(file_path, sheet_name='PORTFOLIO Trans')
    
    # Ensure datetime columns are properly formatted
    df['Entry_Datetime'] = pd.to_datetime(df['Entry_Datetime'])
    df['Exit_Datetime'] = pd.to_datetime(df['Exit_Datetime'])
    
    # Create visualizations
    create_visualizations(df, output_prefix)
    
    # Perform additional analyses
    analyze_risk_management_effectiveness(df)
    analyze_execution_quality(df)
    
    # Generate HTML report
    generate_comprehensive_report(df, f"{output_prefix}_analysis_report.html")
    
    print("\n" + "="*80)
    print("ANALYSIS COMPLETE")
    print("="*80)
    print(f"\nGenerated files:")
    print(f"- {output_prefix}_strategy_pnl.png")
    print(f"- {output_prefix}_strategy_winrate.png")
    print(f"- {output_prefix}_pnl_distribution.png")
    print(f"- {output_prefix}_cumulative_pnl.png")
    print(f"- {output_prefix}_strike_analysis.png")
    print(f"- {output_prefix}_analysis_report.html")

if __name__ == "__main__":
    main()