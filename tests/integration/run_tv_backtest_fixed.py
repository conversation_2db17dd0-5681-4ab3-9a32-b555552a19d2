#!/usr/bin/env python3
"""Run TV backtest with fixed imports"""

import os
import sys

# Set up Python path
project_root = "/srv/samba/shared"
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, "bt"))

# Import the fixed BT_TV_GPU module directly
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import the TV models from the correct location
try:
    from bt.backtester_stable.models.tv_models import TvSettingModel, RawTvSignalModel, ProcessedTvSignalModel
    logger.info("Successfully imported TV models from bt.backtester_stable.models.tv_models")
except ImportError as e:
    logger.error(f"Failed to import TV models: {e}")
    sys.exit(1)

# Now import BT_TV_GPU
try:
    # We need to patch the import in BT_TV_GPU before loading it
    import importlib.util
    
    # Load the module
    bt_tv_path = os.path.join(project_root, "bt/backtester_stable/BTRUN/BT_TV_GPU.py")
    spec = importlib.util.spec_from_file_location("BT_TV_GPU", bt_tv_path)
    bt_tv_module = importlib.util.module_from_spec(spec)
    
    # Patch the models into the module's namespace before executing
    bt_tv_module.TvSettingModel = TvSettingModel
    bt_tv_module.RawTvSignalModel = RawTvSignalModel
    bt_tv_module.ProcessedTvSignalModel = ProcessedTvSignalModel
    
    # Execute the module
    spec.loader.exec_module(bt_tv_module)
    
    logger.info("Successfully loaded BT_TV_GPU module")
except Exception as e:
    logger.error(f"Failed to load BT_TV_GPU: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

# Set up environment
os.environ["INPUT_FILE_FOLDER"] = "bt/backtester_stable/BTRUN/input_sheets"
os.environ["TV_FILE_PATH"] = "input_tv.xlsx"

# Create output directory
output_dir = os.path.join(project_root, "bt/backtester_stable/BTRUN/output/tv_test")
os.makedirs(output_dir, exist_ok=True)

# Parse arguments
args = bt_tv_module.parse_args()
args.legacy_excel = True
args.output_dir = output_dir
args.start_date = "240101"  # Jan 1, 2024
args.end_date = "241231"    # Dec 31, 2024

logger.info("Running TV backtest with settings:")
logger.info(f"  Output directory: {args.output_dir}")
logger.info(f"  Date range: {args.start_date} to {args.end_date}")
logger.info(f"  Legacy Excel mode: {args.legacy_excel}")

# Change to project root
os.chdir(project_root)

# Run the backtest
try:
    success = bt_tv_module.run_tv_backtest(args)
    if success:
        logger.info("TV backtest completed successfully!")
        sys.exit(0)
    else:
        logger.error("TV backtest failed!")
        sys.exit(1)
except Exception as e:
    logger.error(f"Error running TV backtest: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1) 