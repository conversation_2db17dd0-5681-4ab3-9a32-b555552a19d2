#!/usr/bin/env python3
"""
Test script for multi-index backtesting
Tests NIFTY, BANKNIFTY, MIDCAPNIFTY, and SENSEX
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'bt/backtester_stable'))

from datetime import datetime
import pandas as pd
from heavydb import connect

# Test data availability for each index
def test_data_availability():
    """Check available data for each index"""
    print("=" * 80)
    print("TESTING DATA AVAILABILITY")
    print("=" * 80)
    
    conn = connect(
        host='localhost',
        port=6274,
        user='admin',
        password='HyperInteractive',
        dbname='heavyai'
    )
    cursor = conn.cursor()
    
    # Check data for each index
    indices = ['NIFTY', 'BANKNIFTY', 'MIDCAPNIFTY', 'SENSEX']
    
    for index in indices:
        print(f"\n{index}:")
        print("-" * 40)
        
        # Get date range and row count
        query = f"""
        SELECT 
            MIN(trade_date) as min_date,
            MAX(trade_date) as max_date,
            COUNT(DISTINCT trade_date) as trading_days,
            COUNT(*) as total_rows,
            COUNT(DISTINCT strike) as unique_strikes,
            COUNT(DISTINCT expiry_bucket) as expiry_types
        FROM nifty_option_chain
        WHERE index_name = '{index}'
        """
        
        cursor.execute(query)
        result = cursor.fetchone()
        
        if result and result[0]:
            print(f"  Date Range: {result[0]} to {result[1]}")
            print(f"  Trading Days: {result[2]}")
            print(f"  Total Rows: {result[3]:,}")
            print(f"  Unique Strikes: {result[4]}")
            print(f"  Expiry Types: {result[5]}")
            
            # Get expiry types
            query2 = f"""
            SELECT DISTINCT expiry_bucket
            FROM nifty_option_chain
            WHERE index_name = '{index}'
            ORDER BY expiry_bucket
            """
            cursor.execute(query2)
            expiry_types = [row[0] for row in cursor.fetchall()]
            print(f"  Expiry Buckets: {', '.join(expiry_types)}")
        else:
            print(f"  No data available")
    
    cursor.close()
    conn.close()

# Test multi-index configuration
def test_multi_index_config():
    """Test the multi-index configuration module"""
    print("\n" + "=" * 80)
    print("TESTING MULTI-INDEX CONFIGURATION")
    print("=" * 80)
    
    try:
        from BTRUN.core.multi_index_config import (
            get_index_config, get_lot_size, get_strike_increment,
            get_margin_requirement, get_supported_indices
        )
        
        print("\nSupported Indices:", ', '.join(get_supported_indices()))
        
        # Test each index
        for index in ['NIFTY', 'BANKNIFTY', 'MIDCAPNIFTY', 'SENSEX']:
            config = get_index_config(index)
            print(f"\n{index} Configuration:")
            print(f"  Lot Size: {get_lot_size(index)}")
            print(f"  Strike Increment: {get_strike_increment(index)}")
            print(f"  Margin (Selling): {get_margin_requirement(index) * 100:.1f}%")
            print(f"  Expiry Types: {', '.join(config['expiry_types'])}")
            
    except Exception as e:
        print(f"Error testing multi-index config: {e}")

# Test simple backtest query for each index
def test_backtest_queries():
    """Test basic backtest queries for each index"""
    print("\n" + "=" * 80)
    print("TESTING BACKTEST QUERIES")
    print("=" * 80)
    
    conn = connect(
        host='localhost',
        port=6274,
        user='admin',
        password='HyperInteractive',
        dbname='heavyai'
    )
    cursor = conn.cursor()
    
    indices = ['NIFTY', 'BANKNIFTY', 'MIDCAPNIFTY', 'SENSEX']
    test_date = '2024-01-15'  # A date we likely have data for
    
    for index in indices:
        print(f"\n{index} - Testing ATM query for {test_date}:")
        print("-" * 40)
        
        # Test ATM strike query
        query = f"""
        SELECT 
            trade_time,
            spot,
            atm_strike,
            strike,
            ce_close,
            pe_close
        FROM nifty_option_chain
        WHERE index_name = '{index}'
        AND trade_date = '{test_date}'
        AND zone_name = 'ATM'
        AND trade_time = '09:15:00'
        LIMIT 5
        """
        
        try:
            cursor.execute(query)
            results = cursor.fetchall()
            
            if results:
                print(f"  Found {len(results)} ATM strikes at market open")
                for row in results[:2]:  # Show first 2
                    print(f"    Time: {row[0]}, Spot: {row[1]}, ATM: {row[2]}, Strike: {row[3]}, CE: {row[4]}, PE: {row[5]}")
            else:
                # Try without zone_name filter
                query2 = f"""
                SELECT COUNT(*) 
                FROM nifty_option_chain
                WHERE index_name = '{index}'
                AND trade_date = '{test_date}'
                """
                cursor.execute(query2)
                count = cursor.fetchone()[0]
                print(f"  No ATM data, but found {count} total rows for this date")
                
        except Exception as e:
            print(f"  Error: {e}")
    
    cursor.close()
    conn.close()

# Test strike selection for different indices
def test_strike_selection():
    """Test strike selection logic for different indices"""
    print("\n" + "=" * 80)
    print("TESTING STRIKE SELECTION")
    print("=" * 80)
    
    from BTRUN.core.multi_index_config import get_strike_increment
    
    test_spots = {
        'NIFTY': 24000,
        'BANKNIFTY': 48000,
        'MIDCAPNIFTY': 8500,
        'SENSEX': 72000
    }
    
    for index, spot in test_spots.items():
        increment = get_strike_increment(index)
        atm_strike = round(spot / increment) * increment
        
        print(f"\n{index}:")
        print(f"  Spot Price: {spot}")
        print(f"  Strike Increment: {increment}")
        print(f"  Calculated ATM: {atm_strike}")
        print(f"  Strike Range: {atm_strike - 5*increment} to {atm_strike + 5*increment}")

if __name__ == "__main__":
    print("Multi-Index Backtesting Test Suite")
    print("=" * 80)
    print(f"Test Time: {datetime.now()}")
    
    # Run all tests
    test_data_availability()
    test_multi_index_config()
    test_backtest_queries()
    test_strike_selection()
    
    print("\n" + "=" * 80)
    print("Test completed!")