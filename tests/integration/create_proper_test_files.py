#!/usr/bin/env python3
"""
Create proper test files with actual input format
"""

import pandas as pd
import os

# Use the actual input files as templates
def create_dte0_test_files():
    """Create test files for DTE=0 validation"""
    
    # Read existing files as templates
    portfolio_template = pd.read_excel('/srv/samba/shared/input_portfolio.xlsx', sheet_name=None)
    strategy_template = pd.read_excel('/srv/samba/shared/input_tbs_multi_legs.xlsx', sheet_name=None)
    
    # Modify PortfolioSetting for DTE=0 test
    portfolio_df = portfolio_template['PortfolioSetting'].copy()
    portfolio_df.loc[0, 'StartDate'] = '2024-01-01'
    portfolio_df.loc[0, 'EndDate'] = '2024-01-07'
    portfolio_df.loc[0, 'Enabled'] = 'Yes'
    portfolio_df.loc[0, 'PortfolioName'] = 'DTE0_TEST'
    
    # Modify StrategySetting
    strategy_df = portfolio_template['StrategySetting'].copy()
    strategy_df = strategy_df.iloc[:1].copy()  # Keep only first row
    strategy_df.loc[0, 'Enabled'] = 'Yes'
    strategy_df.loc[0, 'StrategyName'] = 'DTE0_TEST' 
    strategy_df.loc[0, 'LegsInStrategy'] = 2
    strategy_df.loc[0, 'PortfolioStartTime'] = '09:16:00'
    strategy_df.loc[0, 'PortfolioEndTime'] = '12:00:00'
    strategy_df.loc[0, 'StrategyExcelFilePath'] = 'test_dte0_strategy_proper.xlsx'
    
    # Create GeneralParameter with DTE=0
    general_df = strategy_template['GeneralParameter'].copy()
    general_df = general_df.iloc[:1].copy()
    general_df.loc[0, 'StrategyName'] = 'DTE0_TEST'
    general_df.loc[0, 'DTE'] = 0  # Only trade on expiry days
    general_df.loc[0, 'StartTime'] = 91600
    general_df.loc[0, 'EndTime'] = 120000
    general_df.loc[0, 'Index'] = 'NIFTY'
    general_df.loc[0, 'Underlying'] = 'SPOT'
    general_df.loc[0, 'Weekdays'] = '1,2,3,4,5'
    
    # Create LegParameter - simple ATM straddle
    leg_data = []
    for i, (instrument, leg_id) in enumerate([('call', '1'), ('put', '2')]):
        leg_data.append({
            'StrategyName': 'DTE0_TEST',
            'LegID': leg_id,
            'Instrument': instrument,
            'Transaction': 'sell',
            'StrikeMethod': 'ATM',
            'StrikeValue': 0,
            'Expiry': 'current',
            'Lots': 1,
            'SLType': 'percentage',
            'SLValue': 500,
            'TGTType': 'percentage', 
            'TGTValue': 100
        })
    
    leg_df = pd.DataFrame(leg_data)
    
    # Save files
    output_portfolio = '/srv/samba/shared/test_dte0_portfolio_proper.xlsx'
    output_strategy = '/srv/samba/shared/test_dte0_strategy_proper.xlsx'
    
    with pd.ExcelWriter(output_portfolio) as writer:
        portfolio_df.to_excel(writer, sheet_name='PortfolioSetting', index=False)
        strategy_df.to_excel(writer, sheet_name='StrategySetting', index=False)
    
    with pd.ExcelWriter(output_strategy) as writer:
        general_df.to_excel(writer, sheet_name='GeneralParameter', index=False)
        leg_df.to_excel(writer, sheet_name='LegParameter', index=False)
    
    print(f"Created proper test files:")
    print(f"  Portfolio: {output_portfolio}")
    print(f"  Strategy: {output_strategy}")
    
    return output_portfolio, output_strategy

def create_strike_time_test():
    """Create test for StrikeSelectionTime"""
    
    strategy_template = pd.read_excel('/srv/samba/shared/input_tbs_multi_legs.xlsx', sheet_name=None)
    
    # Create GeneralParameter with StrikeSelectionTime
    general_df = strategy_template['GeneralParameter'].copy()
    general_df = general_df.iloc[:1].copy()
    general_df.loc[0, 'StrategyName'] = 'STRIKE_TIME_TEST'
    general_df.loc[0, 'DTE'] = 1
    general_df.loc[0, 'StartTime'] = 91600  # Entry at 09:16
    general_df.loc[0, 'StrikeSelectionTime'] = 92000  # Strike at 09:20
    general_df.loc[0, 'EndTime'] = 120000
    general_df.loc[0, 'Index'] = 'NIFTY'
    
    output_file = '/srv/samba/shared/test_strike_time.xlsx'
    with pd.ExcelWriter(output_file) as writer:
        general_df.to_excel(writer, sheet_name='GeneralParameter', index=False)
    
    print(f"\nCreated StrikeSelectionTime test: {output_file}")
    
    return output_file

def create_weekday_test():
    """Create test for weekday filtering"""
    
    strategy_template = pd.read_excel('/srv/samba/shared/input_tbs_multi_legs.xlsx', sheet_name=None)
    
    # Create GeneralParameter with Weekdays filter
    general_df = strategy_template['GeneralParameter'].copy()
    general_df = general_df.iloc[:1].copy()
    general_df.loc[0, 'StrategyName'] = 'WEEKDAY_TEST'
    general_df.loc[0, 'DTE'] = 10
    general_df.loc[0, 'Weekdays'] = '2,4'  # Only Tuesday and Thursday
    general_df.loc[0, 'StartTime'] = 91600
    general_df.loc[0, 'EndTime'] = 120000
    
    output_file = '/srv/samba/shared/test_weekday_filter.xlsx'
    with pd.ExcelWriter(output_file) as writer:
        general_df.to_excel(writer, sheet_name='GeneralParameter', index=False)
    
    print(f"\nCreated weekday filter test: {output_file}")
    
    return output_file

if __name__ == "__main__":
    print("Creating proper test files based on actual input format...")
    print("="*60)
    
    # Create all test files
    portfolio_file, strategy_file = create_dte0_test_files()
    strike_time_file = create_strike_time_test()
    weekday_file = create_weekday_test()
    
    print("\n" + "="*60)
    print("Test files created successfully!")
    print("\nTo run DTE=0 test:")
    print(f"python3 /srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py \\")
    print(f"  --portfolio-excel {portfolio_file} \\")
    print(f"  --output-path /srv/samba/shared/test_dte0_output.xlsx")