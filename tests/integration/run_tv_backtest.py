#!/usr/bin/env python3
"""Run TV backtest with proper environment setup"""

import os
import sys
import subprocess

# Set up Python path
project_root = "/srv/samba/shared"
bt_path = os.path.join(project_root, "bt")
sys.path.insert(0, bt_path)

# Set up environment variables
os.environ["PYTHONPATH"] = bt_path
os.environ["INPUT_FILE_FOLDER"] = "bt/backtester_stable/BTRUN/input_sheets"
os.environ["TV_FILE_PATH"] = "input_tv.xlsx"  # Override the default input_tv_fixed.xlsx

# Create output directory
output_dir = "bt/backtester_stable/BTRUN/output/tv_test"
os.makedirs(output_dir, exist_ok=True)

# Run the TV backtester
cmd = [
    sys.executable,
    "-m", "backtester_stable.BTRUN.BT_TV_GPU",
    "--legacy-excel",
    "--output-dir", output_dir,
    "--start-date", "240101",  # Jan 1, 2024
    "--end-date", "241231",    # Dec 31, 2024 (limiting to 1 year for testing)
]

print(f"Running TV backtest with command:")
print(" ".join(cmd))
print(f"Working directory: {project_root}")
print(f"PYTHONPATH: {os.environ.get('PYTHONPATH')}")
print(f"TV file override: {os.environ.get('TV_FILE_PATH')}")
print("=" * 80)

# Change to project root
os.chdir(project_root)

# Run the command
result = subprocess.run(cmd, capture_output=False, text=True)
sys.exit(result.returncode) 