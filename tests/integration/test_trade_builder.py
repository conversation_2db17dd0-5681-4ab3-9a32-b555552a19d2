#!/usr/bin/env python3
"""
Unit tests for the trade builder in bt.backtester_stable.BTRUN.trade_builder

Tests both the build_trade_record function and its handling of exit times and reasons,
with special focus on the exit time override functionality for SL/TP exits.
"""

import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, date
import sys
import os
import logging

# Configure test logging
logging.basicConfig(level=logging.ERROR)

# Add project root to path for imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Import trade_builder and related modules
from bt.backtester_stable.BTRUN.trade_builder import build_trade_record, build_test_trade
from bt.backtester_stable.BTRUN.models.common import OptionType, TransactionType, StrikeRule, ExpiryRule
from bt.backtester_stable.BTRUN.models.leg import LegModel

class TestTradeBuilder(unittest.TestCase):
    """Test suite for trade builder functionality"""

    def setUp(self):
        """Create sample data for tests"""
        # Set up common test data
        self.entry_time = "09:16:00"
        self.exit_time = "12:00:00"
        self.trade_date = date(2025, 4, 1)
        self.entry_price = 100.0
        self.exit_price = 110.0
        self.underlying = 22000.0
        self.portfolio_name = "TestPortfolio"
        self.strategy_name = "TestStrategy"
        
        # Create entry row
        self.entry_row = pd.Series({
            'datetime': datetime.combine(self.trade_date, datetime.strptime(self.entry_time, "%H:%M:%S").time()),
            'trade_date': self.trade_date,
            'trade_time': int(self.entry_time.replace(':', '')),
            'ce_close': self.entry_price,
            'pe_close': self.entry_price,
            'underlying_price': self.underlying,
            'spot': self.underlying,
            'strike': self.underlying,
            'expiry_date': self.trade_date + timedelta(days=3),
        })
        
        # Create exit row
        self.exit_row = pd.Series({
            'datetime': datetime.combine(self.trade_date, datetime.strptime(self.exit_time, "%H:%M:%S").time()),
            'trade_date': self.trade_date,
            'trade_time': int(self.exit_time.replace(':', '')),
            'ce_close': self.exit_price,
            'pe_close': self.exit_price,
            'underlying_price': self.underlying,
            'spot': self.underlying,
            'strike': self.underlying,
        })
        
        # Create test leg
        self.leg = LegModel(
            leg_id="1",
            index="NIFTY",
            option_type=OptionType.CALL,
            transaction=TransactionType.SELL,  # SELL option
            lots=1,
            entry_time="091600",
            exit_time="120000",
            strike_rule=StrikeRule.ATM,  
            expiry_rule=ExpiryRule.CURRENT_WEEK,
            risk_rules=[
                {
                    "rule_type": "STOPLOSS",
                    "number_type": "PERCENTAGE",
                    "value": 100.0  # This would normally trigger immediately for SELL
                }
            ]
        )

    def test_normal_exit_time(self):
        """Test trade record with normal exit at scheduled time"""
        # Use exit_row with normal exit time (12:00)
        trade_record = build_trade_record(
            portfolio_name=self.portfolio_name,
            strategy_name=self.strategy_name,
            leg=self.leg,
            entry_row=self.entry_row,
            exit_row=self.exit_row.copy(),
            slippage_pct=0.1
        )
        
        # Verify exit time is as expected
        self.assertEqual(trade_record['exit_time'], self.exit_time)
        # Verify exit datetime matches exit time
        self.assertEqual(trade_record['exit_datetime'], f"{self.trade_date.strftime('%Y-%m-%d')} {self.exit_time}")
        # Default reason should be "Exit Time Hit"
        self.assertEqual(trade_record['reason'], "Exit Time Hit")

    def test_early_sl_exit_override(self):
        """Test that early SL exits are overridden with scheduled exit time"""
        # Create exit row with early time (9:17) and SL exit reason
        early_exit_row = self.exit_row.copy()
        early_exit_row['datetime'] = datetime.combine(self.trade_date, datetime.strptime("09:17:00", "%H:%M:%S").time())
        early_exit_row['trade_time'] = 91700
        early_exit_row['exit_reason'] = "Stop Loss Hit"
        
        trade_record = build_trade_record(
            portfolio_name=self.portfolio_name,
            strategy_name=self.strategy_name,
            leg=self.leg,
            entry_row=self.entry_row,
            exit_row=early_exit_row,
            slippage_pct=0.1
        )
        
        # Exit time should be overridden with leg's exit time (12:00)
        self.assertEqual(trade_record['exit_time'], self.exit_time)
        # Exit datetime should match exit time
        self.assertEqual(trade_record['exit_datetime'], f"{self.trade_date.strftime('%Y-%m-%d')} {self.exit_time}")
        # Reason should be changed to "Exit Time Hit"
        self.assertEqual(trade_record['reason'], "Exit Time Hit")

    def test_early_tp_exit_override(self):
        """Test that early TP exits are overridden with scheduled exit time"""
        # Create exit row with early time (10:30) and TP exit reason
        early_exit_row = self.exit_row.copy()
        early_exit_row['datetime'] = datetime.combine(self.trade_date, datetime.strptime("10:30:00", "%H:%M:%S").time())
        early_exit_row['trade_time'] = 103000
        early_exit_row['exit_reason'] = "Target Hit"
        
        trade_record = build_trade_record(
            portfolio_name=self.portfolio_name,
            strategy_name=self.strategy_name,
            leg=self.leg,
            entry_row=self.entry_row,
            exit_row=early_exit_row,
            slippage_pct=0.1
        )
        
        # Exit time should be overridden with leg's exit time (12:00)
        self.assertEqual(trade_record['exit_time'], self.exit_time)
        # Exit datetime should match exit time
        self.assertEqual(trade_record['exit_datetime'], f"{self.trade_date.strftime('%Y-%m-%d')} {self.exit_time}")
        # Reason should be changed to "Exit Time Hit"
        self.assertEqual(trade_record['reason'], "Exit Time Hit")

    def test_trail_sl_exit_override(self):
        """Test that early Trail SL exits are overridden with scheduled exit time"""
        # Create exit row with early time (11:00) and Trail SL exit reason
        early_exit_row = self.exit_row.copy()
        early_time = datetime.strptime("11:00:00", "%H:%M:%S").time()
        early_exit_row['datetime'] = datetime.combine(self.trade_date, early_time)
        early_exit_row['trade_time'] = 110000
        early_exit_row['exit_reason'] = "Trail SL Hit"
        
        # Make a copy of self.leg with exit_time matching self.exit_time to ensure consistency
        leg_with_exit = self.leg.model_copy(deep=True)
        leg_with_exit.exit_time = self.exit_time.replace(":", "")
        
        trade_record = build_trade_record(
            portfolio_name=self.portfolio_name,
            strategy_name=self.strategy_name,
            leg=leg_with_exit,
            entry_row=self.entry_row,
            exit_row=early_exit_row,
            slippage_pct=0.1
        )
        
        # Exit time should be overridden with leg's exit time (12:00)
        self.assertEqual(trade_record['exit_time'], self.exit_time)
        # Exit datetime should match exit time
        self.assertEqual(trade_record['exit_datetime'], f"{self.trade_date.strftime('%Y-%m-%d')} {self.exit_time}")
        # Reason should be changed to "Exit Time Hit"
        self.assertEqual(trade_record['reason'], "Exit Time Hit")

    def test_different_option_types(self):
        """Test trade building with different option types"""
        # Test for CALL option
        call_leg = self.leg.model_copy(deep=True)
        call_leg.option_type = OptionType.CALL
        
        call_trade = build_trade_record(
            portfolio_name=self.portfolio_name,
            strategy_name=self.strategy_name,
            leg=call_leg,
            entry_row=self.entry_row,
            exit_row=self.exit_row,
            slippage_pct=0.1
        )
        
        self.assertEqual(call_trade['instrument_type'], "CE")
        
        # Test for PUT option
        put_leg = self.leg.model_copy(deep=True)
        put_leg.option_type = OptionType.PUT
        
        put_trade = build_trade_record(
            portfolio_name=self.portfolio_name,
            strategy_name=self.strategy_name,
            leg=put_leg,
            entry_row=self.entry_row,
            exit_row=self.exit_row,
            slippage_pct=0.1
        )
        
        self.assertEqual(put_trade['instrument_type'], "PE")

    def test_transaction_types(self):
        """Test trade building with different transaction types"""
        # Test for BUY transaction
        buy_leg = self.leg.model_copy(deep=True)
        buy_leg.transaction = TransactionType.BUY
        
        buy_trade = build_trade_record(
            portfolio_name=self.portfolio_name,
            strategy_name=self.strategy_name,
            leg=buy_leg,
            entry_row=self.entry_row,
            exit_row=self.exit_row,
            slippage_pct=0.1
        )
        
        self.assertEqual(buy_trade['side'], "BUY")
        # For BUY, PnL = exit_price - entry_price
        self.assertGreater(buy_trade['pnl'], 0)  # Should be positive (exit > entry)
        
        # Test for SELL transaction
        sell_leg = self.leg.model_copy(deep=True)
        sell_leg.transaction = TransactionType.SELL
        
        sell_trade = build_trade_record(
            portfolio_name=self.portfolio_name,
            strategy_name=self.strategy_name,
            leg=sell_leg,
            entry_row=self.entry_row,
            exit_row=self.exit_row,
            slippage_pct=0.1
        )
        
        self.assertEqual(sell_trade['side'], "SELL")
        # For SELL, PnL = entry_price - exit_price
        self.assertLess(sell_trade['pnl'], 0)  # Should be negative (exit > entry)

    def test_slippage_calculation(self):
        """Test slippage calculation in trade records"""
        # Test with different slippage values
        slippage_values = [0.0, 0.1, 0.5, 1.0]
        
        for slippage_pct in slippage_values:
            with self.subTest(slippage_pct=slippage_pct):
                trade_record = build_trade_record(
                    portfolio_name=self.portfolio_name,
                    strategy_name=self.strategy_name,
                    leg=self.leg,
                    entry_row=self.entry_row,
                    exit_row=self.exit_row,
                    slippage_pct=slippage_pct
                )
                
                # Verify that pnl and pnlAfterSlippage are different with non-zero slippage
                if slippage_pct > 0:
                    self.assertNotEqual(trade_record['pnl'], trade_record['pnlAfterSlippage'])
                else:
                    self.assertEqual(trade_record['pnl'], trade_record['pnlAfterSlippage'])

    def test_build_test_trade_helper(self):
        """Test the build_test_trade helper function"""
        test_trade = build_test_trade(
            index="NIFTY",
            option_type="CALL",
            transaction="BUY",
            strike=22000.0,
            entry_date="2025-04-01",
            entry_time="09:16:00",
            exit_date="2025-04-01", 
            exit_time="12:00:00",
            entry_price=100.0
        )
        
        # Verify that the test trade has the expected fields
        self.assertEqual(test_trade['index'], "NIFTY")
        self.assertEqual(test_trade['option_type'], "CALL")
        self.assertEqual(test_trade['transaction'], "BUY")
        self.assertEqual(test_trade['strike'], 22000.0)
        self.assertEqual(test_trade['entry_time'], "09:16:00")
        self.assertEqual(test_trade['exit_time'], "12:00:00")
        self.assertEqual(test_trade['entry_price'], 100.0)
        self.assertEqual(test_trade['exit_reason'], "Exit Time Hit")  # Default reason

    def test_leg_with_parent_strategy(self):
        """Test that leg with a parent strategy uses strategy exit time"""
        # Create a mock parent strategy with a different exit time
        class MockStrategy:
            def __init__(self):
                self.entry_end = 153000  # 15:30:00
        
        # Create a leg with parent strategy
        leg_with_parent = self.leg.model_copy(deep=True)
        leg_with_parent.parent = MockStrategy()
        
        trade_record = build_trade_record(
            portfolio_name=self.portfolio_name,
            strategy_name=self.strategy_name,
            leg=leg_with_parent,
            entry_row=self.entry_row,
            exit_row=self.exit_row.copy(),
            slippage_pct=0.1
        )
        
        # Exit time should match parent strategy's exit time (15:30:00)
        self.assertEqual(trade_record['exit_time'], "15:30:00")

    def test_date_format_handling(self):
        """Test different date format handling in trade builder"""
        # Test with date objects
        date_entry_row = self.entry_row.copy()
        date_entry_row['expiry_date'] = date(2025, 4, 5)
        
        trade_record = build_trade_record(
            portfolio_name=self.portfolio_name,
            strategy_name=self.strategy_name,
            leg=self.leg,
            entry_row=date_entry_row,
            exit_row=self.exit_row,
            slippage_pct=0.1
        )
        
        # Expiry date should be formatted as YYYY-MM-DD
        self.assertEqual(trade_record['expiry'], "2025-04-05")
        
        # Test with string dates
        string_entry_row = self.entry_row.copy()
        string_entry_row['expiry_date'] = "2025-04-05"
        
        trade_record = build_trade_record(
            portfolio_name=self.portfolio_name,
            strategy_name=self.strategy_name,
            leg=self.leg,
            entry_row=string_entry_row,
            exit_row=self.exit_row,
            slippage_pct=0.1
        )
        
        # Should still be formatted correctly
        self.assertEqual(trade_record['expiry'], "2025-04-05")

    def test_required_fields_present(self):
        """Test that all required fields are present in the trade record"""
        trade_record = build_trade_record(
            portfolio_name=self.portfolio_name,
            strategy_name=self.strategy_name,
            leg=self.leg,
            entry_row=self.entry_row,
            exit_row=self.exit_row,
            slippage_pct=0.1
        )
        
        # Check for required fields
        required_fields = [
            "portfolio_name", "strategy", "leg_id", 
            "entry_date", "entry_time", "exit_date", "exit_time",
            "symbol", "expiry", "strike", "instrument_type",
            "side", "filled_quantity", "entry_price", "exit_price",
            "points", "pointsAfterSlippage", "pnl", "pnlAfterSlippage",
            "expenses", "netPnlAfterExpenses", "reason",
            "entry_datetime", "exit_datetime"
        ]
        
        for field in required_fields:
            self.assertIn(field, trade_record, f"Field '{field}' missing from trade record")
            self.assertIsNotNone(trade_record[field], f"Field '{field}' is None in trade record")

if __name__ == "__main__":
    unittest.main() 