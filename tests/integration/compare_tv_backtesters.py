#!/usr/bin/env python3
"""
Compare CPU and GPU TV backtesters with proper module imports
"""

import os
import sys
import subprocess
import pandas as pd
from datetime import datetime
import time

# Set up Python path
project_root = "/srv/samba/shared"
bt_path = os.path.join(project_root, "bt")
sys.path.insert(0, bt_path)

def run_cpu_version():
    """Run CPU-based TV backtester"""
    print("\n" + "="*80)
    print("Running CPU-based TV Backtester (BT_TV_GPU_aggregated_v4)")
    print("="*80)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f"Trades/tv_cpu_test_{timestamp}.xlsx"
    
    # Change to project directory
    os.chdir(project_root)
    
    # Run as module
    cmd = [
        sys.executable,
        "-m", "backtester_stable.BTRUN.BT_TV_GPU_aggregated_v4",
        "--tv-excel", "bt/backtester_stable/BTRUN/input_sheets/input_tv.xlsx",
        "--output-path", output_file,
        "--workers", "4"
    ]
    
    print("Command:", " ".join(cmd))
    start_time = time.time()
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        elapsed = time.time() - start_time
        
        print(f"Return code: {result.returncode}")
        print(f"Time taken: {elapsed:.2f} seconds")
        
        if result.stdout:
            print("STDOUT:", result.stdout[-1000:])  # Last 1000 chars
        if result.stderr:
            print("STDERR:", result.stderr[-1000:])  # Last 1000 chars
            
        full_path = os.path.join(project_root, output_file)
        if os.path.exists(full_path):
            print(f"✅ CPU version completed successfully")
            return full_path, elapsed
        else:
            print(f"❌ CPU version failed - no output file")
            return None, elapsed
            
    except Exception as e:
        print(f"Error running CPU version: {e}")
        import traceback
        traceback.print_exc()
        return None, 0

def run_gpu_version():
    """Run GPU-enhanced TV backtester"""
    print("\n" + "="*80)
    print("Running GPU-enhanced TV Backtester (BT_TV_GPU_enhanced)")
    print("="*80)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f"Trades/tv_gpu_test_{timestamp}.xlsx"
    
    # Change to project directory
    os.chdir(project_root)
    
    # Run as module
    cmd = [
        sys.executable,
        "-m", "backtester_stable.BTRUN.BT_TV_GPU_enhanced",
        "--tv-excel", "bt/backtester_stable/BTRUN/input_sheets/input_tv.xlsx",
        "--output-path", output_file,
        "--workers", "auto",
        "--batch-days", "7"
    ]
    
    print("Command:", " ".join(cmd))
    start_time = time.time()
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        elapsed = time.time() - start_time
        
        print(f"Return code: {result.returncode}")
        print(f"Time taken: {elapsed:.2f} seconds")
        
        if result.stdout:
            print("STDOUT:", result.stdout[-1000:])  # Last 1000 chars
        if result.stderr:
            print("STDERR:", result.stderr[-1000:])  # Last 1000 chars
            
        full_path = os.path.join(project_root, output_file)
        if os.path.exists(full_path):
            print(f"✅ GPU version completed successfully")
            return full_path, elapsed
        else:
            print(f"❌ GPU version failed - no output file")
            return None, elapsed
            
    except Exception as e:
        print(f"Error running GPU version: {e}")
        import traceback
        traceback.print_exc()
        return None, 0

def compare_results(cpu_file, gpu_file, cpu_time, gpu_time):
    """Compare outputs from both versions"""
    print("\n" + "="*80)
    print("COMPARISON RESULTS")
    print("="*80)
    
    # Performance comparison
    print(f"\n📊 Performance:")
    print(f"CPU version: {cpu_time:.2f} seconds")
    print(f"GPU version: {gpu_time:.2f} seconds")
    if cpu_time > 0 and gpu_time > 0:
        speedup = cpu_time / gpu_time
        print(f"Speedup: {speedup:.2f}x")
    
    if not cpu_file or not gpu_file:
        print("\n❌ Cannot compare outputs - one or both files missing")
        return
    
    print(f"\n📁 Output files:")
    print(f"CPU: {cpu_file}")
    print(f"GPU: {gpu_file}")
    
    try:
        # Compare key sheets
        sheets_to_check = ['PORTFOLIO Trans', 'Metrics', 'Max Profit and Loss', 'PORTFOLIO Results']
        
        for sheet in sheets_to_check:
            print(f"\n--- {sheet} Sheet ---")
            
            try:
                cpu_df = pd.read_excel(cpu_file, sheet_name=sheet)
                gpu_df = pd.read_excel(gpu_file, sheet_name=sheet)
                
                print(f"Shape - CPU: {cpu_df.shape}, GPU: {gpu_df.shape}")
                
                if sheet == 'PORTFOLIO Trans':
                    # Compare trade details
                    if len(cpu_df) != len(gpu_df):
                        print(f"⚠️  Different number of trades!")
                    
                    # Compare key columns
                    cols_to_compare = ['Net PNL', 'Points', 'Points After Slippage', 'MaxProfit', 'MaxLoss']
                    for col in cols_to_compare:
                        if col in cpu_df.columns and col in gpu_df.columns:
                            cpu_sum = cpu_df[col].sum()
                            gpu_sum = gpu_df[col].sum()
                            diff = abs(cpu_sum - gpu_sum)
                            
                            if diff < 0.01:
                                print(f"✅ {col} sum matches: {cpu_sum:.2f}")
                            else:
                                print(f"⚠️  {col} differs - CPU: {cpu_sum:.2f}, GPU: {gpu_sum:.2f}, Diff: {diff:.2f}")
                
                elif sheet == 'Metrics':
                    # Compare overall metrics
                    if not cpu_df.empty and not gpu_df.empty:
                        print("Sample metrics comparison:")
                        metrics_cols = cpu_df.columns[:5]
                        for col in metrics_cols:
                            if col in cpu_df.columns and col in gpu_df.columns:
                                cpu_val = cpu_df[col].iloc[0] if len(cpu_df) > 0 else None
                                gpu_val = gpu_df[col].iloc[0] if len(gpu_df) > 0 else None
                                print(f"  {col}: CPU={cpu_val}, GPU={gpu_val}")
                                
            except Exception as e:
                print(f"Error reading {sheet}: {e}")
                
    except Exception as e:
        print(f"Error in comparison: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main execution"""
    print("TV Backtester CPU vs GPU Comparison")
    print("=" * 80)
    print(f"Started at: {datetime.now()}")
    print(f"Working directory: {project_root}")
    
    # Run both versions
    cpu_file, cpu_time = run_cpu_version()
    gpu_file, gpu_time = run_gpu_version()
    
    # Compare results
    compare_results(cpu_file, gpu_file, cpu_time, gpu_time)
    
    print(f"\n\nCompleted at: {datetime.now()}")
    
    # Summary
    print("\n" + "="*80)
    print("SUMMARY")
    print("="*80)
    
    if cpu_file and gpu_file:
        print("✅ Both versions ran successfully")
        print("📊 Check the comparison results above")
    else:
        if not cpu_file:
            print("❌ CPU version failed")
        if not gpu_file:
            print("❌ GPU version failed")

if __name__ == "__main__":
    main()