#!/usr/bin/env python3
"""
Test script to process a single Nifty Option Chain CSV file.
This script fixes strike classifications and verifies the output matches the expected format.

Usage:
  python test_fix_moneyness.py
"""

import pandas as pd
import numpy as np
import os
import sys
import time

# Constants
INPUT_FILE = "/srv/samba/shared/market_data/nifty/IV_2023_apr_nifty_cleaned.csv"
OUTPUT_FILE = "/srv/samba/shared/market_data/nifty/oc/toupload/test_IV_2023_apr_nifty_fixed.csv"
REFERENCE_FILE = "/srv/samba/shared/market_data/nifty/oc/nifty_option_chain_fixed_final_2023_apr_fixed_moneyness.csv"

def calculate_atm_strike(underlying_price):
    """
    Calculate ATM strike based on Nifty's rounding rules.
    Always uses 50-point increments.
    """
    return round(underlying_price / 50) * 50

def classify_strike(strike, atm_strike, underlying_price, option_type='call'):
    """
    Classify a strike as ATM, ITM or OTM with proper numeric ranking.
    For calls: ITM if strike < ATM, OTM if strike > ATM
    For puts: ITM if strike > ATM, OTM if strike < ATM
    
    Nifty always uses 50-point increments regardless of price level
    """
    if strike == atm_strike:
        return 'ATM'
    
    # Always use 50-point increment for Nifty
    step_size = 50
    
    # Calculate distance in steps (ceiling division)
    distance = abs(strike - atm_strike)
    steps = int(np.ceil(distance / step_size))
    
    if option_type == 'call':
        return f'ITM{steps}' if strike < atm_strike else f'OTM{steps}'
    else:  # put
        return f'ITM{steps}' if strike > atm_strike else f'OTM{steps}'

def fix_strike_classification(value):
    """Fix any classification with commas by keeping only the part before the comma."""
    if isinstance(value, str) and ',' in value:
        return value.split(',')[0]
    return value

def standardize_column_names(df):
    """
    Standardize column names to ensure compatibility.
    This handles various naming conventions found in different files.
    """
    # Create a mapping of potential column names to standard names
    column_map = {
        'underlying_price': 'spot',
        'spot_price': 'spot',
        'index_price': 'spot',
        'index_value': 'spot',
        'expiration_date': 'expiry_date',
        'expiry': 'expiry_date',
        'datetime': 'trade_date',
        'date': 'trade_date'
    }
    
    # Apply the mapping to standardize column names
    for old_name, new_name in column_map.items():
        if old_name in df.columns and new_name not in df.columns:
            df = df.rename(columns={old_name: new_name})
    
    return df

def process_file(input_file, output_file):
    """
    Process a single file to fix strike classifications.
    
    Args:
        input_file: Path to the input CSV file
        output_file: Path to the output CSV file
    
    Returns:
        True if processed successfully, False otherwise
    """
    print(f"Processing {input_file}...")
    start_time = time.time()
    
    # Check if files are the same
    if os.path.abspath(input_file) == os.path.abspath(output_file):
        print(f"Error: Input and output files cannot be the same: {input_file}")
        return False
    
    # Read the CSV file
    print(f"Reading file {input_file}...")
    try:
        df = pd.read_csv(input_file)
        original_rows = len(df)
        print(f"Loaded {original_rows} rows")
        
        # Skip empty files
        if original_rows == 0:
            print("Skipping empty file.")
            return False
        
        # Standardize column names
        df = standardize_column_names(df)
        
        # Check for required columns
        required_cols = ['strike']
        price_cols = ['spot', 'underlying_price']
        
        # Verify strike column exists
        if 'strike' not in df.columns:
            print(f"Error: Missing required 'strike' column.")
            return False
            
        # Verify at least one price column exists
        if not any(col in df.columns for col in price_cols):
            print(f"Error: File must contain one of {price_cols} columns.")
            return False
        
        # Get the price column name
        price_col = 'spot' if 'spot' in df.columns else 'underlying_price'
        
        # Ensure numeric types for classification
        for col in ['strike', 'spot', 'underlying_price', 'atm_strike']:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Add atm_strike column if it doesn't exist
        if 'atm_strike' not in df.columns:
            print("Adding missing atm_strike column...")
            df['atm_strike'] = df[price_col].apply(calculate_atm_strike)
            print(f"Added atm_strike column based on {price_col}")
        
        # Fix any existing comma-separated classifications
        for col in ['call_strike_type', 'put_strike_type']:
            if col in df.columns:
                df[col] = df[col].apply(fix_strike_classification)
        
        # Add call_strike_type and put_strike_type columns if they don't exist
        print("Calculating strike classifications...")
        df['call_strike_type'] = df.apply(
            lambda row: classify_strike(row['strike'], row['atm_strike'], row[price_col], 'call'),
            axis=1
        )
        df['put_strike_type'] = df.apply(
            lambda row: classify_strike(row['strike'], row['atm_strike'], row[price_col], 'put'),
            axis=1
        )
        
        # Save the fixed data
        print(f"Saving processed data to {output_file}...")
        df.to_csv(output_file, index=False)
        
        # Print verification
        print(f"File processed in {time.time() - start_time:.2f} seconds")
        print(f"Strike classification fixed for {len(df)} rows")
        
        # Verify no comma-separated values remain
        comma_count = sum(df['call_strike_type'].str.contains(',', na=False)) + \
                    sum(df['put_strike_type'].str.contains(',', na=False))
        if comma_count > 0:
            print(f"Warning: {comma_count} rows still have comma-separated values")
        else:
            print("Verification: No comma-separated values found in output")
        
        # Display a sample of the output file to verify classifications
        sample_size = min(5, len(df))
        print(f"\nSample of processed data ({sample_size} rows):")
        sample_df = df.head(sample_size)
        for _, row in sample_df.iterrows():
            strike = row['strike']
            atm_strike = row['atm_strike']
            cs_type = row['call_strike_type']
            ps_type = row['put_strike_type']
            print(f"Strike: {strike}, ATM Strike: {atm_strike}, Call Type: {cs_type}, Put Type: {ps_type}")
        
        return True
    
    except Exception as e:
        print(f"Error processing file: {str(e)}")
        return False

def main():
    # Make sure the output directory exists
    output_dir = os.path.dirname(OUTPUT_FILE)
    os.makedirs(output_dir, exist_ok=True)
    
    # Process the test file
    success = process_file(INPUT_FILE, OUTPUT_FILE)
    
    if success:
        print("\nFile processed successfully.")
        print(f"Output file: {OUTPUT_FILE}")
        
        # Recommend next step
        print("\nTo process all files, you can use process_all_nifty_files_enhanced.py")
    else:
        print("\nFile processing failed.")

if __name__ == "__main__":
    main() 