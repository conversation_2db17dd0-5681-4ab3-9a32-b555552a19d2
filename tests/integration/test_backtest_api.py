#!/usr/bin/env python3
"""
Test script to demonstrate how to run backtests using the V2 API
"""

import requests
import json
import time
import os
from datetime import datetime

# API base URL
API_BASE_URL = "http://**************:8000/api/v2"

# File paths - using existing test files
PORTFOLIO_FILE = "/srv/samba/shared/input_portfolio.xlsx"
TBS_STRATEGY_FILE = "/srv/samba/shared/input_tbs_multi_legs.xlsx"
TV_STRATEGY_FILE = "/srv/samba/shared/INPUT TBS MULTI LEGS.xlsx"  # This is actually a TV file based on project structure


def test_api_status():
    """Test if API is running"""
    print("Testing API status...")
    response = requests.get(f"{API_BASE_URL}/status")
    if response.status_code == 200:
        print(f"✅ API is operational")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return True
    else:
        print(f"❌ API error: {response.status_code}")
        return False


def run_tbs_backtest():
    """Run a TBS backtest"""
    print("\n" + "="*50)
    print("Running TBS Backtest...")
    
    # Check if files exist
    if not os.path.exists(PORTFOLIO_FILE):
        print(f"❌ Portfolio file not found: {PORTFOLIO_FILE}")
        return None
    if not os.path.exists(TBS_STRATEGY_FILE):
        print(f"❌ Strategy file not found: {TBS_STRATEGY_FILE}")
        return None
    
    # Prepare files
    files = {
        'portfolio': ('input_portfolio.xlsx', open(PORTFOLIO_FILE, 'rb'), 
                     'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'),
        'strategy': ('input_tbs_multi_legs.xlsx', open(TBS_STRATEGY_FILE, 'rb'),
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    }
    
    # Form data
    data = {
        'start_date': '2024-04-01',
        'end_date': '2024-04-10',
        'index_name': 'NIFTY'
    }
    
    try:
        # Submit backtest
        response = requests.post(
            f"{API_BASE_URL}/strategies/tbs/backtest",
            files=files,
            data=data
        )
        
        # Close files
        for _, (_, file_obj, _) in files.items():
            file_obj.close()
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Backtest submitted successfully!")
            print(f"Backtest ID: {result['backtest_id']}")
            print(f"Status: {result['status']}")
            return result['backtest_id']
        else:
            print(f"❌ Error submitting backtest: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return None


def run_tv_backtest():
    """Run a TV backtest"""
    print("\n" + "="*50)
    print("Running TV Backtest...")
    
    # For TV, we only need the strategy file
    if not os.path.exists(TV_STRATEGY_FILE):
        print(f"❌ TV strategy file not found: {TV_STRATEGY_FILE}")
        return None
    
    files = {
        'portfolio': ('INPUT TBS MULTI LEGS.xlsx', open(TV_STRATEGY_FILE, 'rb'),
                     'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'),
        'settings': ('INPUT TBS MULTI LEGS.xlsx', open(TV_STRATEGY_FILE, 'rb'),
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    }
    
    data = {
        'start_date': '2024-04-01',
        'end_date': '2024-04-10'
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/strategies/tv/backtest",
            files=files,
            data=data
        )
        
        # Close files
        for _, (_, file_obj, _) in files.items():
            file_obj.close()
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ TV Backtest submitted!")
            print(f"Backtest ID: {result['backtest_id']}")
            return result['backtest_id']
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return None


def check_backtest_status(backtest_id):
    """Check status of a backtest"""
    print(f"\nChecking status for backtest: {backtest_id}")
    
    try:
        response = requests.get(f"{API_BASE_URL}/backtest/status/{backtest_id}")
        
        if response.status_code == 200:
            status = response.json()
            print(f"Status: {status['status']}")
            print(f"Progress: {status.get('progress', 0)}%")
            if 'current_step' in status:
                print(f"Current step: {status['current_step']}")
            return status
        else:
            print(f"❌ Error getting status: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return None


def get_backtest_results(backtest_id):
    """Get results of a completed backtest"""
    print(f"\nGetting results for backtest: {backtest_id}")
    
    try:
        response = requests.get(f"{API_BASE_URL}/backtest/result/{backtest_id}")
        
        if response.status_code == 200:
            results = response.json()
            print(f"✅ Results retrieved!")
            print(json.dumps(results, indent=2))
            return results
        else:
            print(f"❌ Error getting results: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return None


def monitor_backtest(backtest_id, max_wait=300):
    """Monitor backtest until completion"""
    print(f"\nMonitoring backtest: {backtest_id}")
    
    start_time = time.time()
    while time.time() - start_time < max_wait:
        status = check_backtest_status(backtest_id)
        
        if not status:
            break
            
        if status['status'] == 'completed':
            print("✅ Backtest completed!")
            return get_backtest_results(backtest_id)
        elif status['status'] == 'failed':
            print("❌ Backtest failed!")
            print(f"Error: {status.get('error', 'Unknown error')}")
            break
            
        # Wait before checking again
        time.sleep(5)
    
    print("⏱️ Timeout reached")
    return None


def main():
    """Main test function"""
    print("GPU Backtester V2 API Test")
    print("="*50)
    
    # Test API status
    if not test_api_status():
        return
    
    # Run TBS backtest
    tbs_backtest_id = run_tbs_backtest()
    if tbs_backtest_id:
        # Monitor until completion
        results = monitor_backtest(tbs_backtest_id)
        
        # Try to download results
        if results and results.get('status') == 'completed':
            print(f"\nDownload URL: {API_BASE_URL}/backtest/download/{tbs_backtest_id}")
    
    # Run TV backtest
    tv_backtest_id = run_tv_backtest()
    if tv_backtest_id:
        # Just check status once
        time.sleep(2)
        check_backtest_status(tv_backtest_id)


if __name__ == "__main__":
    main()