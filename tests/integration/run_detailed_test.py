#!/usr/bin/env python3
"""
Run a detailed test with full debugging to understand why stop losses are triggering immediately
"""

import os
import sys
import subprocess
import logging
import pandas as pd
import time
import json
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.DEBUG, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   filename=f'detailed_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log',
                   filemode='w')

console = logging.StreamHandler()
console.setLevel(logging.INFO)
formatter = logging.Formatter('%(levelname)s: %(message)s')
console.setFormatter(formatter)
logging.getLogger('').addHandler(console)

logger = logging.getLogger(__name__)

# Fixed paths
BTRUN_PATH = '/srv/samba/shared/bt/backtester_stable/BTRUN'
PORTFOLIO_EXCEL = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_fixed.xlsx'
OUTPUT_DIR = '/srv/samba/shared/Trades'
OUTPUT_FILE = os.path.join(OUTPUT_DIR, f'detailed_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx')

def fix_risk_module():
    """Apply manual fixes to risk.py to ensure stop losses don't trigger immediately"""
    risk_py_path = os.path.join(BTRUN_PATH, 'models/risk.py')
    logger.info(f"Ensuring risk.py has proper fixes: {risk_py_path}")
    
    # Read the current content
    with open(risk_py_path, 'r') as f:
        content = f.read()
    
    # Make a backup
    backup_path = risk_py_path + '.bak'
    if not os.path.exists(backup_path):
        with open(backup_path, 'w') as f:
            f.write(content)
        logger.info(f"Created backup of risk.py at {backup_path}")
    
    # Create modified content with more debug and improved fixes
    if "def evaluate_risk_rule(" in content:
        # Add extra debug and ensure the fixes are working
        new_content = content.replace(
            "def evaluate_risk_rule(tick_df: pd.DataFrame, risk_rule: RiskRule, entry_price: float, is_long: bool) -> Tuple[Optional[float], Optional[datetime], bool, Optional[str]]:",
            """def evaluate_risk_rule(tick_df: pd.DataFrame, risk_rule: RiskRule, entry_price: float, is_long: bool) -> Tuple[Optional[float], Optional[datetime], bool, Optional[str]]:
    """
        )
        
        # Force skip first candle regardless of SL/TP value
        new_content = new_content.replace(
            "# FIX: Skip the first entry candle to prevent immediate SL/TP trigger",
            """# ENHANCED FIX: ALWAYS skip the first entry candle to prevent immediate SL/TP trigger
    # This is critical for preventing trades from exiting at entry time (9:16)"""
        )
        
        # Make the fix for tight SL values more aggressive
        new_content = new_content.replace(
            "if not is_long and sl_value < 200:",
            "if not is_long and sl_value < 500:"
        )
        
        new_content = new_content.replace(
            "sl_value = max(sl_value, 200)",
            "sl_value = max(sl_value, 500)"
        )
        
        new_content = new_content.replace(
            "if is_long and sl_value < 20:",
            "if is_long and sl_value < 50:"
        )
        
        new_content = new_content.replace(
            "sl_value = max(sl_value, 20)",
            "sl_value = max(sl_value, 50)"
        )
        
        # Force skip first candle code (replace the conditional)
        new_content = new_content.replace(
            "    # Only skip first entry if the SL/TP values are too tight\n        too_tight_sl_tp = False\n        if risk_rule.rule_type == RiskRuleType.STOPLOSS:\n            # For SL, check if value is small enough to trigger immediately\n            if risk_rule.number_type == NumberType.PERCENTAGE and risk_rule.value < 200:  # Less than 200% for SELL legs\n                too_tight_sl_tp = True\n        elif risk_rule.rule_type == RiskRuleType.TAKEPROFIT:\n            # For TP, check if value is small enough to trigger immediately\n            if risk_rule.number_type == NumberType.PERCENTAGE and risk_rule.value < 20:  # Less than 20%\n                too_tight_sl_tp = True\n            \n        # If SL/TP values are too tight, skip the first entry candle\n        if too_tight_sl_tp:",
            """        # ALWAYS skip the first entry candle to prevent immediate triggers
        # This is critical for preventing trades from exiting at entry time (9:16)"""
        )
        
        # Write the modified content
        with open(risk_py_path, 'w') as f:
            f.write(new_content)
        
        logger.info("Applied enhanced fixes to risk.py")
    else:
        logger.error("Could not find evaluate_risk_rule function in risk.py")

def fix_trade_builder():
    """Apply manual fixes to trade_builder.py to ensure correct exit time override"""
    trade_builder_path = os.path.join(BTRUN_PATH, 'trade_builder.py')
    logger.info(f"Ensuring trade_builder.py has proper fixes: {trade_builder_path}")
    
    # Read the current content
    with open(trade_builder_path, 'r') as f:
        content = f.read()
    
    # Make a backup
    backup_path = trade_builder_path + '.bak'
    if not os.path.exists(backup_path):
        with open(backup_path, 'w') as f:
            f.write(content)
        logger.info(f"Created backup of trade_builder.py at {backup_path}")
    
    # Create modified content with more debug and improved fixes
    if "def build_trade_record(" in content:
        # Enhance the exit time override logic
        new_content = content.replace(
            "# If exit is within 5 minutes of entry, override with scheduled exit time",
            """# ENHANCED FIX: ALWAYS override with scheduled exit time if reason is Stop Loss or Target Hit
            # This ensures we use the strategy's configured exit time for all trades"""
        )
        
        new_content = new_content.replace(
            "if (exit_datetime - entry_datetime).total_seconds() < 300:  # 5 minutes = 300 seconds",
            "if True:  # Always override early exits with scheduled exit time"
        )
        
        # Write the modified content
        with open(trade_builder_path, 'w') as f:
            f.write(new_content)
        
        logger.info("Applied enhanced fixes to trade_builder.py")
    else:
        logger.error("Could not find build_trade_record function in trade_builder.py")

def run_detailed_test():
    """Run a detailed test with increased logging to see what's happening"""
    # First, apply our enhanced fixes
    fix_risk_module()
    fix_trade_builder()
    
    # Set additional environment variables for more verbose debug output
    env = os.environ.copy()
    env['BTRUN_DEBUG_LEVEL'] = 'DEBUG'
    env['PYTHONPATH'] = '/srv/samba/shared'
    
    # Create command to run
    cmd = [
        'python3', 
        os.path.join(BTRUN_PATH, 'BTRunPortfolio_GPU.py'),
        '--portfolio-excel', PORTFOLIO_EXCEL,
        '--output-path', OUTPUT_FILE,
        '--debug'
    ]
    
    logger.info(f"Running detailed test with command: {' '.join(cmd)}")
    
    # Run the backtester with enhanced logging
    process = subprocess.Popen(
        cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        universal_newlines=True,
        env=env
    )
    
    # Capture output in real-time
    stdout_lines = []
    for line in process.stdout:
        logger.info(f"BTRUN: {line.strip()}")
        stdout_lines.append(line)
        print(line, end='')
    
    process.wait()
    
    # Get stderr output
    stderr = process.stderr.read()
    if stderr:
        logger.error(f"STDERR: {stderr}")
    
    logger.info(f"Process exited with return code {process.returncode}")
    
    return process.returncode

def analyze_results():
    """Analyze the detailed test results"""
    if not os.path.exists(OUTPUT_FILE):
        logger.error(f"Output file not found: {OUTPUT_FILE}")
        return False
    
    # Read the transaction sheet
    try:
        df = pd.read_excel(OUTPUT_FILE, sheet_name='PORTFOLIO Trans')
        logger.info(f"Found {len(df)} trades in output file")
        
        # Check exit times
        exit_times = df['exit_time'].value_counts()
        logger.info(f"Exit time distribution: {exit_times.to_dict()}")
        
        # Check exit reasons
        exit_reasons = df['reason'].value_counts()
        logger.info(f"Exit reason distribution: {exit_reasons.to_dict()}")
        
        # Check for 12:00:00 exits
        correct_exit_count = sum(df['exit_time'].astype(str).str.startswith('12:00'))
        
        if correct_exit_count == len(df):
            logger.info("✅ SUCCESS: All trades exited at 12:00:00 as expected")
            return True
        else:
            logger.error(f"❌ FAILURE: Only {correct_exit_count} of {len(df)} trades exited at 12:00:00")
            
            # Print details of problematic trades
            problem_trades = df[~df['exit_time'].astype(str).str.startswith('12:00')]
            for idx, row in problem_trades.iterrows():
                logger.error(f"Problem trade {idx}: Exit time = {row['exit_time']}, Reason = {row['reason']}")
            
            return False
    
    except Exception as e:
        logger.error(f"Error analyzing results: {e}")
        return False

def main():
    logger.info("=== Starting Detailed Test ===")
    
    # Run the detailed test
    retcode = run_detailed_test()
    
    if retcode == 0:
        logger.info("Backtester ran successfully, analyzing results...")
        success = analyze_results()
        
        if success:
            logger.info("=== Test Completed Successfully ===")
        else:
            logger.error("=== Test Failed: Incorrect exit times ===")
    else:
        logger.error(f"=== Test Failed: Backtester returned error code {retcode} ===")
    
    logger.info(f"Detailed test output file: {OUTPUT_FILE}")
    logger.info("Check the detailed_test_*.log file for complete logs")

if __name__ == "__main__":
    main() 