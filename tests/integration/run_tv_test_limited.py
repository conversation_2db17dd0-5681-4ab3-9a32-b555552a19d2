#!/usr/bin/env python3
"""Run TV backtest with limited date range"""

import os
import sys
import subprocess

# Set up environment
os.environ["PYTHONPATH"] = "/srv/samba/shared/bt"
os.environ["INPUT_FILE_FOLDER"] = "bt/backtester_stable/BTRUN/input_sheets"
os.environ["TV_FILE_PATH"] = "input_tv.xlsx"

# Create output directory
output_dir = "bt/backtester_stable/BTRUN/output/tv_test_limited"
os.makedirs(output_dir, exist_ok=True)

print("Running TV backtest with limited date range...")
print("Date range: 2024-01-01 to 2024-01-05 (5 days)")
print("Output directory:", output_dir)
print("=" * 80)

# Run the TV backtest
cmd = [
    sys.executable,
    "-m", "backtester_stable.BTRUN.BT_TV_GPU",
    "--legacy-excel",
    "--output-dir", output_dir,
    "--start-date", "240101",  # Jan 1, 2024
    "--end-date", "240105",    # Jan 5, 2024 (just 5 days)
    "--cpu-only"  # Force CPU mode to avoid GPU issues
]

print("Command:", " ".join(cmd))
print("=" * 80)

# Change to project root
os.chdir("/srv/samba/shared")

# Run the command
result = subprocess.run(cmd, capture_output=True, text=True)

print("STDOUT:")
print(result.stdout)
print("\nSTDERR:")
print(result.stderr)
print("\nReturn code:", result.returncode)

# Check if any output files were created
print("\nChecking output directory:")
if os.path.exists(output_dir):
    files = os.listdir(output_dir)
    if files:
        print(f"Found {len(files)} output files:")
        for f in files[:10]:  # Show first 10 files
            print(f"  - {f}")
    else:
        print("No output files created")
else:
    print("Output directory not created") 