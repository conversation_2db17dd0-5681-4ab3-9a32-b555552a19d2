#!/usr/bin/env python3
"""
Test direct loading using HeavyDB's load_table method
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime

sys.path.append('/srv/samba/shared')

try:
    from bt.dal.heavydb_conn import get_conn
    import heavyai
    # Get connection
    conn = get_conn()
    cursor = conn.cursor()
    
    print("Connected to HeavyDB")
    
    # First check if table exists
    cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
    count = cursor.fetchone()[0]
    print(f"Current rows in table: {count}")
    
    # Read a small sample
    data_dir = "/srv/samba/shared/market_data/nifty/oc_with_futures"
    csv_file = os.path.join(data_dir, "IV_2025_apr_nifty_futures.csv")
    
    print(f"\nReading sample from {csv_file}...")
    df = pd.read_csv(csv_file, nrows=100)
    
    # Process dates and times
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.date
    df['trade_time'] = pd.to_datetime(df['trade_time'], format='%H:%M:%S').dt.time
    df['trade_ts'] = pd.to_datetime(df['trade_date'].astype(str) + ' ' + df['trade_time'].astype(str))
    df['expiry_date'] = pd.to_datetime(df['expiry_date']).dt.date
    
    # Convert numeric fields
    numeric_cols = [col for col in df.columns if col not in ['trade_date', 'trade_time', 'trade_ts', 
                                                              'expiry_date', 'index_name', 'expiry_bucket',
                                                              'zone_name', 'call_strike_type', 'put_strike_type',
                                                              'ce_symbol', 'pe_symbol']]
    
    for col in numeric_cols:
        df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
    
    # Try to use load_table_arrow method
    print("\nAttempting to load data using load_table_arrow...")
    try:
        # Import pyarrow
        import pyarrow as pa
        
        # Convert to arrow table
        arrow_table = pa.Table.from_pandas(df)
        
        # Load using arrow
        conn.load_table_arrow('nifty_option_chain', arrow_table)
        
        print("Successfully loaded data using PyArrow!")
        
        # Check new count
        cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
        new_count = cursor.fetchone()[0]
        print(f"New row count: {new_count}")
        
    except Exception as e:
        print(f"PyArrow load failed: {e}")
        
        # Try batch insert
        print("\nTrying batch insert method...")
        try:
            # Prepare data for insert
            columns = list(df.columns)
            values_list = []
            
            for _, row in df.iterrows():
                values = []
                for col in columns:
                    val = row[col]
                    if pd.isna(val):
                        values.append('NULL')
                    elif isinstance(val, (str, np.str_)):
                        values.append(f"'{val}'")
                    elif isinstance(val, (pd.Timestamp, datetime)):
                        values.append(f"'{val}'")
                    else:
                        values.append(str(val))
                values_list.append(f"({','.join(values)})")
            
            # Create insert statement
            insert_sql = f"""
            INSERT INTO nifty_option_chain ({','.join(columns)})
            VALUES {','.join(values_list[:10])}
            """
            
            print("Inserting first 10 rows...")
            cursor.execute(insert_sql)
            
            print("Batch insert successful!")
            
            # Check count
            cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
            final_count = cursor.fetchone()[0]
            print(f"Final row count: {final_count}")
            
        except Exception as insert_error:
            print(f"Batch insert failed: {insert_error}")
    
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()