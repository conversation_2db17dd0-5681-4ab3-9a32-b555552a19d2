#!/usr/bin/env python3
"""
Create ML Indicator Test Sheets for TV Backtesting
This script generates the required Excel files for backtesting ML indicator strategies
"""

import pandas as pd
import os
from datetime import datetime, timedelta
import numpy as np

def create_tv_settings_file(output_path, strategies):
    """Create the main TV settings Excel file"""
    
    settings_data = []
    
    for strategy in strategies:
        settings_data.append({
            'StartDate': strategy.get('start_date', '01_01_2024'),
            'EndDate': strategy.get('end_date', '31_12_2024'),
            'SignalDateFormat': '%Y-%m-%d %H:%M:%S',
            'Enabled': 'yes',
            'TvExitApplicable': 'yes',
            'ManualTradeEntryTime': 0,
            'ManualTradeLots': 1,
            'IncreaseEntrySignalTimeBy': 0,
            'IncreaseExitSignalTimeBy': 0,
            'IntradaySqOffApplicable': 'yes',
            'FirstTradeEntryTime': 91500,  # 9:15 AM
            'IntradayExitTime': 151500,    # 3:15 PM
            'ExpiryDayExitTime': 151500,   # 3:15 PM
            'DoRollover': 'no',
            'RolloverTime': 151500,
            'Name': strategy['name'],
            'SignalFilePath': strategy['signal_file'],
            'LongPortfolioFilePath': strategy.get('long_portfolio', ''),
            'ShortPortfolioFilePath': strategy.get('short_portfolio', ''),
            'ManualPortfolioFilePath': ''
        })
    
    df = pd.DataFrame(settings_data)
    
    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='Setting', index=False)
    
    print(f"Created TV settings file: {output_path}")

def create_signal_file(output_path, signals):
    """Create signal file with ML indicator signals"""
    
    signal_data = []
    
    for i, signal in enumerate(signals, 1):
        signal_data.append({
            'Trade #': i,
            'Type': signal['type'],
            'Date/Time': signal['datetime'],
            'Contracts': signal.get('contracts', 1)
        })
    
    df = pd.DataFrame(signal_data)
    
    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='List of trades', index=False)
    
    print(f"Created signal file: {output_path}")

def create_portfolio_file(output_path, portfolio_name, strategy_type='TBS', strategy_file=''):
    """Create portfolio file"""
    
    # Portfolio settings
    portfolio_settings = pd.DataFrame([{
        'StartDate': '01_01_2024',
        'EndDate': '31_12_2024',
        'IsTickBT': 'Yes',
        'Enabled': 'Yes',
        'PortfolioName': portfolio_name,
        'PortfolioTarget': 0,
        'PortfolioStoploss': 0,
        'PortfolioTrailingType': 'Lock Minimum Profit',
        'ProfitReaches': 0,
        'LockMinProfitAt': 0,
        'IncreaseInProfit': 0,
        'TrailMinProfitBy': 0,
        'Multiplier': 1,
        'SlippagePercent': 0
    }])
    
    # Strategy settings
    strategy_settings = pd.DataFrame([{
        'Enabled': 'YES',
        'PortfolioName': portfolio_name,
        'StrategyType': strategy_type,
        'StrategyExcelFilePath': strategy_file
    }])
    
    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        portfolio_settings.to_excel(writer, sheet_name='PortfolioSetting', index=False)
        strategy_settings.to_excel(writer, sheet_name='StrategySetting', index=False)
    
    print(f"Created portfolio file: {output_path}")

def generate_sample_ml_signals(start_date, end_date, signal_frequency='daily'):
    """Generate sample ML indicator signals"""
    
    signals = []
    current_date = datetime.strptime(start_date, '%d_%m_%Y')
    end = datetime.strptime(end_date, '%d_%m_%Y')
    
    trade_num = 1
    position_open = False
    
    while current_date <= end:
        # Skip weekends
        if current_date.weekday() >= 5:
            current_date += timedelta(days=1)
            continue
        
        # Generate ML indicator signal (simplified example)
        ml_score = np.random.random()
        
        if not position_open and ml_score > 0.7:
            # Entry signal
            entry_time = current_date.replace(hour=9, minute=30, second=0)
            signals.append({
                'type': 'Entry Long' if np.random.random() > 0.5 else 'Entry Short',
                'datetime': entry_time.strftime('%Y-%m-%d %H:%M:%S'),
                'contracts': 1
            })
            position_open = True
            
        elif position_open and (ml_score < 0.3 or current_date.hour >= 15):
            # Exit signal
            exit_time = current_date.replace(hour=15, minute=0, second=0)
            if ml_score < 0.3:
                exit_time = current_date.replace(hour=int(9 + ml_score * 6), minute=30, second=0)
            
            signals.append({
                'type': 'Exit Long' if signals[-1]['type'] == 'Entry Long' else 'Exit Short',
                'datetime': exit_time.strftime('%Y-%m-%d %H:%M:%S'),
                'contracts': 1
            })
            position_open = False
        
        current_date += timedelta(days=1)
    
    return signals

def create_ml_indicator_test_suite(base_path='ml_indicator_tests'):
    """Create a complete test suite for ML indicators"""
    
    os.makedirs(base_path, exist_ok=True)
    
    # Define ML strategies
    strategies = []
    
    # Strategy 1: Simple ML Indicator
    signal_file_1 = os.path.join(base_path, 'ml_signals_simple.xlsx')
    signals_1 = generate_sample_ml_signals('01_01_2024', '31_03_2024')
    create_signal_file(signal_file_1, signals_1)
    
    long_portfolio_1 = os.path.join(base_path, 'ml_portfolio_long_simple.xlsx')
    create_portfolio_file(long_portfolio_1, 'ML_SIMPLE_LONG', 'TBS', 'path/to/tbs_strategy.xlsx')
    
    strategies.append({
        'name': 'ML_Indicator_Simple',
        'start_date': '01_01_2024',
        'end_date': '31_03_2024',
        'signal_file': signal_file_1,
        'long_portfolio': long_portfolio_1,
        'short_portfolio': ''
    })
    
    # Strategy 2: Advanced ML Indicator with both long and short
    signal_file_2 = os.path.join(base_path, 'ml_signals_advanced.xlsx')
    signals_2 = generate_sample_ml_signals('01_01_2024', '30_06_2024', 'intraday')
    create_signal_file(signal_file_2, signals_2)
    
    long_portfolio_2 = os.path.join(base_path, 'ml_portfolio_long_advanced.xlsx')
    short_portfolio_2 = os.path.join(base_path, 'ml_portfolio_short_advanced.xlsx')
    create_portfolio_file(long_portfolio_2, 'ML_ADVANCED_LONG', 'TBS', 'path/to/tbs_long.xlsx')
    create_portfolio_file(short_portfolio_2, 'ML_ADVANCED_SHORT', 'TBS', 'path/to/tbs_short.xlsx')
    
    strategies.append({
        'name': 'ML_Indicator_Advanced',
        'start_date': '01_01_2024',
        'end_date': '30_06_2024',
        'signal_file': signal_file_2,
        'long_portfolio': long_portfolio_2,
        'short_portfolio': short_portfolio_2
    })
    
    # Create main TV settings file
    tv_settings_file = os.path.join(base_path, 'INPUT_TV_ML_INDICATORS.xlsx')
    create_tv_settings_file(tv_settings_file, strategies)
    
    print(f"\nML Indicator test suite created in: {base_path}")
    print("\nFiles created:")
    print(f"- Main TV settings: {tv_settings_file}")
    for strategy in strategies:
        print(f"\n- Strategy: {strategy['name']}")
        print(f"  - Signal file: {strategy['signal_file']}")
        print(f"  - Long portfolio: {strategy['long_portfolio']}")
        if strategy['short_portfolio']:
            print(f"  - Short portfolio: {strategy['short_portfolio']}")

if __name__ == "__main__":
    # Create sample ML indicator test sheets
    create_ml_indicator_test_suite()
    
    print("\n" + "="*60)
    print("ML Indicator test sheets created successfully!")
    print("="*60)
    print("\nTo use these files:")
    print("1. Update the file paths in the TV settings to match your system")
    print("2. Replace the sample ML signals with your actual ML indicator outputs")
    print("3. Update portfolio files to reference your actual strategy files")
    print("4. Run the backtest using the TV backtester")