from flask import Flask, request, jsonify, send_file
import os
import sys
import subprocess
import uuid
import json
from datetime import datetime
import threading
import queue

# Add backtester to path
sys.path.append(r"C:\path\to\backtester_stable\BTRUN")

app = Flask(__name__)

# Job queue for async processing
job_queue = {}
result_queue = {}

class BacktestJob:
    def __init__(self, job_id, params):
        self.job_id = job_id
        self.params = params
        self.status = "pending"
        self.result = None
        self.error = None
        self.started_at = None
        self.completed_at = None

def run_backtest_async(job):
    """Run backtest in background thread"""
    try:
        job.status = "running"
        job.started_at = datetime.now()
        
        # Prepare command
        cmd = [
            sys.executable,
            "BTRunPortfolio.py",
            "--portfolio-excel", job.params['portfolio_excel'],
            "--start-date", job.params['start_date'],
            "--end-date", job.params['end_date']
        ]
        
        if 'output_path' in job.params:
            cmd.extend(["--output-path", job.params['output_path']])
        
        # Run backtest
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            job.status = "completed"
            job.result = {
                "output": result.stdout,
                "output_file": job.params.get('output_path')
            }
        else:
            job.status = "failed"
            job.error = result.stderr
            
        job.completed_at = datetime.now()
        
    except Exception as e:
        job.status = "failed"
        job.error = str(e)
        job.completed_at = datetime.now()

@app.route('/api/v1/backtest', methods=['POST'])
def submit_backtest():
    """Submit a new backtest job"""
    params = request.json
    
    # Validate required parameters
    required = ['portfolio_excel', 'start_date', 'end_date']
    for param in required:
        if param not in params:
            return jsonify({"error": f"Missing required parameter: {param}"}), 400
    
    # Generate job ID
    job_id = str(uuid.uuid4())
    
    # Set default output path if not provided
    if 'output_path' not in params:
        params['output_path'] = f"output/backtest_{job_id}.xlsx"
    
    # Create job
    job = BacktestJob(job_id, params)
    job_queue[job_id] = job
    
    # Start async processing
    thread = threading.Thread(target=run_backtest_async, args=(job,))
    thread.start()
    
    return jsonify({
        "job_id": job_id,
        "status": "submitted",
        "message": "Backtest job submitted successfully"
    }), 202

@app.route('/api/v1/backtest/<job_id>', methods=['GET'])
def get_job_status(job_id):
    """Get status of a backtest job"""
    if job_id not in job_queue:
        return jsonify({"error": "Job not found"}), 404
    
    job = job_queue[job_id]
    response = {
        "job_id": job_id,
        "status": job.status,
        "started_at": job.started_at.isoformat() if job.started_at else None,
        "completed_at": job.completed_at.isoformat() if job.completed_at else None
    }
    
    if job.status == "completed":
        response["result"] = job.result
    elif job.status == "failed":
        response["error"] = job.error
    
    return jsonify(response)

@app.route('/api/v1/backtest/<job_id>/download', methods=['GET'])
def download_results(job_id):
    """Download backtest results"""
    if job_id not in job_queue:
        return jsonify({"error": "Job not found"}), 404
    
    job = job_queue[job_id]
    if job.status != "completed":
        return jsonify({"error": "Job not completed"}), 400
    
    output_file = job.result.get('output_file')
    if output_file and os.path.exists(output_file):
        return send_file(output_file, as_attachment=True)
    else:
        return jsonify({"error": "Output file not found"}), 404

@app.route('/api/v1/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "service": "legacy_backtester",
        "timestamp": datetime.now().isoformat()
    })

if __name__ == '__main__':
    # Create output directory
    os.makedirs('output', exist_ok=True)
    
    # Run on all interfaces for external access
    app.run(host='0.0.0.0', port=5000, debug=False) 