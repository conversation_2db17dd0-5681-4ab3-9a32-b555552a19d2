#!/usr/bin/env python3
"""
Test script to verify API integration is working
Tests with real HeavyDB data using 1-day filter
"""
import requests
import json
import time
import asyncio
from datetime import datetime

BASE_URL = "http://localhost:8002"

def test_health_check():
    """Test health check endpoint"""
    print("\n1. Testing Health Check...")
    response = requests.get(f"{BASE_URL}/api/v2/health")
    
    if response.status_code == 200:
        data = response.json()
        print(f"✓ Health Status: {data['status']}")
        print(f"  - Version: {data['version']}")
        print(f"  - Components:")
        for component, status in data['components'].items():
            print(f"    - {component}: {status['status']}")
    else:
        print(f"✗ Health check failed: {response.status_code}")

def test_validation():
    """Test input validation"""
    print("\n2. Testing Input Validation...")
    
    # Test TBS validation
    validation_request = {
        "strategy_type": "tbs",
        "files": {
            "portfolio": "/test_data/tbs_test_portfolio.xlsx",
            "strategy": "/test_data/tbs_test_strategy.xlsx"
        },
        "validate_data": True
    }
    
    response = requests.post(
        f"{BASE_URL}/api/v2/backtest/validate",
        json=validation_request
    )
    
    if response.status_code == 200:
        data = response.json()
        print(f"✓ Validation: {'Passed' if data['is_valid'] else 'Failed'}")
        
        if data.get('data_preview'):
            print(f"  - Test Date: {data['data_preview']['test_date']}")
            print(f"  - Row Count: {data['data_preview']['row_count']:,}")
            print(f"  - Unique Strikes: {data['data_preview']['unique_strikes']}")
            print(f"  - Spot Range: {data['data_preview']['spot_range']}")
        
        if data.get('suggestions'):
            print("  - Suggestions:")
            for suggestion in data['suggestions']:
                print(f"    • {suggestion}")
    else:
        print(f"✗ Validation failed: {response.status_code}")

def test_submit_backtest():
    """Test submitting a backtest"""
    print("\n3. Testing Backtest Submission...")
    
    # Submit TBS backtest
    backtest_request = {
        "strategy_type": "tbs",
        "test_mode": "test",  # Use 1-day test data
        "files": {
            "portfolio": "/test_data/tbs_test_portfolio.xlsx",
            "strategy": "/test_data/tbs_test_strategy.xlsx"
        }
    }
    
    response = requests.post(
        f"{BASE_URL}/api/v2/backtest/submit",
        json=backtest_request
    )
    
    if response.status_code == 200:
        data = response.json()
        job_id = data['job_id']
        print(f"✓ Backtest Submitted")
        print(f"  - Job ID: {job_id}")
        print(f"  - Status: {data['status']}")
        print(f"  - Message: {data['message']}")
        print(f"  - WebSocket: {data['websocket_url']}")
        
        # Monitor job progress
        print("\n4. Monitoring Job Progress...")
        return monitor_job(job_id)
    else:
        print(f"✗ Submission failed: {response.status_code}")
        print(f"  Error: {response.text}")
        return None

def monitor_job(job_id, timeout=30):
    """Monitor job progress"""
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        response = requests.get(f"{BASE_URL}/api/v2/backtest/status/{job_id}")
        
        if response.status_code == 200:
            data = response.json()
            status = data['status']
            progress = data.get('progress', 0)
            message = data.get('message', '')
            
            print(f"\r  Progress: {progress:.1f}% - {status} - {message}", end='', flush=True)
            
            if status == "completed":
                print("\n✓ Job Completed!")
                return job_id
            elif status == "failed":
                print(f"\n✗ Job Failed: {message}")
                return None
        
        time.sleep(0.5)
    
    print("\n⚠ Timeout waiting for job completion")
    return None

def test_get_results(job_id):
    """Test getting job results"""
    print("\n5. Getting Job Results...")
    
    response = requests.get(f"{BASE_URL}/api/v2/backtest/results/{job_id}")
    
    if response.status_code == 200:
        data = response.json()
        print("✓ Results Retrieved")
        print(f"  - Strategy Type: {data['strategy_type']}")
        print(f"  - Test Mode: {data['test_mode']}")
        print(f"  - Execution Time: {data['execution_time_seconds']}s")
        
        if 'summary' in data:
            print("  - Summary:")
            for key, value in data['summary'].items():
                print(f"    • {key}: {value}")
        
        if 'metadata' in data:
            print("  - Metadata:")
            print(f"    • Rows Processed: {data['metadata']['rows_processed']:,}")
            print(f"    • Trades Generated: {data['metadata']['trades_generated']}")
    else:
        print(f"✗ Failed to get results: {response.status_code}")

def test_all_strategies():
    """Test all strategy types"""
    print("\n6. Testing All Strategy Types...")
    
    strategies = ["tbs", "tv", "orb", "oi"]
    results = {}
    
    for strategy in strategies:
        print(f"\n  Testing {strategy.upper()}...")
        
        # Prepare request based on strategy
        if strategy == "tbs":
            files = {
                "portfolio": "/test_data/tbs_test_portfolio.xlsx",
                "strategy": "/test_data/tbs_test_strategy.xlsx"
            }
        elif strategy == "tv":
            files = {
                "settings": "/test_data/tv_test_settings.xlsx",
                "signals": "/test_data/tv_test_signals.csv"
            }
        else:  # orb, oi
            files = {
                "strategy": f"/test_data/{strategy}_test_strategy.xlsx"
            }
        
        request = {
            "strategy_type": strategy,
            "test_mode": "test",
            "files": files
        }
        
        response = requests.post(f"{BASE_URL}/api/v2/backtest/submit", json=request)
        
        if response.status_code == 200:
            job_id = response.json()['job_id']
            results[strategy] = "Submitted"
        else:
            results[strategy] = f"Failed: {response.status_code}"
    
    print("\n  Results Summary:")
    for strategy, result in results.items():
        print(f"    - {strategy.upper()}: {result}")

def main():
    """Run all tests"""
    print("=" * 60)
    print("ENTERPRISE GPU BACKTESTER v2 - API INTEGRATION TEST")
    print("=" * 60)
    print(f"Server: {BASE_URL}")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code != 200:
            print("\n❌ Server is not responding. Please start the server first:")
            print("   python main_v2.py")
            return
    except requests.exceptions.ConnectionError:
        print("\n❌ Cannot connect to server. Please start the server first:")
        print("   python main_v2.py")
        return
    
    # Run tests
    test_health_check()
    test_validation()
    
    # Submit and monitor a job
    job_id = test_submit_backtest()
    if job_id:
        test_get_results(job_id)
    
    # Test all strategies
    test_all_strategies()
    
    print("\n" + "=" * 60)
    print("TEST COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    main()