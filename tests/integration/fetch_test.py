#!/usr/bin/env python3
import os
import sys
import subprocess
import tempfile

def fetch_test():
    """Test fetching from HeavyDB."""
    # Create a temporary SQL file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.sql', delete=False) as f:
        sql_file = f.name
        f.write("""
SELECT 
    trade_date, 
    trade_time, 
    expiry_date, 
    strike
FROM nifty_greeks
LIMIT 5;
""")
    
    # Execute the SQL and capture the output
    command = [
        "/opt/heavyai/bin/heavysql", 
        "-s", "127.0.0.1", 
        "--port", "6274", 
        "-u", "admin", 
        "-p", "HyperInteractive", 
        "-d", "heavyai", 
        "-q", sql_file
    ]
    
    try:
        result = subprocess.run(command, capture_output=True, text=True)
        
        # Clean up
        os.unlink(sql_file)
        
        # Print the output
        print("SQL Output:")
        print(result.stdout)
        
        # Process the output
        lines = result.stdout.strip().split('\n')
        data = []
        
        # Skip header and footer lines
        for line in lines:
            if not line.strip() or "User" in line or "connected" in line or "heavyai" in line or "trade_date" in line:
                continue
            print("Processing line:", line)
    except Exception as e:
        print(f"Error running subprocess: {e}")

if __name__ == "__main__":
    fetch_test() 