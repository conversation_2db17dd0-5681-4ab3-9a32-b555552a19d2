#!/usr/bin/env python3
"""
Test the backtester integration with the newly loaded nifty_option_chain data
"""
import sys
import os
sys.path.append('/srv/samba/shared/bt/backtester_stable/BTRUN')

from heavydb import connect
import pandas as pd

def test_data_availability():
    """Test if data is accessible and in correct format for backtester"""
    
    # Connection parameters
    conn_params = {
        'host': 'localhost',
        'port': 6274,
        'user': 'admin',
        'password': 'HyperInteractive',
        'dbname': 'heavyai'
    }
    
    print("=" * 80)
    print("Testing Backtester Integration with Loaded Data")
    print("=" * 80)
    
    conn = connect(**conn_params)
    cursor = conn.cursor()
    
    # Test 1: Check data structure
    print("\n1. Checking data structure...")
    cursor.execute("SELECT * FROM nifty_option_chain LIMIT 1")
    columns = [desc[0] for desc in cursor.description]
    print(f"   Total columns: {len(columns)}")
    
    # Check required columns for backtester
    required_cols = ['trade_date', 'trade_time', 'spot', 'atm_strike', 'strike', 
                     'dte', 'ce_symbol', 'pe_symbol', 'ce_close', 'pe_close']
    missing_cols = [col for col in required_cols if col not in columns]
    if missing_cols:
        print(f"   ERROR: Missing columns: {missing_cols}")
    else:
        print(f"   ✓ All required columns present")
    
    # Test 2: Check data availability by date
    print("\n2. Checking data availability...")
    cursor.execute("""
        SELECT 
            MIN(trade_date) as min_date,
            MAX(trade_date) as max_date,
            COUNT(DISTINCT trade_date) as trading_days,
            COUNT(*) as total_rows
        FROM nifty_option_chain
    """)
    result = cursor.fetchone()
    print(f"   Date range: {result[0]} to {result[1]}")
    print(f"   Trading days: {result[2]}")
    print(f"   Total rows: {result[3]:,}")
    
    # Test 3: Check specific backtester queries
    print("\n3. Testing backtester-style queries...")
    
    # Test strike selection query (similar to what backtester uses)
    test_date = '2024-01-02'
    test_time = '09:30:00'
    
    print(f"\n   Testing strike selection for {test_date} at {test_time}:")
    
    query = f"""
        SELECT 
            strike,
            ce_symbol,
            pe_symbol,
            ce_close,
            pe_close,
            ABS(strike - spot) as distance
        FROM nifty_option_chain
        WHERE trade_date = '{test_date}'
          AND trade_time = TIME '{test_time}'
          AND ce_symbol IS NOT NULL
          AND pe_symbol IS NOT NULL
        ORDER BY distance
        LIMIT 5
    """
    
    try:
        cursor.execute(query)
        results = cursor.fetchall()
        print(f"   Found {len(results)} strikes:")
        for row in results:
            print(f"     Strike: {row[0]}, CE: {row[1]}, PE: {row[2]}, "
                  f"CE Price: {row[3]:.2f}, PE Price: {row[4]:.2f}")
    except Exception as e:
        print(f"   ERROR in strike selection: {e}")
    
    # Test 4: Check expiry_bucket data
    print("\n4. Checking expiry_bucket data...")
    cursor.execute("""
        SELECT 
            expiry_bucket,
            COUNT(*) as count
        FROM nifty_option_chain
        WHERE trade_date = '2024-01-02'
        GROUP BY expiry_bucket
        ORDER BY count DESC
    """)
    
    print("   Expiry buckets:")
    for row in cursor.fetchall():
        print(f"     {row[0]}: {row[1]:,} rows")
    
    # Test 5: Performance test
    print("\n5. Testing query performance...")
    import time
    
    start = time.time()
    cursor.execute("""
        SELECT COUNT(*)
        FROM nifty_option_chain
        WHERE trade_date BETWEEN '2024-01-01' AND '2024-01-31'
          AND strike_type IN ('ATM', 'OTM1', 'ITM1')
    """)
    count = cursor.fetchone()[0]
    elapsed = time.time() - start
    
    print(f"   Complex query returned {count:,} rows in {elapsed:.3f} seconds")
    
    # Test 6: Check underlying_price column (if needed by backtester)
    print("\n6. Checking for underlying_price...")
    try:
        cursor.execute("SELECT underlying_price FROM nifty_option_chain LIMIT 1")
        print("   ✓ underlying_price column exists")
    except:
        print("   ✗ underlying_price column missing (using 'spot' instead)")
    
    conn.close()
    
    print("\n" + "=" * 80)
    print("Integration test complete!")
    print("The data appears to be ready for backtesting.")
    print("=" * 80)

if __name__ == "__main__":
    test_data_availability()