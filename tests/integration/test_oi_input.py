#!/usr/bin/env python3
"""Create a test OI strategy Excel file."""

import pandas as pd
import os

# Create output directory
output_dir = "/srv/samba/shared/Trades"
os.makedirs(output_dir, exist_ok=True)

# Create GeneralParameter data for OI strategy
general_data = {
    'StrategyName': ['MAXOI_TEST'],
    'Timeframe': [3],  # Must be multiple of 3
    'MaxOpenPositions': [2],
    'Underlying': ['SPOT'],
    'Index': ['NIFTY'],
    'Weekdays': ['1,2,3,4,5'],
    'DTE': [0],
    'StrikeSelectionTime': [93000],
    'StartTime': [93000],
    'LastEntryTime': [142900],
    'EndTime': [142900],
    'StrategyProfit': [0],
    'StrategyLoss': [0],
    'StrategyProfitReExecuteNo': [0],
    'StrategyLossReExecuteNo': [0],
    'StrategyTrailingType': [''],
    'TgtTrackingFrom': ['close'],
    'SlTrackingFrom': ['close'],
    'PnLCalculationFrom': ['close'],
    'OnExpiryDayTradeNextExpiry': ['No']
}

# Create LegParameter data with OI strike methods
leg_data = {
    'StrategyName': ['MAXOI_TEST', 'MAXOI_TEST'],
    'OiThreshold': [800000, 800000],
    'LegID': ['ce_leg', 'pe_leg'],
    'Instrument': ['call', 'put'],
    'Transaction': ['sell', 'sell'],
    'Expiry': ['current', 'current'],
    'StrikeMethod': ['MAXOI_1', 'MAXOI_1'],  # Use MAXOI_1 for highest OI
    'StrikeValue': [0, 0],
    'Lots': [1, 1],
    'SLType': ['percentage', 'percentage'],
    'SLValue': [30, 30],
    'TGTType': ['percentage', 'percentage'],
    'TGTValue': [80, 80],
    'TrailSLType': ['percentage', 'percentage'],
    'SL_TrailAt': [0, 0],
    'SL_TrailBy': [0, 0]
}

# Create DataFrame and save to Excel
output_file = '/srv/samba/shared/test_oi_strategy.xlsx'
with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
    pd.DataFrame(general_data).to_excel(writer, sheet_name='GeneralParameter', index=False)
    pd.DataFrame(leg_data).to_excel(writer, sheet_name='LegParameter', index=False)

print(f"Created test OI strategy file: {output_file}")

# Also create a portfolio wrapper
portfolio_data = {
    'PortfolioName': ['OI_TEST_PORTFOLIO'],
    'BacktestStartDate': ['2025-04-01'],
    'BacktestEndDate': ['2025-04-02'],
    'InitialCapital': [1000000],
    'Slippage': [0.1],
    'TransactionCost': [0]
}

strategy_data = {
    'PortfolioName': ['OI_TEST_PORTFOLIO'],
    'StrategyType': ['OI'],
    'StrategyName': ['MAXOI_TEST'],
    'StrategyExcelFilePath': [output_file],
    'Enabled': ['YES']
}

# Create portfolio Excel
portfolio_file = '/srv/samba/shared/test_oi_portfolio.xlsx'
with pd.ExcelWriter(portfolio_file, engine='openpyxl') as writer:
    pd.DataFrame(portfolio_data).to_excel(writer, sheet_name='PortfolioSetting', index=False)
    pd.DataFrame(strategy_data).to_excel(writer, sheet_name='StrategySetting', index=False)

print(f"Created test OI portfolio file: {portfolio_file}")