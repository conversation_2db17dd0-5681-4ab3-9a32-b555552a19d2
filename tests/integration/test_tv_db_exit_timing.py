#!/usr/bin/env python3
"""
Test TV backtesting with database exit timing feature
"""

import sys
import os
import pandas as pd
from datetime import datetime

# Add path for imports
sys.path.append('/srv/samba/shared')
sys.path.append('/srv/samba/shared/bt/backtester_stable')
sys.path.append('/srv/samba/shared/bt/backtester_stable/BTRUN')

def test_db_exit_timing():
    """Test database exit timing functionality"""
    
    print("TV DATABASE EXIT TIMING TEST")
    print("="*70)
    
    # Import required modules
    try:
        from bt.backtester_stable.models.tv_models import TvSettingModel, RawTvSignalModel
        from bt.backtester_stable.BTRUN.strategies.tv_processor import find_precise_exit_time
        from bt.backtester_stable.BTRUN.core import heavydb_helpers
        
        print("✅ Successfully imported all modules")
        
        # Create a test TV setting with DB exit timing enabled
        tv_setting = TvSettingModel(
            Name='TestStrategy',
            Enabled='yes',
            ManualTrade='YES',
            UseDbExitTiming=True,
            ExitSearchInterval=10,
            ExitPriceSource='SPOT',
            StartDate='240401',
            EndDate='240410'
        )
        
        print(f"\n📊 Test TV Setting:")
        print(f"   Strategy: {tv_setting.Name}")
        print(f"   UseDbExitTiming: {tv_setting.UseDbExitTiming}")
        print(f"   ExitSearchInterval: {tv_setting.ExitSearchInterval} minutes")
        print(f"   ExitPriceSource: {tv_setting.ExitPriceSource}")
        
        # Create a test exit signal
        test_signal = RawTvSignalModel(
            tradeno=1,
            signal_type='Exit Long',
            datetime_str='03-04-2024 12:00',
            lots=2
        )
        test_signal.parsed_datetime = datetime(2024, 4, 3, 12, 0, 0)
        test_signal.parsed_signal = 'EXIT LONG'
        
        print(f"\n📈 Test Exit Signal:")
        print(f"   Trade #: {test_signal.tradeno}")
        print(f"   Type: {test_signal.signal_type}")
        print(f"   Time: {test_signal.parsed_datetime}")
        
        # Test the find_precise_exit_time function
        print(f"\n🔍 Testing Database Exit Timing:")
        
        # Get database connection
        try:
            connection = heavydb_helpers.get_connection()
            print("   ✅ Database connection established")
            
            # Call the function
            precise_time = find_precise_exit_time(test_signal, tv_setting, connection)
            
            if precise_time != test_signal.parsed_datetime:
                print(f"   ✅ Found precise exit time: {precise_time}")
                print(f"   📊 Original time: {test_signal.parsed_datetime}")
                print(f"   📊 Difference: {(precise_time - test_signal.parsed_datetime).total_seconds()} seconds")
            else:
                print(f"   ℹ️  No better exit time found, using original: {precise_time}")
                
        except Exception as e:
            print(f"   ❌ Database error: {e}")
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("\nTrying alternative test...")
        
        # Alternative: Just verify the models have the fields
        try:
            from models.tv_models import TvSettingModel
            
            # Check if model has the fields
            model_fields = TvSettingModel.__fields__.keys()
            db_fields = ['UseDbExitTiming', 'ExitSearchInterval', 'ExitPriceSource']
            
            print("\n📋 Model Field Verification:")
            for field in db_fields:
                if field in model_fields:
                    print(f"   ✅ {field} exists in TvSettingModel")
                else:
                    print(f"   ❌ {field} NOT found in TvSettingModel")
                    
        except Exception as e2:
            print(f"   Alternative test also failed: {e2}")

    print("\n" + "="*70)
    print("TEST SUMMARY:")
    print("The database exit timing feature is implemented with:")
    print("1. UseDbExitTiming - Enable/disable the feature")
    print("2. ExitSearchInterval - Time window to search (minutes)")
    print("3. ExitPriceSource - SPOT or FUTURE price data")
    print("\nThese columns should be added to TBS Excel files to use this feature.")

if __name__ == "__main__":
    test_db_exit_timing()