#!/usr/bin/env python3
"""Test OI backtest and compare with golden output."""

import os
import sys
import pandas as pd
from datetime import datetime

# Add project root to path
_SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, _SCRIPT_DIR)

# Now import required modules
from bt.backtester_stable.BTRUN.BT_OI_GPU import OiBacktester
from bt.backtester_stable.BTRUN.models import StrategyModel, LegModel, OptionType, TransactionType, StrikeRule, ExpiryRule
from bt.backtester_stable.BTRUN.models.oi import OiStrategyModel

# Create OI strategy with MAXOI
strategy = StrategyModel(
    strategy_name="OI_MAXOI_TEST",
    evaluator='OI',
    is_tick_bt=False,
    entry_start='09:16:00',
    entry_end='15:15:00',
    legs=[
        LegModel(
            leg_id='ce_leg',
            index='NIFTY',
            option_type=OptionType.CALL,
            transaction=TransactionType.SELL,
            lots=1,
            strike_rule=StrikeRule.MAXOI_1,
            expiry_rule=ExpiryRule.CURRENT_WEEK,
            entry_time='09:16:00',
            exit_time='15:15:00',
            risk_rules=[],
            extra_params={'OiThreshold': '800000'}
        ),
        LegModel(
            leg_id='pe_leg',
            index='NIFTY',
            option_type=OptionType.PUT,
            transaction=TransactionType.SELL,
            lots=1,
            strike_rule=StrikeRule.MAXOI_1,
            expiry_rule=ExpiryRule.CURRENT_WEEK,
            entry_time='09:16:00',
            exit_time='15:15:00',
            risk_rules=[],
            extra_params={'OiThreshold': '800000'}
        )
    ],
    extra_params={
        'Timeframe': '3',
        'MaxOpenPositions': '2',
        'OiThreshold': '800000',
        'CoiBasedOn': 'yesterday_close',
        'Index': 'NIFTY'
    },
    strategy_excel_path="test_oi_strategy.xlsx"
)

# Create OI strategy config
oi_strategy = OiStrategyModel.from_excel_params({
    'Timeframe': '3',
    'MaxOpenPositions': '2',
    'OiThreshold': '800000',
    'StrikeMethod': 'MAXOI_1',
    'CoiBasedOn': 'yesterday_close',
    'StrikeCount': '15'
})

# Run backtest
print("Starting OI backtest...")
backtester = OiBacktester()
results = backtester.run_strategy_backtest(
    strategy, 
    oi_strategy, 
    datetime(2025, 4, 1).date(), 
    datetime(2025, 4, 2).date()
)

print(f"Generated {len(results)} trades")

# Convert results to DataFrame
if results:
    data = []
    for trade in results:
        data.append({
            'Date': trade.entry_date.strftime('%Y-%m-%d'),
            'Time': trade.entry_time.strftime('%H:%M:%S'),
            'Symbol': trade.symbol,
            'Buy/Sell': 'SELL',  # Initial entry
            'Qty': trade.lots * 50,  # Lot size for NIFTY
            'Rate': trade.entry_price,
            'Strategy': trade.strategy_name,
            'Leg': trade.leg_id,
            'Strike': trade.strike,
            'Type': trade.option_type,
            'OI_Rank': trade.oi_rank,
            'Trade_Type': 'ENTRY'
        })
        
        if trade.exit_date:
            data.append({
                'Date': trade.exit_date.strftime('%Y-%m-%d'),
                'Time': trade.exit_time.strftime('%H:%M:%S'),
                'Symbol': trade.symbol,
                'Buy/Sell': 'BUY',  # Exit
                'Qty': trade.lots * 50,
                'Rate': trade.exit_price,
                'Strategy': trade.strategy_name,
                'Leg': trade.leg_id,
                'Strike': trade.strike,
                'Type': trade.option_type,
                'OI_Rank': trade.oi_rank,
                'Trade_Type': 'EXIT',
                'Exit_Reason': trade.exit_reason
            })
    
    # Create DataFrame and save
    df = pd.DataFrame(data)
    output_file = '/srv/samba/shared/Trades/oi_test_output.xlsx'
    
    # Create Excel with multiple sheets
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # PORTFOLIO Trans sheet
        portfolio_df = df[['Date', 'Time', 'Symbol', 'Buy/Sell', 'Qty', 'Rate']].copy()
        portfolio_df.to_excel(writer, sheet_name='PORTFOLIO Trans', index=False)
        
        # STRATEGY Trans sheet  
        strategy_df = df.copy()
        strategy_df.to_excel(writer, sheet_name='OI_MAXOI_TEST Trans', index=False)
        
        # Summary metrics
        total_trades = len(df[df['Trade_Type'] == 'ENTRY'])
        summary_data = {
            'Metric': ['Total Trades', 'Total P&L', 'Win Rate', 'Avg Trade'],
            'Value': [total_trades, 0, 0, 0]
        }
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='Summary', index=False)
    
    print(f"Results saved to: {output_file}")
    
    # Display first few trades
    print("\nFirst 5 trades:")
    print(df.head())
else:
    print("No trades generated")