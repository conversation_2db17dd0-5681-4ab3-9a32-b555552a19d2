#!/usr/bin/env python3
"""Test the v1 compatibility endpoint"""
import requests
import json

# Test without files first
url = "http://localhost:8000/api/v1/auto-backtest/upload-and-run"

# Create test files
portfolio_path = "/srv/samba/shared/bt/archive/backtester_stable/BTRUN/INPUT SHEETS/INPUT PORTFOLIO.xlsx"
strategy_path = "/srv/samba/shared/bt/archive/backtester_stable/BTRUN/INPUT SHEETS/INPUT TBS MULTI LEGS.xlsx"

with open(portfolio_path, 'rb') as p, open(strategy_path, 'rb') as s:
    files = {
        'portfolio_file': ('INPUT PORTFOLIO.xlsx', p, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'),
        'strategy_file': ('INPUT TBS MULTI LEGS.xlsx', s, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    }
    data = {
        'strategy_type': 'tbs',
        'index': 'NIFTY',
        'gpu_config': 'auto'
    }
    
    response = requests.post(url, files=files, data=data)
    
print(f"Status Code: {response.status_code}")
print(f"Response: {json.dumps(response.json(), indent=2)}")