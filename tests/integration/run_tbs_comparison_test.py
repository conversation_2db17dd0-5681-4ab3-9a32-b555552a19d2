#!/usr/bin/env python3
"""
Comprehensive TBS comparison test between archive and new GPU systems
Part of Phase 3.1 E2E testing plan
"""
import os
import sys
import json
import subprocess
import pandas as pd
from datetime import datetime
import shutil

class TBSComparisonTest:
    def __init__(self):
        self.test_file = "/srv/samba/shared/bt/backtester_stable/BTRUN/test_files/TBS_Test_Input.xlsx"
        self.base_dir = "/srv/samba/shared"
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.results_dir = f"{self.base_dir}/tbs_comparison_{self.timestamp}"
        
        # Ensure results directory exists
        os.makedirs(self.results_dir, exist_ok=True)
        
        self.test_config = {
            "symbol": "NIFTY",
            "start_date": "240401",  # April 1, 2024 - same as archive tests
            "end_date": "240401",    # Single day test first
            "test_file": self.test_file,
            "results_dir": self.results_dir
        }
    
    def log(self, message):
        print(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
        with open(f"{self.results_dir}/test_log.txt", "a") as f:
            f.write(f"[{datetime.now().strftime('%H:%M:%S')}] {message}\n")
    
    def verify_test_file(self):
        """Verify the TBS test file is valid"""
        self.log("Verifying TBS test file...")
        
        if not os.path.exists(self.test_file):
            self.log(f"ERROR: Test file not found: {self.test_file}")
            return False
        
        try:
            xl = pd.ExcelFile(self.test_file)
            required_sheets = ['GeneralParameter', 'LegParameter']
            
            for sheet in required_sheets:
                if sheet not in xl.sheet_names:
                    self.log(f"ERROR: Missing required sheet: {sheet}")
                    return False
            
            # Check strategies
            gp = pd.read_excel(self.test_file, sheet_name='GeneralParameter')
            strategies = len(gp)
            self.log(f"Found {strategies} strategies in test file")
            
            if strategies == 0:
                self.log("ERROR: No strategies found in GeneralParameter")
                return False
            
            self.log("✅ TBS test file verified successfully")
            return True
            
        except Exception as e:
            self.log(f"ERROR verifying test file: {e}")
            return False
    
    def run_new_gpu_system_test(self):
        """Run TBS test on new GPU system - try multiple approaches"""
        self.log("Starting TBS test on NEW GPU SYSTEM...")
        
        # Approach 1: Direct BT_TBS_GPU.py script
        self.log("Approach 1: Direct BT_TBS_GPU.py script")
        gpu_output_dir = f"{self.results_dir}/new_gpu_system"
        os.makedirs(gpu_output_dir, exist_ok=True)
        
        try:
            cmd = [
                "python3",
                "/srv/samba/shared/bt/backtester_stable/BTRUN/BT_TBS_GPU.py",
                "--input", self.test_file,
                "--symbol", self.test_config["symbol"],
                "--start-date", self.test_config["start_date"],
                "--end-date", self.test_config["end_date"],
                "--output-dir", gpu_output_dir
            ]
            
            self.log(f"Running: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                cwd="/srv/samba/shared/bt/backtester_stable/BTRUN",
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            # Save command output
            with open(f"{gpu_output_dir}/command_output.txt", "w") as f:
                f.write(f"Command: {' '.join(cmd)}\n")
                f.write(f"Return code: {result.returncode}\n")
                f.write(f"STDOUT:\n{result.stdout}\n")
                f.write(f"STDERR:\n{result.stderr}\n")
            
            if result.returncode == 0:
                self.log("✅ GPU system test completed successfully")
                return True, gpu_output_dir
            else:
                self.log(f"❌ GPU system test failed with return code: {result.returncode}")
                self.log(f"STDERR: {result.stderr}")
                
                # Try approach 2: API method
                return self.run_gpu_api_approach()
                
        except subprocess.TimeoutExpired:
            self.log("❌ GPU system test timed out")
            return self.run_gpu_api_approach()
        except Exception as e:
            self.log(f"❌ GPU system test error: {e}")
            return self.run_gpu_api_approach()
    
    def run_gpu_api_approach(self):
        """Try running TBS test via API"""
        self.log("Approach 2: API method")
        
        try:
            # Check if server is running
            result = subprocess.run(
                ["curl", "-s", "http://localhost:8000/api/v2/health"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode != 0:
                self.log("Server not running, starting it...")
                # Start server in background
                subprocess.Popen(
                    ["docker-compose", "up", "-d"],
                    cwd="/srv/samba/shared/bt/backtester_stable/BTRUN"
                )
                import time
                time.sleep(30)  # Wait for server to start
            
            # Use the API client approach
            api_output_dir = f"{self.results_dir}/new_gpu_api"
            os.makedirs(api_output_dir, exist_ok=True)
            
            self.log("API approach not implemented yet, will use fallback")
            return False, None
            
        except Exception as e:
            self.log(f"❌ API approach failed: {e}")
            return False, None
    
    def run_archive_system_test(self):
        """Run TBS test on archive system"""
        self.log("Starting TBS test on ARCHIVE SYSTEM...")
        
        archive_output_dir = f"{self.results_dir}/archive_system"
        os.makedirs(archive_output_dir, exist_ok=True)
        
        try:
            # Use the legacy backtester
            cmd = [
                "python3",
                "/srv/samba/shared/BTRunPortfolio.py",
                "--input", self.test_file,
                "--symbol", self.test_config["symbol"],
                "--start-date", self.test_config["start_date"],
                "--end-date", self.test_config["end_date"],
                "--output-dir", archive_output_dir,
                "--use-mysql"  # Force MySQL usage
            ]
            
            self.log(f"Running: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                cwd="/srv/samba/shared",
                capture_output=True,
                text=True,
                timeout=600  # 10 minute timeout
            )
            
            # Save command output
            with open(f"{archive_output_dir}/command_output.txt", "w") as f:
                f.write(f"Command: {' '.join(cmd)}\n")
                f.write(f"Return code: {result.returncode}\n")
                f.write(f"STDOUT:\n{result.stdout}\n")
                f.write(f"STDERR:\n{result.stderr}\n")
            
            if result.returncode == 0:
                self.log("✅ Archive system test completed successfully")
                return True, archive_output_dir
            else:
                self.log(f"❌ Archive system test failed with return code: {result.returncode}")
                self.log(f"STDERR: {result.stderr}")
                return False, archive_output_dir
                
        except subprocess.TimeoutExpired:
            self.log("❌ Archive system test timed out")
            return False, archive_output_dir
        except Exception as e:
            self.log(f"❌ Archive system test error: {e}")
            return False, archive_output_dir
    
    def compare_outputs(self, gpu_output_dir, archive_output_dir):
        """Compare outputs from both systems"""
        self.log("Comparing outputs from both systems...")
        
        comparison_results = {
            "gpu_files": [],
            "archive_files": [],
            "comparison_status": "pending"
        }
        
        # List files from both systems
        if gpu_output_dir and os.path.exists(gpu_output_dir):
            comparison_results["gpu_files"] = os.listdir(gpu_output_dir)
        
        if archive_output_dir and os.path.exists(archive_output_dir):
            comparison_results["archive_files"] = os.listdir(archive_output_dir)
        
        self.log(f"GPU files: {comparison_results['gpu_files']}")
        self.log(f"Archive files: {comparison_results['archive_files']}")
        
        # Save comparison results
        with open(f"{self.results_dir}/comparison_results.json", "w") as f:
            json.dump(comparison_results, f, indent=2)
        
        return comparison_results
    
    def generate_report(self, gpu_success, archive_success, comparison_results):
        """Generate comprehensive test report"""
        self.log("Generating comprehensive test report...")
        
        report = {
            "test_timestamp": self.timestamp,
            "test_configuration": self.test_config,
            "results": {
                "new_gpu_system": {
                    "status": "success" if gpu_success else "failed",
                    "files_generated": comparison_results.get("gpu_files", [])
                },
                "archive_system": {
                    "status": "success" if archive_success else "failed", 
                    "files_generated": comparison_results.get("archive_files", [])
                },
                "comparison": comparison_results
            },
            "next_steps": [],
            "phase_status": "Phase 3.1 TBS Comparison"
        }
        
        # Determine next steps
        if gpu_success and archive_success:
            report["next_steps"] = [
                "Perform detailed trade-by-trade comparison",
                "Apply ATM conversion methodology", 
                "Generate final comparison report",
                "Complete Phase 3.1 of E2E testing"
            ]
        elif gpu_success:
            report["next_steps"] = [
                "Debug archive system issues",
                "Retry archive system test",
                "Complete comparison after both systems work"
            ]
        elif archive_success:
            report["next_steps"] = [
                "Debug GPU system issues",
                "Try alternative GPU testing approaches",
                "Complete comparison after both systems work"
            ]
        else:
            report["next_steps"] = [
                "Debug both systems",
                "Check TBS input file format",
                "Verify system dependencies",
                "Retry tests after fixes"
            ]
        
        # Save report
        report_file = f"{self.results_dir}/comprehensive_report.json"
        with open(report_file, "w") as f:
            json.dump(report, f, indent=2)
        
        # Create markdown summary
        md_report = f"""# TBS Comparison Test Report

## Test Configuration
- **Timestamp**: {self.timestamp}
- **Test File**: {self.test_file}
- **Symbol**: {self.test_config['symbol']}
- **Date Range**: {self.test_config['start_date']} to {self.test_config['end_date']}

## Results Summary

### New GPU System
- **Status**: {'✅ SUCCESS' if gpu_success else '❌ FAILED'}
- **Files Generated**: {len(comparison_results.get('gpu_files', []))}

### Archive System  
- **Status**: {'✅ SUCCESS' if archive_success else '❌ FAILED'}
- **Files Generated**: {len(comparison_results.get('archive_files', []))}

## Next Steps
{chr(10).join(f'- {step}' for step in report['next_steps'])}

## Phase Status
**{report['phase_status']}** - {'✅ READY FOR NEXT PHASE' if gpu_success and archive_success else '⏳ PENDING FIXES'}
"""
        
        with open(f"{self.results_dir}/report_summary.md", "w") as f:
            f.write(md_report)
        
        self.log(f"Report generated: {report_file}")
        self.log(f"Summary: {self.results_dir}/report_summary.md")
        
        return report
    
    def run_full_test(self):
        """Run the complete TBS comparison test"""
        self.log("="*80)
        self.log("STARTING TBS COMPARISON TEST")
        self.log("Phase 3.1 of E2E Testing Plan")
        self.log("="*80)
        
        # Step 1: Verify test file
        if not self.verify_test_file():
            self.log("❌ Test file verification failed. Aborting.")
            return False
        
        # Step 2: Run GPU system test
        gpu_success, gpu_output_dir = self.run_new_gpu_system_test()
        
        # Step 3: Run archive system test
        archive_success, archive_output_dir = self.run_archive_system_test()
        
        # Step 4: Compare outputs
        comparison_results = self.compare_outputs(gpu_output_dir, archive_output_dir)
        
        # Step 5: Generate report
        report = self.generate_report(gpu_success, archive_success, comparison_results)
        
        self.log("="*80)
        self.log("TBS COMPARISON TEST COMPLETED")
        self.log(f"Results directory: {self.results_dir}")
        self.log("="*80)
        
        return gpu_success or archive_success

def main():
    test = TBSComparisonTest()
    success = test.run_full_test()
    
    if success:
        print(f"\n✅ Test completed. Check results in: {test.results_dir}")
    else:
        print(f"\n❌ Test failed. Check logs in: {test.results_dir}")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())