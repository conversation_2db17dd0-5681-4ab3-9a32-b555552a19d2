#!/usr/bin/env python3
"""
Test multi-index data processing with one file per index
"""
import os
import sys
sys.path.append('/srv/samba/shared')
from process_all_multiindex_data import process_zip_file, INDEX_CONFIG
from heavydb import connect

def main():
    print("="*80)
    print("TEST MULTI-INDEX DATA PROCESSING")
    print("="*80)
    
    # Connect to HeavyDB
    try:
        conn = connect(
            host='localhost',
            port=6274,
            user='admin',
            password='HyperInteractive',
            dbname='heavyai'
        )
        print("✅ Connected to HeavyDB")
    except Exception as e:
        print(f"❌ HeavyDB connection error: {e}")
        sys.exit(1)
    
    # Create temp directory
    temp_dir = "/srv/samba/shared/temp_multiindex_test"
    os.makedirs(temp_dir, exist_ok=True)
    
    # Test files - one per index
    test_files = {
        'BANKNIFTY': '/srv/samba/shared/market_data/banknifty/banknifty_2024.zip',
        'MIDCAPNIFTY': '/srv/samba/shared/market_data/midcpnifty/2024_midcpnifty.zip',
        'SENSEX': '/srv/samba/shared/market_data/sensex/sensex_2024.zip'
    }
    
    # Process each test file
    for index_name, zip_path in test_files.items():
        if os.path.exists(zip_path):
            print(f"\n{'='*60}")
            print(f"Testing {index_name}")
            print(f"File: {os.path.basename(zip_path)}")
            print(f"{'='*60}")
            
            rows = process_zip_file(zip_path, index_name, temp_dir, conn)
            print(f"Processed {rows:,} rows")
        else:
            print(f"\n❌ Test file not found: {zip_path}")
    
    # Clean up
    import shutil
    if os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)
    
    # Check results
    cursor = conn.cursor()
    cursor.execute("""
        SELECT index_name, 
               expiry_bucket,
               COUNT(*) as row_count,
               MIN(trade_date) as min_date,
               MAX(trade_date) as max_date
        FROM nifty_option_chain
        WHERE trade_date >= '2024-01-01'
        GROUP BY index_name, expiry_bucket
        ORDER BY index_name, expiry_bucket
    """)
    
    results = cursor.fetchall()
    if results:
        print("\n" + "="*80)
        print("TEST RESULTS")
        print("="*80)
        current_index = None
        for row in results:
            if row[0] != current_index:
                current_index = row[0]
                print(f"\n{current_index}:")
            print(f"  {row[1]}: {row[2]:,} rows ({row[3]} to {row[4]})")
    
    cursor.close()
    conn.close()
    
    print("\n✅ Test complete!")

if __name__ == "__main__":
    main()