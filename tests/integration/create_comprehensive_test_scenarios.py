#!/usr/bin/env python3
"""
Create comprehensive test scenarios covering ALL TBS column functionalities
"""

import pandas as pd
import os
from datetime import datetime

def create_test_directories():
    """Create directory structure for test files"""
    base_dir = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/comprehensive_tests'
    os.makedirs(base_dir, exist_ok=True)
    
    subdirs = ['strike_selection', 'risk_management', 'reentry', 'advanced_features', 'edge_cases']
    for subdir in subdirs:
        os.makedirs(os.path.join(base_dir, subdir), exist_ok=True)
    
    return base_dir

def create_strike_selection_tests(base_dir):
    """Test all strike selection methods"""
    
    print("Creating Strike Selection Tests...")
    strike_dir = os.path.join(base_dir, 'strike_selection')
    
    # Test 1: All ATM Variations
    test1_general = pd.DataFrame([{
        'StrategyName': 'ATM_ALL_VARIATIONS',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '0',
        'StrikeSelectionTime': '92000',
        'StartTime': '91600',
        'EndTime': '153000',
        'Weekdays': '1,2,3,4,5'
    }])
    
    test1_legs = pd.DataFrame([
        # ATM exact
        {'LegID': 1, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'ATM', 
         'StrikeValue': 0, 'Expiry': 'CW', 'Lots': 1},
        # ATM +2 (OTM)
        {'LegID': 2, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'ATM', 
         'StrikeValue': 2, 'Expiry': 'CW', 'Lots': 1},
        # ATM -2 (ITM)
        {'LegID': 3, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'ATM', 
         'StrikeValue': -2, 'Expiry': 'CW', 'Lots': 1},
        # ATM +5 (Far OTM)
        {'LegID': 4, 'Instrument': 'OPTIONS', 'Transaction': 'BUY', 'StrikeMethod': 'ATM', 
         'StrikeValue': 5, 'Expiry': 'CW', 'Lots': 1},
    ])
    
    # Test 2: ITM/OTM Methods
    test2_general = pd.DataFrame([{
        'StrategyName': 'ITM_OTM_METHODS',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '0',
        'StrikeSelectionTime': '92000',
        'StartTime': '91600',
        'EndTime': '153000',
        'Weekdays': '1,2,3,4,5'
    }])
    
    test2_legs = pd.DataFrame([
        # Direct ITM/OTM
        {'LegID': 1, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'ITM1', 
         'StrikeValue': 0, 'Expiry': 'CW', 'Lots': 2},
        {'LegID': 2, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'OTM1', 
         'StrikeValue': 0, 'Expiry': 'CW', 'Lots': 2},
        {'LegID': 3, 'Instrument': 'OPTIONS', 'Transaction': 'BUY', 'StrikeMethod': 'ITM3', 
         'StrikeValue': 0, 'Expiry': 'CW', 'Lots': 2},
        {'LegID': 4, 'Instrument': 'OPTIONS', 'Transaction': 'BUY', 'StrikeMethod': 'OTM3', 
         'StrikeValue': 0, 'Expiry': 'CW', 'Lots': 2},
    ])
    
    # Test 3: FIXED and PREMIUM based
    test3_general = pd.DataFrame([{
        'StrategyName': 'FIXED_PREMIUM_STRIKES',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '0',
        'StrikeSelectionTime': '92000',
        'StartTime': '91600',
        'EndTime': '153000',
        'Weekdays': '1,2,3,4,5'
    }])
    
    test3_legs = pd.DataFrame([
        # Fixed strike
        {'LegID': 1, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'FIXED', 
         'StrikeValue': 22000, 'Expiry': 'CW', 'Lots': 1},
        # Premium > 50
        {'LegID': 2, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'PREMIUM', 
         'MatchPremium': 50, 'StrikePremiumCondition': 'GT', 'Expiry': 'CW', 'Lots': 1},
        # Premium < 100
        {'LegID': 3, 'Instrument': 'OPTIONS', 'Transaction': 'BUY', 'StrikeMethod': 'PREMIUM', 
         'MatchPremium': 100, 'StrikePremiumCondition': 'LT', 'Expiry': 'CW', 'Lots': 1},
        # Premium = 75 (approximately)
        {'LegID': 4, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'PREMIUM', 
         'MatchPremium': 75, 'StrikePremiumCondition': 'EQ', 'Expiry': 'CW', 'Lots': 1},
    ])
    
    # Test 4: Different Expiries
    test4_general = pd.DataFrame([{
        'StrategyName': 'MULTI_EXPIRY_TEST',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '-1',  # All days
        'StrikeSelectionTime': '92000',
        'StartTime': '91600',
        'EndTime': '153000',
        'Weekdays': '1,2,3,4,5'
    }])
    
    test4_legs = pd.DataFrame([
        # Current week
        {'LegID': 1, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'ATM', 
         'StrikeValue': 0, 'Expiry': 'CW', 'Lots': 2},
        # Next week
        {'LegID': 2, 'Instrument': 'OPTIONS', 'Transaction': 'BUY', 'StrikeMethod': 'ATM', 
         'StrikeValue': 0, 'Expiry': 'NW', 'Lots': 2},
        # Current month
        {'LegID': 3, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'OTM', 
         'StrikeValue': 2, 'Expiry': 'CWM', 'Lots': 1},
        # Next month
        {'LegID': 4, 'Instrument': 'OPTIONS', 'Transaction': 'BUY', 'StrikeMethod': 'OTM', 
         'StrikeValue': 2, 'Expiry': 'NWM', 'Lots': 1},
    ])
    
    # Save all strike selection tests
    tests = [
        ('atm_all_variations.xlsx', test1_general, test1_legs),
        ('itm_otm_methods.xlsx', test2_general, test2_legs),
        ('fixed_premium_strikes.xlsx', test3_general, test3_legs),
        ('multi_expiry_test.xlsx', test4_general, test4_legs)
    ]
    
    for filename, general_df, leg_df in tests:
        filepath = os.path.join(strike_dir, filename)
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            general_df.to_excel(writer, sheet_name='GeneralParameter', index=False)
            leg_df.to_excel(writer, sheet_name='LegParameter', index=False)
        print(f"  ✅ Created: {filename}")

def create_risk_management_tests(base_dir):
    """Test all SL/TP variations"""
    
    print("\nCreating Risk Management Tests...")
    risk_dir = os.path.join(base_dir, 'risk_management')
    
    # Test 1: All SL Types
    test1_general = pd.DataFrame([{
        'StrategyName': 'ALL_SL_TYPES',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '0',
        'StartTime': '91600',
        'EndTime': '153000'
    }])
    
    test1_legs = pd.DataFrame([
        # Percent SL
        {'LegID': 1, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'ATM',
         'StrikeValue': 0, 'SLType': 'PERCENT', 'SLValue': 25, 'Lots': 2},
        # Points SL
        {'LegID': 2, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'OTM',
         'StrikeValue': 2, 'SLType': 'POINTS', 'SLValue': 30, 'Lots': 2},
        # Premium SL
        {'LegID': 3, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'OTM',
         'StrikeValue': -2, 'SLType': 'PREMIUM', 'SLValue': 150, 'Lots': 2},
        # No SL
        {'LegID': 4, 'Instrument': 'OPTIONS', 'Transaction': 'BUY', 'StrikeMethod': 'OTM',
         'StrikeValue': 5, 'SLType': 'NONE', 'SLValue': 0, 'Lots': 2},
    ])
    
    # Test 2: All Target Types
    test2_general = pd.DataFrame([{
        'StrategyName': 'ALL_TARGET_TYPES',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '0',
        'StartTime': '91600',
        'EndTime': '153000'
    }])
    
    test2_legs = pd.DataFrame([
        # Percent Target
        {'LegID': 1, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'ATM',
         'StrikeValue': 0, 'TGTType': 'PERCENT', 'TGTValue': 50, 'Lots': 2},
        # Points Target
        {'LegID': 2, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'OTM',
         'StrikeValue': 1, 'TGTType': 'POINTS', 'TGTValue': 40, 'Lots': 2},
        # Premium Target
        {'LegID': 3, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'OTM',
         'StrikeValue': -1, 'TGTType': 'PREMIUM', 'TGTValue': 20, 'Lots': 2},
        # Combined SL and Target
        {'LegID': 4, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'ATM',
         'StrikeValue': 0, 'SLType': 'PERCENT', 'SLValue': 30, 'TGTType': 'PERCENT', 'TGTValue': 60, 'Lots': 2},
    ])
    
    # Test 3: Trailing Stop Loss
    test3_general = pd.DataFrame([{
        'StrategyName': 'TRAILING_SL_ALL_TYPES',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '0',
        'StartTime': '91600',
        'EndTime': '153000'
    }])
    
    test3_legs = pd.DataFrame([
        # Percent trailing
        {'LegID': 1, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'ATM',
         'StrikeValue': 0, 'SLType': 'PERCENT', 'SLValue': 50, 'TrailSLType': 'PERCENT',
         'SL_TrailAt': 20, 'SL_TrailBy': 10, 'Lots': 2},
        # Points trailing
        {'LegID': 2, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'OTM',
         'StrikeValue': 2, 'SLType': 'POINTS', 'SLValue': 40, 'TrailSLType': 'POINTS',
         'SL_TrailAt': 30, 'SL_TrailBy': 15, 'Lots': 2},
        # Premium trailing
        {'LegID': 3, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'ATM',
         'StrikeValue': 0, 'SLType': 'PREMIUM', 'SLValue': 100, 'TrailSLType': 'PREMIUM',
         'SL_TrailAt': 50, 'SL_TrailBy': 25, 'Lots': 2},
    ])
    
    # Test 4: Strategy Level Risk Management
    test4_general = pd.DataFrame([{
        'StrategyName': 'STRATEGY_LEVEL_RISK',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '0',
        'StartTime': '91600',
        'EndTime': '153000',
        'StrategyProfit': 5000,  # Strategy level target
        'StrategyLoss': -3000,   # Strategy level SL
        'StrategyTrailingType': 'LOCK_AND_TRAIL',
        'ProfitReaches': 3000,
        'LockMinProfitAt': 2000,
        'IncreaseInProfit': 1000,
        'TrailMinProfitBy': 500
    }])
    
    test4_legs = pd.DataFrame([
        {'LegID': 1, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'ATM',
         'StrikeValue': 0, 'Lots': 2},
        {'LegID': 2, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'ATM',
         'StrikeValue': 0, 'Lots': 2},
        {'LegID': 3, 'Instrument': 'OPTIONS', 'Transaction': 'BUY', 'StrikeMethod': 'OTM',
         'StrikeValue': 3, 'Lots': 2},
        {'LegID': 4, 'Instrument': 'OPTIONS', 'Transaction': 'BUY', 'StrikeMethod': 'OTM',
         'StrikeValue': -3, 'Lots': 2},
    ])
    
    # Save all risk management tests
    tests = [
        ('all_sl_types.xlsx', test1_general, test1_legs),
        ('all_target_types.xlsx', test2_general, test2_legs),
        ('trailing_sl_all_types.xlsx', test3_general, test3_legs),
        ('strategy_level_risk.xlsx', test4_general, test4_legs)
    ]
    
    for filename, general_df, leg_df in tests:
        filepath = os.path.join(risk_dir, filename)
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            general_df.to_excel(writer, sheet_name='GeneralParameter', index=False)
            leg_df.to_excel(writer, sheet_name='LegParameter', index=False)
        print(f"  ✅ Created: {filename}")

def create_reentry_tests(base_dir):
    """Test all re-entry and re-execute scenarios"""
    
    print("\nCreating Re-entry Tests...")
    reentry_dir = os.path.join(base_dir, 'reentry')
    
    # Test 1: Leg Level Re-entry
    test1_general = pd.DataFrame([{
        'StrategyName': 'LEG_REENTRY_ALL_TYPES',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '0',
        'StartTime': '91600',
        'EndTime': '153000',
        'LastEntryTime': '143000'
    }])
    
    test1_legs = pd.DataFrame([
        # Re-enter at same strike
        {'LegID': 1, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'ATM',
         'StrikeValue': 0, 'SLType': 'PERCENT', 'SLValue': 25, 'ReEntryType': 'ASPERSTRIKE',
         'ReEnteriesCount': 3, 'Lots': 2},
        # Re-enter with new strike
        {'LegID': 2, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'OTM',
         'StrikeValue': 2, 'SLType': 'PERCENT', 'SLValue': 30, 'ReEntryType': 'STRIKE',
         'ReEnteriesCount': 2, 'Lots': 2},
        # No re-entry
        {'LegID': 3, 'Instrument': 'OPTIONS', 'Transaction': 'BUY', 'StrikeMethod': 'OTM',
         'StrikeValue': 5, 'ReEntryType': 'NONE', 'ReEnteriesCount': 0, 'Lots': 2},
    ])
    
    # Test 2: Strategy Re-execution
    test2_general = pd.DataFrame([{
        'StrategyName': 'STRATEGY_REEXECUTE',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '0',
        'StartTime': '91600',
        'EndTime': '153000',
        'StrategyProfit': 3000,
        'StrategyProfitReExecuteNo': 2,  # Re-execute 2 times on profit
        'StrategyLoss': -2000,
        'StrategyLossReExecuteNo': 1     # Re-execute 1 time on loss
    }])
    
    test2_legs = pd.DataFrame([
        {'LegID': 1, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'ATM',
         'StrikeValue': 0, 'SLType': 'PERCENT', 'SLValue': 40, 'Lots': 1},
        {'LegID': 2, 'Instrument': 'OPTIONS', 'Transaction': 'BUY', 'StrikeMethod': 'OTM',
         'StrikeValue': 2, 'Lots': 1},
    ])
    
    # Test 3: Complex Re-entry with Conditions
    test3_general = pd.DataFrame([{
        'StrategyName': 'COMPLEX_REENTRY_CONDITIONS',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '0',
        'StartTime': '91600',
        'EndTime': '153000',
        'LastEntryTime': '143000'
    }])
    
    test3_legs = pd.DataFrame([
        # Main leg with re-entry and exit actions
        {'LegID': 1, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'ATM',
         'StrikeValue': 0, 'SLType': 'PERCENT', 'SLValue': 20, 'ReEntryType': 'ASPERSTRIKE',
         'ReEnteriesCount': 2, 'OnExit_OpenTradeOn': 'YES', 'OnExit_OpenTradeDelay': 60,
         'OnExit_SqOffAllLegs': 'YES', 'Lots': 2},
        # Hedge leg with entry actions
        {'LegID': 2, 'Instrument': 'OPTIONS', 'Transaction': 'BUY', 'StrikeMethod': 'OTM',
         'StrikeValue': 5, 'OnEntry_SqOffTradeOff': 'YES', 'OnEntry_SqOffDelay': 30, 'Lots': 2},
        # Additional leg
        {'LegID': 3, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'OTM',
         'StrikeValue': -2, 'SLType': 'PERCENT', 'SLValue': 30, 'ReEntryType': 'STRIKE',
         'ReEnteriesCount': 1, 'Lots': 2},
    ])
    
    # Save all re-entry tests
    tests = [
        ('leg_reentry_all_types.xlsx', test1_general, test1_legs),
        ('strategy_reexecute.xlsx', test2_general, test2_legs),
        ('complex_reentry_conditions.xlsx', test3_general, test3_legs)
    ]
    
    for filename, general_df, leg_df in tests:
        filepath = os.path.join(reentry_dir, filename)
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            general_df.to_excel(writer, sheet_name='GeneralParameter', index=False)
            leg_df.to_excel(writer, sheet_name='LegParameter', index=False)
        print(f"  ✅ Created: {filename}")

def create_advanced_features_tests(base_dir):
    """Test advanced features like partial exits, hedges, etc."""
    
    print("\nCreating Advanced Features Tests...")
    advanced_dir = os.path.join(base_dir, 'advanced_features')
    
    # Test 1: Partial Exits (SqOff)
    test1_general = pd.DataFrame([{
        'StrategyName': 'PARTIAL_EXITS_TEST',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '0',
        'StartTime': '91600',
        'EndTime': '153000',
        'SqOff1Time': '110000',  # 11 AM
        'SqOff1Percent': 50,     # Exit 50%
        'SqOff2Time': '140000',  # 2 PM
        'SqOff2Percent': 100     # Exit remaining
    }])
    
    test1_legs = pd.DataFrame([
        {'LegID': 1, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'ATM',
         'StrikeValue': 0, 'SLType': 'PERCENT', 'SLValue': 100, 'Lots': 4},  # 4 lots for partial
        {'LegID': 2, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'ATM',
         'StrikeValue': 0, 'SLType': 'PERCENT', 'SLValue': 100, 'Lots': 4},
    ])
    
    # Test 2: Hedge Management
    test2_general = pd.DataFrame([{
        'StrategyName': 'HEDGE_MANAGEMENT_TEST',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '0',
        'StartTime': '91600',
        'EndTime': '153000'
    }])
    
    test2_legs = pd.DataFrame([
        # Main leg with hedge
        {'LegID': 1, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'ATM',
         'StrikeValue': 0, 'SLType': 'PERCENT', 'SLValue': 30, 'OpenHedge': 'YES',
         'HedgeStrikeMethod': 'OTM', 'HedgeStrikeValue': 10, 'Lots': 2},
        # Another leg with different hedge
        {'LegID': 2, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'OTM',
         'StrikeValue': 2, 'OpenHedge': 'YES', 'HedgeStrikeMethod': 'PREMIUM',
         'HedgeStrikeValue': 20, 'HedgeStrikePremiumCondition': 'LT', 'Lots': 2},
    ])
    
    # Test 3: Time-based Features
    test3_general = pd.DataFrame([{
        'StrategyName': 'TIME_BASED_FEATURES',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '0',
        'StartTime': '91600',
        'StrikeSelectionTime': '92000',  # Different from start time
        'LastEntryTime': '140000',
        'EndTime': '153000',
        'PnLCalTime': '150000',  # Calculate P&L at 3 PM
        'ConsiderHedgePnLForStgyPnL': 'YES',
        'StoplossCheckingInterval': 60,  # Check every 60 seconds
        'TargetCheckingInterval': 30,    # Check every 30 seconds
        'ReEntryCheckingInterval': 120   # Check every 2 minutes
    }])
    
    test3_legs = pd.DataFrame([
        {'LegID': 1, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'ATM',
         'StrikeValue': 0, 'SLType': 'PERCENT', 'SLValue': 25, 'TGTType': 'PERCENT',
         'TGTValue': 50, 'ReEntryType': 'ASPERSTRIKE', 'ReEnteriesCount': 1, 'Lots': 2},
        {'LegID': 2, 'Instrument': 'OPTIONS', 'Transaction': 'BUY', 'StrikeMethod': 'OTM',
         'StrikeValue': 3, 'Lots': 2},
    ])
    
    # Test 4: Idle Legs and W&T (Wait & Trade)
    test4_general = pd.DataFrame([{
        'StrategyName': 'IDLE_AND_WAIT_TRADE',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '0',
        'StartTime': '91600',
        'EndTime': '153000'
    }])
    
    test4_legs = pd.DataFrame([
        # Active leg
        {'LegID': 1, 'IsIdle': 'NO', 'Instrument': 'OPTIONS', 'Transaction': 'SELL',
         'StrikeMethod': 'ATM', 'StrikeValue': 0, 'W&Type': 'TIME', 'W&TValue': 93000,
         'TrailW&T': 0, 'Lots': 2},
        # Idle leg (for calculation only)
        {'LegID': 2, 'IsIdle': 'YES', 'Instrument': 'OPTIONS', 'Transaction': 'BUY',
         'StrikeMethod': 'OTM', 'StrikeValue': 5, 'Lots': 2},
        # Wait based on premium
        {'LegID': 3, 'IsIdle': 'NO', 'Instrument': 'OPTIONS', 'Transaction': 'SELL',
         'StrikeMethod': 'ATM', 'StrikeValue': 0, 'W&Type': 'PREMIUM', 'W&TValue': 100,
         'TrailW&T': 10, 'Lots': 2},
    ])
    
    # Save all advanced tests
    tests = [
        ('partial_exits_test.xlsx', test1_general, test1_legs),
        ('hedge_management_test.xlsx', test2_general, test2_legs),
        ('time_based_features.xlsx', test3_general, test3_legs),
        ('idle_and_wait_trade.xlsx', test4_general, test4_legs)
    ]
    
    for filename, general_df, leg_df in tests:
        filepath = os.path.join(advanced_dir, filename)
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            general_df.to_excel(writer, sheet_name='GeneralParameter', index=False)
            leg_df.to_excel(writer, sheet_name='LegParameter', index=False)
        print(f"  ✅ Created: {filename}")

def create_edge_cases_tests(base_dir):
    """Test edge cases and complex scenarios"""
    
    print("\nCreating Edge Cases Tests...")
    edge_dir = os.path.join(base_dir, 'edge_cases')
    
    # Test 1: Maximum Complexity
    test1_general = pd.DataFrame([{
        'StrategyName': 'MAXIMUM_COMPLEXITY',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '-1',  # All days
        'Weekdays': '1,3,5',  # Mon, Wed, Fri only
        'StrikeSelectionTime': '92000',
        'StartTime': '91600',
        'LastEntryTime': '143000',
        'EndTime': '153000',
        'StrategyProfit': 10000,
        'StrategyLoss': -5000,
        'StrategyProfitReExecuteNo': 3,
        'StrategyLossReExecuteNo': 2,
        'StrategyTrailingType': 'LOCK_AND_TRAIL',
        'ProfitReaches': 5000,
        'LockMinProfitAt': 3000,
        'IncreaseInProfit': 2000,
        'TrailMinProfitBy': 1000,
        'SqOff1Time': '113000',
        'SqOff1Percent': 25,
        'SqOff2Time': '133000',
        'SqOff2Percent': 50,
        'OnExpiryDayTradeNextExpiry': 'YES'
    }])
    
    test1_legs = pd.DataFrame([
        # Complex leg 1
        {'LegID': 1, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'ATM',
         'StrikeValue': -1, 'Expiry': 'CW', 'SLType': 'PERCENT', 'SLValue': 20,
         'TGTType': 'POINTS', 'TGTValue': 50, 'TrailSLType': 'PERCENT', 'SL_TrailAt': 15,
         'SL_TrailBy': 10, 'ReEntryType': 'STRIKE', 'ReEnteriesCount': 2,
         'OnExit_OpenTradeOn': 'YES', 'OnExit_OpenTradeDelay': 30, 'OnExit_SqOffAllLegs': 'YES',
         'OpenHedge': 'YES', 'HedgeStrikeMethod': 'OTM', 'HedgeStrikeValue': 15, 'Lots': 4},
        # Complex leg 2
        {'LegID': 2, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'PREMIUM',
         'MatchPremium': 75, 'StrikePremiumCondition': 'GT', 'Expiry': 'NW', 'W&Type': 'TIME',
         'W&TValue': 93000, 'TrailW&T': 0, 'SLType': 'PREMIUM', 'SLValue': 200,
         'ReEntryType': 'ASPERSTRIKE', 'ReEnteriesCount': 1, 'OnEntry_SqOffTradeOff': 'YES',
         'OnEntry_SqOffDelay': 60, 'Lots': 4},
        # Support legs
        {'LegID': 3, 'Instrument': 'OPTIONS', 'Transaction': 'BUY', 'StrikeMethod': 'FIXED',
         'StrikeValue': 22500, 'Expiry': 'CW', 'OnEntry_OpenTradeOn': 'YES', 'Lots': 4},
        {'LegID': 4, 'Instrument': 'OPTIONS', 'Transaction': 'BUY', 'StrikeMethod': 'ITM3',
         'StrikeValue': 0, 'Expiry': 'CW', 'IsIdle': 'YES', 'Lots': 4},
    ])
    
    # Test 2: DTE and Weekday Combinations
    test2_general = pd.DataFrame([{
        'StrategyName': 'DTE_WEEKDAY_COMBO',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '1',  # 1 day to expiry
        'Weekdays': '3,4',  # Wed, Thu only
        'StartTime': '91600',
        'EndTime': '153000'
    }])
    
    test2_legs = pd.DataFrame([
        {'LegID': 1, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'ATM',
         'StrikeValue': 0, 'Lots': 2},
        {'LegID': 2, 'Instrument': 'OPTIONS', 'Transaction': 'BUY', 'StrikeMethod': 'OTM',
         'StrikeValue': 3, 'Lots': 2},
    ])
    
    # Test 3: Move SL to Cost
    test3_general = pd.DataFrame([{
        'StrategyName': 'MOVE_SL_TO_COST',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '0',
        'MoveSlToCost': 'YES',  # Move SL to breakeven
        'StartTime': '91600',
        'EndTime': '153000',
        'TgtTrackingFrom': 'REGISTER',
        'TgtRegisterPriceFrom': 'REGISTER',
        'SlTrackingFrom': 'REGISTER',
        'SlRegisterPriceFrom': 'REGISTER',
        'PnLCalculationFrom': 'REGISTER'
    }])
    
    test3_legs = pd.DataFrame([
        {'LegID': 1, 'Instrument': 'OPTIONS', 'Transaction': 'SELL', 'StrikeMethod': 'ATM',
         'StrikeValue': 0, 'SLType': 'PERCENT', 'SLValue': 30, 'TGTType': 'PERCENT',
         'TGTValue': 50, 'Lots': 2},
        {'LegID': 2, 'Instrument': 'OPTIONS', 'Transaction': 'BUY', 'StrikeMethod': 'OTM',
         'StrikeValue': 2, 'Lots': 2},
    ])
    
    # Save all edge case tests
    tests = [
        ('maximum_complexity.xlsx', test1_general, test1_legs),
        ('dte_weekday_combo.xlsx', test2_general, test2_legs),
        ('move_sl_to_cost.xlsx', test3_general, test3_legs)
    ]
    
    for filename, general_df, leg_df in tests:
        filepath = os.path.join(edge_dir, filename)
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            general_df.to_excel(writer, sheet_name='GeneralParameter', index=False)
            leg_df.to_excel(writer, sheet_name='LegParameter', index=False)
        print(f"  ✅ Created: {filename}")

def create_master_portfolio(base_dir):
    """Create master portfolio including all test strategies"""
    
    print("\nCreating Master Portfolio...")
    
    # Get all strategy files
    all_strategies = []
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            if file.endswith('.xlsx'):
                rel_path = os.path.relpath(os.path.join(root, file), base_dir)
                strategy_name = file.replace('.xlsx', '').upper()
                all_strategies.append((strategy_name, rel_path))
    
    # Portfolio settings
    portfolio_df = pd.DataFrame([{
        'PortfolioName': 'COMPREHENSIVE_TEST_ALL_COLUMNS',
        'StartDate': '2024-04-01',
        'EndDate': '2024-04-30',
        'InitialCapital': 5000000,  # 50 lakh for all tests
        'Enabled': 'YES'
    }])
    
    # Strategy settings
    strategy_data = []
    for strategy_name, file_path in all_strategies:
        strategy_data.append({
            'StrategyName': strategy_name,
            'StrategyExcelFilePath': file_path,
            'Enabled': 'YES',
            'LegsInStrategy': 4,  # Max 4 legs
            'CapitalAllocation': 200000  # 2 lakh per strategy
        })
    
    strategy_df = pd.DataFrame(strategy_data)
    
    # Save portfolio
    portfolio_file = os.path.join(base_dir, 'MASTER_COMPREHENSIVE_TEST_PORTFOLIO.xlsx')
    with pd.ExcelWriter(portfolio_file, engine='openpyxl') as writer:
        portfolio_df.to_excel(writer, sheet_name='PortfolioSetting', index=False)
        strategy_df.to_excel(writer, sheet_name='StrategySetting', index=False)
    
    print(f"✅ Created master portfolio with {len(all_strategies)} strategies")
    print(f"📁 Location: {portfolio_file}")
    
    return portfolio_file, len(all_strategies)

def main():
    """Main execution"""
    
    print("="*80)
    print("CREATING COMPREHENSIVE TEST SCENARIOS")
    print("="*80)
    print(f"Timestamp: {datetime.now()}")
    
    # Create directory structure
    base_dir = create_test_directories()
    print(f"\n📁 Base directory: {base_dir}")
    
    # Create all test scenarios
    create_strike_selection_tests(base_dir)
    create_risk_management_tests(base_dir)
    create_reentry_tests(base_dir)
    create_advanced_features_tests(base_dir)
    create_edge_cases_tests(base_dir)
    
    # Create master portfolio
    portfolio_file, total_strategies = create_master_portfolio(base_dir)
    
    print("\n" + "="*80)
    print("SUMMARY")
    print("="*80)
    print(f"✅ Created {total_strategies} test strategies covering:")
    print("  - All strike selection methods (ATM, ITM, OTM, FIXED, PREMIUM)")
    print("  - All SL/TP types (PERCENT, POINTS, PREMIUM)")
    print("  - Trailing stop loss variations")
    print("  - Re-entry at leg level (ASPERSTRIKE, STRIKE)")
    print("  - Re-execution at strategy level")
    print("  - Partial exits (SqOff)")
    print("  - Hedge management")
    print("  - Time-based features")
    print("  - Complex conditions and edge cases")
    print(f"\n📁 All files saved in: {base_dir}")
    print(f"📊 Master portfolio: {portfolio_file}")
    
    return portfolio_file

if __name__ == "__main__":
    main()