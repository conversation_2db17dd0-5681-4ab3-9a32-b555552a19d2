#!/usr/bin/env python3
"""
Backtest Comparison Script

This script runs the GPU backtester with the same parameters as the golden file,
compares the outputs, and reports any differences.
"""

import os
import sys
import subprocess
import pandas as pd
import argparse
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"backtest_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("backtest_comparison")

def get_sheet_names(excel_path):
    """Get a list of sheet names from an Excel file."""
    try:
        xls = pd.ExcelFile(excel_path)
        return xls.sheet_names
    except Exception as e:
        logger.error(f"Error getting sheet names from {excel_path}: {e}")
        return []

def compare_sheets(golden_file, output_file, sheet_name):
    """Compare a specific sheet between two Excel files."""
    try:
        # Read with specific dtypes for columns that need type enforcement
        dtype_map = {'Reason': 'float64'} if sheet_name in ['PORTFOLIO Trans', 'RS,916-1200,ATM-SELL,OTM2-BUY W'] else None
        
        # Read the golden file, enforcing float64 type for Reason column
        df_golden = pd.read_excel(golden_file, sheet_name=sheet_name, dtype=dtype_map)
        
        # Read the output file, enforcing float64 type for Reason column
        df_output = pd.read_excel(output_file, sheet_name=sheet_name, dtype=dtype_map)
        
        # Check if shapes match
        if df_golden.shape != df_output.shape:
            logger.warning(f"Sheet '{sheet_name}' has different shapes: Golden {df_golden.shape} vs Output {df_output.shape}")
        
        # Check for column differences
        golden_cols = set(df_golden.columns)
        output_cols = set(df_output.columns)
        
        missing_cols = golden_cols - output_cols
        extra_cols = output_cols - golden_cols
        
        if missing_cols:
            logger.warning(f"Sheet '{sheet_name}' missing columns in output: {missing_cols}")
        if extra_cols:
            logger.warning(f"Sheet '{sheet_name}' has extra columns in output: {extra_cols}")
        
        # Compare data for common columns
        common_cols = golden_cols.intersection(output_cols)
        matching = True
        
        for col in common_cols:
            # Special handling for 'Reason' column - treat NaN and 0.0 as equivalent
            if col == 'Reason':
                # Create copies of the columns with special NaN handling
                golden_values = df_golden[col].copy()
                output_values = df_output[col].copy()
                
                # Create masks for NaN values in both columns
                golden_nan_mask = pd.isna(golden_values)
                output_nan_mask = pd.isna(output_values)
                output_zero_mask = output_values == 0.0
                
                # Check if the only difference is NaN vs 0.0
                if not golden_values.equals(output_values):
                    # If golden has NaN and output has 0.0, or if values match exactly, consider them equal
                    equivalent_mask = (golden_nan_mask & output_zero_mask) | (~golden_nan_mask & ~output_nan_mask & (golden_values == output_values))
                    
                    # Check if all values are equivalent under our special rule
                    if equivalent_mask.all():
                        logger.info(f"Sheet '{sheet_name}', column '{col}' values match with special NaN/0.0 equivalence rule")
                    else:
                        # Some values still don't match - log the issue
                        logger.warning(f"Sheet '{sheet_name}', column '{col}' values don't match even with NaN/0.0 equivalence")
                        
                        # Print sample of mismatches (up to 5)
                        try:
                            mismatches = ~equivalent_mask
                            if mismatches.any():
                                sample_indices = mismatches[mismatches].index[:5]
                                for idx in sample_indices:
                                    logger.warning(f"  Row {idx}: Golden='{df_golden[col].iloc[idx]}', Output='{df_output[col].iloc[idx]}'")
                        except Exception:
                            pass
                            
                        matching = False
                continue
            
            # For other columns, regular comparison logic
            if df_golden[col].dtype != df_output[col].dtype:
                # Try to convert to a common type for comparison
                try:
                    # Convert both to string for safer comparison
                    s1 = df_golden[col].astype(str)
                    s2 = df_output[col].astype(str)
                    if not s1.equals(s2):
                        logger.warning(f"Sheet '{sheet_name}', column '{col}' values don't match (different types: {df_golden[col].dtype} vs {df_output[col].dtype})")
                        matching = False
                except Exception as e:
                    logger.error(f"Error comparing column '{col}' in sheet '{sheet_name}': {e}")
                    matching = False
            else:
                # Same type, direct comparison
                if not df_golden[col].equals(df_output[col]):
                    logger.warning(f"Sheet '{sheet_name}', column '{col}' values don't match")
                    
                    # Print sample of mismatches (up to 5)
                    try:
                        mismatches = (df_golden[col] != df_output[col])
                        if mismatches.any():
                            sample_indices = mismatches[mismatches].index[:5]
                            for idx in sample_indices:
                                logger.warning(f"  Row {idx}: Golden='{df_golden[col].iloc[idx]}', Output='{df_output[col].iloc[idx]}'")
                    except Exception:
                        pass
                    
                    matching = False
        
        return matching
    except Exception as e:
        logger.error(f"Error comparing sheet '{sheet_name}': {e}")
        return False

def run_backtest():
    """Run the GPU backtester with updated parameters."""
    try:
        cmd = [
            "python3", 
            "bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py",
            "--portfolio-excel", "input_portfolio.xlsx",
            "--output-path", "gpu_backtest_output.xlsx",
            "--slippage", "0.1"
        ]
        logger.info(f"Running command: {' '.join(cmd)}")
        
        process = subprocess.run(cmd, 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE,
                                 text=True)
        
        logger.info(f"Command finished with return code: {process.returncode}")
        
        if process.stdout:
            logger.info(f"Command stdout:\n{process.stdout}")
        if process.stderr:
            logger.warning(f"Command stderr:\n{process.stderr}")
        
        return process.returncode == 0
    except Exception as e:
        logger.error(f"Error running backtest: {e}")
        return False

def find_golden_file(search_dir="Trades"):
    """Find the most recent golden file in the given directory."""
    try:
        if not os.path.exists(search_dir):
            logger.error(f"Search directory '{search_dir}' does not exist")
            return None
        
        excel_files = [f for f in os.listdir(search_dir) if f.endswith(".xlsx")]
        if not excel_files:
            logger.error(f"No Excel files found in '{search_dir}'")
            return None
        
        # Sort by modification time (newest first)
        excel_files.sort(key=lambda f: os.path.getmtime(os.path.join(search_dir, f)), reverse=True)
        
        golden_file_path = os.path.join(search_dir, excel_files[0])
        logger.info(f"Found golden file: {golden_file_path}")
        
        return golden_file_path
    except Exception as e:
        logger.error(f"Error finding golden file: {e}")
        return None

def main():
    parser = argparse.ArgumentParser(description='Run GPU backtest and compare with golden file')
    parser.add_argument('--golden-file', help='Path to the golden file for comparison')
    parser.add_argument('--output-file', default='gpu_backtest_output.xlsx', 
                      help='Path to save the GPU backtest output')
    
    args = parser.parse_args()
    
    # Run the backtest
    success = run_backtest()
    if not success:
        logger.error("Backtest execution failed")
        return 1
    
    # Find the golden file if not specified
    golden_file = args.golden_file
    if not golden_file:
        golden_file = find_golden_file()
        if not golden_file:
            logger.error("No golden file found or specified")
            return 1
    
    # Get sheet names from both files
    golden_sheets = get_sheet_names(golden_file)
    output_sheets = get_sheet_names(args.output_file)
    
    logger.info(f"Golden file sheets: {golden_sheets}")
    logger.info(f"Output file sheets: {output_sheets}")
    
    # Check for missing sheets
    missing_sheets = set(golden_sheets) - set(output_sheets)
    extra_sheets = set(output_sheets) - set(golden_sheets)
    
    if missing_sheets:
        logger.warning(f"Missing sheets in output: {missing_sheets}")
    if extra_sheets:
        logger.warning(f"Extra sheets in output: {extra_sheets}")
    
    # Compare common sheets
    common_sheets = set(golden_sheets).intersection(set(output_sheets))
    all_matching = True
    
    for sheet in common_sheets:
        logger.info(f"Comparing sheet: {sheet}")
        matching = compare_sheets(golden_file, args.output_file, sheet)
        if matching:
            logger.info(f"Sheet '{sheet}' matches")
        else:
            logger.warning(f"Sheet '{sheet}' has differences")
            all_matching = False
    
    if all_matching:
        logger.info("All sheets match! 🎉")
    else:
        logger.warning("Some sheets have differences")
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 