#!/usr/bin/env python3
"""
Direct GPU Backtester Test
==========================

Simple direct test of GPU backtester without wrapper complications.

Author: Senior Engineer
Date: June 9, 2025
"""

import subprocess
import sys
import os
from pathlib import Path
import pandas as pd

def run_direct_test():
    """Run direct GPU backtester test"""
    
    print("="*60)
    print("DIRECT GPU BACKTESTER TEST")
    print("="*60)
    
    # Change to backtester directory
    os.chdir("/srv/samba/shared/bt/backtester_stable/BTRUN")
    
    # Command with minimal arguments
    cmd = [
        sys.executable,
        "BTRunPortfolio_GPU.py",
        "--portfolio-excel", "/srv/samba/shared/test_results/tbs/inputs/test_portfolio_basic.xlsx",
        "--output-path", "/srv/samba/shared/test_results/tbs/gpu_outputs/direct_test.xlsx",
        "--start-date", "20240401",
        "--end-date", "20240402",
        "--cpu-only"
    ]
    
    print(f"\nCommand: {' '.join(cmd)}")
    print("\nExecuting...")
    
    # Run the command
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    print(f"\nReturn code: {result.returncode}")
    
    if result.stdout:
        print("\nSTDOUT:")
        print(result.stdout[:1000])  # First 1000 chars
        
    if result.stderr:
        print("\nSTDERR:")
        print(result.stderr[:1000])  # First 1000 chars
        
    # Check if output was created
    output_file = Path("/srv/samba/shared/test_results/tbs/gpu_outputs/direct_test.xlsx")
    if output_file.exists():
        print(f"\n✅ Output file created: {output_file}")
        try:
            # Try to read it
            df = pd.read_excel(output_file, sheet_name=None)
            print(f"   Sheets: {list(df.keys())}")
            if 'Trans' in df:
                print(f"   Transactions: {len(df['Trans'])} rows")
        except Exception as e:
            print(f"   Error reading: {e}")
    else:
        print("\n❌ No output file created")
        
    return result.returncode == 0


if __name__ == "__main__":
    success = run_direct_test()
    sys.exit(0 if success else 1)