#!/usr/bin/env python3
"""
Run archive backtester for April 1, 2024 to generate golden output
"""
import sys
import os
import json
from datetime import datetime

# Add archive backtester path
sys.path.insert(0, '/srv/samba/shared/bt/archive/backtester_stable/BTRUN')

import config
from Util import Util
import pandas as pd

# Set the dates for April 1, 2024
START_DATE = "2024-04-01"
END_DATE = "2024-04-01"

# Configure paths
config.INPUT_FILE_FOLDER = "/srv/samba/shared/bt/archive/backtester_stable/BTRUN/INPUT SHEETS"
config.PORTFOLIO_FILE_PATH = "INPUT PORTFOLIO.xlsx"

# Create output directory
output_dir = "/srv/samba/shared/archive_golden_output"
os.makedirs(output_dir, exist_ok=True)

print(f"Running archive backtester for {START_DATE}")
print(f"Using portfolio: {config.PORTFOLIO_FILE_PATH}")

try:
    # Load portfolio
    portfolioForBt, portfolioSettingDf = Util.getPortfolioJson(
        excelFilePath=os.path.join(config.INPUT_FILE_FOLDER, config.PORTFOLIO_FILE_PATH)
    )
    
    # Update dates in portfolio
    for pNo in portfolioForBt:
        portfolioForBt[pNo]['start_date'] = START_DATE
        portfolioForBt[pNo]['end_date'] = END_DATE
        print(f"Processing portfolio: {portfolioForBt[pNo]['portfolio']['name']}")
        
        # Save updated parameters
        param_file = os.path.join(output_dir, f"btpara_{START_DATE}.json")
        with open(param_file, "w") as f:
            json.dump(portfolioForBt[pNo], f, indent=4)
        
        # Run backtest
        print("Executing backtest...")
        btResp = Util.getBacktestResults(btPara=portfolioForBt[pNo])
        
        if btResp and 'strategies' in btResp and len(btResp.get('strategies', {}).get('orders', [])) > 0:
            print(f"Generated {len(btResp['strategies']['orders'])} orders")
            
            # Parse results
            parsedOrderDf, marginReqByEachStgy, maxProfitLossDf = Util.parseBacktestingResponse(
                btResponse=btResp['strategies'], 
                slippagePercent=portfolioForBt[pNo]['slippage_percent']
            )
            
            # Generate Excel output
            output_file = os.path.join(output_dir, f"Archive_Golden_Output_{START_DATE}.xlsx")
            excelGenerated = Util.generateExcelReport(
                parsedOrderDf=parsedOrderDf,
                marginReqByEachStgy=marginReqByEachStgy,
                portfolioJsonDict=portfolioForBt[pNo],
                maxProfitLossDf=maxProfitLossDf,
                portfolioSettingDf=portfolioSettingDf,
                outputFile=output_file,
                STRATEGYWISE_RESULTS=True
            )
            
            print(f"Golden output saved to: {output_file}")
            
            # Also save raw response
            raw_file = os.path.join(output_dir, f"Archive_Raw_Response_{START_DATE}.json")
            with open(raw_file, "w") as f:
                json.dump(btResp, f, indent=2)
            print(f"Raw response saved to: {raw_file}")
            
        else:
            print("No orders generated!")
            
except Exception as e:
    print(f"Error running archive backtester: {e}")
    import traceback
    traceback.print_exc()