#!/usr/bin/env python3
"""
Test script to validate DTE=0 and expiry column fixes
"""

import pandas as pd
import os
import sys
import subprocess
from datetime import datetime, timed<PERSON><PERSON>

def run_1day_dte_test():
    """Run a 1-day test with DTE=0 to verify it only trades on expiry days"""
    print("🧪 Running 1-Day DTE=0 Test")
    print("=" * 80)
    
    # Use the TBS input file with DTE=0
    input_file = "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_exit_120000.xlsx"
    output_file = f"/srv/samba/shared/Trades/test_dte0_1day_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    # Run backtest for a specific date range that includes both expiry and non-expiry days
    cmd = [
        sys.executable,
        "/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py",
        "--portfolio-excel", input_file,
        "--output-path", output_file,
        "--start-date", "240101",  # Jan 1, 2024
        "--end-date", "240107",    # Jan 7, 2024 (1 week)
        "--workers", "1",
        "--batch-days", "7",
        "--slippage", "0.1"
    ]
    
    print(f"Running command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ Backtest completed successfully")
            
            # Analyze the output
            if os.path.exists(output_file):
                analyze_dte_output(output_file)
            else:
                print("❌ Output file not created")
        else:
            print(f"❌ Backtest failed with return code: {result.returncode}")
            print(f"Error: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("❌ Backtest timed out after 5 minutes")
    except Exception as e:
        print(f"❌ Error running backtest: {e}")

def analyze_dte_output(output_file):
    """Analyze the output to check if DTE=0 and expiry are working correctly"""
    print(f"\n📊 Analyzing output: {output_file}")
    print("-" * 40)
    
    with pd.ExcelFile(output_file) as xls:
        if 'PORTFOLIO Trans' in xls.sheet_names:
            trans_df = pd.read_excel(xls, sheet_name='PORTFOLIO Trans')
            
            print(f"Total trades: {len(trans_df)}")
            
            # Check expiry column
            print("\n1. Expiry Column Check:")
            if 'Expiry' in trans_df.columns:
                non_null_expiry = trans_df['Expiry'].notna().sum()
                print(f"   • Expiry values populated: {non_null_expiry}/{len(trans_df)}")
                if non_null_expiry > 0:
                    sample_expiry = trans_df[trans_df['Expiry'].notna()]['Expiry'].iloc[0]
                    print(f"   • Sample expiry: {sample_expiry}")
                else:
                    print("   ❌ All expiry values are still NaN!")
            
            # Check trading dates
            print("\n2. DTE=0 Check:")
            if 'Entry_Date' in trans_df.columns:
                unique_dates = trans_df['Entry_Date'].unique()
                print(f"   • Dates traded: {len(unique_dates)}")
                for date in unique_dates:
                    print(f"     - {date}")
                
                # For DTE=0, we should only see Thursday dates (typical expiry)
                # or dates that match expiry dates in the data
                
            # Check filled quantity (lots)
            print("\n3. Lots Check:")
            if 'Filled_Quantity' in trans_df.columns:
                quantities = trans_df['Filled_Quantity'].value_counts()
                print(f"   • Filled quantities: {dict(quantities)}")
                print(f"   • Expected: 50 (1 lot * 50 NIFTY size)")

def create_comprehensive_column_test():
    """Create a comprehensive test for all column mappings"""
    print("\n\n🧪 Comprehensive Column Validation Tests")
    print("=" * 80)
    
    test_configs = [
        {
            "name": "DTE=0 Test",
            "file": "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_exit_120000.xlsx",
            "date_range": ["240101", "240107"],
            "expected": "Should only trade on Thursday (expiry day)"
        },
        {
            "name": "Exit Time Test",
            "file": "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_exit_120000.xlsx", 
            "check": "All trades should exit at 12:00:00"
        },
        {
            "name": "Lots Test",
            "file": "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_exit_120000.xlsx",
            "check": "Filled_Quantity should be Lots * 50"
        }
    ]
    
    for test in test_configs:
        print(f"\n📌 {test['name']}")
        print(f"   File: {os.path.basename(test['file'])}")
        if 'expected' in test:
            print(f"   Expected: {test['expected']}")
        if 'check' in test:
            print(f"   Check: {test['check']}")

def check_database_expiry_dates():
    """Check what expiry dates exist in the database for verification"""
    print("\n\n🗄️ Database Expiry Date Check")
    print("=" * 80)
    
    try:
        # Add project root to path
        sys.path.insert(0, '/srv/samba/shared')
        from bt.backtester_stable.BTRUN.core.heavydb_connection import get_connection
        
        conn = get_connection()
        cursor = conn.cursor()
        
        # Check expiry dates in January 2024
        query = """
        SELECT DISTINCT trade_date, expiry_date 
        FROM nifty_option_chain 
        WHERE trade_date >= DATE '2024-01-01' 
          AND trade_date <= DATE '2024-01-07'
          AND expiry_bucket = 'CW'
        ORDER BY trade_date
        LIMIT 10
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        
        print("Trade Date -> Expiry Date mapping:")
        for trade_date, expiry_date in results:
            is_expiry = "✅ EXPIRY DAY" if trade_date == expiry_date else ""
            print(f"   {trade_date} -> {expiry_date} {is_expiry}")
            
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")

def main():
    print("🔍 DTE=0 and Expiry Column Fix Validation")
    print("=" * 80)
    
    # Run 1-day test
    run_1day_dte_test()
    
    # Create comprehensive tests
    create_comprehensive_column_test()
    
    # Check database
    check_database_expiry_dates()
    
    print("\n\n✅ Test Plan Complete")
    print("Next steps:")
    print("1. Run the backtest with DTE=0 input file")
    print("2. Verify only expiry days are traded")
    print("3. Verify expiry column is populated")
    print("4. Run multi-worker GPU test")

if __name__ == "__main__":
    main()