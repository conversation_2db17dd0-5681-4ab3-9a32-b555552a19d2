#!/usr/bin/env python3
"""
Run simple test with 5 strategies first
"""

import subprocess
import os
from datetime import datetime

def run_simple_test():
    """Run simple test with GPU optimization"""
    
    print("="*80)
    print("SIMPLE TEST - 5 STRATEGIES")
    print("="*80)
    
    portfolio_file = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/comprehensive_tests/SIMPLE_TEST_PORTFOLIO.xlsx'
    output_file = f'/srv/samba/shared/simple_test_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
    
    cmd = [
        'python3',
        '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py',
        '--portfolio-excel', portfolio_file,
        '--output-path', output_file,
        '--workers', 'auto',
        '--batch-days', '7',
        '--merge-output',
        '--use-gpu-optimization',
        '--gpu-workers', '4'
    ]
    
    # Set GPU environment
    env = os.environ.copy()
    env['USE_GPU'] = '1'
    
    print("Command:")
    print(' '.join(cmd))
    print("\nRunning simple test...")
    print("-"*80)
    
    # Run backtest
    result = subprocess.run(cmd, env=env)
    
    if result.returncode == 0:
        print(f"\n✅ Test completed successfully!")
        print(f"Output: {output_file}")
        
        # Quick check
        import pandas as pd
        xls = pd.ExcelFile(output_file)
        print(f"\nSheets in output: {xls.sheet_names}")
        
        # Count trades
        total_trades = 0
        for sheet in xls.sheet_names:
            if 'Trans' in sheet:
                df = pd.read_excel(output_file, sheet_name=sheet)
                trades = len(df)
                if trades > 0:
                    print(f"  {sheet}: {trades} trades")
                    total_trades += trades
        
        print(f"\nTotal trades across all strategies: {total_trades}")
        
    else:
        print(f"\n❌ Test failed with return code: {result.returncode}")

if __name__ == "__main__":
    run_simple_test()