#!/usr/bin/env python3
"""
Direct test of golden format functionality
"""

import os
import sys
import pandas as pd
import logging

# Add path to import modules
sys.path.insert(0, '/srv/samba/shared')

from bt.backtester_stable.BTRUN.utils.golden_format_converter import GoldenFormatConverter
from bt.backtester_stable.BTRUN.utils.io_golden import prepare_output_file_golden

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_golden_format_conversion():
    """Test the golden format conversion directly."""
    
    logger.info("=== Testing Golden Format Conversion ===")
    
    # Create sample transaction data matching GPU output
    sample_trades = {
        'portfolio_name': ['TEST_PORTFOLIO'] * 4,
        'strategy_name': ['ATM_STRADDLE'] * 4,
        'id': [1, 2, 3, 4],
        'entry_datetime': ['2024-04-01 09:16:00'] * 4,
        'entry_time': [91600] * 4,
        'entry_day': ['Monday'] * 4,
        'exit_datetime': ['2024-04-01 15:25:00'] * 4,
        'exit_time': [152500] * 4,
        'exit_day': ['Monday'] * 4,
        'symbol': ['NIFTY01APR2422500CE', 'NIFTY01APR2422500PE', 'NIFTY01APR2422500CE', 'NIFTY01APR2422500PE'],
        'strike': [22500, 22500, 22500, 22500],
        'option_type': ['CE', 'PE', 'CE', 'PE'],
        'transaction_type': ['SELL', 'SELL', 'SELL', 'SELL'],
        'quantity': [50, 50, 50, 50],
        'entry_price': [118.0, 141.15, 118.0, 141.15],
        'exit_price': [125.0, 135.0, 125.0, 135.0],
        'points': [-7.0, 6.15, -7.0, 6.15],
        'pnl': [-350.0, 307.5, -350.0, 307.5],
        'net_pnl': [-355.0, 302.5, -355.0, 302.5],
        'index_at_entry': [22476.85] * 4,
        'index_at_exit': [22490.0] * 4,
        'reason': ['Exit Time Hit'] * 4
    }
    
    df = pd.DataFrame(sample_trades)
    
    # Test conversion
    logger.info(f"Original columns: {df.columns.tolist()}")
    logger.info(f"Original shape: {df.shape}")
    
    # Convert to golden format
    golden_df = GoldenFormatConverter.convert_portfolio_trans(df)
    
    logger.info(f"\nGolden format columns: {golden_df.columns.tolist()}")
    logger.info(f"Golden format shape: {golden_df.shape}")
    logger.info(f"Number of columns: {len(golden_df.columns)}")
    
    # Check if we have all 32 columns
    expected_columns = [
        'Portfolio Name', 'Strategy Name', 'ID', 'Entry Date', 'Enter On', 'Entry Day',
        'Exit Date', 'Exit at', 'Exit Day', 'Index', 'Expiry', 'Strike', 'CE/PE', 'Trade',
        'Qty', 'Entry at', 'Exit at.1', 'Points', 'Points After Slippage', 'PNL',
        'AfterSlippage', 'Taxes', 'Net PNL', 'Re-entry No', 'SL Re-entry No',
        'TGT Re-entry No', 'Reason', 'Strategy Entry No', 'Index At Entry',
        'Index At Exit', 'MaxProfit', 'MaxLoss'
    ]
    
    missing_columns = set(expected_columns) - set(golden_df.columns)
    if missing_columns:
        logger.error(f"Missing columns: {missing_columns}")
    else:
        logger.info("✅ All 32 required columns present!")
    
    # Test full golden format output
    output_path = '/srv/samba/shared/test_results/golden_format_test/direct_golden_test.xlsx'
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    transaction_dfs = {'portfolio': df}
    
    try:
        prepare_output_file_golden(
            output_path=output_path,
            metrics_df=pd.DataFrame(),
            transaction_dfs=transaction_dfs,
            day_stats={},
            month_stats={},
            margin_stats={},
            daily_max_pl_df=pd.DataFrame(),
            portfolio_model=None
        )
        
        logger.info(f"\n✅ Golden format file created: {output_path}")
        
        # Verify the output
        xl = pd.ExcelFile(output_path)
        logger.info(f"Sheets created: {xl.sheet_names}")
        
        # Check PORTFOLIO Trans
        trans_df = pd.read_excel(xl, 'PORTFOLIO Trans')
        logger.info(f"PORTFOLIO Trans columns: {len(trans_df.columns)}")
        logger.info(f"PORTFOLIO Trans shape: {trans_df.shape}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error creating golden format: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_golden_format_conversion()
    if success:
        logger.info("\n✅ Golden format conversion test PASSED!")
    else:
        logger.error("\n❌ Golden format conversion test FAILED!")