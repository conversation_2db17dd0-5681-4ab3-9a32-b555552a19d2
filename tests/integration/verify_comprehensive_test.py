#!/usr/bin/env python3
"""
Verify comprehensive test results
"""

import pandas as pd
import os

def verify_results():
    output_file = '/srv/samba/shared/comprehensive_test_results_20250530_092751.xlsx'
    
    if not os.path.exists(output_file):
        print("❌ Output file not found!")
        return
    
    print("="*80)
    print("COMPREHENSIVE TEST VERIFICATION")
    print("="*80)
    
    # Read Excel file
    xls = pd.ExcelFile(output_file)
    print(f"\nSheets available: {xls.sheet_names}")
    
    # Check each strategy
    strategy_results = {}
    
    for sheet in xls.sheet_names:
        if sheet.endswith('Trans'):
            df = pd.read_excel(output_file, sheet_name=sheet)
            if len(df) > 0:
                strategy_name = sheet.replace(' Trans', '')
                strategy_results[strategy_name] = {
                    'trades': len(df),
                    'exit_reasons': df['Reason'].value_counts() if 'Reason' in df.columns else {},
                    'total_pnl': df['Netpnlafterexpenses'].sum() if 'Netpnlafterexpenses' in df.columns else 0
                }
    
    # Print verification results
    print("\n1. STRATEGY EXECUTION SUMMARY:")
    print("-"*60)
    for strategy, results in strategy_results.items():
        print(f"\n{strategy}:")
        print(f"  Trades: {results['trades']}")
        print(f"  Total P&L: ₹{results['total_pnl']:,.2f}")
        print("  Exit reasons:")
        for reason, count in results['exit_reasons'].items():
            print(f"    - {reason}: {count}")
    
    # Check specific features
    print("\n2. FEATURE VERIFICATION:")
    print("-"*60)
    
    # Check SL/TP exits
    sl_hits = sum(1 for s, r in strategy_results.items() 
                  for reason in r['exit_reasons'] if 'SL' in str(reason))
    tp_hits = sum(1 for s, r in strategy_results.items() 
                  for reason in r['exit_reasons'] if 'TGT' in str(reason))
    
    print(f"✓ Stop Loss hits: {sl_hits}")
    print(f"✓ Target hits: {tp_hits}")
    
    # Check re-entries
    if 'PORTFOLIO Trans' in xls.sheet_names:
        portfolio_df = pd.read_excel(output_file, sheet_name='PORTFOLIO Trans')
        if 'Re_Entry_No' in portfolio_df.columns:
            max_reentry = portfolio_df['Re_Entry_No'].max()
            reentry_trades = len(portfolio_df[portfolio_df['Re_Entry_No'] > 0])
            print(f"✓ Re-entries detected: {reentry_trades} trades")
            print(f"✓ Maximum re-entry number: {max_reentry}")
    
    print("\n✅ Verification complete!")

if __name__ == "__main__":
    verify_results()
