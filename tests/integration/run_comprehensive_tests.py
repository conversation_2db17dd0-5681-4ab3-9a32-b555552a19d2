#!/usr/bin/env python3
"""
Run comprehensive tests on both archive and new systems using the same input files.
Compare outputs trade-by-trade and generate detailed comparison reports.
"""

import os
import subprocess
import pandas as pd
import numpy as np
from datetime import datetime
import json
import logging
from typing import Dict, List, Tuple
import asyncio
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('comprehensive_test_run.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Paths
ARCHIVE_DIR = "/srv/samba/shared/bt/archive/backtester_stable/BTRUN"
NEW_SYSTEM_DIR = "/srv/samba/shared/bt/backtester_stable/BTRUN"
INPUT_SHEETS_DIR = "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets"
COMPREHENSIVE_TESTS_DIR = f"{INPUT_SHEETS_DIR}/comprehensive_tests"
OUTPUT_DIR = "/srv/samba/shared/test_outputs"
COMPARISON_DIR = "/srv/samba/shared/test_comparisons"

# Create output directories
os.makedirs(OUTPUT_DIR, exist_ok=True)
os.makedirs(f"{OUTPUT_DIR}/archive", exist_ok=True)
os.makedirs(f"{OUTPUT_DIR}/new_system", exist_ok=True)
os.makedirs(COMPARISON_DIR, exist_ok=True)

class ComprehensiveTestRunner:
    """Run comprehensive tests on both systems and compare results"""
    
    def __init__(self):
        self.test_cases = self.load_test_cases()
        self.results = {}
        
    def load_test_cases(self) -> Dict[str, List[Dict]]:
        """Load all test cases for each strategy type"""
        
        test_cases = {
            'oi': [
                {
                    'name': 'OI_Comprehensive_All_Columns',
                    'input_file': f"{COMPREHENSIVE_TESTS_DIR}/oi/comprehensive_oi_test_all_columns.xlsx",
                    'description': 'Test all OI columns including MAXOI, MAXCOI, thresholds'
                },
                {
                    'name': 'OI_Archive_Format',
                    'input_file': f"{COMPREHENSIVE_TESTS_DIR}/oi/archive_format_oi_test.xlsx",
                    'description': 'Test archive format compatibility'
                },
                {
                    'name': 'OI_Standard',
                    'input_file': f"{INPUT_SHEETS_DIR}/oi/input_maxoi.xlsx",
                    'description': 'Standard OI test file'
                }
            ],
            'tbs': [
                {
                    'name': 'TBS_Comprehensive_Portfolio',
                    'input_file': f"{COMPREHENSIVE_TESTS_DIR}/tbs/comprehensive_tbs_portfolio.xlsx",
                    'description': 'Comprehensive TBS portfolio test'
                },
                {
                    'name': 'TBS_Simple',
                    'input_file': f"{INPUT_SHEETS_DIR}/tbs/input_portfolio.xlsx",
                    'secondary_file': f"{INPUT_SHEETS_DIR}/tbs/input_tbs_portfolio.xlsx",
                    'description': 'Simple TBS test'
                },
                {
                    'name': 'TBS_Risk_Management',
                    'input_file': f"{COMPREHENSIVE_TESTS_DIR}/risk_management/all_sl_types.xlsx",
                    'description': 'All SL types test'
                }
            ],
            'tv': [
                {
                    'name': 'TV_Comprehensive',
                    'input_file': f"{COMPREHENSIVE_TESTS_DIR}/tv/comprehensive_tv_config.xlsx",
                    'signal_file': f"{COMPREHENSIVE_TESTS_DIR}/tv/comprehensive_signals.xlsx",
                    'description': 'Comprehensive TV test with signals'
                },
                {
                    'name': 'TV_Standard',
                    'input_file': f"{INPUT_SHEETS_DIR}/tv/input_tv.xlsx",
                    'signal_file': f"{INPUT_SHEETS_DIR}/tv/sample_nifty_list_of_trades.xlsx",
                    'portfolio_files': [
                        f"{INPUT_SHEETS_DIR}/tv/input_portfolio_long.xlsx",
                        f"{INPUT_SHEETS_DIR}/tv/input_portfolio_short.xlsx"
                    ],
                    'tbs_files': [
                        f"{INPUT_SHEETS_DIR}/tv/input_tbs_long.xlsx",
                        f"{INPUT_SHEETS_DIR}/tv/input_tbs_short.xlsx"
                    ],
                    'description': 'Standard TV 6-file hierarchy test'
                }
            ],
            'orb': [
                {
                    'name': 'ORB_Comprehensive',
                    'input_file': f"{COMPREHENSIVE_TESTS_DIR}/orb/comprehensive_orb_test.xlsx",
                    'description': 'Comprehensive ORB test with all features'
                },
                {
                    'name': 'ORB_Standard',
                    'input_file': f"{INPUT_SHEETS_DIR}/orb/input_orb.xlsx",
                    'description': 'Standard ORB test'
                }
            ],
            'pos': [
                {
                    'name': 'POS_Comprehensive',
                    'input_file': f"{COMPREHENSIVE_TESTS_DIR}/pos/comprehensive_pos_portfolio.xlsx",
                    'description': 'Comprehensive POS test (new system only)'
                },
                {
                    'name': 'POS_Iron_Fly',
                    'input_file': f"{INPUT_SHEETS_DIR}/pos/input_iron_fly_strategy.xlsx",
                    'description': 'Iron Fly strategy test'
                }
            ],
            'ml_indicator': [
                {
                    'name': 'ML_Comprehensive',
                    'input_file': f"{COMPREHENSIVE_TESTS_DIR}/ml_indicator/comprehensive_ml_config.xlsx",
                    'description': 'Comprehensive ML Indicator test (new system only)'
                }
            ]
        }
        
        return test_cases
    
    async def run_archive_system(self, strategy_type: str, test_case: Dict) -> str:
        """Run test on archive system"""
        
        logger.info(f"Running archive system test: {test_case['name']}")
        
        # Copy input files to archive directory
        input_file = test_case['input_file']
        filename = os.path.basename(input_file)
        archive_input = os.path.join(ARCHIVE_DIR, filename)
        
        # Copy file
        subprocess.run(['cp', input_file, archive_input], check=True)
        
        # For TBS with secondary file
        if 'secondary_file' in test_case:
            secondary = test_case['secondary_file']
            subprocess.run(['cp', secondary, ARCHIVE_DIR], check=True)
        
        # For TV with multiple files
        if strategy_type == 'tv' and 'signal_file' in test_case:
            for key in ['signal_file', 'portfolio_files', 'tbs_files']:
                if key in test_case:
                    files = test_case[key] if isinstance(test_case[key], list) else [test_case[key]]
                    for f in files:
                        if f:
                            subprocess.run(['cp', f, ARCHIVE_DIR], check=True)
        
        # Run archive system
        output_file = f"{OUTPUT_DIR}/archive/{test_case['name']}_output.xlsx"
        
        cmd = f"cd /srv/samba/shared/bt/archive && python3 -m backtester_stable.BTRUN.BTRunPortfolio --input {archive_input} --start 01-04-2024 --end 05-04-2024"
        
        try:
            # Set environment for Python path
            env = os.environ.copy()
            env['PYTHONPATH'] = '/srv/samba/shared/bt/archive:' + env.get('PYTHONPATH', '')
            
            result = subprocess.run(
                cmd,
                cwd=ARCHIVE_DIR,
                capture_output=True,
                text=True,
                timeout=300,  # 5 minute timeout
                shell=True,
                env=env
            )
            
            if result.returncode == 0:
                logger.info(f"Archive test {test_case['name']} completed successfully")
                # Find and copy output file
                self._copy_archive_output(test_case['name'], output_file)
                return output_file
            else:
                logger.error(f"Archive test {test_case['name']} failed: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            logger.error(f"Archive test {test_case['name']} timed out")
            return None
        except Exception as e:
            logger.error(f"Error running archive test {test_case['name']}: {e}")
            return None
    
    async def run_new_system(self, strategy_type: str, test_case: Dict) -> str:
        """Run test on new system"""
        
        logger.info(f"Running new system test: {test_case['name']}")
        
        # New system uses the files directly from input_sheets
        output_file = f"{OUTPUT_DIR}/new_system/{test_case['name']}_output.xlsx"
        
        # Determine the correct command based on strategy type
        if strategy_type == 'tbs':
            cmd = f"cd /srv/samba/shared/bt && python3 -m backtester_stable.BTRUN.BTRunPortfolio --input {test_case['input_file']} --start 01-04-2024 --end 05-04-2024"
        elif strategy_type == 'tv':
            cmd = f"cd /srv/samba/shared/bt && python3 -m backtester_stable.BTRUN.BT_TV_GPU --config {test_case['input_file']} --start-date 240401 --end-date 240405"
        elif strategy_type == 'orb':
            cmd = f"cd /srv/samba/shared/bt && python3 -m backtester_stable.BTRUN.BT_ORB_GPU --input {test_case['input_file']} --start 20240401 --end 20240405"
        elif strategy_type == 'oi':
            cmd = f"cd /srv/samba/shared/bt && python3 -m backtester_stable.BTRUN.BT_OI_GPU --portfolio-excel {test_case['input_file']} --start-date 240401 --end-date 240405"
        elif strategy_type == 'pos':
            cmd = f"cd /srv/samba/shared/bt && python3 -m backtester_stable.BTRUN.BT_POS_GPU --input {test_case['input_file']} --start 20240401 --end 20240405"
        elif strategy_type == 'ml_indicator':
            cmd = f"cd /srv/samba/shared/bt && python3 -m backtester_stable.BTRUN.BT_ML_GPU --config {test_case['input_file']} --start 20240401 --end 20240405"
        
        try:
            # Set environment for Python path
            env = os.environ.copy()
            env['PYTHONPATH'] = '/srv/samba/shared:' + env.get('PYTHONPATH', '')
            
            result = subprocess.run(
                cmd,
                cwd=NEW_SYSTEM_DIR,
                capture_output=True,
                text=True,
                timeout=300,
                shell=True,
                env=env
            )
            
            if result.returncode == 0:
                logger.info(f"New system test {test_case['name']} completed successfully")
                return output_file
            else:
                logger.error(f"New system test {test_case['name']} failed: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            logger.error(f"New system test {test_case['name']} timed out")
            return None
        except Exception as e:
            logger.error(f"Error running new system test {test_case['name']}: {e}")
            return None
    
    def compare_outputs(self, archive_output: str, new_output: str, test_name: str) -> Dict:
        """Compare outputs trade by trade"""
        
        logger.info(f"Comparing outputs for {test_name}")
        
        try:
            # Load both outputs
            archive_df = pd.read_excel(archive_output)
            new_df = pd.read_excel(new_output)
            
            comparison = {
                'test_name': test_name,
                'archive_trades': len(archive_df),
                'new_trades': len(new_df),
                'trade_count_match': len(archive_df) == len(new_df),
                'differences': []
            }
            
            # Compare trade by trade
            for idx in range(min(len(archive_df), len(new_df))):
                arch_trade = archive_df.iloc[idx]
                new_trade = new_df.iloc[idx]
                
                # Check key fields
                diffs = {}
                
                # Entry time (allow 1 minute tolerance)
                if abs((arch_trade['entry_time'] - new_trade['entry_time']).seconds) > 60:
                    diffs['entry_time'] = {
                        'archive': arch_trade['entry_time'],
                        'new': new_trade['entry_time']
                    }
                
                # Strike (allow 50 points for ATM difference)
                if abs(arch_trade['strike'] - new_trade['strike']) > 50:
                    diffs['strike'] = {
                        'archive': arch_trade['strike'],
                        'new': new_trade['strike'],
                        'difference': new_trade['strike'] - arch_trade['strike']
                    }
                
                # P&L (allow 0.1% variance)
                pnl_var = abs(arch_trade['pnl'] - new_trade['pnl']) / abs(arch_trade['pnl'])
                if pnl_var > 0.001:
                    diffs['pnl'] = {
                        'archive': arch_trade['pnl'],
                        'new': new_trade['pnl'],
                        'variance': pnl_var
                    }
                
                if diffs:
                    comparison['differences'].append({
                        'trade_idx': idx,
                        'differences': diffs
                    })
            
            # Calculate match percentage
            match_pct = (1 - len(comparison['differences']) / max(1, comparison['archive_trades'])) * 100
            comparison['match_percentage'] = match_pct
            
            # Save comparison report
            report_file = f"{COMPARISON_DIR}/{test_name}_comparison.json"
            with open(report_file, 'w') as f:
                json.dump(comparison, f, indent=2, default=str)
            
            logger.info(f"Comparison complete for {test_name}: {match_pct:.2f}% match")
            
            return comparison
            
        except Exception as e:
            logger.error(f"Error comparing outputs for {test_name}: {e}")
            return {
                'test_name': test_name,
                'error': str(e)
            }
    
    def _copy_archive_output(self, test_name: str, output_path: str):
        """Find and copy archive system output file"""
        
        # Archive system creates output in specific format
        # Look for most recent Excel file
        import glob
        excel_files = glob.glob(f"{ARCHIVE_DIR}/*.xlsx")
        
        if excel_files:
            # Get most recent file
            latest_file = max(excel_files, key=os.path.getctime)
            subprocess.run(['cp', latest_file, output_path], check=True)
            logger.info(f"Copied archive output from {latest_file} to {output_path}")
    
    async def run_all_tests(self):
        """Run all comprehensive tests"""
        
        logger.info("Starting comprehensive test execution")
        
        overall_results = {}
        
        for strategy_type, test_cases in self.test_cases.items():
            logger.info(f"\n{'='*60}")
            logger.info(f"Testing {strategy_type.upper()} Strategy")
            logger.info(f"{'='*60}")
            
            strategy_results = []
            
            for test_case in test_cases:
                logger.info(f"\nRunning test: {test_case['name']}")
                logger.info(f"Description: {test_case['description']}")
                
                # Skip archive for POS and ML (new system only)
                if strategy_type in ['pos', 'ml_indicator']:
                    logger.info(f"{strategy_type.upper()} is new system only - skipping archive")
                    new_output = await self.run_new_system(strategy_type, test_case)
                    
                    result = {
                        'test_name': test_case['name'],
                        'archive_output': None,
                        'new_output': new_output,
                        'comparison': None,
                        'new_system_only': True
                    }
                else:
                    # Run on both systems
                    archive_output = await self.run_archive_system(strategy_type, test_case)
                    new_output = await self.run_new_system(strategy_type, test_case)
                    
                    # Compare if both outputs exist
                    if archive_output and new_output:
                        comparison = self.compare_outputs(archive_output, new_output, test_case['name'])
                    else:
                        comparison = None
                    
                    result = {
                        'test_name': test_case['name'],
                        'archive_output': archive_output,
                        'new_output': new_output,
                        'comparison': comparison,
                        'new_system_only': False
                    }
                
                strategy_results.append(result)
            
            overall_results[strategy_type] = strategy_results
        
        # Generate final report
        self.generate_final_report(overall_results)
        
        return overall_results
    
    def generate_final_report(self, results: Dict):
        """Generate comprehensive test report"""
        
        logger.info("\n" + "="*60)
        logger.info("COMPREHENSIVE TEST RESULTS SUMMARY")
        logger.info("="*60)
        
        report = {
            'test_run': datetime.now().isoformat(),
            'summary': {},
            'details': results
        }
        
        total_tests = 0
        passed_tests = 0
        
        for strategy, strategy_results in results.items():
            strategy_summary = {
                'total': len(strategy_results),
                'passed': 0,
                'failed': 0,
                'errors': 0
            }
            
            for result in strategy_results:
                total_tests += 1
                
                if result.get('new_system_only'):
                    if result['new_output']:
                        strategy_summary['passed'] += 1
                        passed_tests += 1
                    else:
                        strategy_summary['failed'] += 1
                else:
                    if result['comparison'] and 'match_percentage' in result['comparison']:
                        if result['comparison']['match_percentage'] >= 95:
                            strategy_summary['passed'] += 1
                            passed_tests += 1
                        else:
                            strategy_summary['failed'] += 1
                    else:
                        strategy_summary['errors'] += 1
            
            report['summary'][strategy] = strategy_summary
            
            # Print summary
            logger.info(f"\n{strategy.upper()}:")
            logger.info(f"  Total Tests: {strategy_summary['total']}")
            logger.info(f"  Passed: {strategy_summary['passed']}")
            logger.info(f"  Failed: {strategy_summary['failed']}")
            logger.info(f"  Errors: {strategy_summary['errors']}")
        
        # Overall summary
        report['summary']['overall'] = {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0
        }
        
        logger.info(f"\nOVERALL:")
        logger.info(f"  Total Tests: {total_tests}")
        logger.info(f"  Passed: {passed_tests}")
        logger.info(f"  Success Rate: {report['summary']['overall']['success_rate']:.2f}%")
        
        # Save detailed report
        report_file = f"{COMPARISON_DIR}/comprehensive_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"\nDetailed report saved to: {report_file}")
        
        # Generate HTML report
        self.generate_html_report(report, report_file.replace('.json', '.html'))
    
    def generate_html_report(self, report: Dict, output_file: str):
        """Generate HTML report for better visualization"""
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Comprehensive Test Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                h1, h2, h3 {{ color: #333; }}
                table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .passed {{ color: green; font-weight: bold; }}
                .failed {{ color: red; font-weight: bold; }}
                .error {{ color: orange; font-weight: bold; }}
                .summary-box {{ background-color: #f9f9f9; padding: 15px; margin: 20px 0; border-radius: 5px; }}
            </style>
        </head>
        <body>
            <h1>Comprehensive Test Report</h1>
            <p>Generated: {report['test_run']}</p>
            
            <div class="summary-box">
                <h2>Overall Summary</h2>
                <p>Total Tests: {report['summary']['overall']['total_tests']}</p>
                <p>Passed: <span class="passed">{report['summary']['overall']['passed_tests']}</span></p>
                <p>Success Rate: <span class="{'passed' if report['summary']['overall']['success_rate'] >= 90 else 'failed'}">{report['summary']['overall']['success_rate']:.2f}%</span></p>
            </div>
            
            <h2>Strategy Results</h2>
            <table>
                <tr>
                    <th>Strategy</th>
                    <th>Total</th>
                    <th>Passed</th>
                    <th>Failed</th>
                    <th>Errors</th>
                    <th>Success Rate</th>
                </tr>
        """
        
        for strategy, summary in report['summary'].items():
            if strategy != 'overall':
                success_rate = (summary['passed'] / summary['total'] * 100) if summary['total'] > 0 else 0
                html_content += f"""
                <tr>
                    <td>{strategy.upper()}</td>
                    <td>{summary['total']}</td>
                    <td class="passed">{summary['passed']}</td>
                    <td class="failed">{summary['failed']}</td>
                    <td class="error">{summary['errors']}</td>
                    <td class="{'passed' if success_rate >= 90 else 'failed'}">{success_rate:.2f}%</td>
                </tr>
                """
        
        html_content += """
            </table>
            
            <h2>Detailed Test Results</h2>
        """
        
        # Add detailed results for each test
        for strategy, results in report['details'].items():
            html_content += f"<h3>{strategy.upper()} Tests</h3>"
            
            for result in results:
                status = "N/A"
                if result.get('new_system_only'):
                    status = "PASSED" if result['new_output'] else "FAILED"
                elif result.get('comparison'):
                    if 'match_percentage' in result['comparison']:
                        match_pct = result['comparison']['match_percentage']
                        status = f"{'PASSED' if match_pct >= 95 else 'FAILED'} ({match_pct:.2f}% match)"
                else:
                    status = "ERROR"
                
                html_content += f"""
                <div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd;">
                    <h4>{result['test_name']}</h4>
                    <p>Status: <span class="{'passed' if 'PASSED' in status else 'failed'}">{status}</span></p>
                """
                
                if result.get('comparison') and 'differences' in result['comparison']:
                    html_content += f"<p>Differences found: {len(result['comparison']['differences'])}</p>"
                
                html_content += "</div>"
        
        html_content += """
        </body>
        </html>
        """
        
        with open(output_file, 'w') as f:
            f.write(html_content)
        
        logger.info(f"HTML report saved to: {output_file}")

async def main():
    """Main execution function"""
    
    runner = ComprehensiveTestRunner()
    
    # Run all tests
    results = await runner.run_all_tests()
    
    logger.info("\n" + "="*60)
    logger.info("COMPREHENSIVE TESTING COMPLETE")
    logger.info("="*60)
    
    # Print final instructions
    logger.info("\nNext Steps:")
    logger.info("1. Review the comparison reports in: " + COMPARISON_DIR)
    logger.info("2. Investigate any failures or mismatches")
    logger.info("3. Update the code to fix any issues")
    logger.info("4. Re-run failed tests")
    logger.info("5. Document any acceptable differences (e.g., ATM calculations)")

if __name__ == "__main__":
    asyncio.run(main())