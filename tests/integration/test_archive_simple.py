#!/usr/bin/env python3
"""Simple test of archive backtester"""
import sys
sys.path.insert(0, '/srv/samba/shared/bt/archive/backtester_stable/BTRUN')

try:
    print("Importing config...")
    import config
    print(f"Config imported. USE_LOCAL_ENGINE = {config.USE_LOCAL_ENGINE}")
    
    print("\nImporting Util...")
    from Util import Util
    print("Util imported successfully")
    
    print("\nImporting LocalBacktestEngine...")
    from LocalBacktestEngine import LocalBacktestEngine
    print("LocalBacktestEngine imported successfully")
    
    # Test database connection
    print("\nTesting database connection...")
    import mysql.connector
    conn = mysql.connector.connect(
        host='localhost',
        user='root',
        password='password',
        database='NSEFO'
    )
    print("Database connected successfully")
    conn.close()
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()