#!/usr/bin/env python3
"""
Iterative Test Runner with MCP Browser Integration
Executes GPU Backtester tests repeatedly until consistency is achieved
"""

import asyncio
import sys
import json
from datetime import datetime
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import the test framework
sys.path.append('/srv/samba/shared')
from test_gpu_backtester_iterative import IterativeGPUBacktesterTestSuite, TestIteration

# Test configuration
ITERATION_CONFIG = {
    "max_iterations": 5,
    "consistency_threshold": 3,
    "retry_delay": 2,  # seconds between retries
    "parallel_execution": False,  # Run tests sequentially for consistency
    "auto_correct": True,  # Attempt to auto-correct issues
    "detailed_logging": True
}


class MCPIterativeTestRunner:
    """Runner for iterative tests with MCP browser control"""
    
    def __init__(self):
        self.config = ITERATION_CONFIG
        self.correction_attempts = {}
        self.test_suite = None
        
    async def initialize(self):
        """Initialize test environment and MCP browser"""
        logger.info("Initializing iterative test environment...")
        
        # Initialize browser via MCP
        logger.info("Installing/verifying browser via MCP...")
        # Would call mcp__playwright__browser_install
        
        # Create test suite
        self.test_suite = IterativeGPUBacktesterTestSuite()
        await self.test_suite.setup()
        
        logger.info("Test environment initialized successfully")
        
    async def run_with_auto_correction(self, test_name: str, test_func):
        """Run test with automatic issue correction"""
        logger.info(f"Running {test_name} with auto-correction enabled")
        
        iteration = 1
        consistent_results = []
        last_error = None
        
        while iteration <= self.config["max_iterations"]:
            try:
                # Run test
                result = await test_func(iteration)
                
                # Check if correction needed
                if result.status == "FAIL" and self.config["auto_correct"]:
                    correction_applied = await self.apply_corrections(test_name, result)
                    if correction_applied:
                        logger.info(f"Applied corrections for {test_name}, retrying...")
                        await asyncio.sleep(self.config["retry_delay"])
                        continue
                        
                # Track consistent results
                if result.status == "PASS":
                    consistent_results.append(result)
                    
                    # Check if we have enough consistent passes
                    if len(consistent_results) >= self.config["consistency_threshold"]:
                        logger.info(f"✓ {test_name} achieved consistency after {iteration} iterations")
                        return True, consistent_results
                else:
                    # Reset consistency counter on failure
                    consistent_results = []
                    last_error = result.errors[0] if result.errors else "Unknown error"
                    
                iteration += 1
                
            except Exception as e:
                logger.error(f"Exception in {test_name} iteration {iteration}: {e}")
                last_error = str(e)
                iteration += 1
                
            # Wait before next iteration
            if iteration <= self.config["max_iterations"]:
                await asyncio.sleep(self.config["retry_delay"])
                
        # Failed to achieve consistency
        logger.warning(f"✗ {test_name} failed to achieve consistency after {iteration-1} iterations")
        logger.warning(f"Last error: {last_error}")
        return False, consistent_results
        
    async def apply_corrections(self, test_name: str, result: TestIteration):
        """Apply automatic corrections based on test failures"""
        logger.info(f"Attempting to correct issues in {test_name}")
        
        corrections_applied = False
        
        # Track correction attempts
        if test_name not in self.correction_attempts:
            self.correction_attempts[test_name] = 0
        self.correction_attempts[test_name] += 1
        
        # Limit correction attempts
        if self.correction_attempts[test_name] > 3:
            logger.warning(f"Max correction attempts reached for {test_name}")
            return False
            
        # Apply corrections based on specific errors
        for error in result.errors:
            if "element not found" in error.lower():
                # Wait longer for page load
                logger.info("Applying correction: Increasing wait time for elements")
                await asyncio.sleep(3)
                corrections_applied = True
                
            elif "timeout" in error.lower():
                # Clear browser state
                logger.info("Applying correction: Clearing browser state")
                # Would call mcp__playwright__browser_close and restart
                await self.restart_browser()
                corrections_applied = True
                
            elif "login failed" in error.lower():
                # Clear cookies and retry login
                logger.info("Applying correction: Clearing session and retrying login")
                await self.clear_session()
                corrections_applied = True
                
            elif "file upload failed" in error.lower():
                # Verify file paths and permissions
                logger.info("Applying correction: Verifying file paths")
                await self.verify_test_files()
                corrections_applied = True
                
        return corrections_applied
        
    async def restart_browser(self):
        """Restart browser instance"""
        logger.info("Restarting browser...")
        # Would call mcp__playwright__browser_close
        await asyncio.sleep(1)
        # Would call mcp__playwright__browser_install
        logger.info("Browser restarted")
        
    async def clear_session(self):
        """Clear browser session"""
        logger.info("Clearing browser session...")
        # Navigate to logout or clear cookies
        # Would use MCP browser commands
        
    async def verify_test_files(self):
        """Verify test files exist and are accessible"""
        test_files = [
            "/srv/samba/shared/input_portfolio.xlsx",
            "/srv/samba/shared/input_tbs_multi_legs.xlsx"
        ]
        
        for file_path in test_files:
            if not Path(file_path).exists():
                logger.warning(f"Test file not found: {file_path}")
                # Could create dummy test files here
                
    async def run_all_tests(self):
        """Run all tests with iterative validation and auto-correction"""
        logger.info("Starting iterative test execution with auto-correction")
        logger.info("="*80)
        
        # Define test sequence
        test_sequence = [
            ("Authentication Page", self.test_suite.test_authentication_iterative),
            ("Navigation", self.test_suite.test_navigation_iterative),
            ("New Backtest Page", self.test_suite.test_new_backtest_iterative),
            ("Logs UI", self.test_suite.test_logs_ui_iterative),
            ("GPU Performance", self.test_suite.test_gpu_performance_iterative),
            ("Backtesting Systems", self.test_suite.test_backtesting_systems_iterative),
            ("Output Validation", self.test_suite.test_output_validation_iterative),
            ("Performance Scalability", self.test_suite.test_performance_scalability_iterative)
        ]
        
        overall_results = {
            "total_tests": len(test_sequence),
            "passed": 0,
            "failed": 0,
            "test_results": {},
            "correction_attempts": self.correction_attempts
        }
        
        for test_name, test_func in test_sequence:
            logger.info(f"\n{'='*80}")
            logger.info(f"Executing: {test_name}")
            logger.info(f"{'='*80}")
            
            # Run with auto-correction
            success, consistent_results = await self.run_with_auto_correction(
                test_name, test_func
            )
            
            if success:
                overall_results["passed"] += 1
                overall_results["test_results"][test_name] = {
                    "status": "PASS",
                    "iterations": len(consistent_results),
                    "consistency_achieved": True
                }
            else:
                overall_results["failed"] += 1
                overall_results["test_results"][test_name] = {
                    "status": "FAIL",
                    "iterations": self.config["max_iterations"],
                    "consistency_achieved": False
                }
                
            # Log progress
            logger.info(f"Progress: {overall_results['passed']}/{overall_results['total_tests']} passed")
            
        return overall_results
        
    async def generate_execution_report(self, results: dict):
        """Generate detailed execution report"""
        logger.info("Generating execution report...")
        
        timestamp = datetime.now()
        report = {
            "execution_summary": {
                "timestamp": timestamp.isoformat(),
                "total_tests": results["total_tests"],
                "passed": results["passed"],
                "failed": results["failed"],
                "success_rate": (results["passed"] / results["total_tests"] * 100) if results["total_tests"] > 0 else 0,
                "configuration": self.config
            },
            "test_details": results["test_results"],
            "correction_attempts": results["correction_attempts"],
            "recommendations": []
        }
        
        # Add recommendations
        if results["failed"] > 0:
            report["recommendations"].append("Review and fix failing tests before deployment")
            
            # Specific recommendations for failed tests
            for test_name, result in results["test_results"].items():
                if result["status"] == "FAIL":
                    report["recommendations"].append(f"Investigate consistency issues in: {test_name}")
                    
        if any(attempts > 2 for attempts in results["correction_attempts"].values()):
            report["recommendations"].append("Some tests required multiple correction attempts - investigate stability")
            
        # Save report
        report_path = f"/srv/samba/shared/iterative_execution_report_{timestamp.strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_path, "w") as f:
            json.dump(report, f, indent=2)
            
        logger.info(f"Execution report saved to: {report_path}")
        
        # Print summary
        print("\n" + "="*80)
        print("ITERATIVE TEST EXECUTION SUMMARY")
        print("="*80)
        print(f"Total Tests: {results['total_tests']}")
        print(f"Passed: {results['passed']}")
        print(f"Failed: {results['failed']}")
        print(f"Success Rate: {report['execution_summary']['success_rate']:.2f}%")
        print("\nTest Results:")
        for test_name, result in results["test_results"].items():
            status_symbol = "✓" if result["status"] == "PASS" else "✗"
            print(f"  {status_symbol} {test_name}: {result['status']} ({result['iterations']} iterations)")
        print("="*80)
        
        return report


async def main():
    """Main execution function"""
    print("""
    ╔════════════════════════════════════════════════════════════════════╗
    ║         Iterative GPU Backtester Test Runner with MCP              ║
    ║                                                                    ║
    ║  This runner executes tests iteratively with auto-correction      ║
    ║  until consistent results are achieved across multiple runs.       ║
    ║                                                                    ║
    ║  Features:                                                         ║
    ║  • Automatic retry on failures                                     ║
    ║  • Self-correction mechanisms                                      ║
    ║  • Consistency validation                                          ║
    ║  • Detailed iteration tracking                                     ║
    ║                                                                    ║
    ║  Configuration:                                                    ║
    ║  • Max Iterations: 5                                              ║
    ║  • Consistency Threshold: 3                                       ║
    ║  • Auto-Correction: Enabled                                       ║
    ╚════════════════════════════════════════════════════════════════════╝
    """)
    
    runner = MCPIterativeTestRunner()
    
    try:
        # Initialize
        await runner.initialize()
        
        # Run all tests
        results = await runner.run_all_tests()
        
        # Generate report
        report = await runner.generate_execution_report(results)
        
        # Also run the detailed iterative report from test suite
        if runner.test_suite:
            await runner.test_suite.generate_iterative_report()
            
        # Determine exit code
        exit_code = 0 if results["failed"] == 0 else 1
        
        logger.info(f"\nTest execution completed with exit code: {exit_code}")
        
        return exit_code
        
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)