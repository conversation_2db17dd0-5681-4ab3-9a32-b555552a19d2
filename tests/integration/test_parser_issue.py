#!/usr/bin/env python3
"""
Test the parser issue with Instrument values
"""

import sys
import os

# Add the path to import the parser
sys.path.insert(0, '/srv/samba/shared/bt/backtester_stable/BTRUN')

try:
    from excel_parser.strategy_parser import parse_strategy_excel
    
    # Test parsing the problematic file
    strategy_file = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/comprehensive_tests/reentry/leg_reentry_all_types.xlsx'
    
    print(f"Testing parser on: {strategy_file}")
    print("=" * 60)
    
    result = parse_strategy_excel(strategy_file)
    
    if result:
        print(f"Parse result type: {type(result)}")
        print(f"Strategy name: {result.strategy_name if hasattr(result, 'strategy_name') else 'N/A'}")
        
        if hasattr(result, 'legs'):
            print(f"\nNumber of legs: {len(result.legs)}")
            for i, leg in enumerate(result.legs):
                print(f"\nLeg {i+1}:")
                print(f"  Instrument: {leg.instrument if hasattr(leg, 'instrument') else 'N/A'}")
                print(f"  Option Type: {leg.option_type if hasattr(leg, 'option_type') else 'N/A'}")
                print(f"  Transaction: {leg.transaction if hasattr(leg, 'transaction') else 'N/A'}")
        else:
            print("No legs attribute found")
    else:
        print("Parser returned None!")
        
except ImportError as e:
    print(f"Import error: {e}")
    print("\nTrying direct pandas read...")
    
    import pandas as pd
    
    strategy_file = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/comprehensive_tests/reentry/leg_reentry_all_types.xlsx'
    df = pd.read_excel(strategy_file, sheet_name='LegParameter')
    
    print(f"\nDirect pandas read:")
    print(f"Instrument values: {df['Instrument'].tolist()}")
    print(f"Raw repr: {[repr(x) for x in df['Instrument'].tolist()]}")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()