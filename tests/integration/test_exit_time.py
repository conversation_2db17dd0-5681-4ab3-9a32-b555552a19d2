#!/usr/bin/env python3
"""
Test script to verify exit time implementation.
"""

import os
import sys
import pandas as pd
from datetime import datetime

# Add backtester_stable/BTRUN directory to sys.path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), "bt/backtester_stable/BTRUN"))

# Import models and evaluation functions
from models.risk import RiskRule, RiskRuleType, NumberType, evaluate_risk_rule
from trade_builder import build_test_trade

def test_exit_time():
    """
    Test the exit time functionality with different SL/TP values
    """
    print("Testing exit time functionality...")
    
    # Current date
    today = datetime.now().date()
    
    # Create tick data with price movements
    tick_data = pd.DataFrame({
        'datetime': [
            datetime.combine(today, datetime.strptime("09:16:00", "%H:%M:%S").time()),
            datetime.combine(today, datetime.strptime("09:30:00", "%H:%M:%S").time()),
            datetime.combine(today, datetime.strptime("10:00:00", "%H:%M:%S").time()),
            datetime.combine(today, datetime.strptime("10:30:00", "%H:%M:%S").time()),
            datetime.combine(today, datetime.strptime("11:00:00", "%H:%M:%S").time()),
            datetime.combine(today, datetime.strptime("11:30:00", "%H:%M:%S").time()),
            datetime.combine(today, datetime.strptime("12:00:00", "%H:%M:%S").time())
        ],
        'close': [100.0, 101.0, 99.0, 102.0, 98.0, 103.0, 97.0],
        'high': [100.5, 101.5, 100.0, 102.5, 99.0, 104.0, 98.0],
        'low': [99.5, 100.5, 98.0, 101.0, 97.0, 102.0, 96.0],
    })
    
    # Test 1: Tight SL (should trigger early exit)
    sl_rule_tight = RiskRule(
        rule_type=RiskRuleType.STOPLOSS,
        number_type=NumberType.PERCENTAGE,
        value=1.0  # 1% SL - very tight, should trigger early
    )
    
    # Test 2: Wider SL (should not trigger early exit)
    sl_rule_wide = RiskRule(
        rule_type=RiskRuleType.STOPLOSS,
        number_type=NumberType.PERCENTAGE,
        value=10.0  # 10% SL - should not trigger
    )
    
    # Test 3: Tight TP (should trigger early exit)
    tp_rule_tight = RiskRule(
        rule_type=RiskRuleType.TAKEPROFIT,
        number_type=NumberType.PERCENTAGE,
        value=1.0  # 1% TP - very tight, should trigger early
    )
    
    # Test 4: Wider TP (should not trigger early exit)
    tp_rule_wide = RiskRule(
        rule_type=RiskRuleType.TAKEPROFIT,
        number_type=NumberType.PERCENTAGE,
        value=10.0  # 10% TP - should not trigger
    )
    
    # Run tests
    tests = [
        ("Tight SL (BUY)", sl_rule_tight, True),   # Long position, tight SL
        ("Tight SL (SELL)", sl_rule_tight, False), # Short position, tight SL
        ("Wide SL (BUY)", sl_rule_wide, True),     # Long position, wide SL
        ("Wide SL (SELL)", sl_rule_wide, False),   # Short position, wide SL
        ("Tight TP (BUY)", tp_rule_tight, True),   # Long position, tight TP
        ("Tight TP (SELL)", tp_rule_tight, False), # Short position, tight TP
        ("Wide TP (BUY)", tp_rule_wide, True),     # Long position, wide TP
        ("Wide TP (SELL)", tp_rule_wide, False)    # Short position, wide TP
    ]
    
    for test_name, rule, is_long in tests:
        print(f"\nTesting: {test_name}")
        
        # Call evaluate_risk_rule function from the risk.py module
        exit_price, exit_time, triggered, reason = evaluate_risk_rule(
            tick_df=tick_data,
            risk_rule=rule,
            entry_price=100.0,
            is_long=is_long
        )
        
        if triggered:
            print(f"✅ Rule triggered: Exit at {exit_price:.2f} at {exit_time}, Reason: {reason}")
        else:
            print(f"❌ Rule not triggered")

if __name__ == "__main__":
    try:
        test_exit_time()
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1) 