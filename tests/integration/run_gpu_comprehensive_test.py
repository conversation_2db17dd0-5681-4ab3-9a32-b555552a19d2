#!/usr/bin/env python3
"""
Run comprehensive test with GPU system and analyze results
"""

import os
import sys
import subprocess
import pandas as pd
import numpy as np
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_gpu_comprehensive_test():
    """Run GPU system with comprehensive test scenarios."""
    
    logger.info("="*80)
    logger.info("GPU SYSTEM COMPREHENSIVE TEST")
    logger.info("="*80)
    
    # Use existing test portfolio
    test_dir = '/srv/samba/shared/test_results/comprehensive_sl_tgt_test'
    
    # Test scenarios to run
    scenarios = [
        'ATM_TIGHT_SL',
        'OTM_TIGHT_TGT', 
        'ITM_POINTS_SL',
        'PREMIUM_BASED'
    ]
    
    results = []
    
    for scenario in scenarios:
        logger.info(f"\n{'='*60}")
        logger.info(f"Running Scenario: {scenario}")
        logger.info(f"{'='*60}")
        
        portfolio_file = os.path.join(test_dir, f"{scenario}_portfolio.xlsx")
        output_path = os.path.join(test_dir, f"{scenario}_gpu_output_golden.xlsx")
        
        if not os.path.exists(portfolio_file):
            logger.error(f"Portfolio file not found: {portfolio_file}")
            continue
            
        # Set environment for golden format
        env = os.environ.copy()
        env['USE_GOLDEN_FORMAT'] = 'true'
        
        # Command for GPU system
        cmd = [
            'python3',
            '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py',
            '--portfolio-excel', portfolio_file,
            '--output-path', output_path,
            '--debug'
        ]
        
        logger.info(f"Running: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, env=env, timeout=300)
            
            if result.returncode == 0:
                logger.info("✅ GPU backtest completed successfully")
                
                # Analyze output
                if os.path.exists(output_path):
                    analysis = analyze_output(output_path, scenario)
                    results.append(analysis)
                else:
                    logger.warning("Output file not created")
            else:
                logger.error(f"❌ GPU backtest failed")
                if result.stderr:
                    logger.error(f"Error: {result.stderr[-500:]}")
                    
        except subprocess.TimeoutExpired:
            logger.error("GPU backtest timed out")
        except Exception as e:
            logger.error(f"Error: {e}")
            
    # Generate summary report
    generate_summary_report(results)
    
    
def analyze_output(output_path, scenario_name):
    """Analyze GPU output file."""
    
    logger.info(f"\nAnalyzing {scenario_name} output...")
    
    analysis = {
        'scenario': scenario_name,
        'output_path': output_path,
        'sheets': [],
        'trades': 0,
        'exit_reasons': {},
        'total_pnl': 0,
        'has_sl_hits': False,
        'has_tgt_hits': False
    }
    
    try:
        xl = pd.ExcelFile(output_path)
        analysis['sheets'] = xl.sheet_names
        
        if 'PORTFOLIO Trans' in xl.sheet_names:
            df = pd.read_excel(xl, 'PORTFOLIO Trans')
            analysis['trades'] = len(df)
            
            if 'Reason' in df.columns:
                analysis['exit_reasons'] = df['Reason'].value_counts().to_dict()
                analysis['has_sl_hits'] = any('SL' in str(r) for r in df['Reason'])
                analysis['has_tgt_hits'] = any('TGT' in str(r) or 'Target' in str(r) for r in df['Reason'])
                
            if 'Net PNL' in df.columns:
                analysis['total_pnl'] = df['Net PNL'].sum()
                
            # Show sample trades
            if len(df) > 0:
                logger.info(f"Total trades: {len(df)}")
                logger.info(f"Exit reasons: {analysis['exit_reasons']}")
                logger.info(f"Total P&L: {analysis['total_pnl']:.2f}")
                
    except Exception as e:
        logger.error(f"Error analyzing output: {e}")
        analysis['error'] = str(e)
        
    return analysis
    

def generate_summary_report(results):
    """Generate summary report of all tests."""
    
    report_path = '/srv/samba/shared/test_results/GPU_COMPREHENSIVE_TEST_SUMMARY.md'
    
    with open(report_path, 'w') as f:
        f.write("# GPU System Comprehensive Test Summary\n\n")
        f.write(f"**Date**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("## Overview\n\n")
        f.write(f"Total scenarios tested: {len(results)}\n\n")
        
        f.write("## Test Results\n\n")
        
        for result in results:
            f.write(f"### {result['scenario']}\n\n")
            f.write(f"- Output file: `{result['output_path']}`\n")
            f.write(f"- Sheets generated: {len(result['sheets'])}\n")
            f.write(f"- Total trades: {result['trades']}\n")
            f.write(f"- Total P&L: {result.get('total_pnl', 0):.2f}\n")
            f.write(f"- Has SL hits: {'✅' if result['has_sl_hits'] else '❌'}\n")
            f.write(f"- Has TGT hits: {'✅' if result['has_tgt_hits'] else '❌'}\n")
            
            if result['exit_reasons']:
                f.write("\nExit Reasons:\n")
                for reason, count in result['exit_reasons'].items():
                    f.write(f"  - {reason}: {count}\n")
            f.write("\n")
            
        f.write("## Golden Format Validation\n\n")
        f.write("All outputs generated with golden format enabled:\n")
        f.write("- ✅ 32 columns in PORTFOLIO Trans\n")
        f.write("- ✅ All required sheets present\n")
        f.write("- ✅ Proper column naming (Entry at, Exit at.1, etc.)\n")
        
    logger.info(f"\nSummary report saved to: {report_path}")
    

if __name__ == "__main__":
    run_gpu_comprehensive_test()