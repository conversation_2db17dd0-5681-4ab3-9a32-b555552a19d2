#!/usr/bin/env python3
"""
Test ORB implementation with database
"""

import sys
from datetime import date
from heavydb import connect

# Add path
sys.path.append('/srv/samba/shared/bt/backtester_stable/BTRUN/backtester_v2')

from strategies.orb.parser import ORBParser
from strategies.orb.models import ORBSettingModel, ORBLegModel
from strategies.orb.range_calculator import RangeCalculator
from strategies.orb.signal_generator import SignalGenerator

def test_orb_with_db():
    """Test ORB components with database"""
    
    # Connect to database
    print("Connecting to HeavyDB...")
    try:
        conn = connect(
            host='localhost',
            port=6274,
            user='admin',
            password='HyperInteractive',
            dbname='heavyai'
        )
        print("Connected successfully!")
    except Exception as e:
        print(f"Failed to connect: {e}")
        return
    
    # Parse ORB input
    parser = ORBParser()
    input_file = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/orb/input_orb.xlsx'
    
    result = parser.parse_orb_excel(input_file)
    strategies = result['strategies']
    
    # Use first strategy for testing
    if not strategies:
        print("No strategies found")
        return
    
    # Pick a strategy with legs
    strategy = None
    for s in strategies:
        if len(s.get('legs', [])) > 0:
            strategy = s
            break
    
    if not strategy:
        print("No strategy with legs found")
        return
    
    print(f"\nUsing strategy: {strategy['strategy_name']}")
    print(f"ORB Range: {strategy['orb_range_start']} - {strategy['orb_range_end']}")
    
    # Test range calculation
    print("\n--- Testing Range Calculator ---")
    range_calc = RangeCalculator()
    
    test_date = date(2024, 4, 1)
    print(f"Calculating range for {test_date}")
    
    orb_range = range_calc.calculate_opening_range(
        db_connection=conn,
        index=strategy['index'],
        trade_date=test_date,
        range_start=strategy['orb_range_start'],
        range_end=strategy['orb_range_end'],
        underlying_type=strategy['underlying']
    )
    
    if orb_range:
        print(f"Range calculated successfully:")
        print(f"  High: {orb_range['range_high']}")
        print(f"  Low: {orb_range['range_low']}")
        print(f"  Size: {orb_range['range_size']}")
        print(f"  Tick count: {orb_range.get('tick_count', 'N/A')}")
        
        # Test breakout detection
        print("\n--- Testing Signal Generator ---")
        signal_gen = SignalGenerator()
        
        breakout = signal_gen.detect_first_breakout(
            db_connection=conn,
            index=strategy['index'],
            trade_date=test_date,
            range_high=orb_range['range_high'],
            range_low=orb_range['range_low'],
            range_end_time=strategy['orb_range_end'],
            last_entry_time=strategy['last_entry_time'],
            underlying_type=strategy['underlying']
        )
        
        if breakout:
            print(f"Breakout detected:")
            print(f"  Type: {breakout['breakout_type']}")
            print(f"  Time: {breakout['breakout_time']}")
            print(f"  Price: {breakout['breakout_price']}")
            print(f"  Strength: {breakout['breakout_strength_pct']:.2f}%")
        else:
            print("No breakout detected")
    else:
        print("Failed to calculate range")
    
    # Close connection
    conn.close()
    print("\nTest completed!")

if __name__ == "__main__":
    test_orb_with_db()