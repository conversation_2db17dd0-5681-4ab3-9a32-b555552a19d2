#!/usr/bin/env python3
"""
Test script for ORB implementation
"""

import os
import sys
from datetime import datetime, date
import logging

# Add path for imports
sys.path.append('/srv/samba/shared/bt/backtester_stable/BTRUN/backtester_v2')

from strategies.orb import ORB<PERSON>ars<PERSON>, ORBExecutor

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_orb_parser():
    """Test ORB parser functionality"""
    logger.info("=== Testing ORB Parser ===")
    
    # Check if input files exist
    orb_input_path = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/orb'
    if not os.path.exists(orb_input_path):
        logger.error(f"ORB input directory not found: {orb_input_path}")
        return False
    
    # Find ORB input files
    orb_files = [f for f in os.listdir(orb_input_path) if f.endswith('.xlsx') and 'orb' in f.lower()]
    
    if not orb_files:
        logger.warning("No ORB input files found")
        return False
    
    logger.info(f"Found {len(orb_files)} ORB input files")
    
    # Test parsing first file
    parser = ORBParser()
    test_file = os.path.join(orb_input_path, orb_files[0])
    logger.info(f"Testing parser with: {test_file}")
    
    try:
        result = parser.parse_orb_excel(test_file)
        
        if result and 'strategies' in result:
            strategies = result['strategies']
            logger.info(f"Successfully parsed {len(strategies)} strategies")
            
            # Display first strategy details
            if strategies:
                first_strategy = strategies[0]
                logger.info(f"First strategy: {first_strategy.get('strategy_name', 'Unknown')}")
                logger.info(f"  Index: {first_strategy.get('index', 'Unknown')}")
                logger.info(f"  ORB Range: {first_strategy.get('orb_range_start', 'N/A')} - {first_strategy.get('orb_range_end', 'N/A')}")
                logger.info(f"  Legs: {len(first_strategy.get('legs', []))}")
                
                # Display leg details
                for i, leg in enumerate(first_strategy.get('legs', [])):
                    logger.info(f"  Leg {i+1}: {leg.get('instrument', 'Unknown')} {leg.get('transaction', 'Unknown')} @ {leg.get('strike_method', 'Unknown')}")
            
            return True
        else:
            logger.error("Parser returned invalid result")
            return False
            
    except Exception as e:
        logger.error(f"Error parsing ORB file: {e}")
        return False


def test_orb_models():
    """Test ORB model creation"""
    logger.info("\n=== Testing ORB Models ===")
    
    from strategies.orb.models import ORBSettingModel, ORBLegModel, ORBBreakoutType
    
    try:
        # Create test leg
        leg = ORBLegModel(
            leg_id="LEG1",
            instrument="CE",
            transaction="BUY",
            expiry="CW",
            lots=1,
            strike_method="ATM",
            strike_value=0
        )
        
        # Create test settings
        settings = ORBSettingModel(
            strategy_name="Test ORB Strategy",
            index="NIFTY",
            legs=[leg]
        )
        
        logger.info(f"Created ORB settings: {settings.strategy_name}")
        logger.info(f"  Trading days: {settings.weekdays}")
        logger.info(f"  ORB Range: {settings.orb_range_start} - {settings.orb_range_end}")
        
        # Test breakout logic
        high_breakout_legs = settings.get_entry_legs(ORBBreakoutType.HIGHBREAKOUT)
        low_breakout_legs = settings.get_entry_legs(ORBBreakoutType.LOWBREAKOUT)
        
        logger.info(f"  High breakout legs: {len(high_breakout_legs)}")
        logger.info(f"  Low breakout legs: {len(low_breakout_legs)}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing models: {e}")
        return False


def test_orb_executor():
    """Test ORB executor with small date range"""
    logger.info("\n=== Testing ORB Executor ===")
    
    # Find input files
    orb_input_path = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/orb'
    portfolio_path = '/srv/samba/shared/input_portfolio.xlsx'
    
    if not os.path.exists(orb_input_path):
        logger.error("ORB input directory not found")
        return False
    
    orb_files = [f for f in os.listdir(orb_input_path) if f.endswith('.xlsx') and 'orb' in f.lower()]
    
    if not orb_files:
        logger.error("No ORB input files found")
        return False
    
    # Use first ORB file
    input_file = os.path.join(orb_input_path, orb_files[0])
    logger.info(f"Using input file: {input_file}")
    
    if not os.path.exists(portfolio_path):
        logger.warning(f"Portfolio file not found at {portfolio_path}, will proceed without it")
        portfolio_path = None
    
    try:
        # Create executor
        executor = ORBExecutor()
        
        # Test with a small date range (1 day)
        start_date = date(2024, 4, 1)
        end_date = date(2024, 4, 1)
        
        logger.info(f"Running test backtest from {start_date} to {end_date}")
        
        # Run backtest
        result = executor.execute_orb_backtest(
            input_file=input_file,
            portfolio_file=portfolio_path or input_file,  # Use input file if no portfolio
            start_date=start_date,
            end_date=end_date,
            output_file=None  # Don't save for test
        )
        
        if result['status'] == 'success':
            logger.info("Backtest executed successfully!")
            logger.info(f"  Trade count: {result['trade_count']}")
            logger.info(f"  Summary: {result['summary']}")
            return True
        else:
            logger.error(f"Backtest failed: {result.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        logger.error(f"Error testing executor: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if 'executor' in locals():
            executor.close()


def main():
    """Run all tests"""
    logger.info("Starting ORB implementation tests...\n")
    
    tests = [
        ("Parser Test", test_orb_parser),
        ("Models Test", test_orb_models),
        ("Executor Test", test_orb_executor)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            logger.error(f"Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n=== Test Summary ===")
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "PASSED" if success else "FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nTotal: {passed}/{total} tests passed")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)