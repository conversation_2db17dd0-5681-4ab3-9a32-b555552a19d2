#!/usr/bin/env python3
"""
Iterative Playwright MCP-based Test Script for Enhanced GPU Backtester
Performs repeated testing with validation loops until consistent results are achieved
Author: MarvelQuant Test Automation
Date: January 2025
"""

import asyncio
import json
import os
import time
import hashlib
import statistics
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Callable
from collections import defaultdict
from dataclasses import dataclass, asdict
import logging
import sys

# Configure logging with both operational and debug logs
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'gpu_backtester_iterative_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Debug logger for detailed troubleshooting
debug_logger = logging.getLogger('debug')
debug_handler = logging.FileHandler(f'gpu_backtester_iterative_debug_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
debug_handler.setLevel(logging.DEBUG)
debug_logger.addHandler(debug_handler)
debug_logger.setLevel(logging.DEBUG)

# Test configuration
BASE_URL = "http://173.208.247.17:8000"
TEST_PHONE = "9876543210"
TEST_OTP = "123456"
TIMEOUT = 30000  # 30 seconds default timeout
MAX_ITERATIONS = 5  # Maximum iterations per test
CONSISTENCY_THRESHOLD = 3  # Number of consistent results needed
SCREENSHOT_DIR = Path("/srv/samba/shared/test_screenshots_iterative")
SCREENSHOT_DIR.mkdir(exist_ok=True)

# File paths
LOGO_PATH = "/srv/samba/shared/Logo-Main.svg"
QUANTECH_LOGIN_REFERENCE = "/srv/samba/shared/Quantech_login.PNG"
QUANTECH_NAV_REFERENCE = "/srv/samba/shared/Quantech.PNG"
GOLDEN_OUTPUT_PATH = "/srv/samba/shared/Nifty_Golden_Output.xlsx"
INPUT_PORTFOLIO_PATH = "/srv/samba/shared/input_portfolio.xlsx"
INPUT_TBS_PATH = "/srv/samba/shared/input_tbs_multi_legs.xlsx"

# Documentation paths
DOCS_BASE = "/srv/samba/shared/docs"
EXCEL_YAML_GUIDE = f"{DOCS_BASE}/EXCEL_TO_YAML_PIPELINE_USER_GUIDE.md"
COLUMN_MAPPINGS = {
    "OI": "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/column_mapping_ml_oi.md",
    "TBS": "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/column_mapping_ml_tbs.md",
    "TV": "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/column_mapping_ml_tv.md",
    "ORB": "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/column_mapping_ml_orb.md"
}

# Browser configurations for compatibility testing
BROWSER_CONFIGS = [
    {"name": "Chrome", "channel": "chrome"},
    {"name": "Firefox", "channel": "firefox"},
    {"name": "Edge", "channel": "msedge"}
]

# Viewport configurations for responsiveness testing
VIEWPORT_CONFIGS = [
    {"width": 1920, "height": 1080, "name": "desktop_full_hd"},
    {"width": 1366, "height": 768, "name": "desktop_hd"},
    {"width": 768, "height": 1024, "name": "tablet_portrait"},
    {"width": 1024, "height": 768, "name": "tablet_landscape"},
    {"width": 375, "height": 667, "name": "mobile_portrait"},
    {"width": 667, "height": 375, "name": "mobile_landscape"}
]


@dataclass
class TestIteration:
    """Data class for tracking test iterations"""
    iteration_number: int
    test_name: str
    status: str
    duration: float
    details: Dict[str, Any]
    errors: List[str]
    warnings: List[str]
    screenshots: List[str]
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()


@dataclass
class ConsistencyResult:
    """Data class for tracking consistency across iterations"""
    test_name: str
    total_iterations: int
    consistent_iterations: int
    is_consistent: bool
    variations: List[Dict[str, Any]]
    final_status: str
    confidence_score: float


class IterativeTestFramework:
    """Framework for iterative testing with consistency validation"""
    
    def __init__(self):
        self.iteration_history = defaultdict(list)
        self.consistency_tracker = defaultdict(lambda: {"results": [], "variations": []})
        
    async def run_with_iterations(self, test_func: Callable, test_name: str, 
                                 max_iterations: int = MAX_ITERATIONS,
                                 consistency_threshold: int = CONSISTENCY_THRESHOLD) -> ConsistencyResult:
        """Run a test function iteratively until consistent results are achieved"""
        logger.info(f"Starting iterative test: {test_name}")
        logger.info(f"Max iterations: {max_iterations}, Consistency threshold: {consistency_threshold}")
        
        consistent_count = 0
        last_result = None
        variations = []
        
        for iteration in range(1, max_iterations + 1):
            logger.info(f"Iteration {iteration}/{max_iterations} for {test_name}")
            
            try:
                # Run the test
                result = await test_func(iteration)
                
                # Store iteration result
                self.iteration_history[test_name].append(result)
                
                # Check consistency
                if last_result and self._results_consistent(last_result, result):
                    consistent_count += 1
                    logger.info(f"Consistent result #{consistent_count}")
                else:
                    if last_result:
                        variations.append({
                            "iteration": iteration,
                            "difference": self._get_difference(last_result, result)
                        })
                    consistent_count = 1  # Reset counter
                    
                last_result = result
                
                # Check if we've reached consistency threshold
                if consistent_count >= consistency_threshold:
                    logger.info(f"✓ Consistency achieved for {test_name} after {iteration} iterations")
                    break
                    
                # Wait before next iteration
                if iteration < max_iterations:
                    await asyncio.sleep(2)
                    
            except Exception as e:
                logger.error(f"Error in iteration {iteration}: {e}")
                self.iteration_history[test_name].append(
                    TestIteration(
                        iteration_number=iteration,
                        test_name=test_name,
                        status="ERROR",
                        duration=0,
                        details={},
                        errors=[str(e)],
                        warnings=[],
                        screenshots=[]
                    )
                )
                
        # Calculate final consistency result
        is_consistent = consistent_count >= consistency_threshold
        confidence_score = consistent_count / len(self.iteration_history[test_name])
        
        return ConsistencyResult(
            test_name=test_name,
            total_iterations=len(self.iteration_history[test_name]),
            consistent_iterations=consistent_count,
            is_consistent=is_consistent,
            variations=variations,
            final_status="PASS" if is_consistent else "FAIL",
            confidence_score=confidence_score
        )
        
    def _results_consistent(self, result1: TestIteration, result2: TestIteration) -> bool:
        """Check if two test results are consistent"""
        # Compare status
        if result1.status != result2.status:
            return False
            
        # Compare key details
        key_fields = ["elements_found", "functionality_working", "performance_acceptable"]
        for field in key_fields:
            if result1.details.get(field) != result2.details.get(field):
                return False
                
        # Check if errors are similar
        if set(result1.errors) != set(result2.errors):
            return False
            
        return True
        
    def _get_difference(self, result1: TestIteration, result2: TestIteration) -> Dict[str, Any]:
        """Get differences between two results"""
        differences = {}
        
        if result1.status != result2.status:
            differences["status"] = {"before": result1.status, "after": result2.status}
            
        # Compare details
        all_keys = set(result1.details.keys()) | set(result2.details.keys())
        for key in all_keys:
            val1 = result1.details.get(key)
            val2 = result2.details.get(key)
            if val1 != val2:
                differences[key] = {"before": val1, "after": val2}
                
        return differences


class IterativeGPUBacktesterTestSuite:
    """Iterative test suite for GPU Backtester with consistency validation"""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.framework = IterativeTestFramework()
        self.test_results = {
            "total_tests": 0,
            "consistent_tests": 0,
            "inconsistent_tests": 0,
            "consistency_reports": [],
            "performance_metrics": defaultdict(list),
            "security_checks": defaultdict(list),
            "browser_compatibility": defaultdict(list),
            "start_time": datetime.now(),
            "iterations_summary": {}
        }
        self.current_browser = "chrome"
        
    async def setup(self):
        """Initialize test environment"""
        logger.info("Setting up iterative test environment...")
        
        # Create directories for iterative results
        self.iteration_dir = Path(f"/srv/samba/shared/iterations_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        self.iteration_dir.mkdir(exist_ok=True)
        
        # Check environment
        await self.check_environment()
        
    async def check_environment(self):
        """Check test environment and dependencies"""
        logger.info("Checking test environment...")
        
        env_info = {
            "timestamp": datetime.now().isoformat(),
            "base_url": self.base_url,
            "files_available": {},
            "iteration_config": {
                "max_iterations": MAX_ITERATIONS,
                "consistency_threshold": CONSISTENCY_THRESHOLD
            }
        }
        
        # Check required files
        required_files = {
            "logo": LOGO_PATH,
            "login_reference": QUANTECH_LOGIN_REFERENCE,
            "nav_reference": QUANTECH_NAV_REFERENCE,
            "golden_output": GOLDEN_OUTPUT_PATH,
            "portfolio_input": INPUT_PORTFOLIO_PATH,
            "tbs_input": INPUT_TBS_PATH
        }
        
        for name, path in required_files.items():
            env_info["files_available"][name] = os.path.exists(path)
            if not os.path.exists(path):
                logger.warning(f"Required file not found: {path}")
                
        self.test_results["environment_info"] = env_info
        
    async def test_authentication_iterative(self, iteration: int) -> TestIteration:
        """Iterative authentication page test"""
        test_name = f"Authentication_Page_Iteration_{iteration}"
        start_time = time.time()
        screenshots = []
        errors = []
        warnings = []
        details = {
            "elements_found": {},
            "functionality_working": {},
            "design_match": False,
            "responsiveness_ok": True
        }
        
        try:
            logger.info(f"Testing authentication page - Iteration {iteration}")
            
            # Navigate to login page
            await self.navigate_to(f"{self.base_url}/login")
            await self.wait_for(time_seconds=2)
            
            # Take screenshot for comparison
            screenshot_path = f"login_page_iter_{iteration}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            await self.take_screenshot(screenshot_path)
            screenshots.append(screenshot_path)
            
            # Get page snapshot
            snapshot = await self.get_snapshot()
            
            # Check required elements
            required_elements = ["phone_input", "otp_button", "logo", "login_form"]
            for element in required_elements:
                found = await self.check_element_exists(element, snapshot)
                details["elements_found"][element] = found
                if not found:
                    errors.append(f"Missing element: {element}")
                    
            # Test responsiveness across viewports
            responsiveness_results = []
            for viewport in VIEWPORT_CONFIGS[:3]:  # Test subset for efficiency
                await self.resize_browser(viewport["width"], viewport["height"])
                await self.wait_for(time_seconds=1)
                
                # Check if phone input still visible
                snapshot = await self.get_snapshot()
                phone_visible = await self.check_element_exists("phone_input", snapshot)
                responsiveness_results.append(phone_visible)
                
                if not phone_visible:
                    warnings.append(f"Phone input not visible on {viewport['name']}")
                    
            details["responsiveness_ok"] = all(responsiveness_results)
            
            # Reset to desktop
            await self.resize_browser(1920, 1080)
            
            # Test login functionality
            login_success = await self.test_login_flow()
            details["functionality_working"]["login"] = login_success
            
            if not login_success:
                errors.append("Login flow failed")
                
            # Determine status
            status = "PASS" if not errors else "FAIL"
            
            duration = time.time() - start_time
            
            return TestIteration(
                iteration_number=iteration,
                test_name=test_name,
                status=status,
                duration=duration,
                details=details,
                errors=errors,
                warnings=warnings,
                screenshots=screenshots
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return TestIteration(
                iteration_number=iteration,
                test_name=test_name,
                status="ERROR",
                duration=duration,
                details=details,
                errors=[str(e)],
                warnings=warnings,
                screenshots=screenshots
            )
            
    async def test_navigation_iterative(self, iteration: int) -> TestIteration:
        """Iterative navigation test"""
        test_name = f"Navigation_Iteration_{iteration}"
        start_time = time.time()
        screenshots = []
        errors = []
        warnings = []
        details = {
            "elements_found": {},
            "functionality_working": {},
            "logo_present": False,
            "all_links_working": True
        }
        
        try:
            logger.info(f"Testing navigation - Iteration {iteration}")
            
            # Ensure logged in
            await self.ensure_logged_in()
            
            # Check for MarvelQuant logo
            snapshot = await self.get_snapshot()
            logo_found = await self.check_element_exists("marvelquant_logo", snapshot)
            details["logo_present"] = logo_found
            
            if not logo_found:
                warnings.append("MarvelQuant logo not found")
                
            # Test navigation items
            nav_items = ["Dashboard", "New Backtest", "Logs", "Templates", "Documentation"]
            nav_results = {}
            
            for nav_item in nav_items:
                # Navigate to each item
                success = await self.navigate_to_nav_item(nav_item)
                nav_results[nav_item] = success
                details["functionality_working"][f"nav_{nav_item}"] = success
                
                if not success:
                    errors.append(f"Navigation to {nav_item} failed")
                    
                # Take screenshot
                screenshot_path = f"nav_{nav_item.lower()}_iter_{iteration}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                await self.take_screenshot(screenshot_path)
                screenshots.append(screenshot_path)
                
                await self.wait_for(time_seconds=1)
                
            details["all_links_working"] = all(nav_results.values())
            
            # Determine status
            status = "PASS" if not errors else "FAIL"
            
            duration = time.time() - start_time
            
            return TestIteration(
                iteration_number=iteration,
                test_name=test_name,
                status=status,
                duration=duration,
                details=details,
                errors=errors,
                warnings=warnings,
                screenshots=screenshots
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return TestIteration(
                iteration_number=iteration,
                test_name=test_name,
                status="ERROR",
                duration=duration,
                details=details,
                errors=[str(e)],
                warnings=warnings,
                screenshots=screenshots
            )
            
    async def test_new_backtest_iterative(self, iteration: int) -> TestIteration:
        """Iterative new backtest page test"""
        test_name = f"New_Backtest_Page_Iteration_{iteration}"
        start_time = time.time()
        screenshots = []
        errors = []
        warnings = []
        details = {
            "elements_found": {},
            "functionality_working": {},
            "upload_areas_count": 0,
            "validation_working": False,
            "progress_indicators_present": False
        }
        
        try:
            logger.info(f"Testing new backtest page - Iteration {iteration}")
            
            # Navigate to new backtest
            await self.navigate_to(f"{self.base_url}/backtest/new")
            await self.wait_for(time_seconds=2)
            
            # Take screenshot
            screenshot_path = f"new_backtest_iter_{iteration}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            await self.take_screenshot(screenshot_path)
            screenshots.append(screenshot_path)
            
            # Check upload areas
            snapshot = await self.get_snapshot()
            upload_areas = await self.count_upload_areas(snapshot)
            details["upload_areas_count"] = upload_areas
            
            if upload_areas < 2:
                errors.append(f"Expected 2 upload areas, found {upload_areas}")
                
            # Test invalid file upload
            validation_test = await self.test_file_validation_invalid()
            details["functionality_working"]["invalid_file_rejection"] = validation_test
            details["validation_working"] = validation_test
            
            if not validation_test:
                warnings.append("File validation for invalid files not working properly")
                
            # Test valid file upload
            if os.path.exists(INPUT_PORTFOLIO_PATH) and os.path.exists(INPUT_TBS_PATH):
                upload_success = await self.test_file_upload_valid()
                details["functionality_working"]["valid_file_upload"] = upload_success
                
                if upload_success:
                    # Test run button and progress
                    run_success = await self.test_run_backtest()
                    details["functionality_working"]["run_button"] = run_success
                    
                    if run_success:
                        # Check progress indicators
                        progress_check = await self.check_progress_indicators()
                        details["progress_indicators_present"] = progress_check
                        
                        if not progress_check:
                            warnings.append("Some progress indicators missing")
                else:
                    errors.append("Valid file upload failed")
            else:
                warnings.append("Test files not available")
                
            # Determine status
            status = "PASS" if not errors else "FAIL"
            
            duration = time.time() - start_time
            
            return TestIteration(
                iteration_number=iteration,
                test_name=test_name,
                status=status,
                duration=duration,
                details=details,
                errors=errors,
                warnings=warnings,
                screenshots=screenshots
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return TestIteration(
                iteration_number=iteration,
                test_name=test_name,
                status="ERROR",
                duration=duration,
                details=details,
                errors=[str(e)],
                warnings=warnings,
                screenshots=screenshots
            )
            
    async def test_logs_ui_iterative(self, iteration: int) -> TestIteration:
        """Iterative logs UI test"""
        test_name = f"Logs_UI_Iteration_{iteration}"
        start_time = time.time()
        screenshots = []
        errors = []
        warnings = []
        details = {
            "elements_found": {},
            "functionality_working": {},
            "categories_present": [],
            "collapsible_working": True,
            "download_available": False
        }
        
        try:
            logger.info(f"Testing logs UI - Iteration {iteration}")
            
            # Navigate to logs
            await self.navigate_to(f"{self.base_url}/logs")
            await self.wait_for(time_seconds=2)
            
            # Take screenshot
            screenshot_path = f"logs_ui_iter_{iteration}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            await self.take_screenshot(screenshot_path)
            screenshots.append(screenshot_path)
            
            # Check log categories
            expected_categories = ["Information", "Warning", "BT", "Error"]
            snapshot = await self.get_snapshot()
            
            for category in expected_categories:
                found = await self.check_log_category(category, snapshot)
                if found:
                    details["categories_present"].append(category)
                else:
                    errors.append(f"Log category not found: {category}")
                    
            # Test collapsible functionality
            collapse_results = []
            for category in details["categories_present"]:
                result = await self.test_log_collapse(category)
                collapse_results.append(result)
                details["functionality_working"][f"collapse_{category}"] = result
                
            details["collapsible_working"] = all(collapse_results) if collapse_results else False
            
            # Check download functionality
            download_found = await self.check_download_button(snapshot)
            details["download_available"] = download_found
            
            if not download_found:
                warnings.append("Download button not found")
                
            # Check debug options
            debug_found = await self.check_debug_options(snapshot)
            details["functionality_working"]["debug_options"] = debug_found
            
            if not debug_found:
                warnings.append("Debug options not found")
                
            # Determine status
            status = "PASS" if not errors else "FAIL"
            
            duration = time.time() - start_time
            
            return TestIteration(
                iteration_number=iteration,
                test_name=test_name,
                status=status,
                duration=duration,
                details=details,
                errors=errors,
                warnings=warnings,
                screenshots=screenshots
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return TestIteration(
                iteration_number=iteration,
                test_name=test_name,
                status="ERROR",
                duration=duration,
                details=details,
                errors=[str(e)],
                warnings=warnings,
                screenshots=screenshots
            )
            
    async def test_gpu_performance_iterative(self, iteration: int) -> TestIteration:
        """Iterative GPU performance test"""
        test_name = f"GPU_Performance_Iteration_{iteration}"
        start_time = time.time()
        screenshots = []
        errors = []
        warnings = []
        details = {
            "gpu_config_available": False,
            "gpu_utilization": [],
            "performance_metrics": {},
            "performance_acceptable": True
        }
        
        try:
            logger.info(f"Testing GPU performance - Iteration {iteration}")
            
            # Navigate to new backtest
            await self.navigate_to(f"{self.base_url}/backtest/new")
            await self.wait_for(time_seconds=2)
            
            # Check GPU configuration
            snapshot = await self.get_snapshot()
            gpu_config = await self.check_gpu_config(snapshot)
            details["gpu_config_available"] = gpu_config
            
            if not gpu_config:
                warnings.append("GPU configuration options not found")
                
            # Start a test backtest if files available
            if os.path.exists(INPUT_PORTFOLIO_PATH) and os.path.exists(INPUT_TBS_PATH):
                # Upload files and start backtest
                upload_success = await self.test_file_upload_valid()
                
                if upload_success and gpu_config:
                    # Configure GPU to maximum
                    await self.configure_gpu_max()
                    
                    # Start backtest
                    await self.start_backtest()
                    
                    # Monitor performance for 10 seconds
                    metrics = await self.monitor_gpu_performance(duration=10)
                    details["gpu_utilization"] = metrics.get("gpu_utilization", [])
                    details["performance_metrics"] = metrics
                    
                    # Calculate average GPU utilization
                    if details["gpu_utilization"]:
                        avg_gpu = statistics.mean(details["gpu_utilization"])
                        details["performance_metrics"]["avg_gpu_utilization"] = avg_gpu
                        
                        # Check if performance is acceptable
                        if avg_gpu < 20:  # Less than 20% utilization might indicate issues
                            warnings.append(f"Low GPU utilization: {avg_gpu:.2f}%")
                            details["performance_acceptable"] = False
                            
                    # Take performance screenshot
                    screenshot_path = f"gpu_performance_iter_{iteration}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                    await self.take_screenshot(screenshot_path)
                    screenshots.append(screenshot_path)
                    
            # Determine status
            status = "PASS" if not errors and details["performance_acceptable"] else "FAIL"
            
            duration = time.time() - start_time
            
            return TestIteration(
                iteration_number=iteration,
                test_name=test_name,
                status=status,
                duration=duration,
                details=details,
                errors=errors,
                warnings=warnings,
                screenshots=screenshots
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return TestIteration(
                iteration_number=iteration,
                test_name=test_name,
                status="ERROR",
                duration=duration,
                details=details,
                errors=[str(e)],
                warnings=warnings,
                screenshots=screenshots
            )
            
    async def test_backtesting_systems_iterative(self, iteration: int) -> TestIteration:
        """Iterative backtesting systems test"""
        test_name = f"Backtesting_Systems_Iteration_{iteration}"
        start_time = time.time()
        screenshots = []
        errors = []
        warnings = []
        details = {
            "systems_tested": {},
            "documentation_available": {},
            "all_systems_working": True
        }
        
        try:
            logger.info(f"Testing backtesting systems - Iteration {iteration}")
            
            systems = ["TBS", "TV", "OI", "ORB"]
            
            for system in systems:
                logger.info(f"Testing {system} system...")
                
                # Navigate to new backtest
                await self.navigate_to(f"{self.base_url}/backtest/new")
                await self.wait_for(time_seconds=2)
                
                # Check system selector
                snapshot = await self.get_snapshot()
                selector_found = await self.check_system_selector(system, snapshot)
                
                # Check documentation
                doc_found = await self.check_system_documentation(system, snapshot)
                
                details["systems_tested"][system] = {
                    "selector_found": selector_found,
                    "documentation_found": doc_found
                }
                
                details["documentation_available"][system] = doc_found
                
                if not selector_found:
                    errors.append(f"{system} selector not found")
                if not doc_found:
                    warnings.append(f"{system} documentation link not found")
                    
                # Take screenshot
                screenshot_path = f"system_{system.lower()}_iter_{iteration}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                await self.take_screenshot(screenshot_path)
                screenshots.append(screenshot_path)
                
            # Check overall documentation
            await self.navigate_to(f"{self.base_url}/docs")
            await self.wait_for(time_seconds=2)
            
            doc_structure = await self.check_documentation_structure()
            details["documentation_structure"] = doc_structure
            
            if not all(doc_structure.values()):
                warnings.append("Some documentation sections missing")
                
            # Determine if all systems working
            details["all_systems_working"] = all(
                result["selector_found"] for result in details["systems_tested"].values()
            )
            
            # Determine status
            status = "PASS" if not errors else "FAIL"
            
            duration = time.time() - start_time
            
            return TestIteration(
                iteration_number=iteration,
                test_name=test_name,
                status=status,
                duration=duration,
                details=details,
                errors=errors,
                warnings=warnings,
                screenshots=screenshots
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return TestIteration(
                iteration_number=iteration,
                test_name=test_name,
                status="ERROR",
                duration=duration,
                details=details,
                errors=[str(e)],
                warnings=warnings,
                screenshots=screenshots
            )
            
    async def test_output_validation_iterative(self, iteration: int) -> TestIteration:
        """Iterative output validation test"""
        test_name = f"Output_Validation_Iteration_{iteration}"
        start_time = time.time()
        screenshots = []
        errors = []
        warnings = []
        details = {
            "completed_backtest_found": False,
            "download_available": False,
            "output_format_correct": False,
            "api_endpoints_working": {}
        }
        
        try:
            logger.info(f"Testing output validation - Iteration {iteration}")
            
            # Navigate to dashboard
            await self.navigate_to(f"{self.base_url}/dashboard")
            await self.wait_for(time_seconds=2)
            
            # Take screenshot
            screenshot_path = f"dashboard_iter_{iteration}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            await self.take_screenshot(screenshot_path)
            screenshots.append(screenshot_path)
            
            # Look for completed backtests
            snapshot = await self.get_snapshot()
            completed = await self.find_completed_backtest(snapshot)
            details["completed_backtest_found"] = completed
            
            if completed:
                # Click on completed backtest
                await self.click_completed_backtest()
                await self.wait_for(time_seconds=2)
                
                # Check download availability
                snapshot = await self.get_snapshot()
                download = await self.check_download_button(snapshot)
                details["download_available"] = download
                
                # Check output format
                format_info = await self.check_output_format(snapshot)
                details["output_format_correct"] = format_info
                
                if not download:
                    errors.append("Download button not available for completed backtest")
                if not format_info:
                    warnings.append("Output format information not clear")
            else:
                warnings.append("No completed backtests found")
                
            # Test API endpoints
            api_endpoints = ["health", "docs", "backtests"]
            
            for endpoint in api_endpoints:
                working = await self.test_api_endpoint(endpoint)
                details["api_endpoints_working"][endpoint] = working
                
                if not working:
                    errors.append(f"API endpoint {endpoint} not working")
                    
            # Determine status
            status = "PASS" if not errors else "FAIL"
            
            duration = time.time() - start_time
            
            return TestIteration(
                iteration_number=iteration,
                test_name=test_name,
                status=status,
                duration=duration,
                details=details,
                errors=errors,
                warnings=warnings,
                screenshots=screenshots
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return TestIteration(
                iteration_number=iteration,
                test_name=test_name,
                status="ERROR",
                duration=duration,
                details=details,
                errors=[str(e)],
                warnings=warnings,
                screenshots=screenshots
            )
            
    async def test_performance_scalability_iterative(self, iteration: int) -> TestIteration:
        """Iterative performance and scalability test"""
        test_name = f"Performance_Scalability_Iteration_{iteration}"
        start_time = time.time()
        screenshots = []
        errors = []
        warnings = []
        details = {
            "system_health": {},
            "scalability_metrics": {},
            "performance_acceptable": True,
            "cleanup_available": False
        }
        
        try:
            logger.info(f"Testing performance and scalability - Iteration {iteration}")
            
            # Check system health
            health_check = await self.check_system_health()
            details["system_health"] = health_check
            
            if not health_check.get("heavydb_connected"):
                errors.append("HeavyDB not connected")
            if not health_check.get("gpu_available"):
                warnings.append("GPU not available")
                
            # Test different load scenarios
            load_scenarios = [
                {"name": "Light", "strategies": 5},
                {"name": "Medium", "strategies": 50}
            ]
            
            for scenario in load_scenarios:
                logger.info(f"Testing {scenario['name']} load...")
                
                # Simulate load test
                metrics = await self.simulate_load_test(scenario)
                details["scalability_metrics"][scenario["name"]] = metrics
                
                # Check if performance acceptable
                if metrics.get("response_time", 0) > 5000:  # > 5 seconds
                    warnings.append(f"{scenario['name']} load has high response time")
                    details["performance_acceptable"] = False
                    
            # Check cleanup options
            cleanup = await self.check_cleanup_options()
            details["cleanup_available"] = cleanup
            
            if not cleanup:
                warnings.append("Data cleanup options not found")
                
            # Take screenshot
            screenshot_path = f"performance_iter_{iteration}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            await self.take_screenshot(screenshot_path)
            screenshots.append(screenshot_path)
            
            # Determine status
            status = "PASS" if not errors and details["performance_acceptable"] else "FAIL"
            
            duration = time.time() - start_time
            
            return TestIteration(
                iteration_number=iteration,
                test_name=test_name,
                status=status,
                duration=duration,
                details=details,
                errors=errors,
                warnings=warnings,
                screenshots=screenshots
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return TestIteration(
                iteration_number=iteration,
                test_name=test_name,
                status="ERROR",
                duration=duration,
                details=details,
                errors=[str(e)],
                warnings=warnings,
                screenshots=screenshots
            )
            
    async def run_all_tests_iteratively(self):
        """Run all tests with iterative validation"""
        logger.info("Starting iterative test suite execution")
        logger.info("=" * 80)
        
        test_configs = [
            ("Authentication Page", self.test_authentication_iterative),
            ("Navigation", self.test_navigation_iterative),
            ("New Backtest Page", self.test_new_backtest_iterative),
            ("Logs UI", self.test_logs_ui_iterative),
            ("GPU Performance", self.test_gpu_performance_iterative),
            ("Backtesting Systems", self.test_backtesting_systems_iterative),
            ("Output Validation", self.test_output_validation_iterative),
            ("Performance Scalability", self.test_performance_scalability_iterative)
        ]
        
        for test_name, test_func in test_configs:
            logger.info(f"\n{'='*80}")
            logger.info(f"Running iterative test: {test_name}")
            logger.info(f"{'='*80}")
            
            # Run test iteratively
            consistency_result = await self.framework.run_with_iterations(
                test_func, test_name
            )
            
            # Store results
            self.test_results["total_tests"] += 1
            if consistency_result.is_consistent:
                self.test_results["consistent_tests"] += 1
            else:
                self.test_results["inconsistent_tests"] += 1
                
            self.test_results["consistency_reports"].append(consistency_result)
            self.test_results["iterations_summary"][test_name] = {
                "total_iterations": consistency_result.total_iterations,
                "consistent_iterations": consistency_result.consistent_iterations,
                "is_consistent": consistency_result.is_consistent,
                "confidence_score": consistency_result.confidence_score,
                "final_status": consistency_result.final_status
            }
            
            # Log consistency result
            logger.info(f"\nConsistency Result for {test_name}:")
            logger.info(f"  Total Iterations: {consistency_result.total_iterations}")
            logger.info(f"  Consistent Iterations: {consistency_result.consistent_iterations}")
            logger.info(f"  Is Consistent: {consistency_result.is_consistent}")
            logger.info(f"  Confidence Score: {consistency_result.confidence_score:.2%}")
            logger.info(f"  Final Status: {consistency_result.final_status}")
            
            # Wait between tests
            await asyncio.sleep(2)
            
    async def generate_iterative_report(self):
        """Generate comprehensive iterative test report"""
        logger.info("Generating iterative test report...")
        
        # Calculate duration
        total_duration = (datetime.now() - self.test_results["start_time"]).total_seconds()
        
        # Calculate consistency rate
        consistency_rate = (
            self.test_results["consistent_tests"] / self.test_results["total_tests"] * 100
        ) if self.test_results["total_tests"] > 0 else 0
        
        # Create report
        report = {
            "summary": {
                "total_tests": self.test_results["total_tests"],
                "consistent_tests": self.test_results["consistent_tests"],
                "inconsistent_tests": self.test_results["inconsistent_tests"],
                "consistency_rate": consistency_rate,
                "duration": f"{total_duration:.2f} seconds",
                "test_date": datetime.now().isoformat()
            },
            "environment": self.test_results.get("environment_info", {}),
            "iterations_summary": self.test_results["iterations_summary"],
            "consistency_reports": [asdict(cr) for cr in self.test_results["consistency_reports"]],
            "iteration_history": dict(self.framework.iteration_history),
            "recommendations": []
        }
        
        # Add recommendations
        if consistency_rate < 100:
            report["recommendations"].append(
                f"Address inconsistencies in {self.test_results['inconsistent_tests']} tests"
            )
            
        inconsistent_tests = [
            cr.test_name for cr in self.test_results["consistency_reports"] 
            if not cr.is_consistent
        ]
        if inconsistent_tests:
            report["recommendations"].append(
                f"Focus on fixing: {', '.join(inconsistent_tests)}"
            )
            
        # Save detailed report
        report_path = self.iteration_dir / f"iterative_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_path, "w") as f:
            json.dump(report, f, indent=2, default=str)
            
        # Save summary
        summary_path = self.iteration_dir / f"iterative_test_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(summary_path, "w") as f:
            f.write("ITERATIVE GPU BACKTESTER TEST SUMMARY\n")
            f.write("=" * 80 + "\n")
            f.write(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total Tests: {report['summary']['total_tests']}\n")
            f.write(f"Consistent Tests: {report['summary']['consistent_tests']}\n")
            f.write(f"Inconsistent Tests: {report['summary']['inconsistent_tests']}\n")
            f.write(f"Consistency Rate: {report['summary']['consistency_rate']:.2f}%\n")
            f.write(f"Total Duration: {report['summary']['duration']}\n")
            f.write("\n")
            
            f.write("TEST DETAILS:\n")
            f.write("-" * 80 + "\n")
            for test_name, summary in report["iterations_summary"].items():
                f.write(f"\n{test_name}:\n")
                f.write(f"  Iterations: {summary['total_iterations']}\n")
                f.write(f"  Consistent: {summary['consistent_iterations']}\n")
                f.write(f"  Confidence: {summary['confidence_score']:.2%}\n")
                f.write(f"  Status: {summary['final_status']}\n")
                
            if report["recommendations"]:
                f.write("\nRECOMMENDATIONS:\n")
                f.write("-" * 80 + "\n")
                for i, rec in enumerate(report["recommendations"], 1):
                    f.write(f"{i}. {rec}\n")
                    
        logger.info(f"Iterative test report saved to: {report_path}")
        logger.info(f"Summary saved to: {summary_path}")
        
        # Print summary
        print("\n" + "="*80)
        print("ITERATIVE TEST EXECUTION COMPLETE")
        print("="*80)
        print(f"Consistency Rate: {consistency_rate:.2f}%")
        print(f"Consistent Tests: {self.test_results['consistent_tests']}/{self.test_results['total_tests']}")
        print(f"Total Duration: {total_duration:.2f} seconds")
        
        if inconsistent_tests:
            print(f"\nInconsistent Tests: {', '.join(inconsistent_tests)}")
            
        print("="*80)
        
        return report
        
    # Helper methods (placeholders for MCP browser operations)
    async def navigate_to(self, url: str):
        """Navigate to URL"""
        debug_logger.debug(f"Navigating to: {url}")
        # MCP implementation
        await asyncio.sleep(0.1)
        
    async def wait_for(self, time_seconds: int):
        """Wait for specified time"""
        await asyncio.sleep(time_seconds)
        
    async def take_screenshot(self, filename: str):
        """Take screenshot"""
        debug_logger.debug(f"Taking screenshot: {filename}")
        # MCP implementation
        
    async def get_snapshot(self):
        """Get page snapshot"""
        # MCP implementation - return mock for now
        return {"elements": [], "url": self.base_url}
        
    async def resize_browser(self, width: int, height: int):
        """Resize browser window"""
        debug_logger.debug(f"Resizing to {width}x{height}")
        # MCP implementation
        
    async def check_element_exists(self, element_type: str, snapshot: dict) -> bool:
        """Check if element exists in snapshot"""
        # Mock implementation - would use actual MCP snapshot analysis
        return True
        
    async def test_login_flow(self) -> bool:
        """Test login flow"""
        # Mock implementation
        return True
        
    async def ensure_logged_in(self):
        """Ensure user is logged in"""
        # Mock implementation
        pass
        
    async def navigate_to_nav_item(self, item: str) -> bool:
        """Navigate to navigation item"""
        # Mock implementation
        return True
        
    async def count_upload_areas(self, snapshot: dict) -> int:
        """Count upload areas in snapshot"""
        # Mock implementation
        return 2
        
    async def test_file_validation_invalid(self) -> bool:
        """Test invalid file validation"""
        # Mock implementation
        return True
        
    async def test_file_upload_valid(self) -> bool:
        """Test valid file upload"""
        # Mock implementation
        return True
        
    async def test_run_backtest(self) -> bool:
        """Test running backtest"""
        # Mock implementation
        return True
        
    async def check_progress_indicators(self) -> bool:
        """Check progress indicators"""
        # Mock implementation
        return True
        
    async def start_backtest(self):
        """Start backtest"""
        # Mock implementation
        pass
        
    async def check_log_category(self, category: str, snapshot: dict) -> bool:
        """Check if log category exists"""
        # Mock implementation
        return True
        
    async def test_log_collapse(self, category: str) -> bool:
        """Test log collapse functionality"""
        # Mock implementation
        return True
        
    async def check_download_button(self, snapshot: dict) -> bool:
        """Check for download button"""
        # Mock implementation
        return True
        
    async def check_debug_options(self, snapshot: dict) -> bool:
        """Check for debug options"""
        # Mock implementation
        return True
        
    async def check_gpu_config(self, snapshot: dict) -> bool:
        """Check GPU configuration availability"""
        # Mock implementation
        return True
        
    async def configure_gpu_max(self):
        """Configure GPU to maximum"""
        # Mock implementation
        pass
        
    async def monitor_gpu_performance(self, duration: int) -> dict:
        """Monitor GPU performance"""
        # Mock implementation
        return {
            "gpu_utilization": [45.2, 48.5, 52.1, 49.8, 51.3],
            "timestamps": [datetime.now().isoformat() for _ in range(5)]
        }
        
    async def check_system_selector(self, system: str, snapshot: dict) -> bool:
        """Check system selector availability"""
        # Mock implementation
        return True
        
    async def check_system_documentation(self, system: str, snapshot: dict) -> bool:
        """Check system documentation"""
        # Mock implementation
        return True
        
    async def check_documentation_structure(self) -> dict:
        """Check documentation structure"""
        # Mock implementation
        return {
            "user_guides": True,
            "column_mappings": True,
            "excel_yaml_pipeline": True
        }
        
    async def find_completed_backtest(self, snapshot: dict) -> bool:
        """Find completed backtest"""
        # Mock implementation
        return True
        
    async def click_completed_backtest(self):
        """Click on completed backtest"""
        # Mock implementation
        pass
        
    async def check_output_format(self, snapshot: dict) -> bool:
        """Check output format"""
        # Mock implementation
        return True
        
    async def test_api_endpoint(self, endpoint: str) -> bool:
        """Test API endpoint"""
        # Mock implementation
        return True
        
    async def check_system_health(self) -> dict:
        """Check system health"""
        # Mock implementation
        return {
            "heavydb_connected": True,
            "gpu_available": True,
            "memory_available": True,
            "disk_space": True
        }
        
    async def simulate_load_test(self, scenario: dict) -> dict:
        """Simulate load test"""
        # Mock implementation
        return {
            "response_time": 2500,
            "throughput": 100,
            "error_rate": 0.1
        }
        
    async def check_cleanup_options(self) -> bool:
        """Check cleanup options availability"""
        # Mock implementation
        return True


async def main():
    """Main execution function for iterative testing"""
    logger.info("""
    ╔═══════════════════════════════════════════════════════════════╗
    ║      Iterative GPU Backtester Test Suite with MCP             ║
    ║                                                               ║
    ║  This suite runs tests iteratively until consistent results   ║
    ║  are achieved, ensuring reliability and repeatability.        ║
    ║                                                               ║
    ║  Target: http://173.208.247.17:8000                          ║
    ║  Max Iterations: 5 | Consistency Threshold: 3                ║
    ╚═══════════════════════════════════════════════════════════════╝
    """)
    
    test_suite = IterativeGPUBacktesterTestSuite()
    
    try:
        # Setup
        await test_suite.setup()
        
        # Run all tests iteratively
        await test_suite.run_all_tests_iteratively()
        
        # Generate comprehensive report
        report = await test_suite.generate_iterative_report()
        
        # Return exit code based on consistency
        exit_code = 0 if test_suite.test_results["inconsistent_tests"] == 0 else 1
        
        return exit_code
        
    except Exception as e:
        logger.error(f"Test suite failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)