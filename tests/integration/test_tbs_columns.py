#!/usr/bin/env python3
"""
Unit tests for TBS column functionality
Run with: python -m pytest test_tbs_columns.py -v
"""

import pytest
import pandas as pd
from datetime import datetime, time
from typing import Dict, Any

class TestTBSColumns:
    """Test all TBS column functionalities"""
    
    def test_dte_zero_filtering(self):
        """Test that DTE=0 only trades on expiry days"""
        # Given
        dte_filter = 0
        trade_date = datetime(2024, 1, 4)  # Thursday
        expiry_date = datetime(2024, 1, 4)  # Same day
        
        # When
        should_trade = (trade_date == expiry_date) if dte_filter == 0 else True
        
        # Then
        assert should_trade == True, "DTE=0 should trade on expiry day"
        
        # Test non-expiry day
        trade_date = datetime(2024, 1, 3)  # Wednesday
        should_trade = (trade_date == expiry_date) if dte_filter == 0 else True
        assert should_trade == False, "DTE=0 should NOT trade on non-expiry day"
    
    def test_time_conversion(self):
        """Test time format conversions"""
        test_cases = [
            (91600, "09:16:00"),
            (120000, "12:00:00"),
            ("091600", "09:16:00"),
            ("9:16:00", "09:16:00")
        ]
        
        for input_val, expected in test_cases:
            # Conversion logic here
            result = self._convert_time(input_val)
            assert result == expected, f"Failed to convert {input_val} to {expected}"
    
    def test_strike_selection_atm_offset(self):
        """Test ATM offset strike selection"""
        atm = 22000
        step = 50
        
        # CE with positive offset (OTM)
        strike = self._calculate_strike(atm, "ATM", 1, "CE", step)
        assert strike == 22050, "CE OTM1 should be ATM + 50"
        
        # PE with positive offset (OTM)
        strike = self._calculate_strike(atm, "ATM", 1, "PE", step)
        assert strike == 21950, "PE OTM1 should be ATM - 50"
        
        # CE with negative offset (ITM)
        strike = self._calculate_strike(atm, "ATM", -1, "CE", step)
        assert strike == 21950, "CE ITM1 should be ATM - 50"
    
    def test_weekday_filtering(self):
        """Test weekday filtering"""
        weekdays = "2,4"  # Tuesday, Thursday
        
        test_dates = [
            (datetime(2024, 1, 1), False),  # Monday
            (datetime(2024, 1, 2), True),   # Tuesday
            (datetime(2024, 1, 3), False),  # Wednesday
            (datetime(2024, 1, 4), True),   # Thursday
            (datetime(2024, 1, 5), False),  # Friday
        ]
        
        for date, expected in test_dates:
            dow = date.weekday() + 1  # Python: 0=Mon, Excel: 1=Mon
            should_trade = str(dow) in weekdays.split(",")
            assert should_trade == expected, f"Weekday filter failed for {date.strftime('%A')}"
    
    def test_partial_exits(self):
        """Test partial exit functionality"""
        total_qty = 100
        sqoff1_pct = 50
        sqoff2_pct = 30
        
        # First exit
        exit1_qty = int(total_qty * sqoff1_pct / 100)
        assert exit1_qty == 50, "First exit should be 50%"
        
        # Second exit
        exit2_qty = int(total_qty * sqoff2_pct / 100)
        assert exit2_qty == 30, "Second exit should be 30%"
        
        # Remaining
        remaining = total_qty - exit1_qty - exit2_qty
        assert remaining == 20, "Remaining should be 20%"
    
    def test_sl_tp_values(self):
        """Test recommended SL/TP values"""
        # SELL leg
        entry_price = 100
        sl_pct_sell = 500
        tp_pct_sell = 100
        
        sl_price = entry_price * (1 + sl_pct_sell/100)
        tp_price = entry_price * (1 - tp_pct_sell/100)
        
        assert sl_price == 600, "SELL SL should be 600 (500% of 100)"
        assert tp_price == 0, "SELL TP should be 0 (100% profit)"
        
        # BUY leg
        sl_pct_buy = 50
        tp_pct_buy = 100
        
        sl_price = entry_price * (1 - sl_pct_buy/100)
        tp_price = entry_price * (1 + tp_pct_buy/100)
        
        assert sl_price == 50, "BUY SL should be 50 (50% of 100)"
        assert tp_price == 200, "BUY TP should be 200 (100% profit)"
    
    def test_expiry_mapping(self):
        """Test expiry code mappings"""
        mappings = {
            "current": "CW",
            "CW": "CW",
            "next": "NW",
            "NW": "NW",
            "monthly": "CM",
            "CM": "CM"
        }
        
        for input_val, expected in mappings.items():
            result = self._map_expiry(input_val)
            assert result == expected, f"Expiry mapping failed for {input_val}"
    
    def test_strategy_trailing(self):
        """Test strategy trailing logic"""
        profit_reaches = 5000
        lock_min_profit = 2500
        trail_percent = 20
        
        current_profit = 6000
        
        # Should activate trailing
        assert current_profit >= profit_reaches, "Trailing should activate"
        
        # Calculate trailing stop
        trail_stop = current_profit - (current_profit * trail_percent / 100)
        assert trail_stop == 4800, "Trail stop should be 80% of current profit"
        
        # But not below minimum
        actual_stop = max(trail_stop, lock_min_profit)
        assert actual_stop == 4800, "Should use trail stop when above minimum"
    
    def _convert_time(self, value):
        """Helper: Convert time format"""
        s = str(value).strip()
        if ":" in s:
            return s
        if len(s) == 5:
            s = "0" + s
        if len(s) == 6:
            return f"{s[0:2]}:{s[2:4]}:{s[4:6]}"
        return s
    
    def _calculate_strike(self, atm, method, value, option_type, step):
        """Helper: Calculate strike price"""
        if method == "ATM":
            if value == 0:
                return atm
            if option_type == "CE":
                return atm + (value * step)
            else:
                return atm - (value * step)
        return atm
    
    def _map_expiry(self, value):
        """Helper: Map expiry codes"""
        mapping = {
            "current": "CW", "CW": "CW",
            "next": "NW", "NW": "NW",
            "monthly": "CM", "CM": "CM"
        }
        return mapping.get(value, value)

if __name__ == "__main__":
    # Run tests
    test = TestTBSColumns()
    test.test_dte_zero_filtering()
    test.test_time_conversion()
    test.test_strike_selection_atm_offset()
    test.test_weekday_filtering()
    test.test_partial_exits()
    test.test_sl_tp_values()
    test.test_expiry_mapping()
    test.test_strategy_trailing()
    print("✅ All unit tests passed!")
