#!/usr/bin/env python3
"""
Comprehensive test with different strike selection methods and SL/TGT scenarios
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta

# Add path for imports
sys.path.insert(0, '/srv/samba/shared')

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_comprehensive_test_portfolios():
    """Create test portfolios with different strike selection methods and SL/TGT scenarios."""
    
    test_dir = '/srv/samba/shared/test_results/comprehensive_sl_tgt_test'
    os.makedirs(test_dir, exist_ok=True)
    
    # Test scenarios
    test_scenarios = [
        {
            'name': 'ATM_TIGHT_SL',
            'strategy_name': 'ATM_TIGHT_SL_STRADDLE',
            'legs': [
                {
                    'LegNo': 1,
                    'Instrument': 'call',
                    'StrikeMethod': 'atm',
                    'StrikeValue': 0,
                    'SLType': 'percentage',
                    'SLValue': 10,  # Tight SL - likely to hit
                    'TGTType': 'percentage', 
                    'TGTValue': 30,
                    'Transaction': 'sell'
                },
                {
                    'LegNo': 2,
                    'Instrument': 'put',
                    'StrikeMethod': 'atm',
                    'StrikeValue': 0,
                    'SLType': 'percentage',
                    'SLValue': 10,  # Tight SL
                    'TGTType': 'percentage',
                    'TGTValue': 30,
                    'Transaction': 'sell'
                }
            ]
        },
        {
            'name': 'OTM_TIGHT_TGT',
            'strategy_name': 'OTM_TIGHT_TGT_STRANGLE',
            'legs': [
                {
                    'LegNo': 1,
                    'Instrument': 'call',
                    'StrikeMethod': 'otm',
                    'StrikeValue': 2,  # 2 strikes OTM
                    'SLType': 'percentage',
                    'SLValue': 50,
                    'TGTType': 'percentage',
                    'TGTValue': 15,  # Tight target - likely to hit
                    'Transaction': 'sell'
                },
                {
                    'LegNo': 2,
                    'Instrument': 'put', 
                    'StrikeMethod': 'otm',
                    'StrikeValue': 2,  # 2 strikes OTM
                    'SLType': 'percentage',
                    'SLValue': 50,
                    'TGTType': 'percentage',
                    'TGTValue': 15,  # Tight target
                    'Transaction': 'sell'
                }
            ]
        },
        {
            'name': 'ITM_POINTS_SL',
            'strategy_name': 'ITM_POINTS_SL_STRATEGY',
            'legs': [
                {
                    'LegNo': 1,
                    'Instrument': 'call',
                    'StrikeMethod': 'itm',
                    'StrikeValue': 1,  # 1 strike ITM
                    'SLType': 'points',
                    'SLValue': 20,  # 20 points SL
                    'TGTType': 'points',
                    'TGTValue': 40,  # 40 points target
                    'Transaction': 'sell'
                },
                {
                    'LegNo': 2,
                    'Instrument': 'put',
                    'StrikeMethod': 'itm', 
                    'StrikeValue': 1,  # 1 strike ITM
                    'SLType': 'points',
                    'SLValue': 20,
                    'TGTType': 'points',
                    'TGTValue': 40,
                    'Transaction': 'sell'
                }
            ]
        },
        {
            'name': 'PREMIUM_BASED',
            'strategy_name': 'PREMIUM_MATCH_STRATEGY',
            'legs': [
                {
                    'LegNo': 1,
                    'Instrument': 'call',
                    'StrikeMethod': 'strike premium',
                    'StrikeValue': 100,  # Match 100 premium
                    'StrikePremiumCondition': '>=',
                    'SLType': 'percentage',
                    'SLValue': 25,
                    'TGTType': 'percentage',
                    'TGTValue': 50,
                    'Transaction': 'sell'
                },
                {
                    'LegNo': 2,
                    'Instrument': 'put',
                    'StrikeMethod': 'strike premium',
                    'StrikeValue': 100,
                    'StrikePremiumCondition': '>=',
                    'SLType': 'percentage', 
                    'SLValue': 25,
                    'TGTType': 'percentage',
                    'TGTValue': 50,
                    'Transaction': 'sell'
                }
            ]
        },
        {
            'name': 'IRON_CONDOR',
            'strategy_name': 'IRON_CONDOR_STRATEGY',
            'legs': [
                {
                    'LegNo': 1,
                    'Instrument': 'call',
                    'StrikeMethod': 'otm',
                    'StrikeValue': 2,
                    'SLType': 'percentage',
                    'SLValue': 40,
                    'TGTType': 'percentage',
                    'TGTValue': 30,
                    'Transaction': 'sell'
                },
                {
                    'LegNo': 2,
                    'Instrument': 'put',
                    'StrikeMethod': 'otm',
                    'StrikeValue': 2,
                    'SLType': 'percentage',
                    'SLValue': 40,
                    'TGTType': 'percentage',
                    'TGTValue': 30,
                    'Transaction': 'sell'
                },
                {
                    'LegNo': 3,
                    'Instrument': 'call',
                    'StrikeMethod': 'otm',
                    'StrikeValue': 4,  # Further OTM for protection
                    'SLType': 'none',
                    'SLValue': 0,
                    'TGTType': 'none',
                    'TGTValue': 0,
                    'Transaction': 'buy'
                },
                {
                    'LegNo': 4,
                    'Instrument': 'put',
                    'StrikeMethod': 'otm',
                    'StrikeValue': 4,  # Further OTM for protection
                    'SLType': 'none',
                    'SLValue': 0,
                    'TGTType': 'none',
                    'TGTValue': 0,
                    'Transaction': 'buy'
                }
            ]
        }
    ]
    
    created_files = []
    
    for i, scenario in enumerate(test_scenarios):
        # Create portfolio file
        portfolio_data = {
            'PortfolioSetting': pd.DataFrame([{
                'StartDate': '01_04_2024',
                'EndDate': '05_04_2024',  # 5 days for more data
                'IsTickBT': 'YES',
                'Enabled': 'YES',
                'PortfolioName': scenario['name'],
                'PortfolioTarget': 100000,
                'PortfolioStoploss': 50000,
                'PortfolioTrailingType': 'none',
                'PnLCalTime': 152500,
                'LockPercent': 0,
                'TrailPercent': 0,
                'Multiplier': 1.0,
                'SlippagePercent': 0.05
            }]),
            'StrategySetting': pd.DataFrame([{
                'Enabled': 'YES',
                'PortfolioName': scenario['name'],
                'StrategyType': 'TBS',
                'StrategyExcelFilePath': f"{scenario['name']}_strategy.xlsx"
            }])
        }
        
        portfolio_file = os.path.join(test_dir, f"{scenario['name']}_portfolio.xlsx")
        with pd.ExcelWriter(portfolio_file) as writer:
            for sheet, df in portfolio_data.items():
                df.to_excel(writer, sheet_name=sheet, index=False)
        
        # Create strategy file with comprehensive parameters
        general_params = {
            'StrategyName': scenario['strategy_name'],
            'MoveSlToCost': 'no',
            'Underlying': 'SPOT',
            'Index': 'NIFTY',
            'Weekdays': '1,2,3,4,5',
            'DTE': 0,
            'StrikeSelectionTime': 91500,
            'StartTime': 91500,
            'LastEntryTime': 93000,
            'EndTime': 152500,
            'StrategyProfit': 20000,
            'StrategyLoss': 10000,
            'StrategyProfitReExecuteNo': 0,
            'StrategyLossReExecuteNo': 0,
            'StrategyTrailingType': 'none',
            'PnLCalTime': 152500,
            'LockPercent': 0,
            'TrailPercent': 0,
            'SqOff1Time': 230000,
            'SqOff1Percent': 0,
            'SqOff2Time': 230000,
            'SqOff2Percent': 0,
            'ProfitReaches': 0,
            'LockMinProfitAt': 0,
            'IncreaseInProfit': 0,
            'TrailMinProfitBy': 0,
            'TgtTrackingFrom': 'high/low',
            'TgtRegisterPriceFrom': 'tracking',
            'SlTrackingFrom': 'high/low',
            'SlRegisterPriceFrom': 'tracking',
            'PnLCalculationFrom': 'close',
            'ConsiderHedgePnLForStgyPnL': 'no',
            'StoplossCheckingInterval': 1,
            'TargetCheckingInterval': 1,
            'ReEntryCheckingInterval': 1,
            'OnExpiryDayTradeNextExpiry': 'no'
        }
        
        # Create leg parameters
        leg_params = []
        for leg in scenario['legs']:
            leg_param = {
                'StrategyName': scenario['strategy_name'],
                'IsIdle': 'no',
                'LegID': leg['LegNo'],
                'Instrument': leg['Instrument'],
                'Transaction': leg['Transaction'],
                'Expiry': 'current',
                'W&Type': 'percentage',
                'W&TValue': 0,
                'TrailW&T': 'no',
                'StrikeMethod': leg['StrikeMethod'],
                'MatchPremium': 'high',
                'StrikeValue': leg.get('StrikeValue', 0),
                'StrikePremiumCondition': leg.get('StrikePremiumCondition', '='),
                'SLType': leg.get('SLType', 'percentage'),
                'SLValue': leg.get('SLValue', 0),
                'TGTType': leg.get('TGTType', 'percentage'),
                'TGTValue': leg.get('TGTValue', 0),
                'TrailSLType': 'percentage',
                'SL_TrailAt': 0,
                'SL_TrailBy': 0,
                'Lots': 1,
                'ReEntryType': 'none',
                'ReEnteriesCount': 0,
                'OnEntry_OpenTradeOn': 0,
                'OnEntry_SqOffTradeOff': 0,
                'OnEntry_SqOffAllLegs': 'no',
                'OnEntry_OpenTradeDelay': 0,
                'OnEntry_SqOffDelay': 0,
                'OnExit_OpenTradeOn': 0,
                'OnExit_SqOffTradeOff': 0,
                'OnExit_SqOffAllLegs': 'no',
                'OnExit_OpenAllLegs': 'no',
                'OnExit_OpenTradeDelay': 0,
                'OnExit_SqOffDelay': 0,
                'OpenHedge': 'No',
                'HedgeStrikeMethod': 'atm',
                'HedgeStrikeValue': 0,
                'HedgeStrikePremiumCondition': '='
            }
            leg_params.append(leg_param)
        
        strategy_file = os.path.join(test_dir, f"{scenario['name']}_strategy.xlsx")
        with pd.ExcelWriter(strategy_file) as writer:
            pd.DataFrame([general_params]).to_excel(writer, sheet_name='GeneralParameter', index=False)
            pd.DataFrame(leg_params).to_excel(writer, sheet_name='LegParameter', index=False)
        
        created_files.append({
            'scenario': scenario['name'],
            'portfolio': portfolio_file,
            'strategy': strategy_file
        })
        
        logger.info(f"Created test scenario {i+1}/{len(test_scenarios)}: {scenario['name']}")
    
    return created_files


def simulate_sl_tgt_scenarios():
    """Create simulated trade data with SL and TGT hits for testing."""
    
    logger.info("\n=== Creating Simulated SL/TGT Test Data ===")
    
    test_dir = '/srv/samba/shared/test_results/comprehensive_sl_tgt_test'
    
    # Simulate different exit scenarios
    scenarios = []
    
    # Scenario 1: SL Hit on Call
    scenarios.append({
        'portfolio_name': 'SL_HIT_TEST',
        'strategy_name': 'ATM_STRADDLE',
        'id': 1,
        'entry_datetime': '2024-04-01 09:16:00',
        'entry_time': 91600,
        'exit_datetime': '2024-04-01 10:30:00',
        'exit_time': 103000,
        'symbol': 'NIFTY01APR2422500CE',
        'strike': 22500,
        'option_type': 'CE',
        'transaction_type': 'SELL',
        'quantity': 50,
        'entry_price': 118.0,
        'exit_price': 130.0,  # Loss for sell position
        'points': -12.0,
        'pnl': -600.0,
        'net_pnl': -610.0,
        'reason': 'SL Hit',
        'max_profit': 250.0,
        'max_loss': -610.0
    })
    
    # Scenario 2: TGT Hit on Put
    scenarios.append({
        'portfolio_name': 'TGT_HIT_TEST',
        'strategy_name': 'ATM_STRADDLE',
        'id': 2,
        'entry_datetime': '2024-04-01 09:16:00',
        'entry_time': 91600,
        'exit_datetime': '2024-04-01 11:45:00',
        'exit_time': 114500,
        'symbol': 'NIFTY01APR2422500PE',
        'strike': 22500,
        'option_type': 'PE',
        'transaction_type': 'SELL',
        'quantity': 50,
        'entry_price': 141.15,
        'exit_price': 100.0,  # Profit for sell position
        'points': 41.15,
        'pnl': 2057.50,
        'net_pnl': 2047.50,
        'reason': 'TGT Hit',
        'max_profit': 2047.50,
        'max_loss': -200.0
    })
    
    # Scenario 3: Points-based SL
    scenarios.append({
        'portfolio_name': 'POINTS_SL_TEST',
        'strategy_name': 'ITM_STRATEGY',
        'id': 3,
        'entry_datetime': '2024-04-02 09:20:00',
        'entry_time': 92000,
        'exit_datetime': '2024-04-02 10:15:00',
        'exit_time': 101500,
        'symbol': 'NIFTY02APR2422400CE',
        'strike': 22400,
        'option_type': 'CE',
        'transaction_type': 'SELL',
        'quantity': 50,
        'entry_price': 180.0,
        'exit_price': 200.0,  # 20 points loss
        'points': -20.0,
        'pnl': -1000.0,
        'net_pnl': -1010.0,
        'reason': 'Points SL Hit',
        'max_profit': 500.0,
        'max_loss': -1010.0
    })
    
    # Scenario 4: Portfolio-level SL
    scenarios.append({
        'portfolio_name': 'PORTFOLIO_SL_TEST',
        'strategy_name': 'MULTI_LEG',
        'id': 4,
        'entry_datetime': '2024-04-03 09:30:00',
        'entry_time': 93000,
        'exit_datetime': '2024-04-03 13:00:00',
        'exit_time': 130000,
        'symbol': 'NIFTY03APR2422600PE',
        'strike': 22600,
        'option_type': 'PE',
        'transaction_type': 'SELL',
        'quantity': 100,
        'entry_price': 120.0,
        'exit_price': 150.0,
        'points': -30.0,
        'pnl': -3000.0,
        'net_pnl': -3020.0,
        'reason': 'Portfolio SL Hit',
        'max_profit': 0.0,
        'max_loss': -3020.0
    })
    
    # Convert to DataFrame
    test_df = pd.DataFrame(scenarios)
    
    # Add missing columns
    test_df['entry_day'] = 'Monday'
    test_df['exit_day'] = 'Monday' 
    test_df['index_at_entry'] = 22500
    test_df['index_at_exit'] = 22520
    test_df['points_after_slippage'] = test_df['points'] * 0.995
    test_df['pnl_after_slippage'] = test_df['pnl'] * 0.995
    test_df['taxes'] = 10.0
    test_df['re_entry_no'] = 0
    test_df['sl_re_entry_no'] = 0
    test_df['tgt_re_entry_no'] = 0
    test_df['strategy_entry_no'] = 1
    
    # Test golden format conversion
    from bt.backtester_stable.BTRUN.utils.golden_format_converter import GoldenFormatConverter
    from bt.backtester_stable.BTRUN.utils.io_golden import prepare_output_file_golden
    
    golden_df = GoldenFormatConverter.convert_portfolio_trans(test_df)
    
    # Create output file
    output_path = os.path.join(test_dir, 'sl_tgt_scenarios_golden.xlsx')
    
    prepare_output_file_golden(
        output_path=output_path,
        metrics_df=pd.DataFrame(),
        transaction_dfs={'portfolio': test_df},
        day_stats={},
        month_stats={},
        margin_stats={},
        daily_max_pl_df=pd.DataFrame()
    )
    
    logger.info(f"Created SL/TGT test scenarios output: {output_path}")
    
    # Show summary
    reason_summary = test_df.groupby('reason')['net_pnl'].agg(['count', 'sum'])
    logger.info("\nExit Reason Summary:")
    logger.info(reason_summary)
    
    return output_path


def analyze_exit_reasons(file_path):
    """Analyze exit reasons in a backtest output file."""
    
    logger.info(f"\n=== Analyzing Exit Reasons in {os.path.basename(file_path)} ===")
    
    try:
        xl = pd.ExcelFile(file_path)
        if 'PORTFOLIO Trans' in xl.sheet_names:
            df = pd.read_excel(xl, 'PORTFOLIO Trans')
            
            if 'Reason' in df.columns:
                reason_counts = df['Reason'].value_counts()
                logger.info("\nExit Reason Distribution:")
                for reason, count in reason_counts.items():
                    logger.info(f"  {reason}: {count} ({count/len(df)*100:.1f}%)")
                
                # Check if any SL/TGT hits
                sl_hits = df[df['Reason'].str.contains('SL', na=False)]
                tgt_hits = df[df['Reason'].str.contains('TGT|Target', na=False, case=False)]
                
                logger.info(f"\nSL Hits: {len(sl_hits)}")
                logger.info(f"TGT Hits: {len(tgt_hits)}")
                
                return reason_counts
            else:
                logger.warning("No 'Reason' column found")
        else:
            logger.warning("No 'PORTFOLIO Trans' sheet found")
    except Exception as e:
        logger.error(f"Error analyzing file: {e}")
    
    return None


def main():
    """Run comprehensive SL/TGT tests."""
    
    logger.info("=" * 80)
    logger.info("Comprehensive SL/TGT Testing with Multiple Strike Selection Methods")
    logger.info("=" * 80)
    
    # 1. Create comprehensive test portfolios
    test_files = create_comprehensive_test_portfolios()
    
    # 2. Create simulated SL/TGT scenarios
    simulated_output = simulate_sl_tgt_scenarios()
    
    # 3. Analyze the simulated output
    analyze_exit_reasons(simulated_output)
    
    # 4. Show test summary
    logger.info("\n=== Test Summary ===")
    logger.info(f"Created {len(test_files)} test scenarios:")
    for i, test in enumerate(test_files):
        logger.info(f"{i+1}. {test['scenario']}")
        logger.info(f"   Portfolio: {test['portfolio']}")
        logger.info(f"   Strategy: {test['strategy']}")
    
    logger.info("\n✅ Test files created successfully!")
    logger.info("\nNext Steps:")
    logger.info("1. Run these portfolios through both Archive and GPU systems")
    logger.info("2. Compare the exit reasons and P&L calculations")
    logger.info("3. Verify SL/TGT logic is working correctly")
    
    return test_files


if __name__ == "__main__":
    main()