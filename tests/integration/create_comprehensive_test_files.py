#!/usr/bin/env python3
"""
Create comprehensive test input files for all strategy types (TBS, TV, ORB, OI, POS, ML)
that cover ALL columns documented in their respective column mapping files.
"""

import pandas as pd
import os
from datetime import datetime, timedelta
import numpy as np

# Base directory for comprehensive tests
BASE_DIR = "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/comprehensive_tests"

def create_directory_structure():
    """Create directories for each strategy type"""
    strategies = ['tbs', 'tv', 'orb', 'oi', 'pos', 'ml_indicator']
    for strategy in strategies:
        os.makedirs(os.path.join(BASE_DIR, strategy), exist_ok=True)

def create_oi_comprehensive_test():
    """Create comprehensive OI test covering all columns from column_mapping_ml_oi.md"""
    
    print("Creating comprehensive OI test files...")
    
    # GeneralParameter sheet with ALL columns
    general_params = {
        'StrategyName': [
            'OI_MAXOI_Test', 'OI_MAXCOI_Test', 'OI_Threshold_Test', 
            'OI_MultiTimeframe', 'OI_ReEntry_Test', 'OI_Trailing_Test'
        ],
        'Timeframe': [3, 6, 9, 12, 15, 3],  # Must be multiple of 3
        'MaxOpenPositions': [1, 2, 3, 1, 2, 1],
        'Underlying': ['SPOT', 'FUT', 'SPOT', 'FUT', 'SPOT', 'SPOT'],
        'Index': ['NIFTY', 'NIFTY', 'BANKNIFTY', 'NIFTY', 'FINNIFTY', 'NIFTY'],
        'Weekdays': ['1,2,3,4,5', '1,2,3,4', '2,3,4,5', '1,2,3,4,5', '1,3,5', '1,2,3,4,5'],
        'DTE': ['0,1,2', '0', '1,2', '0,1,2,3', '0,1', '0'],
        'StrikeSelectionTime': [91500, 93000, 91500, 100000, 91500, 91500],
        'StartTime': [91600, 93100, 91600, 100100, 91600, 91600],
        'LastEntryTime': [150000, 143000, 145000, 150000, 140000, 150000],
        'EndTime': [151500, 151500, 152000, 151500, 151500, 151500],
        'StrategyProfit': [5000, 10000, 8000, 12000, 6000, 5000],
        'StrategyLoss': [-10000, -15000, -12000, -20000, -8000, -10000],
        'StrategyProfitReExecuteNo': [0, 1, 0, 2, 1, 0],
        'StrategyLossReExecuteNo': [0, 0, 1, 0, 2, 0],
        'StrategyTrailingType': ['Lock Minimum Profit', 'Lock & Trail Profits', 'Lock Minimum Profit', 
                                 'Lock & Trail Profits', 'Lock Minimum Profit', 'Lock & Trail Profits'],
        'TgtTrackingFrom': ['close', 'high', 'close', 'close', 'high', 'close'],
        'SlTrackingFrom': ['close', 'low', 'close', 'close', 'low', 'close'],
        'PnLCalculationFrom': ['close', 'close', 'high', 'close', 'close', 'close'],
        'OnExpiryDayTradeNextExpiry': ['yes', 'no', 'yes', 'yes', 'no', 'yes'],
        # Additional columns for comprehensive testing
        'PnLCalTime': [0, 151500, 0, 140000, 0, 0],
        'LockPercent': [50, 60, 40, 70, 50, 55],
        'TrailPercent': [10, 15, 20, 25, 30, 10],
        'SqOff1Time': [0, 130000, 0, 140000, 0, 143000],
        'SqOff1Percent': [0, 50, 0, 30, 0, 40],
        'SqOff2Time': [0, 145000, 0, 0, 0, 150000],
        'SqOff2Percent': [0, 100, 0, 0, 0, 100],
        'ProfitReaches': [4000, 8000, 6000, 10000, 5000, 4000],
        'LockMinProfitAt': [3000, 6000, 4000, 8000, 4000, 3000],
        'IncreaseInProfit': [1000, 2000, 1500, 2500, 1000, 1000],
        'TrailMinProfitBy': [500, 1000, 750, 1250, 500, 500],
        'TgtRegisterPriceFrom': ['tick', 'tracking', 'tick', 'tracking', 'tick', 'tracking'],
        'SlRegisterPriceFrom': ['tick', 'tracking', 'tick', 'tracking', 'tick', 'tracking'],
        'ConsiderHedgePnLForStgyPnL': ['yes', 'no', 'yes', 'no', 'yes', 'no'],
        'StoplossCheckingInterval': [1, 2, 1, 3, 1, 1],
        'TargetCheckingInterval': [1, 2, 1, 3, 1, 1],
        'ReEntryCheckingInterval': [1, 2, 1, 3, 1, 1]
    }
    
    # LegParameter sheet with ALL OI-specific columns
    leg_params = {
        'StrategyName': ['OI_MAXOI_Test'] * 2 + ['OI_MAXCOI_Test'] * 2 + 
                       ['OI_Threshold_Test'] * 4 + ['OI_MultiTimeframe'] * 3 +
                       ['OI_ReEntry_Test'] * 2 + ['OI_Trailing_Test'] * 2,
        'OiThreshold': [1000000, 800000, 1500000, 1200000, 
                       500000, 500000, 1000000, 1000000,
                       2000000, 1500000, 1000000,
                       800000, 600000,
                       1200000, 900000],
        'LegID': ['a1', 'a2', 'b1', 'b2', 
                  'c1', 'c2', 'c3', 'c4',
                  'd1', 'd2', 'd3',
                  'e1', 'e2',
                  'f1', 'f2'],
        'Instrument': ['call', 'put', 'call', 'put',
                      'call', 'put', 'call', 'put',
                      'call', 'put', 'call',
                      'call', 'put',
                      'call', 'put'],
        'Transaction': ['buy', 'buy', 'sell', 'sell',
                       'buy', 'buy', 'sell', 'sell',
                       'buy', 'sell', 'buy',
                       'buy', 'buy',
                       'sell', 'sell'],
        'Expiry': ['current', 'current', 'current', 'current',
                   'current', 'current', 'next', 'next',
                   'current', 'current', 'next',
                   'current', 'current',
                   'current', 'current'],
        'StrikeMethod': ['MAXOI_1', 'MAXOI_1', 'MAXCOI_1', 'MAXCOI_1',
                        'MAXOI_2', 'MAXOI_2', 'MAXCOI_2', 'MAXCOI_2',
                        'MAXOI_1', 'MAXCOI_1', 'MAXOI_3',
                        'ATM', 'ATM',
                        'MAXOI_1', 'MAXOI_1'],
        'StrikeValue': [0, 0, 0, 0,
                       0, 0, 0, 0,
                       0, 0, 0,
                       0, 0,
                       0, 0],
        'Lots': [1, 1, 2, 2,
                1, 1, 1, 1,
                3, 2, 1,
                2, 2,
                1, 1],
        'SLType': ['percentage', 'percentage', 'point', 'point',
                   'index point', 'index point', 'absolute', 'absolute',
                   'percentage', 'point', 'percentage',
                   'percentage', 'percentage',
                   'point', 'point'],
        'SLValue': [30, 30, 20, 20,
                   50, 50, 1000, 1000,
                   25, 30, 40,
                   20, 20,
                   25, 25],
        'TGTType': ['percentage', 'percentage', 'point', 'point',
                    'index point', 'index point', 'absolute', 'absolute',
                    'percentage', 'point', 'percentage',
                    'percentage', 'percentage',
                    'point', 'point'],
        'TGTValue': [50, 50, 40, 40,
                    100, 100, 2000, 2000,
                    60, 50, 80,
                    40, 40,
                    50, 50],
        'TrailSLType': ['percentage', 'percentage', 'point', 'point',
                       'lock & trail', 'lock & trail', 'percentage', 'percentage',
                       'point', 'percentage', 'point',
                       'percentage', 'percentage',
                       'lock & trail', 'lock & trail'],
        'SL_TrailAt': [30, 30, 25, 25,
                      40, 40, 35, 35,
                      20, 30, 25,
                      25, 25,
                      30, 30],
        'SL_TrailBy': [10, 10, 10, 10,
                      15, 15, 10, 10,
                      5, 10, 10,
                      10, 10,
                      15, 15],
        # Hedge columns for comprehensive testing
        'OpenHedge': ['No', 'No', 'Yes', 'Yes',
                     'No', 'No', 'Yes', 'Yes',
                     'No', 'No', 'No',
                     'Yes', 'Yes',
                     'No', 'No'],
        'HedgeStrikeMethod': ['', '', 'atm', 'atm',
                             '', '', 'premium', 'premium',
                             '', '', '',
                             'atm width', 'atm width',
                             '', ''],
        'HedgeStrikeValue': [0, 0, 0, 0,
                            0, 0, 50, 50,
                            0, 0, 0,
                            200, 200,
                            0, 0],
        'HedgeStrikePremiumCondition': ['', '', '', '',
                                       '', '', '>', '>',
                                       '', '', '',
                                       '', '',
                                       '', ''],
        'MatchPremium': ['', '', '', '',
                        '', '', 'high', 'high',
                        '', '', '',
                        '', '',
                        '', ''],
        'StrikePremiumCondition': ['', '', '', '',
                                  '', '', '<', '<',
                                  '', '', '',
                                  '', '',
                                  '', '']
    }
    
    # Create Excel file with both sheets
    output_file = os.path.join(BASE_DIR, 'oi', 'comprehensive_oi_test_all_columns.xlsx')
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        pd.DataFrame(general_params).to_excel(writer, sheet_name='GeneralParameter', index=False)
        pd.DataFrame(leg_params).to_excel(writer, sheet_name='LegParameter', index=False)
    
    print(f"Created: {output_file}")
    
    # Create archive format compatibility test
    create_oi_archive_format_test()

def create_oi_archive_format_test():
    """Create OI test file that matches archive system format"""
    
    # Archive format uses different column names
    archive_params = {
        'strategy': ['maxoi_test', 'maxcoi_test'],
        'striketotrade': ['maxoi1', 'maxcoi1'],
        'oithreshold': [1000000, 800000],
        'timeframe': [3, 6],
        'noofstrikeeachside': [5, 5],
        'indexname': ['NIFTY', 'NIFTY'],
        'instrument': ['CE', 'PE'],
        'transaction': ['BUY', 'SELL'],
        'lotsize': [1, 2],
        'expiry': ['current', 'current'],
        'stoplosstype': ['percentage', 'point'],
        'stoplossvalue': [30, 20],
        'targettype': ['percentage', 'point'],
        'targetvalue': [50, 40]
    }
    
    output_file = os.path.join(BASE_DIR, 'oi', 'archive_format_oi_test.xlsx')
    pd.DataFrame(archive_params).to_excel(output_file, index=False)
    print(f"Created archive format test: {output_file}")

def create_tbs_comprehensive_test():
    """Create comprehensive TBS test covering all columns from column_mapping_ml_tbs.md"""
    
    print("Creating comprehensive TBS test files...")
    
    # Portfolio sheet (PortfolioSetting equivalent)
    portfolio_params = {
        'PortfolioName': ['TBS_Comprehensive_Test'],
        'Strategies': ['TBS_Simple,TBS_Complex,TBS_MultiLeg,TBS_ReEntry,TBS_Hedge,TBS_Trailing'],
        'Instrument': ['NIFTY'],
        'Start': ['01-04-2024'],
        'End': ['05-04-2024'],
        'Capital': [1000000],
        'IsCapitalDivided': ['YES'],
        'OMS': ['papertrade']
    }
    
    # Strategy sheet (StrategySetting equivalent) 
    strategy_params = {
        'StrategyId': ['TBS_Simple', 'TBS_Complex', 'TBS_MultiLeg', 'TBS_ReEntry', 'TBS_Hedge', 'TBS_Trailing'],
        'FileName': ['tbs_simple.xlsx', 'tbs_complex.xlsx', 'tbs_multileg.xlsx', 
                    'tbs_reentry.xlsx', 'tbs_hedge.xlsx', 'tbs_trailing.xlsx'],
        'Multiplier': [1, 2, 1, 1, 1, 1],
        'MaxOrderPerStrategy': [1, 2, 4, 3, 2, 1]
    }
    
    # Create portfolio file
    portfolio_file = os.path.join(BASE_DIR, 'tbs', 'comprehensive_tbs_portfolio.xlsx')
    with pd.ExcelWriter(portfolio_file, engine='openpyxl') as writer:
        pd.DataFrame(portfolio_params).to_excel(writer, sheet_name='PortfolioSetting', index=False)
        pd.DataFrame(strategy_params).to_excel(writer, sheet_name='StrategySetting', index=False)
    print(f"Created: {portfolio_file}")
    
    # Create individual strategy files with ALL columns
    create_tbs_strategy_files()

def create_tbs_strategy_files():
    """Create individual TBS strategy files covering all columns"""
    
    # Simple TBS - Basic single leg
    simple_general = {
        'StrategyName': ['Simple_ATM_CE'],
        'StartTime': [91600],
        'EndTime': [151500],
        'ExitTime': [151500],
        'IdleDuration': [0],
        'SqOffTime': [0],
        'LastEntryTime': [150000],
        'Capital': [100000],
        'TimeBased': ['no'],
        'TimeBasedNum': [0],
        'IntervalBased': ['no'],
        'IntervalBasedNum': [0],
        'Underlying': ['SPOT'],
        'Rebalance': ['no'],
        'RebalanceAt': [0],
        'ConsiderCurrentPnLForStgyPnL': ['yes']
    }
    
    simple_leg = {
        'StrategyName': ['Simple_ATM_CE'],
        'LegID': ['a1'],
        'Expiry': ['current'],
        'DTE': ['0'],
        'Weekdays': ['1,2,3,4,5'],
        'Instrument': ['call'],
        'StrikeMethod': ['ATM'],
        'StrikeValue': [0],
        'Legs': [1],
        'Transaction': ['buy'],
        'OpenHedge': ['no'],
        'IdleRun': ['no'],
        'WaitTradePrevLegId': [''],
        'WaitTradeNumCandle': [0],
        'SLType': ['percentage'],
        'SLValue': [30],
        'TGTType': ['percentage'],
        'TGTValue': [50],
        'MoveSlToCost': ['no']
    }
    
    # Complex TBS - Multi-leg with hedging
    complex_general = {
        'StrategyName': ['Iron_Condor', 'Straddle_Hedge'],
        'StartTime': [91600, 93000],
        'EndTime': [151500, 151500],
        'ExitTime': [151500, 151500],
        'IdleDuration': [0, 0],
        'SqOffTime': [0, 143000],
        'LastEntryTime': [143000, 140000],
        'Capital': [200000, 150000],
        'TimeBased': ['yes', 'no'],
        'TimeBasedNum': [30, 0],
        'IntervalBased': ['no', 'yes'],
        'IntervalBasedNum': [0, 15],
        'Underlying': ['SPOT', 'FUT'],
        'Rebalance': ['yes', 'no'],
        'RebalanceAt': [100000, 0],
        'ConsiderCurrentPnLForStgyPnL': ['yes', 'yes'],
        # Additional columns
        'ProfitReaches': [5000, 3000],
        'LockMinProfitAt': [4000, 2500],
        'IncreaseInProfit': [1000, 500],
        'TrailMinProfitBy': [500, 250],
        'StrategyProfit': [10000, 8000],
        'StrategyLoss': [-15000, -12000],
        'StrategyProfitReExecuteNo': [0, 1],
        'StrategyLossReExecuteNo': [1, 0]
    }
    
    # Create files
    files_data = [
        ('tbs_simple.xlsx', simple_general, simple_leg),
        # Add more comprehensive test cases here
    ]
    
    for filename, general, leg in files_data:
        filepath = os.path.join(BASE_DIR, 'tbs', filename)
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            pd.DataFrame(general).to_excel(writer, sheet_name='GeneralParameter', index=False)
            pd.DataFrame(leg).to_excel(writer, sheet_name='LegParameter', index=False)
        print(f"Created: {filepath}")

def create_tv_comprehensive_test():
    """Create comprehensive TV test with 6-file hierarchy"""
    
    print("Creating comprehensive TV test files...")
    
    # Main TV configuration
    tv_config = {
        'StrategyName': ['TV_Comprehensive_Test'],
        'SignalFile': ['comprehensive_signals.xlsx'],
        'ExitTradeBasedOn': ['Underlying spot intraday Database 1-minute'],
        'PortfolioLong': ['tv_portfolio_long.xlsx'],
        'PortfolioShort': ['tv_portfolio_short.xlsx'],
        'PortfolioManual': ['tv_portfolio_manual.xlsx'],
        'StartDate': ['01-04-2024'],
        'EndDate': ['05-04-2024'],
        'Index': ['NIFTY'],
        'Rollover': ['yes'],
        'RolloverDay': ['wednesday'],
        'RolloverTime': [151400]
    }
    
    # Signal file with comprehensive test cases
    signals = {
        'Trade #': [1, 1, 2, 2, 3, 3, 4, 4, 5, 5],
        'Order': ['entry', 'exit', 'entry', 'exit', 'entry', 'exit', 
                  'entry', 'exit', 'entry', 'exit'],
        'Date/Time': pd.to_datetime([
            '2024-04-01 09:30:00', '2024-04-01 10:45:00',
            '2024-04-01 11:00:00', '2024-04-01 12:30:00',
            '2024-04-01 13:00:00', '2024-04-01 14:00:00',
            '2024-04-02 09:20:00', '2024-04-02 11:30:00',
            '2024-04-02 13:15:00', '2024-04-02 15:00:00'
        ]),
        'Type': ['long', 'long', 'short', 'short', 'long', 'long',
                 'short', 'short', 'long', 'long'],
        'Price': [22000, 22050, 22100, 22080, 22060, 22120,
                  22150, 22100, 22090, 22110]
    }
    
    # Create all required files
    tv_dir = os.path.join(BASE_DIR, 'tv')
    
    # Main config
    pd.DataFrame(tv_config).to_excel(
        os.path.join(tv_dir, 'comprehensive_tv_config.xlsx'), 
        sheet_name='TVConfig', index=False
    )
    
    # Signal file
    pd.DataFrame(signals).to_excel(
        os.path.join(tv_dir, 'comprehensive_signals.xlsx'),
        sheet_name='List of trades', index=False
    )
    
    print(f"Created TV comprehensive test files in {tv_dir}")

def create_orb_comprehensive_test():
    """Create comprehensive ORB test covering all columns"""
    
    print("Creating comprehensive ORB test files...")
    
    # GeneralParameter with ORB-specific columns
    general_params = {
        'StrategyName': ['ORB_15min', 'ORB_30min', 'ORB_Extended', 'ORB_Filter'],
        'OrbRangeStart': [91500, 91500, 91500, 91500],
        'OrbRangeEnd': [93000, 94500, 100000, 93000],
        'StartTime': [93100, 94600, 100100, 93100],
        'EndTime': [151500, 151500, 151500, 151500],
        'LastEntryTime': [150000, 145000, 143000, 150000],
        'Underlying': ['SPOT', 'FUT', 'SPOT', 'SPOT'],
        'Capital': [100000, 150000, 200000, 100000],
        'ReExecuteOnSL': ['yes', 'no', 'yes', 'no'],
        'ReExecuteOnTgt': ['no', 'yes', 'no', 'yes'],
        'MaxReExecute': [2, 1, 3, 1],
        # Additional ORB columns
        'MinRangeSize': [30, 50, 40, 35],
        'MaxRangeSize': [150, 200, 180, 160],
        'BreakoutBuffer': [5, 10, 8, 5],
        'VolumeFilter': ['yes', 'no', 'yes', 'yes'],
        'MinVolume': [1000000, 0, 1500000, 1200000]
    }
    
    # LegParameter for ORB
    leg_params = {
        'StrategyName': ['ORB_15min', 'ORB_15min', 'ORB_30min', 'ORB_30min',
                        'ORB_Extended', 'ORB_Extended', 'ORB_Filter', 'ORB_Filter'],
        'LegID': ['a1', 'a2', 'b1', 'b2', 'c1', 'c2', 'd1', 'd2'],
        'Direction': ['long', 'short', 'long', 'short', 
                     'long', 'short', 'long', 'short'],
        'Instrument': ['call', 'put', 'call', 'put',
                      'call', 'put', 'FUT', 'FUT'],
        'Expiry': ['current', 'current', 'current', 'current',
                   'next', 'next', 'current', 'current'],
        'StrikeMethod': ['ATM', 'ATM', 'OTM1', 'OTM1',
                        'ATM', 'ATM', 'ATM', 'ATM'],
        'Lots': [1, 1, 2, 2, 1, 1, 1, 1],
        'SLType': ['range_low', 'range_high', 'percentage', 'percentage',
                   'point', 'point', 'range_based', 'range_based'],
        'SLValue': [0, 0, 20, 20, 30, 30, 1, 1],
        'TGTType': ['range_size', 'range_size', 'percentage', 'percentage',
                    'point', 'point', 'range_size', 'range_size'],
        'TGTValue': [2, 2, 40, 40, 60, 60, 3, 3]
    }
    
    output_file = os.path.join(BASE_DIR, 'orb', 'comprehensive_orb_test.xlsx')
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        pd.DataFrame(general_params).to_excel(writer, sheet_name='GeneralParameter', index=False)
        pd.DataFrame(leg_params).to_excel(writer, sheet_name='LegParameter', index=False)
    
    print(f"Created: {output_file}")

def create_pos_comprehensive_test():
    """Create comprehensive POS test covering all 200+ parameters"""
    
    print("Creating comprehensive POS test files...")
    
    # Portfolio level configuration
    portfolio_params = {
        'PortfolioName': ['POS_Comprehensive_Test'],
        'Strategies': ['IronCondor,IronFly,CalendarSpread,Butterfly,CustomGreeks'],
        'Instrument': ['NIFTY'],
        'Start': ['01-04-2024'],
        'End': ['05-04-2024'],
        'Capital': [1000000],
        'IsCapitalDivided': ['YES']
    }
    
    # Strategy configurations with all advanced features
    strategy_params = {
        'StrategyId': ['IronCondor', 'IronFly', 'CalendarSpread', 'Butterfly', 'CustomGreeks'],
        'FileName': ['pos_iron_condor.xlsx', 'pos_iron_fly.xlsx', 'pos_calendar.xlsx', 
                    'pos_butterfly.xlsx', 'pos_custom_greeks.xlsx'],
        'Multiplier': [1, 1, 2, 1, 1],
        'MaxOrderPerStrategy': [1, 1, 2, 1, 1],
        # Advanced POS features
        'UseMarketStructure': ['yes', 'yes', 'no', 'yes', 'yes'],
        'UseAdjustmentRules': ['yes', 'yes', 'yes', 'no', 'yes'],
        'UseGreekLimits': ['yes', 'yes', 'no', 'yes', 'yes'],
        'UseVolatilityFilters': ['yes', 'no', 'yes', 'yes', 'yes']
    }
    
    # Create comprehensive Iron Condor strategy with all features
    iron_condor_general = {
        'StrategyName': ['IronCondor_Advanced'],
        'StartTime': [91600],
        'EndTime': [151500],
        'LastEntryTime': [143000],
        'Underlying': ['SPOT'],
        'Capital': [200000],
        # Entry conditions
        'MinIVP': [30],  # Implied Volatility Percentile
        'MaxIVP': [80],
        'MinIVR': [0.3],  # Implied Volatility Rank
        'MaxIVR': [0.8],
        'MinATR': [100],  # Average True Range
        'MaxATR': [300],
        'VIXMin': [12],
        'VIXMax': [25],
        # Greek limits
        'MaxDelta': [0.3],
        'MinDelta': [-0.3],
        'MaxGamma': [0.05],
        'MaxVega': [1000],
        'MaxTheta': [-500],
        # Adjustment rules
        'DeltaAdjustmentTrigger': [0.25],
        'ProfitTarget': [50],  # percentage
        'LossLimit': [100],    # percentage
        'TimeBasedExit': [50], # percentage of DTE
        # Advanced features
        'UseBreakevenAnalysis': ['yes'],
        'BreakevenMode': ['probability_based'],
        'MinProbabilityOfProfit': [60],
        'UseKellyCriterion': ['yes'],
        'MaxKellyPercentage': [25]
    }
    
    iron_condor_legs = {
        'StrategyName': ['IronCondor_Advanced'] * 4,
        'LegID': ['a1', 'a2', 'a3', 'a4'],
        'Instrument': ['call', 'call', 'put', 'put'],
        'Transaction': ['sell', 'buy', 'sell', 'buy'],
        'Expiry': ['current', 'current', 'current', 'current'],
        'StrikeMethod': ['OTM2', 'OTM3', 'OTM2', 'OTM3'],
        'StrikeValue': [0, 0, 0, 0],
        'Lots': [1, 1, 1, 1],
        # Advanced strike selection
        'StrikeSelectionMode': ['delta_based', 'delta_based', 'delta_based', 'delta_based'],
        'TargetDelta': [0.2, 0.1, -0.2, -0.1],
        'UseSkewAdjustment': ['yes', 'yes', 'yes', 'yes'],
        'SkewThreshold': [5, 5, 5, 5]
    }
    
    # Create adjustment rules sheet
    adjustment_rules = {
        'RuleName': ['DeltaAdjustment', 'ProfitTarget', 'TimeDecay', 'VolatilitySpike'],
        'Trigger': ['Delta > 0.25', 'Profit > 50%', 'DTE < 10', 'IV increase > 20%'],
        'Action': ['Close and re-enter', 'Close position', 'Close position', 'Reduce size by 50%'],
        'Priority': [1, 2, 3, 4],
        'Enabled': ['yes', 'yes', 'yes', 'yes']
    }
    
    # Create market structure sheet
    market_structure = {
        'Level': ['Support1', 'Support2', 'Resistance1', 'Resistance2', 'POC'],
        'Price': [21800, 21600, 22200, 22400, 22000],
        'Strength': ['strong', 'medium', 'strong', 'medium', 'strong'],
        'ValidFrom': ['2024-04-01', '2024-04-01', '2024-04-01', '2024-04-01', '2024-04-01'],
        'ValidTo': ['2024-04-05', '2024-04-05', '2024-04-05', '2024-04-05', '2024-04-05']
    }
    
    # Create files
    pos_dir = os.path.join(BASE_DIR, 'pos')
    
    # Portfolio file
    portfolio_file = os.path.join(pos_dir, 'comprehensive_pos_portfolio.xlsx')
    with pd.ExcelWriter(portfolio_file, engine='openpyxl') as writer:
        pd.DataFrame(portfolio_params).to_excel(writer, sheet_name='PortfolioSetting', index=False)
        pd.DataFrame(strategy_params).to_excel(writer, sheet_name='StrategySetting', index=False)
    
    # Iron Condor strategy file with all sheets
    iron_condor_file = os.path.join(pos_dir, 'pos_iron_condor.xlsx')
    with pd.ExcelWriter(iron_condor_file, engine='openpyxl') as writer:
        pd.DataFrame(iron_condor_general).to_excel(writer, sheet_name='GeneralParameter', index=False)
        pd.DataFrame(iron_condor_legs).to_excel(writer, sheet_name='LegParameter', index=False)
        pd.DataFrame(adjustment_rules).to_excel(writer, sheet_name='AdjustmentRules', index=False)
        pd.DataFrame(market_structure).to_excel(writer, sheet_name='MarketStructure', index=False)
    
    print(f"Created comprehensive POS test files in {pos_dir}")

def create_ml_indicator_comprehensive_test():
    """Create comprehensive ML Indicator test covering all features"""
    
    print("Creating comprehensive ML Indicator test files...")
    
    # ML Indicator configuration
    ml_config = {
        'StrategyName': ['ML_Technical', 'ML_SmartMoney', 'ML_Ensemble', 'ML_Pattern'],
        'ModelType': ['technical_indicators', 'smart_money_concepts', 'ensemble', 'pattern_recognition'],
        'StartTime': [91600, 93000, 91600, 100000],
        'EndTime': [151500, 151500, 151500, 151500],
        'LastEntryTime': [150000, 145000, 150000, 143000],
        'Underlying': ['SPOT', 'FUT', 'SPOT', 'SPOT'],
        'Capital': [100000, 150000, 200000, 100000]
    }
    
    # Technical indicators configuration
    technical_indicators = {
        'StrategyName': ['ML_Technical'] * 10,
        'IndicatorName': ['SMA_20', 'SMA_50', 'EMA_20', 'RSI_14', 'MACD', 
                         'BollingerBands', 'Stochastic', 'ADX', 'ATR', 'Volume'],
        'Parameters': ['20', '50', '20', '14', '12,26,9', 
                      '20,2', '14,3,3', '14', '14', '20'],
        'Condition': ['price > value', 'price > value', 'price > value', 'value < 30', 'histogram > 0',
                     'price < lower_band', 'K < 20', 'value > 25', 'value > 100', 'value > avg'],
        'Weight': [0.15, 0.10, 0.15, 0.15, 0.10, 
                  0.10, 0.10, 0.05, 0.05, 0.05],
        'Timeframe': ['5min', '15min', '5min', '5min', '15min',
                     '15min', '5min', '15min', '15min', '5min']
    }
    
    # Smart Money Concepts configuration
    smart_money = {
        'StrategyName': ['ML_SmartMoney'] * 8,
        'ConceptName': ['OrderBlock', 'FairValueGap', 'BreakOfStructure', 'ChochMSS',
                       'LiquidityPool', 'PremiumDiscount', 'Inducement', 'OptimalTradeEntry'],
        'Parameters': ['lookback=50', 'min_gap=0.1%', 'swing_points=3', 'confirmation=true',
                      'volume_threshold=1.5x', 'fib_levels=0.5,0.618', 'trap_detection=true', 'risk_reward=1:3'],
        'EntryCondition': ['price_at_ob', 'price_in_fvg', 'bos_confirmed', 'choch_confirmed',
                          'liquidity_swept', 'discount_zone', 'trap_triggered', 'all_confluences'],
        'StopLoss': ['below_ob', 'opposite_fvg', 'previous_low', 'previous_structure',
                    'below_liquidity', 'premium_zone', 'trap_high', 'invalidation_point'],
        'TakeProfit': ['next_ob', 'fvg_close', 'next_structure', 'measured_move',
                      'next_liquidity', 'equilibrium', 'trapped_high', 'optimal_target']
    }
    
    # ML model predictions
    ml_predictions = {
        'StrategyName': ['ML_Ensemble'] * 5,
        'ModelName': ['RandomForest', 'XGBoost', 'LSTM', 'GRU', 'Ensemble'],
        'Features': ['OHLCV,Technical', 'All_Features', 'Price_Series', 'Price_Series', 'All_Models'],
        'PredictionType': ['direction', 'price_target', 'direction', 'volatility', 'weighted_avg'],
        'ConfidenceThreshold': [0.7, 0.75, 0.65, 0.7, 0.8],
        'UpdateFrequency': ['daily', 'hourly', 'real_time', 'real_time', 'real_time'],
        'Weight': [0.2, 0.25, 0.2, 0.15, 0.2]
    }
    
    # Pattern recognition
    patterns = {
        'StrategyName': ['ML_Pattern'] * 12,
        'PatternName': ['HeadAndShoulders', 'DoubleTop', 'DoubleBottom', 'Triangle',
                       'Flag', 'Pennant', 'Wedge', 'Channel',
                       'CupAndHandle', 'RoundingBottom', 'Breakout', 'Reversal'],
        'MinBars': [20, 15, 15, 20, 10, 10, 15, 20, 30, 25, 5, 10],
        'Accuracy': [0.7, 0.75, 0.75, 0.65, 0.7, 0.7, 0.65, 0.6, 0.8, 0.75, 0.7, 0.7],
        'ConfirmationRequired': ['volume', 'break', 'break', 'break', 
                                'continuation', 'continuation', 'break', 'touch',
                                'break', 'volume', 'volume_price', 'divergence']
    }
    
    # Create files
    ml_dir = os.path.join(BASE_DIR, 'ml_indicator')
    
    # Main configuration
    config_file = os.path.join(ml_dir, 'comprehensive_ml_config.xlsx')
    with pd.ExcelWriter(config_file, engine='openpyxl') as writer:
        pd.DataFrame(ml_config).to_excel(writer, sheet_name='Configuration', index=False)
        pd.DataFrame(technical_indicators).to_excel(writer, sheet_name='TechnicalIndicators', index=False)
        pd.DataFrame(smart_money).to_excel(writer, sheet_name='SmartMoneyConcepts', index=False)
        pd.DataFrame(ml_predictions).to_excel(writer, sheet_name='MLPredictions', index=False)
        pd.DataFrame(patterns).to_excel(writer, sheet_name='PatternRecognition', index=False)
    
    print(f"Created comprehensive ML Indicator test files in {ml_dir}")

def create_edge_case_tests():
    """Create edge case test files for all strategies"""
    
    print("Creating edge case test files...")
    
    # Create subdirectory for edge cases
    edge_dir = os.path.join(BASE_DIR, 'edge_cases')
    os.makedirs(edge_dir, exist_ok=True)
    
    # Holiday/expiry day scenarios
    holiday_params = {
        'StrategyName': ['Holiday_Test', 'Expiry_Test', 'Weekend_Test'],
        'Date': ['2024-04-10', '2024-04-25', '2024-04-06'],  # Wed holiday, Thu expiry, Sat
        'ExpectedBehavior': ['Skip trading', 'Trade next expiry', 'No execution'],
        'SpecialHandling': ['yes', 'yes', 'yes']
    }
    
    # Circuit limit scenarios
    circuit_params = {
        'StrategyName': ['UpperCircuit', 'LowerCircuit', 'BothCircuits'],
        'ScenarioType': ['UC_hit', 'LC_hit', 'Volatile'],
        'ExpectedHandling': ['Exit at available price', 'Exit at available price', 'Skip entry']
    }
    
    # Data quality issues
    data_issues = {
        'StrategyName': ['MissingData', 'DelayedData', 'CorruptData'],
        'IssueType': ['gaps', 'latency', 'invalid_values'],
        'ExpectedBehavior': ['Interpolate/Skip', 'Use last known', 'Reject entry']
    }
    
    pd.DataFrame(holiday_params).to_excel(
        os.path.join(edge_dir, 'holiday_scenarios.xlsx'), index=False
    )
    pd.DataFrame(circuit_params).to_excel(
        os.path.join(edge_dir, 'circuit_scenarios.xlsx'), index=False
    )
    pd.DataFrame(data_issues).to_excel(
        os.path.join(edge_dir, 'data_quality_issues.xlsx'), index=False
    )
    
    print(f"Created edge case tests in {edge_dir}")

def validate_created_files():
    """Validate that all comprehensive test files cover required columns"""
    
    print("\nValidating created test files...")
    
    validations = {
        'oi': {
            'file': 'comprehensive_oi_test_all_columns.xlsx',
            'required_columns': {
                'GeneralParameter': ['StrategyName', 'Timeframe', 'MaxOpenPositions', 'OiThreshold'],
                'LegParameter': ['StrategyName', 'OiThreshold', 'StrikeMethod', 'LegID']
            }
        },
        'tbs': {
            'file': 'comprehensive_tbs_portfolio.xlsx',
            'required_columns': {
                'PortfolioSetting': ['PortfolioName', 'Strategies', 'Instrument'],
                'StrategySetting': ['StrategyId', 'FileName', 'Multiplier']
            }
        },
        'orb': {
            'file': 'comprehensive_orb_test.xlsx',
            'required_columns': {
                'GeneralParameter': ['StrategyName', 'OrbRangeStart', 'OrbRangeEnd'],
                'LegParameter': ['StrategyName', 'Direction', 'LegID']
            }
        }
    }
    
    for strategy, config in validations.items():
        filepath = os.path.join(BASE_DIR, strategy, config['file'])
        if os.path.exists(filepath):
            print(f"\n✓ {strategy.upper()} test file exists: {filepath}")
            
            # Check required columns
            with pd.ExcelFile(filepath) as xls:
                for sheet, required_cols in config['required_columns'].items():
                    if sheet in xls.sheet_names:
                        df = pd.read_excel(xls, sheet_name=sheet)
                        missing = set(required_cols) - set(df.columns)
                        if missing:
                            print(f"  ⚠ Missing columns in {sheet}: {missing}")
                        else:
                            print(f"  ✓ All required columns present in {sheet}")
                    else:
                        print(f"  ✗ Missing sheet: {sheet}")
        else:
            print(f"\n✗ {strategy.upper()} test file not found: {filepath}")

def main():
    """Create all comprehensive test files"""
    
    print("Creating comprehensive test files for all strategies...")
    print(f"Output directory: {BASE_DIR}")
    
    # Create directory structure
    create_directory_structure()
    
    # Create comprehensive tests for each strategy
    create_oi_comprehensive_test()
    create_tbs_comprehensive_test()
    create_tv_comprehensive_test()
    create_orb_comprehensive_test()
    create_pos_comprehensive_test()
    create_ml_indicator_comprehensive_test()
    
    # Create edge case tests
    create_edge_case_tests()
    
    # Validate created files
    validate_created_files()
    
    print("\n✅ Comprehensive test file creation complete!")
    print(f"Files created in: {BASE_DIR}")
    print("\nNext steps:")
    print("1. Run these test files through both archive and new systems")
    print("2. Compare outputs trade-by-trade")
    print("3. Document any differences (especially ATM calculations)")
    print("4. Fix any discrepancies found")

if __name__ == "__main__":
    main()