#!/usr/bin/env python3
"""
Create comprehensive TBS test input sheets covering ALL edge cases and ATM strike logic variations.
Based on detailed analysis of column mapping, strike selection, and business rules.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
from pathlib import Path
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ComprehensiveTBSTestCreator:
    """Creates comprehensive TBS test input files covering all edge cases."""
    
    def __init__(self):
        self.output_dir = Path("/srv/samba/shared/test_inputs_comprehensive")
        self.output_dir.mkdir(exist_ok=True)
        
        # Test date ranges
        self.start_date = "2024-01-01"
        self.end_date = "2024-01-31"
        
        # All possible values from parser analysis
        self.strike_selection_methods = [
            "ATM", "ITM1", "ITM2", "ITM3", "ITM4", "ITM5", "ITM6", "ITM7", "ITM8", "ITM9", "ITM10",
            "OTM1", "OTM2", "OTM3", "OTM4", "OTM5", "OTM6", "OTM7", "OTM8", "OTM9", "OTM10",
            "FIXED", "PREMIUM", "DELTA", "ATM_WIDTH", "STRADDLE_WIDTH"
        ]
        
        self.risk_types = ["PERCENTAGE", "POINT", "INDEXPOINT", "ABSOLUTE"]
        self.boolean_variations = [("YES", "NO"), ("Y", "N"), ("1", "0"), (True, False)]
        self.time_formats = ["91600", "9:16:00", "09:16:00", "915", "0915"]
        
    def create_portfolio_setting_comprehensive(self) -> pd.DataFrame:
        """Create comprehensive PortfolioSetting sheet covering all variations."""
        
        # Base settings with multiple test scenarios
        scenarios = [
            {
                "portfolio_name": "COMPREHENSIVE_ATM_TEST",
                "symbol": "NIFTY",
                "start_date": self.start_date,
                "end_date": self.end_date,
                "lot_size": 50,
                "capital": 1000000
            },
            {
                "portfolio_name": "EDGE_CASE_TESTING",
                "symbol": "BANKNIFTY", 
                "start_date": self.start_date,
                "end_date": self.end_date,
                "lot_size": 25,
                "capital": 500000
            },
            {
                "portfolio_name": "DTE_FILTERING_TEST",
                "symbol": "NIFTY",
                "start_date": self.start_date,
                "end_date": self.end_date,
                "lot_size": 50,
                "capital": 1000000
            }
        ]
        
        portfolio_df = pd.DataFrame(scenarios)
        logger.info(f"Created {len(portfolio_df)} portfolio scenarios")
        return portfolio_df
    
    def create_strategy_setting_comprehensive(self) -> pd.DataFrame:
        """Create comprehensive StrategySetting sheet."""
        
        strategies = []
        
        # Test 1: All ATM Strike Selection Variations
        for i, strike_method in enumerate(self.strike_selection_methods[:10]):  # First 10 for manageability
            strategies.append({
                "strategy_name": f"ATM_TEST_{strike_method}_{i+1:02d}",
                "portfolio_name": "COMPREHENSIVE_ATM_TEST",
                "strategy_enabled": "YES",
                "max_trades_per_day": 1,
                "description": f"Testing {strike_method} strike selection with comprehensive parameters"
            })
        
        # Test 2: Risk Management Edge Cases
        for i, risk_type in enumerate(self.risk_types):
            strategies.append({
                "strategy_name": f"RISK_TEST_{risk_type}_{i+1:02d}",
                "portfolio_name": "EDGE_CASE_TESTING",
                "strategy_enabled": "YES", 
                "max_trades_per_day": 3,
                "description": f"Testing {risk_type} risk management with extreme values"
            })
        
        # Test 3: DTE Filtering Tests
        for dte in [0, 1, 2, 3]:
            strategies.append({
                "strategy_name": f"DTE_{dte}_EXPIRY_TEST",
                "portfolio_name": "DTE_FILTERING_TEST",
                "strategy_enabled": "YES",
                "max_trades_per_day": 1,
                "description": f"Testing DTE={dte} filtering logic"
            })
        
        strategy_df = pd.DataFrame(strategies)
        logger.info(f"Created {len(strategy_df)} strategy scenarios")
        return strategy_df
    
    def create_general_parameters_comprehensive(self) -> pd.DataFrame:
        """Create comprehensive GeneralParameter sheet covering all edge cases."""
        
        general_params = []
        
        # Test 1: ATM Strike Selection Variations
        for i, strike_method in enumerate(self.strike_selection_methods[:10]):
            general_params.append({
                "strategy_name": f"ATM_TEST_{strike_method}_{i+1:02d}",
                "symbol": "NIFTY",
                "product_type": "MIS",
                "dte": 0 if i % 2 == 0 else 1,  # Alternate DTE for comparison
                "start_time": "09:15:00",
                "end_time": "15:29:00",
                "last_entry_time": "14:00:00",
                "strike_selection_time": "09:20:00",
                "re_entry": "YES" if i % 3 == 0 else "NO",
                "max_re_entry": 2 if i % 3 == 0 else 0,
                "weekdays": "1,2,3,4,5",
                "on_expiry_day_trade_next_expiry": "NO",
                "sq_off_1_time": "14:30:00",
                "sq_off_1_percent": 50,
                "sq_off_2_time": "15:15:00", 
                "sq_off_2_percent": 100,
                "comments": f"Comprehensive test for {strike_method} with DTE={0 if i % 2 == 0 else 1}"
            })
        
        # Test 2: Risk Management Edge Cases
        for i, risk_type in enumerate(self.risk_types):
            general_params.append({
                "strategy_name": f"RISK_TEST_{risk_type}_{i+1:02d}",
                "symbol": "BANKNIFTY",
                "product_type": "NRML",
                "dte": 0,
                "start_time": "09:15:00",
                "end_time": "15:29:00",
                "last_entry_time": "13:00:00",
                "strike_selection_time": "09:25:00",
                "re_entry": "YES",
                "max_re_entry": 5,  # Maximum re-entries
                "weekdays": "1,2,3,4,5",
                "on_expiry_day_trade_next_expiry": "YES",
                "sq_off_1_time": "12:00:00",
                "sq_off_1_percent": 25,
                "sq_off_2_time": "14:00:00",
                "sq_off_2_percent": 75,
                "comments": f"Testing {risk_type} risk management with extreme parameters"
            })
        
        # Test 3: DTE Filtering Edge Cases
        for dte in [0, 1, 2, 3]:
            general_params.append({
                "strategy_name": f"DTE_{dte}_EXPIRY_TEST",
                "symbol": "NIFTY",
                "product_type": "MIS",
                "dte": dte,
                "start_time": "09:15:00",
                "end_time": "15:29:00",
                "last_entry_time": "14:30:00",
                "strike_selection_time": "09:15:00",  # Same as start time
                "re_entry": "NO",
                "max_re_entry": 0,
                "weekdays": "1,2,3,4,5",
                "on_expiry_day_trade_next_expiry": "NO",
                "sq_off_1_time": "",  # No partial exits
                "sq_off_1_percent": 0,
                "sq_off_2_time": "",
                "sq_off_2_percent": 0,
                "comments": f"DTE={dte} expiry filtering test - should only trade {dte} days before expiry"
            })
        
        general_df = pd.DataFrame(general_params)
        logger.info(f"Created {len(general_df)} general parameter scenarios")
        return general_df
    
    def create_leg_parameters_comprehensive(self) -> pd.DataFrame:
        """Create comprehensive LegParameter sheet covering all possible combinations."""
        
        leg_params = []
        
        # Test 1: ATM Strike Selection - Single Leg Tests
        strike_methods_subset = self.strike_selection_methods[:10]
        
        for i, strike_method in enumerate(strike_methods_subset):
            strategy_name = f"ATM_TEST_{strike_method}_{i+1:02d}"
            
            # Single leg for pure ATM testing
            leg_params.append({
                "strategy_name": strategy_name,
                "leg_id": 1,
                "option_type": "CE" if i % 2 == 0 else "PE",
                "action": "SELL",
                "quantity": 1,
                "strike_selection": strike_method,
                "strike_value": 22000 if strike_method == "FIXED" else "",
                "premium_condition": ">=" if strike_method == "PREMIUM" else "",
                "premium_value": 50 if strike_method == "PREMIUM" else "",
                "delta_value": 0.3 if strike_method == "DELTA" else "",
                "sl_type": self.risk_types[i % len(self.risk_types)],
                "sl_value": 500 if i % 4 == 0 else 50,  # Mix of high/low SL
                "tgt_type": self.risk_types[(i+1) % len(self.risk_types)],
                "tgt_value": 25,
                "trail_sl": "YES" if i % 3 == 0 else "NO",
                "trail_sl_type": "POINT",
                "trail_sl_value": 10,
                "comments": f"Single leg test for {strike_method}"
            })
        
        # Test 2: Risk Management Edge Cases - Multi-leg Iron Condor
        for i, risk_type in enumerate(self.risk_types):
            strategy_name = f"RISK_TEST_{risk_type}_{i+1:02d}"
            
            # 4-leg Iron Condor with extreme risk values
            legs = [
                {"leg_id": 1, "option_type": "CE", "action": "SELL", "strike_selection": "ATM"},
                {"leg_id": 2, "option_type": "PE", "action": "SELL", "strike_selection": "ATM"},
                {"leg_id": 3, "option_type": "CE", "action": "BUY", "strike_selection": "OTM2"},
                {"leg_id": 4, "option_type": "PE", "action": "BUY", "strike_selection": "OTM2"}
            ]
            
            for leg in legs:
                leg_params.append({
                    "strategy_name": strategy_name,
                    "leg_id": leg["leg_id"],
                    "option_type": leg["option_type"],
                    "action": leg["action"],
                    "quantity": 1,
                    "strike_selection": leg["strike_selection"],
                    "strike_value": "",
                    "premium_condition": "",
                    "premium_value": "",
                    "delta_value": "",
                    "sl_type": risk_type,
                    "sl_value": 10000 if risk_type == "ABSOLUTE" else 500,  # Extreme values
                    "tgt_type": risk_type,
                    "tgt_value": 5000 if risk_type == "ABSOLUTE" else 50,
                    "trail_sl": "YES",
                    "trail_sl_type": risk_type,
                    "trail_sl_value": 100,
                    "comments": f"Multi-leg {risk_type} risk test"
                })
        
        # Test 3: DTE Filtering - Simple Straddle
        for dte in [0, 1, 2, 3]:
            strategy_name = f"DTE_{dte}_EXPIRY_TEST"
            
            # Simple straddle for DTE testing
            for leg_id, option_type in [(1, "CE"), (2, "PE")]:
                leg_params.append({
                    "strategy_name": strategy_name,
                    "leg_id": leg_id,
                    "option_type": option_type,
                    "action": "SELL",
                    "quantity": 1,
                    "strike_selection": "ATM",
                    "strike_value": "",
                    "premium_condition": "",
                    "premium_value": "",
                    "delta_value": "",
                    "sl_type": "PERCENTAGE",
                    "sl_value": 500,  # 500% stop loss
                    "tgt_type": "PERCENTAGE", 
                    "tgt_value": 50,   # 50% target
                    "trail_sl": "NO",
                    "trail_sl_type": "",
                    "trail_sl_value": "",
                    "comments": f"DTE={dte} straddle test"
                })
        
        leg_df = pd.DataFrame(leg_params)
        logger.info(f"Created {len(leg_df)} leg parameter scenarios")
        return leg_df
    
    def create_test_files(self):
        """Create all comprehensive test files."""
        logger.info("Creating comprehensive TBS test input files...")
        
        # Create Portfolio Excel
        portfolio_file = self.output_dir / "TBS_COMPREHENSIVE_PORTFOLIO.xlsx"
        with pd.ExcelWriter(portfolio_file, engine='openpyxl') as writer:
            portfolio_df = self.create_portfolio_setting_comprehensive()
            strategy_df = self.create_strategy_setting_comprehensive()
            
            portfolio_df.to_excel(writer, sheet_name='PortfolioSetting', index=False)
            strategy_df.to_excel(writer, sheet_name='StrategySetting', index=False)
        
        logger.info(f"Created portfolio file: {portfolio_file}")
        
        # Create TBS Multi-leg Excel
        tbs_file = self.output_dir / "TBS_COMPREHENSIVE_MULTI_LEGS.xlsx"
        with pd.ExcelWriter(tbs_file, engine='openpyxl') as writer:
            general_df = self.create_general_parameters_comprehensive()
            leg_df = self.create_leg_parameters_comprehensive()
            
            general_df.to_excel(writer, sheet_name='GeneralParameter', index=False)
            leg_df.to_excel(writer, sheet_name='LegParameter', index=False)
        
        logger.info(f"Created TBS file: {tbs_file}")
        
        # Create test scenarios documentation
        self.create_test_documentation()
        
        return portfolio_file, tbs_file
    
    def create_test_documentation(self):
        """Create detailed documentation of test scenarios."""
        
        doc_content = f"""# Comprehensive TBS Testing Documentation

**Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Test Coverage Summary

### 1. Strike Selection Method Testing
Tests all {len(self.strike_selection_methods)} strike selection methods:
- ATM-based: ATM, ATM_WIDTH, STRADDLE_WIDTH
- ITM methods: ITM1 through ITM10
- OTM methods: OTM1 through OTM10  
- Value-based: FIXED, PREMIUM, DELTA

### 2. Risk Management Type Testing
Tests all {len(self.risk_types)} risk management types:
- PERCENTAGE: Risk as percentage of premium
- POINT: Risk in absolute points
- INDEXPOINT: Risk in index points
- ABSOLUTE: Risk in absolute rupee terms

### 3. DTE (Days to Expiry) Filtering
Tests DTE values: 0, 1, 2, 3
- DTE=0: Should only execute on expiry day
- DTE=1-3: Should execute 1-3 days before expiry

### 4. Edge Cases Covered
- **Time Format Variations**: Multiple time formats tested
- **Boolean Variations**: YES/NO, Y/N, 1/0, True/False
- **Extreme Values**: Very high SL/Target values
- **Multi-leg Strategies**: 4-leg Iron Condor
- **Re-entry Logic**: With various max re-entry limits
- **Partial Exits**: Multiple sq-off times and percentages

## Expected Test Results

### ATM Calculation Verification
Each strike selection method should produce different ATM strikes:
- **Archive System**: Spot-based ATM (round(spot/50)*50)
- **New System**: Synthetic future ATM (strike + CE - PE closest to spot)
- **Expected Difference**: 50-100 points (1-2 strikes)

### Critical Validations Required
1. **DTE=0 trades only on expiry day**: Verify trade_date = expiry_date
2. **Strike selection accuracy**: Verify ITM3 is 3 strikes in-the-money
3. **Risk management triggering**: Verify SL/Target calculations
4. **Time filtering**: Verify trades only within specified time windows
5. **Multi-leg coordination**: Verify all legs execute together

## Test Execution Priority
1. **High Priority**: ATM calculation differences
2. **High Priority**: DTE=0 filtering accuracy  
3. **Medium Priority**: Risk management variations
4. **Medium Priority**: Multi-leg strategy coordination
5. **Low Priority**: Edge case handling

## Files Generated
- `TBS_COMPREHENSIVE_PORTFOLIO.xlsx`: Portfolio and strategy settings
- `TBS_COMPREHENSIVE_MULTI_LEGS.xlsx`: General and leg parameters
- Total test scenarios: {len(self.strike_selection_methods[:10]) + len(self.risk_types) + 4} strategies
- Total leg combinations: Varies by strategy complexity

## Validation Instructions
1. Run both Archive and New system with identical input files
2. Compare strike selection results trade-by-trade
3. Verify DTE filtering works correctly
4. Check risk management triggering accuracy
5. Validate output format matches golden template
"""
        
        doc_file = self.output_dir / "COMPREHENSIVE_TEST_DOCUMENTATION.md"
        with open(doc_file, 'w') as f:
            f.write(doc_content)
        
        logger.info(f"Created documentation: {doc_file}")

def main():
    """Create comprehensive TBS test inputs."""
    creator = ComprehensiveTBSTestCreator()
    portfolio_file, tbs_file = creator.create_test_files()
    
    print(f"\n✅ COMPREHENSIVE TBS TEST FILES CREATED")
    print(f"📁 Location: /srv/samba/shared/test_inputs_comprehensive/")
    print(f"📊 Portfolio File: {portfolio_file.name}")
    print(f"📊 TBS Multi-leg File: {tbs_file.name}")
    print(f"📋 Documentation: COMPREHENSIVE_TEST_DOCUMENTATION.md")
    print(f"\n🎯 Ready for comprehensive ATM strike logic validation!")

if __name__ == "__main__":
    main()