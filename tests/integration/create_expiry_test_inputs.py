#!/usr/bin/env python3
"""
Create Test Input Files for Expiry Selection Validation
=======================================================

Tests mapping between:
- Archive: current, next, monthly expiry selections
- GPU: CW, NW, CM, NM expiry selections
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ExpiryTestInputCreator:
    """Creates test inputs for expiry selection validation."""
    
    def __init__(self):
        self.test_dir = Path("/srv/samba/shared/expiry_test_inputs")
        self.test_dir.mkdir(exist_ok=True)
        
    def create_portfolio_file(self):
        """Create portfolio settings file."""
        
        # Portfolio settings
        portfolio_data = []
        
        # Test different expiry selections
        expiry_tests = [
            ("EXPIRY_CW_TEST", "Test Current Week expiry selection"),
            ("EXPIRY_NW_TEST", "Test Next Week expiry selection"),
            ("EXPIRY_CM_TEST", "Test Current Month expiry selection"),
            ("EXPIRY_NM_TEST", "Test Next Month expiry selection"),
        ]
        
        for portfolio_name, description in expiry_tests:
            portfolio_data.append({
                'portfolio_name': portfolio_name,
                'description': description,
                'initial_capital': 1000000,
                'broker': 'PAPER',
                'exchange': 'NFO',
                'segment': 'FO',
                'product_type': 'INTRADAY',
                'order_type': 'MARKET',
                'slippage': 0,
                'brokerage_type': 'PERCENTAGE',
                'brokerage_value': 0.01,
                'max_trades_per_day': 10,
                'max_open_positions': 5,
                'position_sizing': 'FIXED',
                'position_size': 1
            })
        
        portfolio_df = pd.DataFrame(portfolio_data)
        
        # Strategy settings - enable all expiry tests
        strategy_data = []
        
        for i, (portfolio_name, _) in enumerate(expiry_tests):
            # For Archive system - uses strategy naming
            archive_strategy = portfolio_name.replace("EXPIRY_", "").replace("_TEST", "")
            
            strategy_data.append({
                'strategy_name': portfolio_name,
                'portfolio_name': portfolio_name,
                'strategy_type': 'TBS',
                'strategy_enabled': 'YES',
                'start_date': '240101',
                'end_date': '240110',
                'archive_mapping': archive_strategy  # Maps to current/next/monthly
            })
        
        strategy_df = pd.DataFrame(strategy_data)
        
        # Save portfolio file
        portfolio_file = self.test_dir / "EXPIRY_TEST_PORTFOLIO.xlsx"
        with pd.ExcelWriter(portfolio_file, engine='openpyxl') as writer:
            portfolio_df.to_excel(writer, sheet_name='PortfolioSetting', index=False)
            strategy_df.to_excel(writer, sheet_name='StrategySetting', index=False)
        
        logger.info(f"Created portfolio file: {portfolio_file}")
        return portfolio_file
        
    def create_tbs_file(self):
        """Create TBS multi-leg file with expiry specifications."""
        
        # General parameters
        general_data = []
        
        expiry_mappings = [
            ("EXPIRY_CW_TEST", "current", "CW", "Current Week"),
            ("EXPIRY_NW_TEST", "next", "NW", "Next Week"),
            ("EXPIRY_CM_TEST", "monthly", "CM", "Current Month"),
            ("EXPIRY_NM_TEST", "next_monthly", "NM", "Next Month"),
        ]
        
        for strategy_name, archive_expiry, gpu_expiry, description in expiry_mappings:
            general_data.append({
                'strategy_name': strategy_name,
                'description': f'{description} expiry test',
                'underlying': 'NIFTY',
                'start_time': '09:20:00',
                'end_time': '15:15:00',
                'square_off_time': '15:15:00',
                'strike_selection_time': '09:20:00',
                'archive_expiry': archive_expiry,  # For archive system
                'gpu_expiry': gpu_expiry,           # For GPU system
                'max_trades_per_day': 1,
                'trade_continuous': 'NO',
                'trail_sl': 'NO',
                'incremental_pl': 'NO',
                'overall_sl_type': 'PERCENTAGE',
                'overall_sl_value': 2,
                'overall_tp_type': 'PERCENTAGE',
                'overall_tp_value': 3
            })
        
        general_df = pd.DataFrame(general_data)
        
        # Leg parameters - simple ATM straddle for each
        leg_data = []
        
        for strategy_name, archive_expiry, gpu_expiry, _ in expiry_mappings:
            # CE leg
            leg_data.append({
                'strategy_name': strategy_name,
                'leg_id': 1,
                'leg_name': f'{gpu_expiry}_CE',
                'option_type': 'CE',
                'action': 'SELL',
                'quantity': 50,
                'strike_selection': 'ATM',
                'expiry_selection': archive_expiry,  # Archive uses this
                'gpu_expiry': gpu_expiry,            # GPU uses this
                'entry_type': 'MARKET',
                'exit_type': 'MARKET',
                'sl_type': 'PERCENTAGE',
                'sl_value': 25,
                'tp_type': 'PERCENTAGE',
                'tp_value': 50,
                'trail_sl': 'NO',
                'leg_enabled': 'YES'
            })
            
            # PE leg
            leg_data.append({
                'strategy_name': strategy_name,
                'leg_id': 2,
                'leg_name': f'{gpu_expiry}_PE',
                'option_type': 'PE',
                'action': 'SELL',
                'quantity': 50,
                'strike_selection': 'ATM',
                'expiry_selection': archive_expiry,  # Archive uses this
                'gpu_expiry': gpu_expiry,            # GPU uses this
                'entry_type': 'MARKET',
                'exit_type': 'MARKET',
                'sl_type': 'PERCENTAGE',
                'sl_value': 25,
                'tp_type': 'PERCENTAGE',
                'tp_value': 50,
                'trail_sl': 'NO',
                'leg_enabled': 'YES'
            })
        
        leg_df = pd.DataFrame(leg_data)
        
        # Save TBS file
        tbs_file = self.test_dir / "EXPIRY_TEST_TBS_MULTI_LEGS.xlsx"
        with pd.ExcelWriter(tbs_file, engine='openpyxl') as writer:
            general_df.to_excel(writer, sheet_name='GeneralParameter', index=False)
            leg_df.to_excel(writer, sheet_name='LegParameter', index=False)
        
        logger.info(f"Created TBS file: {tbs_file}")
        return tbs_file
        
    def create_test_documentation(self):
        """Create documentation for the test."""
        
        doc = """# EXPIRY SELECTION TEST DOCUMENTATION

## Test Objective
Validate that both Archive and GPU systems select the SAME expiry based on input file configuration.

## Expiry Mapping

| Test Case | Archive Term | GPU Term | Expected Expiry Type |
|-----------|--------------|----------|---------------------|
| EXPIRY_CW_TEST | current | CW | Current Week (nearest Thursday) |
| EXPIRY_NW_TEST | next | NW | Next Week (next Thursday) |
| EXPIRY_CM_TEST | monthly | CM | Current Month (last Thursday) |
| EXPIRY_NM_TEST | next_monthly | NM | Next Month (last Thursday) |

## Test Dates
- Start: January 1, 2024 (Monday)
- End: January 10, 2024

## Expected Behavior

For date 2024-01-01:
- CW (Current Week): Should select Jan 4, 2024 (Thursday)
- NW (Next Week): Should select Jan 11, 2024 (Next Thursday)
- CM (Current Month): Should select Jan 25, 2024 (Last Thursday of Jan)
- NM (Next Month): Should select Feb 29, 2024 (Last Thursday of Feb)

## Validation Steps

1. Both systems should select the SAME expiry date for each test case
2. Both systems should use the SAME strike (ATM based on synthetic future)
3. Both systems should generate the SAME trades
4. Golden output files should match exactly

## Files Created
- EXPIRY_TEST_PORTFOLIO.xlsx - Portfolio and strategy settings
- EXPIRY_TEST_TBS_MULTI_LEGS.xlsx - TBS configuration with expiry mappings
"""
        
        doc_file = self.test_dir / "EXPIRY_TEST_README.md"
        with open(doc_file, 'w') as f:
            f.write(doc)
        
        logger.info(f"Created documentation: {doc_file}")
        
    def run(self):
        """Create all test files."""
        
        logger.info("Creating expiry selection test inputs...")
        
        portfolio_file = self.create_portfolio_file()
        tbs_file = self.create_tbs_file()
        self.create_test_documentation()
        
        logger.info(f"\n✅ Test inputs created in: {self.test_dir}")
        logger.info("Files:")
        logger.info(f"  - {portfolio_file.name}")
        logger.info(f"  - {tbs_file.name}")
        logger.info("  - EXPIRY_TEST_README.md")
        
        return portfolio_file, tbs_file

def main():
    creator = ExpiryTestInputCreator()
    creator.run()

if __name__ == "__main__":
    main()