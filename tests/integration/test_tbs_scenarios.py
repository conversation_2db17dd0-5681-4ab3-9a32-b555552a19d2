
import pandas as pd
from datetime import datetime, date

# Test 1: DTE=0 filtering
def test_dte_zero():
    '''Test that DTE=0 only trades on expiry days'''
    test_dates = pd.date_range('2024-01-01', '2024-01-07')
    expiry_date = date(2024, 1, 4)  # Thursday
    
    for test_date in test_dates:
        should_trade = (test_date.date() == expiry_date)
        print(f"{test_date.date()}: {'TRADE' if should_trade else 'SKIP'}")

# Test 2: Strike Selection Time
def test_strike_selection_time():
    '''Test that strikes are selected at specified time'''
    strike_selection_time = 92000  # 09:20:00
    entry_time = 91600  # 09:16:00
    
    print(f"Strike selection should use {strike_selection_time}, not {entry_time}")

# Test 3: Weekday filtering  
def test_weekday_filter():
    '''Test weekday filtering'''
    weekdays = "2,4"  # Tuesday, Thursday only
    test_dates = pd.date_range('2024-01-01', '2024-01-07')
    
    for test_date in test_dates:
        dow = test_date.weekday() + 1  # Python 0=Mon, Excel 1=Mon
        should_trade = str(dow) in weekdays.split(',')
        print(f"{test_date.date()} ({test_date.strftime('%A')}): {'TRADE' if should_trade else 'SKIP'}")

if __name__ == "__main__":
    print("Running TBS Column Tests...")
    print("=" * 60)
    
    print("\nDTE=0 Test:")
    test_dte_zero()
    
    print("\nStrike Selection Time Test:")
    test_strike_selection_time()
    
    print("\nWeekday Filter Test:")
    test_weekday_filter()
