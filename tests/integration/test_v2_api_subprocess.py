#!/usr/bin/env python3
"""
Test the V2 API subprocess implementation
This tests the actual working endpoints that use subprocess to run backtests
"""

import requests
import json
import time
import os
from datetime import datetime

# API base URL
API_BASE_URL = "http://173.208.247.17:8000/api"

# Test files that exist
PORTFOLIO_FILE = "/srv/samba/shared/input_portfolio.xlsx"
TBS_STRATEGY_FILE = "/srv/samba/shared/input_tbs_multi_legs.xlsx"


def test_v2_subprocess_status():
    """Test V2 subprocess API status"""
    print("Testing V2 Subprocess API status...")
    try:
        response = requests.get(f"{API_BASE_URL}/v2/status")
        if response.status_code == 200:
            print(f"✅ V2 Subprocess API is operational")
            print(f"Response: {json.dumps(response.json(), indent=2)}")
            return True
        else:
            print(f"❌ API error: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False


def run_tbs_backtest_subprocess():
    """Run TBS backtest using subprocess API"""
    print("\n" + "="*50)
    print("Running TBS Backtest via Subprocess API...")
    
    # Check files
    if not os.path.exists(PORTFOLIO_FILE):
        print(f"❌ Portfolio file not found: {PORTFOLIO_FILE}")
        return None
    if not os.path.exists(TBS_STRATEGY_FILE):
        print(f"❌ Strategy file not found: {TBS_STRATEGY_FILE}")
        return None
    
    # Prepare request
    files = {
        'portfolio': ('input_portfolio.xlsx', open(PORTFOLIO_FILE, 'rb')),
        'strategy': ('input_tbs_multi_legs.xlsx', open(TBS_STRATEGY_FILE, 'rb'))
    }
    
    data = {
        'start_date': '2024-04-01',
        'end_date': '2024-04-10',
        'index_name': 'NIFTY'
    }
    
    try:
        # Try to get auth token first (if needed)
        # For now, we'll proceed without auth
        
        response = requests.post(
            f"{API_BASE_URL}/v2/strategies/tbs/backtest",
            files=files,
            data=data
        )
        
        # Close files
        for _, (_, file_obj) in files.items():
            file_obj.close()
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Backtest submitted!")
            print(f"Backtest ID: {result['backtest_id']}")
            print(f"Status: {result['status']}")
            return result['backtest_id']
        elif response.status_code == 401:
            print(f"❌ Authentication required. Need to login first.")
            return None
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return None


def check_subprocess_status(backtest_id):
    """Check backtest status via subprocess API"""
    print(f"\nChecking status for: {backtest_id}")
    
    try:
        response = requests.get(f"{API_BASE_URL}/v2/backtest/status/{backtest_id}")
        
        if response.status_code == 200:
            status = response.json()
            print(f"Status: {json.dumps(status, indent=2)}")
            return status
        else:
            print(f"❌ Error: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return None


def get_subprocess_result(backtest_id):
    """Get backtest result via subprocess API"""
    print(f"\nGetting result for: {backtest_id}")
    
    try:
        response = requests.get(f"{API_BASE_URL}/v2/backtest/result/{backtest_id}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Result: {json.dumps(result, indent=2)}")
            return result
        else:
            print(f"❌ Error: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return None


def test_direct_script_call():
    """Test calling the backtest script directly"""
    print("\n" + "="*50)
    print("Testing direct script execution...")
    
    import subprocess
    
    script_path = "/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py"
    
    if not os.path.exists(script_path):
        print(f"❌ Script not found: {script_path}")
        return
    
    # Build command
    cmd = [
        "python3", script_path,
        "--portfolio", PORTFOLIO_FILE,
        "--strategy", TBS_STRATEGY_FILE,
        "--start-date", "2024-04-01",
        "--end-date", "2024-04-10",
        "--output", "/tmp/test_backtest_output"
    ]
    
    print(f"Command: {' '.join(cmd)}")
    
    try:
        # Create output directory
        os.makedirs("/tmp/test_backtest_output", exist_ok=True)
        
        # Run with timeout
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=30,  # 30 second timeout
            cwd="/srv/samba/shared/bt/backtester_stable/BTRUN"
        )
        
        print(f"Return code: {result.returncode}")
        if result.stdout:
            print(f"STDOUT:\n{result.stdout[:1000]}")  # First 1000 chars
        if result.stderr:
            print(f"STDERR:\n{result.stderr[:1000]}")  # First 1000 chars
            
    except subprocess.TimeoutExpired:
        print("❌ Command timed out")
    except Exception as e:
        print(f"❌ Exception: {str(e)}")


def main():
    """Main test function"""
    print("GPU Backtester V2 Subprocess API Test")
    print("="*50)
    
    # Test V2 subprocess API
    if test_v2_subprocess_status():
        # Try to run backtest
        backtest_id = run_tbs_backtest_subprocess()
        
        if backtest_id:
            # Check status a few times
            for i in range(3):
                time.sleep(2)
                status = check_subprocess_status(backtest_id)
                if status and status.get('status') == 'completed':
                    get_subprocess_result(backtest_id)
                    break
    
    # Also test direct script execution
    test_direct_script_call()


if __name__ == "__main__":
    main()