#!/usr/bin/env python3
"""
Test script to run 2-month backtest with all optimizations enabled
"""

import os
import sys
import time
import subprocess
from datetime import datetime

# Add project root to path
sys.path.insert(0, '/srv/samba/shared')

def main():
    """Run 2-month backtest with GPU optimization"""
    
    # Prepare test portfolio with 2 months date range
    portfolio_path = "/srv/samba/shared/input_portfolio_2month_test.xlsx"
    output_path = f"/srv/samba/shared/Trades/test_2month_optimized_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    print(f"Starting 2-month optimized backtest test")
    print(f"Portfolio: {portfolio_path}")
    print(f"Output: {output_path}")
    
    # Command to run the backtest with optimizations
    cmd = [
        sys.executable,
        "/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py",
        "--portfolio-excel", portfolio_path,
        "--output-path", output_path,
        "--workers", "auto",  # Auto-detect optimal workers
        "--batch-days", "7",  # Process 7 days at a time
        "--gpu-threshold", "0.7",
        "--slippage", "0.1",
        "--debug"
    ]
    
    print(f"\nCommand: {' '.join(cmd)}")
    
    # Record start time
    start_time = time.time()
    
    try:
        # Run the backtest
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        
        # Calculate duration
        duration = time.time() - start_time
        
        print(f"\n✅ Backtest completed successfully!")
        print(f"Duration: {duration:.2f} seconds ({duration/60:.2f} minutes)")
        print(f"Output saved to: {output_path}")
        
        # Show some output stats
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / 1024 / 1024  # MB
            print(f"Output file size: {file_size:.2f} MB")
        
        # Check for performance improvements
        print("\n📊 Performance Analysis:")
        if "Query count:" in result.stdout:
            for line in result.stdout.split('\n'):
                if "Query count:" in line or "Total queries:" in line:
                    print(f"  {line.strip()}")
        
        # Look for GPU worker info
        if "GPU worker pool" in result.stdout:
            for line in result.stdout.split('\n'):
                if "worker" in line.lower() and "gpu" in line.lower():
                    print(f"  {line.strip()}")
                    
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Backtest failed with error code: {e.returncode}")
        print(f"Error output:\n{e.stderr}")
        if e.stdout:
            print(f"Standard output:\n{e.stdout}")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())