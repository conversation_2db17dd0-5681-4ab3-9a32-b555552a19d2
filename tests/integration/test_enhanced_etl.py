#!/usr/bin/env python3
"""
Test script for Enhanced ETL system
Tests all components without actually loading data
"""

import os
import sys
import glob
from datetime import datetime, timedelta
from heavydb import connect

# Add path for imports
sys.path.append('/srv/samba/shared')
from enhanced_daily_etl import ETLConfig, ETLProcessor

class ETLTester:
    def __init__(self):
        self.config = ETLConfig()
        self.tests_passed = 0
        self.tests_failed = 0
        
    def print_test(self, name, passed, details=""):
        """Print test result"""
        if passed:
            print(f"✓ {name}")
            self.tests_passed += 1
        else:
            print(f"✗ {name}")
            if details:
                print(f"  Details: {details}")
            self.tests_failed += 1
    
    def test_database_connection(self):
        """Test database connectivity"""
        try:
            conn = connect(**self.config.DB_CONFIG)
            cursor = conn.cursor()
            cursor.execute("SELECT 'HeavyDB Connected' as status")
            version = cursor.fetchone()[0]
            cursor.close()
            conn.close()
            self.print_test("Database Connection", True, f"Version: {version}")
        except Exception as e:
            self.print_test("Database Connection", False, str(e))
    
    def test_data_directories(self):
        """Test if data directories exist"""
        for index, config in self.config.INDEX_CONFIG.items():
            data_dir = config['data_dir']
            exists = os.path.exists(data_dir)
            self.print_test(f"{index} data directory", exists, data_dir)
            
            if exists:
                # Check for files
                pattern = os.path.join(data_dir, config['file_pattern'])
                files = glob.glob(pattern)
                
                if config.get('zip_file'):
                    zip_pattern = os.path.join(data_dir, config['zip_file'])
                    zip_files = glob.glob(zip_pattern)
                    self.print_test(f"{index} ZIP files", len(zip_files) > 0, 
                                  f"Found {len(zip_files)} ZIP files")
                else:
                    self.print_test(f"{index} CSV files", len(files) > 0, 
                                  f"Found {len(files)} CSV files")
    
    def test_log_directories(self):
        """Test if log directories exist and are writable"""
        log_dir = '/srv/samba/shared/logs/daily_etl'
        
        exists = os.path.exists(log_dir)
        self.print_test("Log directory exists", exists, log_dir)
        
        if exists:
            # Test write permission
            test_file = os.path.join(log_dir, 'test_write.tmp')
            try:
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)
                self.print_test("Log directory writable", True)
            except Exception as e:
                self.print_test("Log directory writable", False, str(e))
    
    def test_tables(self):
        """Test if tables exist or can be created"""
        try:
            conn = connect(**self.config.DB_CONFIG)
            cursor = conn.cursor()
            
            for index, config in self.config.INDEX_CONFIG.items():
                table_name = config['table_name']
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name} LIMIT 1")
                    count = cursor.fetchone()[0]
                    self.print_test(f"{index} table exists", True, 
                                  f"Table: {table_name}, Rows: {count:,}")
                except:
                    self.print_test(f"{index} table exists", False, 
                                  f"Table {table_name} not found (will be created on first run)")
            
            cursor.close()
            conn.close()
        except Exception as e:
            self.print_test("Table check", False, str(e))
    
    def test_cron_jobs(self):
        """Test if cron jobs are configured"""
        import subprocess
        
        try:
            result = subprocess.run(['crontab', '-l'], 
                                  capture_output=True, text=True)
            
            enhanced_etl = 'enhanced_daily_etl.py' in result.stdout
            self.print_test("Enhanced ETL cron job", enhanced_etl)
            
            cleanup = 'find' in result.stdout and 'daily_etl' in result.stdout
            self.print_test("Log cleanup cron job", cleanup)
            
            if enhanced_etl:
                # Extract schedule
                for line in result.stdout.split('\n'):
                    if 'enhanced_daily_etl.py' in line:
                        print(f"  Schedule: {line.strip()}")
                        
        except Exception as e:
            self.print_test("Cron job check", False, str(e))
    
    def test_recent_data(self):
        """Check for recent data in each index"""
        try:
            conn = connect(**self.config.DB_CONFIG)
            cursor = conn.cursor()
            
            for index, config in self.config.INDEX_CONFIG.items():
                table_name = config['table_name']
                try:
                    # Check for data from last 7 days
                    cursor.execute(f"""
                        SELECT COUNT(*) as count,
                               MAX(trade_date) as latest_date
                        FROM {table_name}
                        WHERE trade_date >= CURRENT_DATE - INTERVAL '7' DAY
                    """)
                    result = cursor.fetchone()
                    
                    if result and result[0] > 0:
                        self.print_test(f"{index} recent data", True,
                                      f"Latest: {result[1]}, Records: {result[0]:,}")
                    else:
                        self.print_test(f"{index} recent data", False,
                                      "No data from last 7 days")
                except:
                    pass  # Table might not exist yet
            
            cursor.close()
            conn.close()
        except Exception as e:
            print(f"Recent data check error: {e}")
    
    def test_etl_processor(self):
        """Test ETL processor initialization"""
        try:
            processor = ETLProcessor()
            self.print_test("ETL Processor initialization", True)
            
            # Test connection
            if processor.connect_db():
                self.print_test("ETL Processor DB connection", True)
                processor.close_db()
            else:
                self.print_test("ETL Processor DB connection", False)
                
        except Exception as e:
            self.print_test("ETL Processor", False, str(e))
    
    def run_all_tests(self):
        """Run all tests"""
        print("Enhanced ETL System Test")
        print("========================\n")
        
        print("1. Infrastructure Tests")
        print("-----------------------")
        self.test_database_connection()
        self.test_log_directories()
        self.test_cron_jobs()
        
        print("\n2. Data Source Tests")
        print("--------------------")
        self.test_data_directories()
        
        print("\n3. Database Tests")
        print("-----------------")
        self.test_tables()
        self.test_recent_data()
        
        print("\n4. ETL Component Tests")
        print("----------------------")
        self.test_etl_processor()
        
        print("\n" + "="*50)
        print(f"Tests Passed: {self.tests_passed}")
        print(f"Tests Failed: {self.tests_failed}")
        
        if self.tests_failed == 0:
            print("\n✓ All tests passed! ETL system is ready.")
        else:
            print(f"\n✗ {self.tests_failed} tests failed. Please check the issues above.")
        
        return self.tests_failed == 0

def main():
    tester = ETLTester()
    success = tester.run_all_tests()
    
    print("\nManual ETL Commands:")
    print("-------------------")
    print("Run ETL now: ./run_etl_manually.sh")
    print("View logs: tail -f /srv/samba/shared/logs/daily_etl/enhanced_etl_$(date +%Y%m%d).log")
    print("Check cron: crontab -l")
    
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()