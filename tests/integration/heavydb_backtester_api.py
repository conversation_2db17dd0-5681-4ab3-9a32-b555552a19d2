from fastapi import <PERSON><PERSON><PERSON>, HTTPException, BackgroundTasks, UploadFile, File
from fastapi.responses import FileResponse
from pydantic import BaseModel
from typing import Optional, Dict, List
import asyncio
import uuid
import os
import sys
from datetime import datetime
import redis
import json
from celery import Celery

# Add backtester to Python path
sys.path.insert(0, '/srv/samba/shared/bt')

app = FastAPI(title="HeavyDB GPU Backtester API", version="2.0")

# Redis for job management
redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)

# Celery for distributed task processing
celery_app = Celery('backtester', broker='redis://localhost:6379')

class BacktestRequest(BaseModel):
    portfolio_excel: str
    start_date: str
    end_date: str
    output_path: Optional[str] = None
    strategy_type: Optional[str] = "TBS"
    use_gpu: Optional[bool] = True
    slippage_percent: Optional[float] = 0.1
    capital: Optional[float] = 1000000

class BacktestResponse(BaseModel):
    job_id: str
    status: str
    message: str

class JobStatus(BaseModel):
    job_id: str
    status: str
    progress: Optional[int] = 0
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    result: Optional[Dict] = None
    error: Optional[str] = None

@celery_app.task(name='run_backtest')
def run_backtest_task(job_id: str, params: dict):
    """Celery task to run backtest"""
    try:
        # Update status
        redis_client.hset(f"job:{job_id}", mapping={
            "status": "running",
            "started_at": datetime.now().isoformat(),
            "progress": 10
        })
        
        # Import backtester modules
        from backtester_stable.BTRUN.BTRunPortfolio_GPU import main as run_gpu_backtest
        
        # Prepare arguments
        args = [
            '--legacy-excel',
            '--portfolio-excel', params['portfolio_excel'],
            '--start-date', params['start_date'],
            '--end-date', params['end_date']
        ]
        
        if params.get('output_path'):
            args.extend(['--output-path', params['output_path']])
        
        if not params.get('use_gpu', True):
            args.append('--cpu-only')
        
        # Update progress
        redis_client.hset(f"job:{job_id}", "progress", 50)
        
        # Run backtest
        result = run_gpu_backtest(args)
        
        # Update completion status
        redis_client.hset(f"job:{job_id}", mapping={
            "status": "completed",
            "completed_at": datetime.now().isoformat(),
            "progress": 100,
            "result": json.dumps(result)
        })
        
    except Exception as e:
        redis_client.hset(f"job:{job_id}", mapping={
            "status": "failed",
            "error": str(e),
            "completed_at": datetime.now().isoformat()
        })

@app.post("/api/v2/backtest", response_model=BacktestResponse)
async def submit_backtest(request: BacktestRequest, background_tasks: BackgroundTasks):
    """Submit a new GPU-accelerated backtest job"""
    # Generate job ID
    job_id = str(uuid.uuid4())
    
    # Set default output path
    if not request.output_path:
        request.output_path = f"/srv/samba/shared/bt/output/backtest_{job_id}.xlsx"
    
    # Store job metadata in Redis
    job_data = {
        "job_id": job_id,
        "status": "pending",
        "params": request.dict(),
        "created_at": datetime.now().isoformat()
    }
    redis_client.hset(f"job:{job_id}", mapping={k: json.dumps(v) if isinstance(v, dict) else str(v) for k, v in job_data.items()})
    
    # Submit to Celery queue
    run_backtest_task.delay(job_id, request.dict())
    
    return BacktestResponse(
        job_id=job_id,
        status="submitted",
        message="Backtest job submitted to GPU processing queue"
    )

@app.get("/api/v2/backtest/{job_id}", response_model=JobStatus)
async def get_job_status(job_id: str):
    """Get status of a backtest job"""
    job_data = redis_client.hgetall(f"job:{job_id}")
    
    if not job_data:
        raise HTTPException(status_code=404, detail="Job not found")
    
    # Parse timestamps
    started_at = job_data.get('started_at')
    completed_at = job_data.get('completed_at')
    
    return JobStatus(
        job_id=job_id,
        status=job_data.get('status', 'unknown'),
        progress=int(job_data.get('progress', 0)),
        started_at=datetime.fromisoformat(started_at) if started_at else None,
        completed_at=datetime.fromisoformat(completed_at) if completed_at else None,
        result=json.loads(job_data.get('result', '{}')) if job_data.get('result') else None,
        error=job_data.get('error')
    )

@app.get("/api/v2/backtest/{job_id}/download")
async def download_results(job_id: str):
    """Download backtest results"""
    job_data = redis_client.hgetall(f"job:{job_id}")
    
    if not job_data:
        raise HTTPException(status_code=404, detail="Job not found")
    
    if job_data.get('status') != 'completed':
        raise HTTPException(status_code=400, detail="Job not completed")
    
    params = json.loads(job_data.get('params', '{}'))
    output_file = params.get('output_path')
    
    if output_file and os.path.exists(output_file):
        return FileResponse(output_file, filename=os.path.basename(output_file))
    else:
        raise HTTPException(status_code=404, detail="Output file not found")

@app.post("/api/v2/backtest/upload")
async def upload_and_backtest(
    portfolio_file: UploadFile = File(...),
    strategy_file: Optional[UploadFile] = File(None),
    start_date: str = "20250401",
    end_date: str = "20250402"
):
    """Upload Excel files and run backtest"""
    # Save uploaded files
    upload_dir = "/srv/samba/shared/bt/uploads"
    os.makedirs(upload_dir, exist_ok=True)
    
    portfolio_path = os.path.join(upload_dir, f"{uuid.uuid4()}_{portfolio_file.filename}")
    with open(portfolio_path, "wb") as f:
        f.write(await portfolio_file.read())
    
    # Submit backtest
    request = BacktestRequest(
        portfolio_excel=portfolio_path,
        start_date=start_date,
        end_date=end_date
    )
    
    return await submit_backtest(request, BackgroundTasks())

@app.get("/api/v2/jobs")
async def list_jobs(status: Optional[str] = None, limit: int = 100):
    """List all backtest jobs"""
    # Get all job keys
    job_keys = redis_client.keys("job:*")
    jobs = []
    
    for key in job_keys[:limit]:
        job_data = redis_client.hgetall(key)
        if status and job_data.get('status') != status:
            continue
        
        jobs.append({
            "job_id": job_data.get('job_id'),
            "status": job_data.get('status'),
            "created_at": job_data.get('created_at'),
            "progress": int(job_data.get('progress', 0))
        })
    
    return {"jobs": jobs, "total": len(jobs)}

@app.delete("/api/v2/backtest/{job_id}")
async def cancel_job(job_id: str):
    """Cancel a running job"""
    job_data = redis_client.hgetall(f"job:{job_id}")
    
    if not job_data:
        raise HTTPException(status_code=404, detail="Job not found")
    
    if job_data.get('status') not in ['pending', 'running']:
        raise HTTPException(status_code=400, detail="Job cannot be cancelled")
    
    # Update status
    redis_client.hset(f"job:{job_id}", "status", "cancelled")
    
    # TODO: Implement actual task cancellation in Celery
    
    return {"message": "Job cancelled successfully"}

@app.get("/api/v2/health")
async def health_check():
    """Health check with system status"""
    try:
        # Check Redis
        redis_client.ping()
        redis_status = "healthy"
    except:
        redis_status = "unhealthy"
    
    # Check HeavyDB
    try:
        from backtester_stable.BTRUN.heavydb_connection import get_connection
        conn = get_connection()
        conn.execute("SELECT 1").fetchone()
        heavydb_status = "healthy"
    except:
        heavydb_status = "unhealthy"
    
    return {
        "status": "healthy" if redis_status == "healthy" and heavydb_status == "healthy" else "degraded",
        "service": "heavydb_gpu_backtester",
        "version": "2.0",
        "timestamp": datetime.now().isoformat(),
        "components": {
            "redis": redis_status,
            "heavydb": heavydb_status
        }
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000) 