#!/usr/bin/env python3
"""
Test script to verify daily max profit/loss calculation
"""

import pandas as pd
import numpy as np
from datetime import datetime, date
import sys
import os

# Add the project root to sys.path
sys.path.insert(0, '/srv/samba/shared/bt')

from backtester_stable.BTRUN.builders import get_daily_max_pl

def create_test_data():
    """Create test data similar to what would come from backtest response"""
    return {
        'current_m2m': {
            '250401': {  # April 1, 2025
                '091500': 0,
                '100000': 0,
                '110000': 0,
                '150000': 0
            },
            '250402': {  # April 2, 2025
                '091500': 0,
                '100000': 0,
                '110000': 0,
                '150000': 0
            },
            '250403': {  # April 3, 2025
                '091500': -500,
                '100000': 300,
                '100100': 960,  # Max profit at 10:01:00
                '110000': -1000,
                '112100': -1797,  # Max loss at 11:21:00
                '150000': -800
            },
            '250404': {  # April 4, 2025
                '091500': 0,
                '100000': 0,
                '110000': 0,
                '150000': 0
            },
            '250407': {  # April 7, 2025
                '091500': 0,
                '100000': 0,
                '110000': 0,
                '150000': 0
            }
        }
    }

def test_daily_max_pl():
    """Test the daily max PL calculation"""
    print("Testing daily max profit/loss calculation...\n")
    
    # Create test data
    test_data = create_test_data()
    
    # Calculate daily max PL
    result_df = get_daily_max_pl(test_data)
    
    print("Result DataFrame:")
    print(result_df)
    print("\nData types:")
    print(result_df.dtypes)
    
    # Verify the results
    print("\n\nVerification:")
    print(f"Number of rows: {len(result_df)}")
    print(f"Columns: {list(result_df.columns)}")
    
    # Check specific values
    if len(result_df) >= 3:
        apr3_row = result_df[result_df['Date'] == pd.to_datetime('2025-04-03').date()]
        if not apr3_row.empty:
            print(f"\nApril 3 data:")
            print(f"Max Profit: {apr3_row['Max Profit'].values[0]} (expected: 960)")
            print(f"Max Profit Time: {apr3_row['Max Profit Time'].values[0]} (expected: 10:01:00)")
            print(f"Max Loss: {apr3_row['Max Loss'].values[0]} (expected: -1797)")
            print(f"Max Loss Time: {apr3_row['Max Loss Time'].values[0]} (expected: 11:21:00)")
    
    return result_df

def test_empty_data():
    """Test with empty data"""
    print("\n\nTesting with empty data...")
    
    empty_data = {'current_m2m': {}}
    result_df = get_daily_max_pl(empty_data)
    
    print(f"Empty data result shape: {result_df.shape}")
    print(f"Empty data columns: {list(result_df.columns)}")
    
    return result_df

def test_missing_current_m2m():
    """Test with missing current_m2m key"""
    print("\n\nTesting with missing current_m2m key...")
    
    bad_data = {'some_other_key': {}}
    result_df = get_daily_max_pl(bad_data)
    
    print(f"Missing key result shape: {result_df.shape}")
    print(f"Missing key columns: {list(result_df.columns)}")
    
    return result_df

if __name__ == "__main__":
    # Run tests
    df1 = test_daily_max_pl()
    df2 = test_empty_data()
    df3 = test_missing_current_m2m()
    
    print("\n\nAll tests completed!")
    
    # Save test results
    if not df1.empty:
        output_file = '/srv/samba/shared/test_daily_max_pl_output.xlsx'
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df1.to_excel(writer, sheet_name='Max Profit and Loss', index=False)
        print(f"\nTest output saved to: {output_file}")