#!/usr/bin/env python3
"""
Comprehensive test suite for TBS column mapping validation
Based on column_mapping_ml_tbs.md specifications
"""

import pandas as pd
import numpy as np
import os
import sys
import json
from datetime import datetime, time
from typing import Dict, List, Any, Tuple

# Add project root to path
sys.path.insert(0, '/srv/samba/shared')

def create_test_scenarios() -> List[Dict[str, Any]]:
    """Create comprehensive test scenarios for all TBS columns"""
    
    scenarios = [
        {
            "name": "DTE=0 Test (Same-day expiry only)",
            "general_params": {
                "StrategyName": "DTE0_TEST",
                "DTE": 0,  # Should only trade on expiry days
                "StartTime": 91600,
                "EndTime": 150000,
                "Index": "NIFTY",
                "Underlying": "SPOT",
                "Weekdays": "1,2,3,4,5"
            },
            "expected": "Should only execute trades when trade_date = expiry_date"
        },
        {
            "name": "Strike Selection Time Test",
            "general_params": {
                "StrategyName": "STRIKE_TIME_TEST",
                "StrikeSelectionTime": 92000,  # 09:20:00
                "StartTime": 91600,
                "EndTime": 150000,
                "DTE": 1
            },
            "expected": "Strikes should be selected based on 09:20:00 prices, not 09:16:00"
        },
        {
            "name": "Weekday Filter Test",
            "general_params": {
                "StrategyName": "WEEKDAY_TEST",
                "Weekdays": "2,4",  # Only Tuesday and Thursday
                "StartTime": 91600,
                "EndTime": 150000,
                "DTE": 1
            },
            "expected": "Should only trade on Tuesdays and Thursdays"
        },
        {
            "name": "Partial Exit Test (SqOff)",
            "general_params": {
                "StrategyName": "PARTIAL_EXIT_TEST",
                "StartTime": 91600,
                "EndTime": 153000,
                "SqOff1Time": 120000,  # 12:00:00
                "SqOff1Percent": 50,    # Exit 50% at noon
                "SqOff2Time": 140000,   # 14:00:00
                "SqOff2Percent": 30,    # Exit 30% at 2pm
                "DTE": 1
            },
            "expected": "50% exit at 12:00, 30% at 14:00, remaining 20% at 15:30"
        },
        {
            "name": "Strategy Trailing Test",
            "general_params": {
                "StrategyName": "TRAIL_TEST",
                "StrategyTrailingType": "Lock & Trail Profits",
                "ProfitReaches": 5000,
                "LockMinProfitAt": 2500,
                "TrailPercent": 20,
                "StartTime": 91600,
                "EndTime": 153000,
                "DTE": 1
            },
            "expected": "When profit hits 5000, lock 2500 and trail by 20%"
        },
        {
            "name": "Premium Difference Test",
            "general_params": {
                "StrategyName": "PREMIUM_DIFF_TEST",
                "CheckPremiumDiffCondition": "yes",
                "PremiumDiffType": "percentage",
                "PremiumDiffValue": 10,
                "PremiumDiffChangeStrike": "yes",
                "StartTime": 91600,
                "EndTime": 150000,
                "DTE": 1
            },
            "expected": "Change strike if CE-PE premium difference > 10%"
        },
        {
            "name": "On Expiry Day Trade Next Expiry",
            "general_params": {
                "StrategyName": "NEXT_EXPIRY_TEST",
                "OnExpiryDayTradeNextExpiry": "yes",
                "StartTime": 91600,
                "EndTime": 150000,
                "DTE": 0
            },
            "expected": "On expiry day, use next week's expiry contracts"
        }
    ]
    
    return scenarios

def create_leg_test_scenarios() -> List[Dict[str, Any]]:
    """Create test scenarios for LegParameter columns"""
    
    scenarios = [
        {
            "name": "ATM Offset Test",
            "legs": [
                {
                    "LegID": "1",
                    "Instrument": "call",
                    "Transaction": "sell",
                    "StrikeMethod": "ATM",
                    "StrikeValue": 0,  # ATM
                    "Expiry": "current",
                    "Lots": 2
                },
                {
                    "LegID": "2", 
                    "Instrument": "put",
                    "Transaction": "sell",
                    "StrikeMethod": "ATM",
                    "StrikeValue": 1,  # OTM1
                    "Expiry": "current",
                    "Lots": 2
                }
            ],
            "expected": "CE at ATM strike, PE at ATM-50 (for NIFTY)"
        },
        {
            "name": "Fixed Strike Test",
            "legs": [
                {
                    "LegID": "1",
                    "Instrument": "call",
                    "Transaction": "buy",
                    "StrikeMethod": "FIXED",
                    "StrikeValue": 22000,
                    "Expiry": "current",
                    "Lots": 1
                }
            ],
            "expected": "Should select exactly 22000 strike"
        },
        {
            "name": "Premium Based Strike",
            "legs": [
                {
                    "LegID": "1",
                    "Instrument": "call", 
                    "Transaction": "buy",
                    "StrikeMethod": "PREMIUM",
                    "StrikeValue": 100,
                    "StrikePremiumCondition": "<=",
                    "Expiry": "current",
                    "Lots": 1
                }
            ],
            "expected": "Select strike with premium <= 100"
        },
        {
            "name": "Delta Based Strike",
            "legs": [
                {
                    "LegID": "1",
                    "Instrument": "call",
                    "Transaction": "sell",
                    "StrikeMethod": "DELTA",
                    "StrikeValue": 0.3,  # 30 delta
                    "Expiry": "current",
                    "Lots": 1
                }
            ],
            "expected": "Select strike closest to 0.3 delta"
        },
        {
            "name": "Hedge Test",
            "legs": [
                {
                    "LegID": "1",
                    "Instrument": "call",
                    "Transaction": "sell",
                    "StrikeMethod": "ATM",
                    "StrikeValue": 0,
                    "OpenHedge": "Yes",
                    "HedgeStrikeMethod": "ATM",
                    "HedgeStrikeValue": 3,  # OTM3 hedge
                    "Expiry": "current",
                    "Lots": 1
                }
            ],
            "expected": "Sell ATM CE with OTM3 CE hedge"
        },
        {
            "name": "Re-entry Test",
            "legs": [
                {
                    "LegID": "1",
                    "Instrument": "put",
                    "Transaction": "sell",
                    "StrikeMethod": "ATM",
                    "StrikeValue": 0,
                    "SL_ReEntryType": "instant new strike",
                    "SL_ReEntryNo": 2,
                    "TGT_ReEntryType": "original",
                    "TGT_ReEntryNo": 1,
                    "Expiry": "current",
                    "Lots": 1
                }
            ],
            "expected": "On SL: re-enter with new strike (max 2), On TGT: re-enter original (max 1)"
        },
        {
            "name": "Trail SL Test",
            "legs": [
                {
                    "LegID": "1",
                    "Instrument": "call",
                    "Transaction": "buy",
                    "StrikeMethod": "ATM",
                    "StrikeValue": 0,
                    "TrailSLType": "point",
                    "SL_TrailAt": 50,
                    "SL_TrailBy": 10,
                    "Expiry": "current",
                    "Lots": 1
                }
            ],
            "expected": "When profit reaches 50 points, start trailing by 10 points"
        }
    ]
    
    return scenarios

def validate_time_conversion():
    """Test time format conversions"""
    print("\n🕐 Time Conversion Tests")
    print("=" * 60)
    
    test_cases = [
        ("91600", "09:16:00"),
        ("120000", "12:00:00"),
        ("153000", "15:30:00"),
        ("092000", "09:20:00"),
        ("9:16:00", "09:16:00"),
        ("15:30:00", "15:30:00")
    ]
    
    for input_val, expected in test_cases:
        # Test conversion logic
        converted = convert_time_format(input_val)
        status = "✅" if converted == expected else "❌"
        print(f"{status} {input_val} → {converted} (expected: {expected})")

def convert_time_format(value: str) -> str:
    """Convert various time formats to HH:MM:SS"""
    s = str(value).strip()
    
    # Already in HH:MM:SS format
    if ":" in s:
        return s
    
    # HHMMSS format
    if len(s) == 5:
        s = "0" + s  # Add leading zero
    
    if len(s) == 6:
        hh = s[0:2]
        mm = s[2:4]
        ss = s[4:6]
        return f"{hh}:{mm}:{ss}"
    
    return s

def validate_dte_filtering():
    """Test DTE filtering logic"""
    print("\n📅 DTE Filtering Tests")
    print("=" * 60)
    
    # Simulate database data
    test_dates = [
        {"trade_date": "2024-01-01", "expiry_date": "2024-01-04", "is_expiry": False},
        {"trade_date": "2024-01-02", "expiry_date": "2024-01-04", "is_expiry": False},
        {"trade_date": "2024-01-03", "expiry_date": "2024-01-04", "is_expiry": False},
        {"trade_date": "2024-01-04", "expiry_date": "2024-01-04", "is_expiry": True},
        {"trade_date": "2024-01-05", "expiry_date": "2024-01-11", "is_expiry": False}
    ]
    
    # Test DTE=0 filtering
    print("DTE=0 (Only expiry days):")
    for date_info in test_dates:
        should_trade = date_info["is_expiry"]
        status = "✅ Trade" if should_trade else "❌ Skip"
        print(f"  {date_info['trade_date']} → {date_info['expiry_date']} : {status}")
    
    # Test DTE=1 filtering
    print("\nDTE=1 (1 day before expiry):")
    for date_info in test_dates:
        # Check if tomorrow is expiry
        should_trade = date_info["trade_date"] == "2024-01-03"  # Day before Jan 4 expiry
        status = "✅ Trade" if should_trade else "❌ Skip"
        print(f"  {date_info['trade_date']} : {status}")

def validate_strike_selection():
    """Test strike selection logic"""
    print("\n🎯 Strike Selection Tests")
    print("=" * 60)
    
    # Test data
    atm_strike = 22000
    step_size = 50  # NIFTY
    
    test_cases = [
        {"method": "ATM", "value": 0, "option": "CE", "expected": 22000},
        {"method": "ATM", "value": 1, "option": "CE", "expected": 22050},  # OTM
        {"method": "ATM", "value": -1, "option": "CE", "expected": 21950}, # ITM
        {"method": "ATM", "value": 1, "option": "PE", "expected": 21950},  # OTM
        {"method": "ATM", "value": -1, "option": "PE", "expected": 22050}, # ITM
        {"method": "ITM1", "value": None, "option": "CE", "expected": 21950},
        {"method": "OTM2", "value": None, "option": "PE", "expected": 21900},
        {"method": "FIXED", "value": 22100, "option": "CE", "expected": 22100}
    ]
    
    for test in test_cases:
        strike = calculate_strike(atm_strike, test["method"], test["value"], test["option"], step_size)
        status = "✅" if strike == test["expected"] else "❌"
        print(f"{status} {test['method']} {test['value']} {test['option']}: {strike} (expected: {test['expected']})")

def calculate_strike(atm: int, method: str, value: Any, option_type: str, step: int) -> int:
    """Calculate strike based on method and parameters"""
    if method == "FIXED":
        return value
    
    if method == "ATM":
        if value is None or value == 0:
            return atm
        
        if option_type == "CE":
            # Positive = OTM (higher), Negative = ITM (lower)
            return atm + (value * step)
        else:  # PE
            # Positive = OTM (lower), Negative = ITM (higher)
            return atm - (value * step)
    
    if method.startswith("ITM"):
        steps = int(method[3:]) if len(method) > 3 else 1
        if option_type == "CE":
            return atm - (steps * step)
        else:
            return atm + (steps * step)
    
    if method.startswith("OTM"):
        steps = int(method[3:]) if len(method) > 3 else 1
        if option_type == "CE":
            return atm + (steps * step)
        else:
            return atm - (steps * step)
    
    return atm

def validate_risk_parameters():
    """Test risk parameter calculations"""
    print("\n⚠️ Risk Parameter Tests")
    print("=" * 60)
    
    entry_price = 100
    
    test_cases = [
        {"type": "percentage", "value": 50, "expected_sl": 150},  # 50% SL
        {"type": "point", "value": 25, "expected_sl": 125},       # 25 point SL
        {"type": "percentage", "value": 500, "expected_sl": 600}, # 500% SL (for SELL)
        {"type": "absolute", "value": 150, "expected_sl": 150}    # Absolute 150
    ]
    
    for test in test_cases:
        sl_price = calculate_sl(entry_price, test["type"], test["value"])
        status = "✅" if sl_price == test["expected_sl"] else "❌"
        print(f"{status} SL {test['type']} {test['value']}: {sl_price} (expected: {test['expected_sl']})")

def calculate_sl(entry: float, sl_type: str, sl_value: float) -> float:
    """Calculate stop loss price"""
    if sl_type == "percentage":
        return entry * (1 + sl_value / 100)
    elif sl_type == "point":
        return entry + sl_value
    elif sl_type == "absolute":
        return sl_value
    return entry

def generate_test_report():
    """Generate comprehensive test report"""
    print("\n📊 Comprehensive TBS Column Validation Report")
    print("=" * 80)
    
    # Test all components
    validate_time_conversion()
    validate_dte_filtering()
    validate_strike_selection()
    validate_risk_parameters()
    
    # Summary of issues to fix
    print("\n🔧 Issues Requiring Fixes:")
    print("=" * 60)
    print("1. ❌ DTE=0 filtering not working - trades all days")
    print("2. ❌ Expiry column not populated in output")
    print("3. ❌ StrikeSelectionTime may not be used correctly")
    print("4. ❌ Weekday filtering needs verification")
    print("5. ❌ Partial exits (SqOff) need implementation check")
    print("6. ❌ Strategy trailing logic needs verification")
    print("7. ❌ Premium difference conditions need testing")
    print("8. ❌ Re-entry logic needs validation")
    
    print("\n✅ Working Features:")
    print("=" * 60)
    print("1. ✅ Exit time (EndTime) working correctly")
    print("2. ✅ Basic strike selection (ATM/ITM/OTM)")
    print("3. ✅ Transaction types (BUY/SELL)")
    print("4. ✅ Lots calculation")
    print("5. ✅ Query optimization (batching)")

def create_unit_test_file():
    """Create unit test file for all column validations"""
    
    test_content = '''#!/usr/bin/env python3
"""
Unit tests for TBS column functionality
Run with: python -m pytest test_tbs_columns.py -v
"""

import pytest
import pandas as pd
from datetime import datetime, time
from typing import Dict, Any

class TestTBSColumns:
    """Test all TBS column functionalities"""
    
    def test_dte_zero_filtering(self):
        """Test that DTE=0 only trades on expiry days"""
        # Given
        dte_filter = 0
        trade_date = datetime(2024, 1, 4)  # Thursday
        expiry_date = datetime(2024, 1, 4)  # Same day
        
        # When
        should_trade = (trade_date == expiry_date) if dte_filter == 0 else True
        
        # Then
        assert should_trade == True, "DTE=0 should trade on expiry day"
        
        # Test non-expiry day
        trade_date = datetime(2024, 1, 3)  # Wednesday
        should_trade = (trade_date == expiry_date) if dte_filter == 0 else True
        assert should_trade == False, "DTE=0 should NOT trade on non-expiry day"
    
    def test_time_conversion(self):
        """Test time format conversions"""
        test_cases = [
            (91600, "09:16:00"),
            (120000, "12:00:00"),
            ("091600", "09:16:00"),
            ("9:16:00", "09:16:00")
        ]
        
        for input_val, expected in test_cases:
            # Conversion logic here
            result = self._convert_time(input_val)
            assert result == expected, f"Failed to convert {input_val} to {expected}"
    
    def test_strike_selection_atm_offset(self):
        """Test ATM offset strike selection"""
        atm = 22000
        step = 50
        
        # CE with positive offset (OTM)
        strike = self._calculate_strike(atm, "ATM", 1, "CE", step)
        assert strike == 22050, "CE OTM1 should be ATM + 50"
        
        # PE with positive offset (OTM)
        strike = self._calculate_strike(atm, "ATM", 1, "PE", step)
        assert strike == 21950, "PE OTM1 should be ATM - 50"
        
        # CE with negative offset (ITM)
        strike = self._calculate_strike(atm, "ATM", -1, "CE", step)
        assert strike == 21950, "CE ITM1 should be ATM - 50"
    
    def test_weekday_filtering(self):
        """Test weekday filtering"""
        weekdays = "2,4"  # Tuesday, Thursday
        
        test_dates = [
            (datetime(2024, 1, 1), False),  # Monday
            (datetime(2024, 1, 2), True),   # Tuesday
            (datetime(2024, 1, 3), False),  # Wednesday
            (datetime(2024, 1, 4), True),   # Thursday
            (datetime(2024, 1, 5), False),  # Friday
        ]
        
        for date, expected in test_dates:
            dow = date.weekday() + 1  # Python: 0=Mon, Excel: 1=Mon
            should_trade = str(dow) in weekdays.split(",")
            assert should_trade == expected, f"Weekday filter failed for {date.strftime('%A')}"
    
    def test_partial_exits(self):
        """Test partial exit functionality"""
        total_qty = 100
        sqoff1_pct = 50
        sqoff2_pct = 30
        
        # First exit
        exit1_qty = int(total_qty * sqoff1_pct / 100)
        assert exit1_qty == 50, "First exit should be 50%"
        
        # Second exit
        exit2_qty = int(total_qty * sqoff2_pct / 100)
        assert exit2_qty == 30, "Second exit should be 30%"
        
        # Remaining
        remaining = total_qty - exit1_qty - exit2_qty
        assert remaining == 20, "Remaining should be 20%"
    
    def test_sl_tp_values(self):
        """Test recommended SL/TP values"""
        # SELL leg
        entry_price = 100
        sl_pct_sell = 500
        tp_pct_sell = 100
        
        sl_price = entry_price * (1 + sl_pct_sell/100)
        tp_price = entry_price * (1 - tp_pct_sell/100)
        
        assert sl_price == 600, "SELL SL should be 600 (500% of 100)"
        assert tp_price == 0, "SELL TP should be 0 (100% profit)"
        
        # BUY leg
        sl_pct_buy = 50
        tp_pct_buy = 100
        
        sl_price = entry_price * (1 - sl_pct_buy/100)
        tp_price = entry_price * (1 + tp_pct_buy/100)
        
        assert sl_price == 50, "BUY SL should be 50 (50% of 100)"
        assert tp_price == 200, "BUY TP should be 200 (100% profit)"
    
    def test_expiry_mapping(self):
        """Test expiry code mappings"""
        mappings = {
            "current": "CW",
            "CW": "CW",
            "next": "NW",
            "NW": "NW",
            "monthly": "CM",
            "CM": "CM"
        }
        
        for input_val, expected in mappings.items():
            result = self._map_expiry(input_val)
            assert result == expected, f"Expiry mapping failed for {input_val}"
    
    def test_strategy_trailing(self):
        """Test strategy trailing logic"""
        profit_reaches = 5000
        lock_min_profit = 2500
        trail_percent = 20
        
        current_profit = 6000
        
        # Should activate trailing
        assert current_profit >= profit_reaches, "Trailing should activate"
        
        # Calculate trailing stop
        trail_stop = current_profit - (current_profit * trail_percent / 100)
        assert trail_stop == 4800, "Trail stop should be 80% of current profit"
        
        # But not below minimum
        actual_stop = max(trail_stop, lock_min_profit)
        assert actual_stop == 4800, "Should use trail stop when above minimum"
    
    def _convert_time(self, value):
        """Helper: Convert time format"""
        s = str(value).strip()
        if ":" in s:
            return s
        if len(s) == 5:
            s = "0" + s
        if len(s) == 6:
            return f"{s[0:2]}:{s[2:4]}:{s[4:6]}"
        return s
    
    def _calculate_strike(self, atm, method, value, option_type, step):
        """Helper: Calculate strike price"""
        if method == "ATM":
            if value == 0:
                return atm
            if option_type == "CE":
                return atm + (value * step)
            else:
                return atm - (value * step)
        return atm
    
    def _map_expiry(self, value):
        """Helper: Map expiry codes"""
        mapping = {
            "current": "CW", "CW": "CW",
            "next": "NW", "NW": "NW",
            "monthly": "CM", "CM": "CM"
        }
        return mapping.get(value, value)

if __name__ == "__main__":
    # Run tests
    test = TestTBSColumns()
    test.test_dte_zero_filtering()
    test.test_time_conversion()
    test.test_strike_selection_atm_offset()
    test.test_weekday_filtering()
    test.test_partial_exits()
    test.test_sl_tp_values()
    test.test_expiry_mapping()
    test.test_strategy_trailing()
    print("✅ All unit tests passed!")
'''
    
    with open('/srv/samba/shared/test_tbs_columns.py', 'w') as f:
        f.write(test_content)
    
    print("\n📝 Created unit test file: test_tbs_columns.py")

def main():
    """Main test execution"""
    print("🔍 TBS Column Mapping Validation Suite")
    print("Based on: column_mapping_ml_tbs.md")
    print("=" * 80)
    
    # Run validation tests
    generate_test_report()
    
    # Create unit test file
    create_unit_test_file()
    
    # Create test scenarios
    print("\n📋 Test Scenarios Created:")
    general_scenarios = create_test_scenarios()
    for scenario in general_scenarios[:3]:
        print(f"\n• {scenario['name']}")
        print(f"  Expected: {scenario['expected']}")
    
    print("\n\n🎯 Recommendations:")
    print("=" * 60)
    print("1. Fix DTE filtering in heavydb_trade_processing.py")
    print("2. Populate expiry column in trade_builder.py") 
    print("3. Implement StrikeSelectionTime logic")
    print("4. Add weekday filtering to query generation")
    print("5. Implement partial exits (SqOff1/SqOff2)")
    print("6. Test strategy trailing implementation")
    print("7. Add premium difference checks")
    print("8. Validate re-entry logic")
    print("9. Enable GPU workers for performance")
    print("10. Run unit tests: python test_tbs_columns.py")

if __name__ == "__main__":
    main()