#!/usr/bin/env python3
"""
Column Mapping Test Runner

This script tests column mappings by running the backtester and analyzing
the output file to ensure all relevant columns from the input Excel files
are correctly processed and appear in the output.
"""

import os
import sys
import pandas as pd
import logging
import subprocess
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'column_mapping_run_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger('column_mapping_test')

# Paths
BACKTESTER_PATH = '/srv/samba/shared/bt/backtester_stable/BTRUN'
PORTFOLIO_FILE = os.path.join(BACKTESTER_PATH, 'input_sheets/input_portfolio_fixed.xlsx')
OUTPUT_DIR = '/srv/samba/shared/Trades'
TIMESTAMP = datetime.now().strftime("%Y%m%d_%H%M%S")
OUTPUT_FILE = os.path.join(OUTPUT_DIR, f'column_test_{TIMESTAMP}.xlsx')
os.makedirs(OUTPUT_DIR, exist_ok=True)

def read_excel_files():
    """Read the input Excel files for testing"""
    logger.info(f"Reading portfolio file: {PORTFOLIO_FILE}")
    
    try:
        # Read portfolio file
        portfolio_sheets = pd.read_excel(PORTFOLIO_FILE, sheet_name=None)
        
        # Get strategy file path
        strategy_setting = portfolio_sheets.get('StrategySetting')
        if strategy_setting is None or 'StrategyExcelFilePath' not in strategy_setting.columns:
            logger.error("StrategyExcelFilePath not found in StrategySetting sheet")
            return None, None
        
        strategy_file_path = strategy_setting.iloc[0]['StrategyExcelFilePath']
        if not os.path.isabs(strategy_file_path):
            strategy_file_path = os.path.join(BACKTESTER_PATH, strategy_file_path)
        
        logger.info(f"Reading strategy file: {strategy_file_path}")
        if not os.path.exists(strategy_file_path):
            logger.error(f"Strategy file not found: {strategy_file_path}")
            return portfolio_sheets, None
        
        # Read strategy file
        strategy_sheets = pd.read_excel(strategy_file_path, sheet_name=None)
        
        return portfolio_sheets, strategy_sheets
    
    except Exception as e:
        logger.error(f"Error reading Excel files: {e}")
        return None, None

def run_backtester():
    """Run the backtester with the test file"""
    logger.info(f"Running backtester with portfolio file: {PORTFOLIO_FILE}")
    
    cmd = [
        "python3",
        os.path.join(BACKTESTER_PATH, "BTRunPortfolio_GPU.py"),
        "--portfolio-excel", PORTFOLIO_FILE,
        "--output-path", OUTPUT_FILE
    ]
    
    logger.info(f"Running command: {' '.join(cmd)}")
    
    try:
        process = subprocess.run(cmd, capture_output=True, text=True)
        if process.returncode == 0:
            logger.info(f"Backtester completed successfully: {OUTPUT_FILE}")
            return True
        else:
            logger.error(f"Backtester failed with return code {process.returncode}")
            logger.error(f"STDOUT: {process.stdout}")
            logger.error(f"STDERR: {process.stderr}")
            return False
    except Exception as e:
        logger.error(f"Error running backtester: {e}")
        return False

def check_output_file():
    """Check the output file for correct columns"""
    if not os.path.exists(OUTPUT_FILE):
        logger.error(f"Output file not found: {OUTPUT_FILE}")
        return False
    
    logger.info(f"Analyzing output file: {OUTPUT_FILE}")
    
    try:
        # Read the output file
        output_sheets = pd.read_excel(OUTPUT_FILE, sheet_name=None)
        logger.info(f"Sheets in output file: {list(output_sheets.keys())}")
        
        # Check for required sheets - update to match actual sheets
        required_sheets = ['Metrics', 'PORTFOLIO Trans']
        missing_sheets = [sheet for sheet in required_sheets if sheet not in output_sheets]
        if missing_sheets:
            logger.error(f"Missing sheets in output file: {missing_sheets}")
            return False
        
        # Check PORTFOLIO Trans sheet columns
        portfolio_trans = output_sheets.get('PORTFOLIO Trans')
        if portfolio_trans is None:
            logger.error("PORTFOLIO Trans sheet not found in output file")
            return False
        
        expected_columns = [
            'portfolio_name', 'strategy', 'leg_id', 'entry_date', 'entry_time', 'entry_day',
            'exit_date', 'exit_time', 'exit_day', 'symbol', 'expiry', 'strike', 'instrument_type',
            'side', 'filled_quantity', 'entry_price', 'exit_price', 'pnl', 'expenses', 'reason'
        ]
        
        # Check for expected columns
        missing_columns = [col for col in expected_columns if col not in portfolio_trans.columns]
        if missing_columns:
            logger.error(f"Missing columns in PORTFOLIO Trans sheet: {missing_columns}")
            return False
        
        logger.info(f"Found {len(portfolio_trans)} trades in output file")
        logger.info(f"All required sheets and columns present")
        
        # Check values
        logger.info("Checking trade values for correctness...")
        
        # Verify entry/exit times
        unique_entry_times = portfolio_trans['entry_time'].unique()
        logger.info(f"Unique entry times: {unique_entry_times}")
        
        unique_exit_times = portfolio_trans['exit_time'].unique()
        logger.info(f"Unique exit times: {unique_exit_times}")
        
        # Verify exit reasons
        unique_reasons = portfolio_trans['reason'].unique()
        logger.info(f"Unique exit reasons: {unique_reasons}")
        
        # Check that specific columns have been mapped from input files
        # This can be extended based on the column_mapping.md
        
        return True
    
    except Exception as e:
        logger.error(f"Error checking output file: {e}")
        return False

def verify_column_mappings():
    """Verify column mappings by checking input against output"""
    # Read the input files
    portfolio_sheets, strategy_sheets = read_excel_files()
    if portfolio_sheets is None:
        logger.error("Failed to read portfolio file")
        return False
    
    # Get key input values for verification
    portfolio_df = portfolio_sheets.get('PortfolioSetting')
    strategy_setting_df = portfolio_sheets.get('StrategySetting')
    
    if portfolio_df is None or strategy_setting_df is None:
        logger.error("Required sheets not found in portfolio file")
        return False
    
    # Get critical input values
    portfolio_name = portfolio_df.iloc[0]['PortfolioName'] if 'PortfolioName' in portfolio_df.columns else None
    strategy_type = strategy_setting_df.iloc[0]['StrategyType'] if 'StrategyType' in strategy_setting_df.columns else None
    
    logger.info(f"Portfolio name from input: {portfolio_name}")
    logger.info(f"Strategy type from input: {strategy_type}")
    
    # Read strategy info if available
    if strategy_sheets is not None:
        general_df = strategy_sheets.get('GeneralParameter')
        leg_df = strategy_sheets.get('LegParameter')
        
        if general_df is not None:
            strategy_name = general_df.iloc[0]['StrategyName'] if 'StrategyName' in general_df.columns else None
            start_time = general_df.iloc[0]['StartTime'] if 'StartTime' in general_df.columns else None
            end_time = general_df.iloc[0]['EndTime'] if 'EndTime' in general_df.columns else None
            
            logger.info(f"Strategy name from input: {strategy_name}")
            logger.info(f"Start time from input: {start_time}")
            logger.info(f"End time from input: {end_time}")
        
        if leg_df is not None:
            leg_transactions = leg_df['Transaction'].unique() if 'Transaction' in leg_df.columns else []
            leg_instruments = leg_df['Instrument'].unique() if 'Instrument' in leg_df.columns else []
            
            logger.info(f"Leg transactions from input: {leg_transactions}")
            logger.info(f"Leg instruments from input: {leg_instruments}")
    
    # Read the output file
    if not os.path.exists(OUTPUT_FILE):
        logger.error(f"Output file not found: {OUTPUT_FILE}")
        return False
    
    output_sheets = pd.read_excel(OUTPUT_FILE, sheet_name=None)
    portfolio_trans = output_sheets.get('PORTFOLIO Trans')
    
    if portfolio_trans is None:
        logger.error("PORTFOLIO Trans sheet not found in output file")
        return False
    
    # Compare input values with output
    mapping_checks = []
    
    # Check portfolio name mapping
    if portfolio_name:
        output_portfolio_names = portfolio_trans['portfolio_name'].unique() if 'portfolio_name' in portfolio_trans.columns else []
        portfolio_name_match = portfolio_name in output_portfolio_names
        mapping_checks.append(('Portfolio name', portfolio_name_match))
        logger.info(f"Portfolio name mapping: {'✅ OK' if portfolio_name_match else '❌ FAILED'}")
    
    # Check strategy name mapping
    if 'strategy_name' in locals() and strategy_name:
        output_strategy_names = portfolio_trans['strategy'].unique() if 'strategy' in portfolio_trans.columns else []
        strategy_name_match = strategy_name in output_strategy_names
        mapping_checks.append(('Strategy name', strategy_name_match))
        logger.info(f"Strategy name mapping: {'✅ OK' if strategy_name_match else '❌ FAILED'}")
    
    # Check transaction mapping (BUY/SELL)
    if 'leg_transactions' in locals() and len(leg_transactions) > 0:
        output_sides = portfolio_trans['side'].unique() if 'side' in portfolio_trans.columns else []
        transaction_match = all(trans.upper() in output_sides for trans in leg_transactions)
        mapping_checks.append(('Transaction mapping', transaction_match))
        logger.info(f"Transaction mapping: {'✅ OK' if transaction_match else '❌ FAILED'}")
    
    # Check instrument mapping (CE/PE)
    if 'leg_instruments' in locals() and len(leg_instruments) > 0:
        output_instruments = portfolio_trans['instrument_type'].unique() if 'instrument_type' in portfolio_trans.columns else []
        instrument_match = True  # Need custom logic to check CE/CALL, PE/PUT mappings
        mapping_checks.append(('Instrument mapping', instrument_match))
        logger.info(f"Instrument mapping: {'✅ OK' if instrument_match else '❌ FAILED'}")
    
    # Check exit time mapping
    if 'end_time' in locals() and end_time:
        # Convert end_time to HH:MM:SS format
        if isinstance(end_time, (int, float)):
            formatted_end_time = f"{int(end_time)//10000:02d}:{int(end_time)//100%100:02d}:{int(end_time)%100:02d}"
        else:
            formatted_end_time = str(end_time)
        
        output_exit_times = portfolio_trans['exit_time'].unique() if 'exit_time' in portfolio_trans.columns else []
        exit_time_match = any(time.endswith(formatted_end_time[-8:]) for time in output_exit_times)
        mapping_checks.append(('Exit time mapping', exit_time_match))
        logger.info(f"Exit time mapping: {'✅ OK' if exit_time_match else '❌ FAILED'}")
    
    # Overall result
    all_passed = all(result for _, result in mapping_checks)
    
    logger.info("\n=== Column Mapping Test Results ===")
    for check, result in mapping_checks:
        logger.info(f"{check}: {'✅ PASSED' if result else '❌ FAILED'}")
    
    if all_passed:
        logger.info("\n✅ ALL CHECKS PASSED: Excel columns correctly mapped to output")
    else:
        logger.error("\n❌ SOME CHECKS FAILED: Some column mappings may be incorrect")
    
    return all_passed

def main():
    """Main function"""
    logger.info("Starting column mapping test...")
    
    # Step 1: Run the backtester
    if not run_backtester():
        logger.error("Failed to run backtester, aborting test")
        return 1
    
    # Step 2: Check the output file
    if not check_output_file():
        logger.error("Output file validation failed, aborting test")
        return 1
    
    # Step 3: Verify column mappings
    if verify_column_mappings():
        logger.info("Column mapping test completed successfully")
        return 0
    else:
        logger.error("Column mapping test failed")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 