#!/usr/bin/env python3
"""
Run TBS Test with Fixed GPU Backtester
======================================

This script runs TBS tests with the now-fixed GPU backtester.

Author: Senior Engineer
Date: June 9, 2025
"""

import subprocess
import sys
import os
import json
import pandas as pd
from pathlib import Path
from datetime import datetime
import shutil

class TBSTestRunner:
    """Run TBS tests with fixed GPU backtester"""
    
    def __init__(self):
        self.test_dir = Path("/srv/samba/shared/test_results/tbs")
        self.output_dir = self.test_dir / "outputs"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Set working directory
        self.btrun_dir = Path("/srv/samba/shared/bt/backtester_stable/BTRUN")
        
    def prepare_test_files(self):
        """Prepare test files in the correct structure"""
        print("\n1. Preparing test files...")
        
        # The GPU backtester expects a specific file structure
        # Let's create a proper portfolio Excel file
        portfolio_data = {
            'PortfolioParameter': pd.DataFrame({
                'Parameter': ['PortfolioName', 'Capital', 'StartDate', 'EndDate'],
                'Value': ['TBS_Test', 1000000, '2024-04-01', '2024-04-02']
            })
        }
        
        strategy_data = {
            'GeneralParameter': pd.DataFrame({
                'Parameter': ['StartTime', 'EndTime', 'ExitTime', 'SquareOff'],
                'Value': ['09:20:00', '15:30:00', '15:15:00', 'TRUE']
            }),
            'LegParameter': pd.DataFrame({
                'LegID': [1, 2],
                'StrategyName': ['ATM_CE', 'ATM_PE'],
                'OptionType': ['CE', 'PE'],
                'StrikeMethod': ['ATM', 'ATM'],
                'StrikeValue': [0, 0],
                'Position': ['BUY', 'BUY'],
                'Quantity': [50, 50],
                'StopLoss': [30, 30],
                'Target': [50, 50],
                'Active': ['TRUE', 'TRUE']
            })
        }
        
        # Create test portfolio file
        portfolio_path = self.output_dir / "test_portfolio_tbs.xlsx"
        with pd.ExcelWriter(portfolio_path, engine='xlsxwriter') as writer:
            for sheet_name, df in portfolio_data.items():
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                
        # Create test strategy file  
        strategy_path = self.output_dir / "test_strategy_tbs.xlsx"
        with pd.ExcelWriter(strategy_path, engine='xlsxwriter') as writer:
            for sheet_name, df in strategy_data.items():
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                
        print(f"   ✅ Portfolio file: {portfolio_path}")
        print(f"   ✅ Strategy file: {strategy_path}")
        
        return str(portfolio_path), str(strategy_path)
        
    def run_gpu_backtest(self, portfolio_file: str, strategy_file: str = None):
        """Run GPU backtester with proper arguments"""
        print("\n2. Running GPU backtester...")
        
        # Change to BTRUN directory
        os.chdir(self.btrun_dir)
        
        # Build command
        output_path = str(self.output_dir / f"tbs_test_output_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
        
        cmd = [
            sys.executable,
            "BTRunPortfolio_GPU.py",
            "--portfolio-excel", portfolio_file,
            "--output-path", output_path,
            "--start-date", "20240401",
            "--end-date", "20240402",
            "--cpu-only",  # Use CPU mode for testing
            "--debug"  # Enable debug mode
        ]
        
        print(f"   Command: {' '.join(cmd)}")
        print("   Executing...")
        
        # Run the command
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        print(f"\n   Return code: {result.returncode}")
        
        if result.stdout:
            print("\n   STDOUT (last 1000 chars):")
            print("   " + "-"*50)
            print(result.stdout[-1000:])
            
        if result.stderr:
            print("\n   STDERR (last 1000 chars):")
            print("   " + "-"*50)
            print(result.stderr[-1000:])
            
        # Check output
        output_file = Path(output_path)
        if output_file.exists():
            print(f"\n   ✅ Output file created: {output_file}")
            return self.analyze_output(output_file)
        else:
            print("\n   ❌ No output file created")
            
            # Check for any output files
            possible_outputs = list(self.output_dir.glob("*.xlsx"))
            if possible_outputs:
                print(f"   Found {len(possible_outputs)} other Excel files:")
                for f in possible_outputs[-3:]:  # Show last 3
                    print(f"     - {f.name}")
                    
        return {
            "success": False,
            "returncode": result.returncode,
            "error": result.stderr if result.stderr else "No output generated"
        }
        
    def analyze_output(self, output_file: Path) -> dict:
        """Analyze the output Excel file"""
        print("\n3. Analyzing output...")
        
        try:
            # Read all sheets
            excel_data = pd.read_excel(output_file, sheet_name=None)
            
            print(f"   Sheets found: {list(excel_data.keys())}")
            
            result = {
                "success": True,
                "sheets": list(excel_data.keys()),
                "analysis": {}
            }
            
            # Analyze Trans sheet if exists
            if 'Trans' in excel_data:
                trans_df = excel_data['Trans']
                result["analysis"]["transactions"] = {
                    "count": len(trans_df),
                    "columns": list(trans_df.columns)
                }
                
                # Check if trades are closed
                if 'Status' in trans_df.columns:
                    status_counts = trans_df['Status'].value_counts().to_dict()
                    result["analysis"]["status_counts"] = status_counts
                    result["analysis"]["all_closed"] = all(trans_df['Status'] == 'CLOSED')
                else:
                    result["analysis"]["all_closed"] = None
                    
                # Check PnL
                if 'NetPL' in trans_df.columns:
                    result["analysis"]["total_pnl"] = trans_df['NetPL'].sum()
                elif 'PnL' in trans_df.columns:
                    result["analysis"]["total_pnl"] = trans_df['PnL'].sum()
                    
                print(f"   Transactions: {result['analysis']['transactions']['count']}")
                print(f"   All closed: {result['analysis'].get('all_closed', 'Unknown')}")
                print(f"   Total PnL: {result['analysis'].get('total_pnl', 'Unknown')}")
                
            # Check Metrics sheet
            if 'Metrics' in excel_data:
                metrics_df = excel_data['Metrics']
                result["analysis"]["metrics"] = {
                    "rows": len(metrics_df),
                    "columns": list(metrics_df.columns)
                }
                
            return result
            
        except Exception as e:
            print(f"   ❌ Error analyzing output: {e}")
            return {
                "success": False,
                "error": str(e)
            }
            
    def run_legacy_backtest(self, portfolio_file: str):
        """Run legacy backtester as fallback"""
        print("\n4. Running legacy backtester as comparison...")
        
        # Check if legacy runner exists
        legacy_runner = Path("/srv/samba/shared/BTRunPortfolio.py")
        if not legacy_runner.exists():
            print("   ❌ Legacy runner not found")
            return None
            
        # Run legacy backtester
        cmd = [
            sys.executable,
            str(legacy_runner),
            portfolio_file
        ]
        
        print(f"   Command: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, cwd="/srv/samba/shared")
        
        print(f"   Return code: {result.returncode}")
        
        return {
            "returncode": result.returncode,
            "success": result.returncode == 0
        }
        
    def generate_report(self, results: dict):
        """Generate comprehensive test report"""
        print("\n5. Generating report...")
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "phase": "3.1",
            "strategy": "TBS",
            "results": results,
            "conclusion": None
        }
        
        # Determine overall success
        gpu_success = results.get("gpu_test", {}).get("success", False)
        trades_closed = results.get("gpu_test", {}).get("analysis", {}).get("all_closed", False)
        
        if gpu_success and trades_closed:
            report["conclusion"] = "PASSED - Ready for Phase 3.2"
            print("\n   ✅ Phase 3.1 TBS Testing: PASSED")
        else:
            report["conclusion"] = "FAILED - Issues need resolution"
            print("\n   ❌ Phase 3.1 TBS Testing: FAILED")
            
        # Save report
        report_path = self.test_dir / f"phase_3_1_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
            
        print(f"   Report saved: {report_path}")
        
        return report
        

def main():
    """Run TBS test"""
    print("="*80)
    print("PHASE 3.1 TBS TESTING - COMPLETE EXECUTION")
    print("="*80)
    
    runner = TBSTestRunner()
    results = {}
    
    # Step 1: Prepare test files
    portfolio_file, strategy_file = runner.prepare_test_files()
    
    # Step 2: Run GPU backtest
    gpu_result = runner.run_gpu_backtest(portfolio_file)
    results["gpu_test"] = gpu_result
    
    # Step 3: Run legacy backtest for comparison (optional)
    # legacy_result = runner.run_legacy_backtest(portfolio_file)
    # results["legacy_test"] = legacy_result
    
    # Step 4: Generate report
    report = runner.generate_report(results)
    
    # Final verdict
    print("\n" + "="*80)
    print("FINAL VERDICT")
    print("="*80)
    
    if "PASSED" in report["conclusion"]:
        print("✅ PHASE 3.1 COMPLETE - READY FOR PHASE 3.2")
        print("\nNext steps:")
        print("1. Update E2E testing plan")
        print("2. Begin TV strategy testing")
        return 0
    else:
        print("❌ PHASE 3.1 REQUIRES FIXES")
        print("\nIssues to resolve:")
        if not results.get("gpu_test", {}).get("success"):
            print("- GPU backtester failed to generate output")
        if not results.get("gpu_test", {}).get("analysis", {}).get("all_closed"):
            print("- Trades not closing properly")
        return 1


if __name__ == "__main__":
    sys.exit(main())