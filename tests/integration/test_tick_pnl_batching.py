#!/usr/bin/env python3
"""
Test script to verify tick P&L batching implementation
Compares performance between old and new implementations
"""

import sys
import os
import logging
import time
from datetime import datetime, date

# Add the backtester path
sys.path.append('/srv/samba/shared/bt/backtester_stable')

from BTRUN.core.heavydb_connection import get_connection
from BTRUN.strategies.tick_pnl_batch import calculate_tick_pnl_batched, calculate_tick_pnl_multi_date_batch
from BTRUN.strategies.tick_pnl_performance_monitor import TickPnLPerformanceMonitor, log_performance_improvement

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_sample_trades():
    """Create sample trade records for testing."""
    trades = [
        {
            'entry_date': '2024-04-01',
            'entry_time': '09:20:00',
            'exit_date': '2024-04-01', 
            'exit_time': '15:25:00',
            'strike': 22000.0,
            'expiry': '2024-04-01',
            'instrument_type': 'CALL',
            'side': 'BUY',
            'filled_quantity': 50,
            'entry_price': 150.0,
            'pnl': 2500.0
        },
        {
            'entry_date': '2024-04-01',
            'entry_time': '09:25:00',
            'exit_date': '2024-04-01',
            'exit_time': '15:25:00', 
            'strike': 22100.0,
            'expiry': '2024-04-01',
            'instrument_type': 'PUT',
            'side': 'SELL',
            'filled_quantity': 50,
            'entry_price': 120.0,
            'pnl': 1800.0
        },
        {
            'entry_date': '2024-04-02',
            'entry_time': '09:30:00',
            'exit_date': '2024-04-02',
            'exit_time': '15:20:00',
            'strike': 22200.0,
            'expiry': '2024-04-02',
            'instrument_type': 'CALL',
            'side': 'BUY',
            'filled_quantity': 100,
            'entry_price': 180.0,
            'pnl': -1200.0
        }
    ]
    return trades


def test_single_date_batch():
    """Test the single-date batching implementation."""
    logger.info("=" * 60)
    logger.info("Testing Single-Date Batch Implementation")
    logger.info("=" * 60)
    
    trades = create_sample_trades()
    conn = None
    
    try:
        conn = get_connection()
        if not conn:
            logger.error("Failed to get database connection")
            return
        
        with TickPnLPerformanceMonitor("Single-Date Batch") as monitor:
            result = calculate_tick_pnl_batched(trades, conn)
        
        stats = monitor.get_stats()
        
        # Log results
        logger.info(f"\nResults: {len(result)} dates processed")
        for date_str, tick_data in result.items():
            logger.info(f"Date {date_str}: {len(tick_data)} timestamps")
            
            # Show sample of P&L values
            if tick_data:
                sorted_times = sorted(tick_data.keys())
                logger.info(f"  First 5 timestamps: {sorted_times[:5]}")
                logger.info(f"  First 5 P&L values: {[tick_data[t] for t in sorted_times[:5]]}")
        
        return stats
        
    except Exception as e:
        logger.error(f"Error in single-date batch test: {e}", exc_info=True)
        return None
    finally:
        if conn:
            conn.close()


def test_multi_date_batch():
    """Test the multi-date batching implementation."""
    logger.info("\n" + "=" * 60)
    logger.info("Testing Multi-Date Batch Implementation")
    logger.info("=" * 60)
    
    # Create more trades across multiple dates
    trades = create_sample_trades()
    
    # Add more dates
    for i in range(3, 6):
        trades.extend([
            {
                'entry_date': f'2024-04-0{i}',
                'entry_time': '09:20:00',
                'exit_date': f'2024-04-0{i}',
                'exit_time': '15:25:00',
                'strike': 22000.0 + (i * 100),
                'expiry': f'2024-04-0{i}',
                'instrument_type': 'CALL' if i % 2 == 0 else 'PUT',
                'side': 'BUY',
                'filled_quantity': 50,
                'entry_price': 150.0 + (i * 10),
                'pnl': 1000.0 * (i % 3 - 1)
            }
        ])
    
    conn = None
    
    try:
        conn = get_connection()
        if not conn:
            logger.error("Failed to get database connection")
            return
        
        with TickPnLPerformanceMonitor("Multi-Date Batch") as monitor:
            result = calculate_tick_pnl_multi_date_batch(trades, conn, max_dates_per_batch=3)
        
        stats = monitor.get_stats()
        
        # Log results
        logger.info(f"\nResults: {len(result)} dates processed")
        total_timestamps = sum(len(tick_data) for tick_data in result.values())
        logger.info(f"Total timestamps across all dates: {total_timestamps}")
        
        return stats
        
    except Exception as e:
        logger.error(f"Error in multi-date batch test: {e}", exc_info=True)
        return None
    finally:
        if conn:
            conn.close()


def simulate_old_implementation_stats():
    """Simulate the stats from the old implementation for comparison."""
    # Based on the reported 80,766 queries taking 5 minutes
    return {
        'total_queries': 80766,
        'total_query_time': 300.0,  # 5 minutes in seconds
        'average_query_time': 300.0 / 80766  # ~0.0037 seconds per query
    }


def main():
    """Main test function."""
    logger.info("Starting Tick P&L Batching Test")
    logger.info("This test verifies the performance improvement from query batching")
    
    # Get simulated old stats
    old_stats = simulate_old_implementation_stats()
    logger.info(f"\nOld Implementation Stats (from performance analysis):")
    logger.info(f"  Total Queries: {old_stats['total_queries']:,}")
    logger.info(f"  Total Time: {old_stats['total_query_time']:.1f}s")
    
    # Test single-date batching
    single_date_stats = test_single_date_batch()
    
    # Test multi-date batching  
    multi_date_stats = test_multi_date_batch()
    
    # Show improvement comparison
    if single_date_stats:
        logger.info("\n" + "=" * 60)
        logger.info("PERFORMANCE IMPROVEMENT - Single-Date Batching")
        log_performance_improvement(old_stats, single_date_stats)
    
    if multi_date_stats:
        logger.info("\n" + "=" * 60)
        logger.info("PERFORMANCE IMPROVEMENT - Multi-Date Batching")
        log_performance_improvement(old_stats, multi_date_stats)
    
    # Expected improvement summary
    logger.info("\n" + "=" * 60)
    logger.info("EXPECTED IMPROVEMENTS")
    logger.info("=" * 60)
    logger.info("Query Reduction: From 80,766 to ~100-200 queries")
    logger.info("Time Reduction: From 5 minutes to <10 seconds")
    logger.info("Speedup Factor: ~30-50x faster")
    logger.info("=" * 60)


if __name__ == "__main__":
    main()