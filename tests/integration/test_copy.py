#!/usr/bin/env python3
import subprocess, os, sys, time

CMD_BASE = ["/opt/heavyai/bin/heavysql", "-s", "127.0.0.1", "--port", "6274", \
            "-u", "admin", "-p", "HyperInteractive", "-d", "heavyai"]

def run_sql(sql: str):
    process = subprocess.Popen(CMD_BASE, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
    stdout, stderr = process.communicate(input=sql)
    return stdout, stderr

def main():
    sql = """
    DROP TABLE IF EXISTS tmp_csv;
    CREATE TABLE tmp_csv (
        trade_date TEXT, trade_time TEXT, expiry_date TEXT, index_name TEXT,
        spot DOUBLE, atm_strike DOUBLE, strike DOUBLE, dte INT, expiry_bucket TEXT,
        zone_id INT, zone_name TEXT,
        call_strike_type TEXT, put_strike_type TEXT,
        ce_symbol TEXT, ce_open DOUBLE, ce_high DOUBLE, ce_low DOUBLE, ce_close DOUBLE,
        ce_volume DOUBLE, ce_oi DOUBLE, ce_coi DOUBLE, ce_iv DOUBLE, ce_delta DOUBLE, ce_gamma DOUBLE,
        ce_theta DOUBLE, ce_vega DOUBLE, ce_rho DOUBLE,
        pe_symbol TEXT, pe_open DOUBLE, pe_high DOUBLE, pe_low DOUBLE, pe_close DOUBLE,
        pe_volume DOUBLE, pe_oi DOUBLE, pe_coi DOUBLE, pe_iv DOUBLE, pe_delta DOUBLE, pe_gamma DOUBLE,
        pe_theta DOUBLE, pe_vega DOUBLE, pe_rho DOUBLE,
        future_open DOUBLE, future_high DOUBLE, future_low DOUBLE, future_close DOUBLE,
        future_volume DOUBLE, future_oi DOUBLE, future_coi DOUBLE);
    COPY tmp_csv FROM '/var/lib/heavyai/storage/import/sample.csv' WITH (header='true', delimiter=',');
    SELECT COUNT(*) FROM tmp_csv;
    """
    out, err = run_sql(sql)
    print("STDOUT\n", out)
    print("STDERR\n", err)

if __name__ == "__main__":
    main() 