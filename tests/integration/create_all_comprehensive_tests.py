#!/usr/bin/env python3
"""
Comprehensive Test Case Generator for GPU Backtester
Creates test files for ALL possible scenarios and combinations
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import itertools
from pathlib import Path
import json

# Base output directory
OUTPUT_DIR = "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/comprehensive_tests_v2"

class ComprehensiveTestGenerator:
    """Generates comprehensive test cases for all strategy types"""
    
    def __init__(self):
        self.output_dir = Path(OUTPUT_DIR)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Create subdirectories for each test category
        self.test_categories = [
            'strike_selection',
            'risk_management', 
            'reentry_scenarios',
            'expiry_combinations',
            'multileg_strategies',
            'time_windows',
            'edge_cases',
            'performance',
            'integration'
        ]
        
        for category in self.test_categories:
            (self.output_dir / category).mkdir(exist_ok=True)
            
    def create_tbs_strike_selection_tests(self):
        """Create test files for all TBS strike selection methods"""
        print("\nCreating TBS Strike Selection Tests...")
        
        strike_dir = self.output_dir / 'strike_selection' / 'tbs'
        strike_dir.mkdir(parents=True, exist_ok=True)
        
        # Test 1: ATM with all offsets
        atm_tests = []
        for offset in [-2, -1, 0, 1, 2]:
            for instrument in ['call', 'put']:
                atm_tests.append({
                    'test_name': f'ATM_offset_{offset}_{instrument}',
                    'strike_method': 'ATM',
                    'strike_value': offset,
                    'instrument': instrument
                })
        
        # Test 2: ITM variants
        itm_tests = []
        for i in range(1, 11):
            for instrument in ['call', 'put']:
                itm_tests.append({
                    'test_name': f'ITM{i}_{instrument}',
                    'strike_method': f'ITM{i}',
                    'instrument': instrument
                })
        
        # Test 3: OTM variants
        otm_tests = []
        for i in range(1, 11):
            for instrument in ['call', 'put']:
                otm_tests.append({
                    'test_name': f'OTM{i}_{instrument}',
                    'strike_method': f'OTM{i}',
                    'instrument': instrument
                })
        
        # Test 4: FIXED strikes
        fixed_tests = []
        for strike in [19000, 19500, 20000, 20500, 21000]:
            for instrument in ['call', 'put']:
                fixed_tests.append({
                    'test_name': f'FIXED_{strike}_{instrument}',
                    'strike_method': 'FIXED',
                    'strike_value': strike,
                    'instrument': instrument
                })
        
        # Test 5: PREMIUM-based
        premium_tests = []
        for premium in [50, 100, 200, 300]:
            for condition in ['=', '<', '>', '<=']:
                for instrument in ['call', 'put']:
                    premium_tests.append({
                        'test_name': f'PREMIUM_{premium}_{condition}_{instrument}',
                        'strike_method': 'PREMIUM',
                        'strike_value': premium,
                        'premium_condition': condition,
                        'instrument': instrument
                    })
        
        # Test 6: ATM WIDTH
        atm_width_tests = []
        for multiplier in [0.5, -0.5, 1.0, -1.0, 1.5, -1.5]:
            for instrument in ['call', 'put']:
                atm_width_tests.append({
                    'test_name': f'ATM_WIDTH_{multiplier}_{instrument}',
                    'strike_method': 'ATM WIDTH',
                    'strike_value': multiplier,
                    'instrument': instrument
                })
        
        # Test 7: DELTA-based
        delta_tests = []
        for delta in [0.1, 0.3, 0.5, 0.7, 0.9]:
            for instrument in ['call', 'put']:
                delta_tests.append({
                    'test_name': f'DELTA_{delta}_{instrument}',
                    'strike_method': 'DELTA',
                    'strike_value': delta,
                    'instrument': instrument
                })
        
        # Create individual test files for each category
        all_tests = {
            'atm_offset': atm_tests,
            'itm_variants': itm_tests,
            'otm_variants': otm_tests,
            'fixed_strikes': fixed_tests,
            'premium_based': premium_tests,
            'atm_width': atm_width_tests,
            'delta_based': delta_tests
        }
        
        for category, tests in all_tests.items():
            self._create_tbs_test_file(
                f"{strike_dir}/tbs_strike_{category}.xlsx",
                tests
            )
            
        print(f"Created {sum(len(tests) for tests in all_tests.values())} strike selection tests")
        
    def _create_tbs_test_file(self, filename, test_configs):
        """Create a TBS test file with multiple test configurations"""
        
        # Portfolio settings
        portfolio_data = []
        
        # General parameters
        general_data = []
        
        # Leg parameters
        leg_data = []
        
        for i, config in enumerate(test_configs):
            # Add portfolio entry
            portfolio_data.append({
                'Enabled': 'YES',
                'PortfolioName': f"TEST_{config['test_name']}",
                'IsTickBT': 'NO',
                'StartDate': '01_04_2024',
                'EndDate': '05_04_2024',
                'PortfolioTarget': 10000,
                'PortfolioStoploss': -5000,
                'Multiplier': 1
            })
            
            # Add general parameters
            general_data.append({
                'StrategyName': config['test_name'],
                'MoveSlToCost': 'NO',
                'Underlying': 'SPOT',
                'Index': 'NIFTY',
                'Weekdays': '1,2,3,4,5',
                'DTE': 0,
                'StrikeSelectionTime': 91600,
                'StartTime': 91600,
                'LastEntryTime': 150000,
                'EndTime': 152000,
                'StrategyProfit': 5000,
                'StrategyLoss': -2500,
                'StrategyProfitReExecuteNo': 0,
                'StrategyLossReExecuteNo': 0
            })
            
            # Add leg parameters
            leg_data.append({
                'StrategyName': config['test_name'],
                'IsIdle': 'no',
                'LegID': f"LEG_{i}_1",
                'Instrument': config.get('instrument', 'call'),
                'Transaction': 'sell',
                'Expiry': 'current',
                'StrikeMethod': config['strike_method'],
                'StrikeValue': config.get('strike_value', 0),
                'StrikePremiumCondition': config.get('premium_condition', '='),
                'SLType': 'percentage',
                'SLValue': 100,
                'TGTType': 'percentage',
                'TGTValue': 50,
                'Lots': 1,
                'OpenHedge': 'No'
            })
            
        # Strategy settings (references this file itself)
        strategy_data = [{
            'Enabled': 'YES',
            'PortfolioName': portfolio['PortfolioName'],
            'StrategyType': 'TBS',
            'StrategyExcelFilePath': os.path.basename(filename)
        } for portfolio in portfolio_data]
        
        # Create Excel file
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            pd.DataFrame(portfolio_data).to_excel(writer, sheet_name='PortfolioSetting', index=False)
            pd.DataFrame(strategy_data).to_excel(writer, sheet_name='StrategySetting', index=False)
            pd.DataFrame(general_data).to_excel(writer, sheet_name='GeneralParameter', index=False)
            pd.DataFrame(leg_data).to_excel(writer, sheet_name='LegParameter', index=False)
            
    def create_risk_management_tests(self):
        """Create test files for all risk management scenarios"""
        print("\nCreating Risk Management Tests...")
        
        risk_dir = self.output_dir / 'risk_management'
        risk_dir.mkdir(exist_ok=True)
        
        # Test all risk types
        risk_types = ['percentage', 'point', 'index point', 'index percentage', 'absolute', 'delta']
        
        risk_tests = []
        for sl_type in risk_types:
            for tgt_type in risk_types:
                for trail_type in risk_types:
                    risk_tests.append({
                        'test_name': f'RISK_{sl_type}_{tgt_type}_{trail_type}'.replace(' ', '_'),
                        'sl_type': sl_type,
                        'tgt_type': tgt_type,
                        'trail_type': trail_type,
                        'sl_value': 100 if 'percentage' in sl_type else 1000,
                        'tgt_value': 50 if 'percentage' in tgt_type else 500,
                        'trail_at': 25 if 'percentage' in trail_type else 250,
                        'trail_by': 10 if 'percentage' in trail_type else 100
                    })
        
        # Create test file
        self._create_risk_test_file(
            f"{risk_dir}/all_risk_type_combinations.xlsx",
            risk_tests[:50]  # Limit to 50 for practicality
        )
        
        # Test trailing types
        trailing_tests = []
        trailing_types = [
            'Lock Minimum Profit',
            'Lock & Trail Profits',
            'trail profits',
            'portfolio lock trail',
            'max possible profit'
        ]
        
        for trail_type in trailing_types:
            trailing_tests.append({
                'test_name': f'TRAIL_{trail_type}'.replace(' ', '_'),
                'trailing_type': trail_type,
                'profit_reaches': 5000,
                'lock_min_profit_at': 2500,
                'increase_in_profit': 500,
                'trail_min_profit_by': 250
            })
            
        self._create_trailing_test_file(
            f"{risk_dir}/all_trailing_types.xlsx",
            trailing_tests
        )
        
        print(f"Created {len(risk_tests[:50]) + len(trailing_tests)} risk management tests")
        
    def _create_risk_test_file(self, filename, test_configs):
        """Create risk management test file"""
        
        general_data = []
        leg_data = []
        
        for config in test_configs:
            general_data.append({
                'StrategyName': config['test_name'],
                'MoveSlToCost': 'NO',
                'Underlying': 'SPOT',
                'Index': 'NIFTY',
                'DTE': 0,
                'StartTime': 91600,
                'EndTime': 152000
            })
            
            leg_data.append({
                'StrategyName': config['test_name'],
                'LegID': 'LEG_1',
                'Instrument': 'call',
                'Transaction': 'sell',
                'Expiry': 'current',
                'StrikeMethod': 'ATM',
                'StrikeValue': 0,
                'SLType': config['sl_type'],
                'SLValue': config['sl_value'],
                'TGTType': config['tgt_type'],
                'TGTValue': config['tgt_value'],
                'TrailSLType': config['trail_type'],
                'SL_TrailAt': config['trail_at'],
                'SL_TrailBy': config['trail_by'],
                'Lots': 1
            })
            
        # Portfolio settings
        portfolio_data = [{
            'Enabled': 'YES',
            'PortfolioName': 'RISK_MGMT_TESTS',
            'StartDate': '01_04_2024',
            'EndDate': '05_04_2024'
        }]
        
        strategy_data = [{
            'Enabled': 'YES',
            'PortfolioName': 'RISK_MGMT_TESTS',
            'StrategyType': 'TBS',
            'StrategyExcelFilePath': os.path.basename(filename)
        }]
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            pd.DataFrame(portfolio_data).to_excel(writer, sheet_name='PortfolioSetting', index=False)
            pd.DataFrame(strategy_data).to_excel(writer, sheet_name='StrategySetting', index=False)
            pd.DataFrame(general_data).to_excel(writer, sheet_name='GeneralParameter', index=False)
            pd.DataFrame(leg_data).to_excel(writer, sheet_name='LegParameter', index=False)
            
    def _create_trailing_test_file(self, filename, test_configs):
        """Create trailing type test file"""
        
        general_data = []
        
        for config in test_configs:
            general_data.append({
                'StrategyName': config['test_name'],
                'Underlying': 'SPOT',
                'Index': 'NIFTY',
                'DTE': 0,
                'StartTime': 91600,
                'EndTime': 152000,
                'StrategyTrailingType': config['trailing_type'],
                'ProfitReaches': config['profit_reaches'],
                'LockMinProfitAt': config['lock_min_profit_at'],
                'IncreaseInProfit': config['increase_in_profit'],
                'TrailMinProfitBy': config['trail_min_profit_by']
            })
            
        leg_data = [{
            'StrategyName': config['test_name'],
            'LegID': 'LEG_1',
            'Instrument': 'call',
            'Transaction': 'sell',
            'Expiry': 'current',
            'StrikeMethod': 'ATM',
            'StrikeValue': 0,
            'Lots': 1
        } for config in test_configs]
        
        portfolio_data = [{
            'Enabled': 'YES',
            'PortfolioName': 'TRAILING_TESTS',
            'StartDate': '01_04_2024',
            'EndDate': '05_04_2024'
        }]
        
        strategy_data = [{
            'Enabled': 'YES',
            'PortfolioName': 'TRAILING_TESTS',
            'StrategyType': 'TBS',
            'StrategyExcelFilePath': os.path.basename(filename)
        }]
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            pd.DataFrame(portfolio_data).to_excel(writer, sheet_name='PortfolioSetting', index=False)
            pd.DataFrame(strategy_data).to_excel(writer, sheet_name='StrategySetting', index=False)
            pd.DataFrame(general_data).to_excel(writer, sheet_name='GeneralParameter', index=False)
            pd.DataFrame(leg_data).to_excel(writer, sheet_name='LegParameter', index=False)
            
    def create_reentry_tests(self):
        """Create test files for all re-entry scenarios"""
        print("\nCreating Re-entry Tests...")
        
        reentry_dir = self.output_dir / 'reentry_scenarios'
        reentry_dir.mkdir(exist_ok=True)
        
        reentry_types = ['cost', 'original', 'instant new strike', 'instant same strike']
        
        reentry_tests = []
        for sl_reentry in reentry_types:
            for tgt_reentry in reentry_types:
                for sl_count in [0, 1, 2, 3, 5]:
                    for tgt_count in [0, 1, 2, 3, 5]:
                        reentry_tests.append({
                            'test_name': f'REENTRY_{sl_reentry}_{sl_count}_{tgt_reentry}_{tgt_count}'.replace(' ', '_'),
                            'sl_reentry_type': sl_reentry,
                            'sl_reentry_no': sl_count,
                            'tgt_reentry_type': tgt_reentry,
                            'tgt_reentry_no': tgt_count
                        })
        
        # Create test file with subset
        self._create_reentry_test_file(
            f"{reentry_dir}/all_reentry_combinations.xlsx",
            reentry_tests[:30]  # Limit for practicality
        )
        
        print(f"Created {len(reentry_tests[:30])} re-entry tests")
        
    def _create_reentry_test_file(self, filename, test_configs):
        """Create re-entry test file"""
        
        general_data = []
        leg_data = []
        
        for config in test_configs:
            general_data.append({
                'StrategyName': config['test_name'],
                'Underlying': 'SPOT',
                'Index': 'NIFTY',
                'DTE': 0,
                'StartTime': 91600,
                'EndTime': 152000
            })
            
            leg_data.append({
                'StrategyName': config['test_name'],
                'LegID': 'LEG_1',
                'Instrument': 'call',
                'Transaction': 'sell',
                'Expiry': 'current',
                'StrikeMethod': 'ATM',
                'StrikeValue': 0,
                'SLType': 'percentage',
                'SLValue': 50,
                'TGTType': 'percentage',
                'TGTValue': 30,
                'SL_ReEntryType': config['sl_reentry_type'],
                'SL_ReEntryNo': config['sl_reentry_no'],
                'TGT_ReEntryType': config['tgt_reentry_type'],
                'TGT_ReEntryNo': config['tgt_reentry_no'],
                'Lots': 1
            })
            
        portfolio_data = [{
            'Enabled': 'YES',
            'PortfolioName': 'REENTRY_TESTS',
            'StartDate': '01_04_2024',
            'EndDate': '05_04_2024'
        }]
        
        strategy_data = [{
            'Enabled': 'YES',
            'PortfolioName': 'REENTRY_TESTS',
            'StrategyType': 'TBS',
            'StrategyExcelFilePath': os.path.basename(filename)
        }]
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            pd.DataFrame(portfolio_data).to_excel(writer, sheet_name='PortfolioSetting', index=False)
            pd.DataFrame(strategy_data).to_excel(writer, sheet_name='StrategySetting', index=False)
            pd.DataFrame(general_data).to_excel(writer, sheet_name='GeneralParameter', index=False)
            pd.DataFrame(leg_data).to_excel(writer, sheet_name='LegParameter', index=False)
            
    def create_expiry_tests(self):
        """Create test files for all expiry combinations"""
        print("\nCreating Expiry Tests...")
        
        expiry_dir = self.output_dir / 'expiry_combinations'
        expiry_dir.mkdir(exist_ok=True)
        
        expiry_types = ['current', 'next', 'monthly', 'CW', 'NW', 'CM', 'NM']
        
        expiry_tests = []
        for expiry in expiry_types:
            for on_expiry_trade_next in ['yes', 'no']:
                expiry_tests.append({
                    'test_name': f'EXPIRY_{expiry}_{on_expiry_trade_next}',
                    'expiry': expiry,
                    'on_expiry_trade_next': on_expiry_trade_next
                })
                
        self._create_expiry_test_file(
            f"{expiry_dir}/all_expiry_types.xlsx",
            expiry_tests
        )
        
        print(f"Created {len(expiry_tests)} expiry tests")
        
    def _create_expiry_test_file(self, filename, test_configs):
        """Create expiry test file"""
        
        general_data = []
        leg_data = []
        
        for config in test_configs:
            general_data.append({
                'StrategyName': config['test_name'],
                'Underlying': 'SPOT',
                'Index': 'NIFTY',
                'DTE': 0,
                'StartTime': 91600,
                'EndTime': 152000,
                'OnExpiryDayTradeNextExpiry': config['on_expiry_trade_next']
            })
            
            leg_data.append({
                'StrategyName': config['test_name'],
                'LegID': 'LEG_1',
                'Instrument': 'call',
                'Transaction': 'sell',
                'Expiry': config['expiry'],
                'StrikeMethod': 'ATM',
                'StrikeValue': 0,
                'Lots': 1
            })
            
        portfolio_data = [{
            'Enabled': 'YES',
            'PortfolioName': 'EXPIRY_TESTS',
            'StartDate': '01_04_2024',
            'EndDate': '30_04_2024'  # Full month to test expiry handling
        }]
        
        strategy_data = [{
            'Enabled': 'YES',
            'PortfolioName': 'EXPIRY_TESTS',
            'StrategyType': 'TBS',
            'StrategyExcelFilePath': os.path.basename(filename)
        }]
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            pd.DataFrame(portfolio_data).to_excel(writer, sheet_name='PortfolioSetting', index=False)
            pd.DataFrame(strategy_data).to_excel(writer, sheet_name='StrategySetting', index=False)
            pd.DataFrame(general_data).to_excel(writer, sheet_name='GeneralParameter', index=False)
            pd.DataFrame(leg_data).to_excel(writer, sheet_name='LegParameter', index=False)
            
    def create_multileg_strategy_tests(self):
        """Create test files for multi-leg strategies"""
        print("\nCreating Multi-leg Strategy Tests...")
        
        multileg_dir = self.output_dir / 'multileg_strategies'
        multileg_dir.mkdir(exist_ok=True)
        
        # Define common multi-leg strategies
        strategies = {
            'long_straddle': [
                {'instrument': 'call', 'transaction': 'buy', 'strike': 'ATM', 'strike_value': 0},
                {'instrument': 'put', 'transaction': 'buy', 'strike': 'ATM', 'strike_value': 0}
            ],
            'short_straddle': [
                {'instrument': 'call', 'transaction': 'sell', 'strike': 'ATM', 'strike_value': 0},
                {'instrument': 'put', 'transaction': 'sell', 'strike': 'ATM', 'strike_value': 0}
            ],
            'long_strangle': [
                {'instrument': 'call', 'transaction': 'buy', 'strike': 'OTM1', 'strike_value': 0},
                {'instrument': 'put', 'transaction': 'buy', 'strike': 'OTM1', 'strike_value': 0}
            ],
            'short_strangle': [
                {'instrument': 'call', 'transaction': 'sell', 'strike': 'OTM1', 'strike_value': 0},
                {'instrument': 'put', 'transaction': 'sell', 'strike': 'OTM1', 'strike_value': 0}
            ],
            'iron_condor': [
                {'instrument': 'call', 'transaction': 'sell', 'strike': 'OTM1', 'strike_value': 0},
                {'instrument': 'call', 'transaction': 'buy', 'strike': 'OTM2', 'strike_value': 0},
                {'instrument': 'put', 'transaction': 'sell', 'strike': 'OTM1', 'strike_value': 0},
                {'instrument': 'put', 'transaction': 'buy', 'strike': 'OTM2', 'strike_value': 0}
            ],
            'iron_fly': [
                {'instrument': 'call', 'transaction': 'sell', 'strike': 'ATM', 'strike_value': 0},
                {'instrument': 'call', 'transaction': 'buy', 'strike': 'OTM1', 'strike_value': 0},
                {'instrument': 'put', 'transaction': 'sell', 'strike': 'ATM', 'strike_value': 0},
                {'instrument': 'put', 'transaction': 'buy', 'strike': 'OTM1', 'strike_value': 0}
            ],
            'call_spread': [
                {'instrument': 'call', 'transaction': 'buy', 'strike': 'ATM', 'strike_value': 0},
                {'instrument': 'call', 'transaction': 'sell', 'strike': 'OTM1', 'strike_value': 0}
            ],
            'put_spread': [
                {'instrument': 'put', 'transaction': 'buy', 'strike': 'ATM', 'strike_value': 0},
                {'instrument': 'put', 'transaction': 'sell', 'strike': 'ITM1', 'strike_value': 0}
            ],
            'ratio_spread': [
                {'instrument': 'call', 'transaction': 'buy', 'strike': 'ATM', 'strike_value': 0, 'lots': 1},
                {'instrument': 'call', 'transaction': 'sell', 'strike': 'OTM1', 'strike_value': 0, 'lots': 2}
            ],
            'butterfly': [
                {'instrument': 'call', 'transaction': 'buy', 'strike': 'ITM1', 'strike_value': 0},
                {'instrument': 'call', 'transaction': 'sell', 'strike': 'ATM', 'strike_value': 0, 'lots': 2},
                {'instrument': 'call', 'transaction': 'buy', 'strike': 'OTM1', 'strike_value': 0}
            ]
        }
        
        for strategy_name, legs in strategies.items():
            self._create_multileg_test_file(
                f"{multileg_dir}/{strategy_name}.xlsx",
                strategy_name,
                legs
            )
            
        print(f"Created {len(strategies)} multi-leg strategy tests")
        
    def _create_multileg_test_file(self, filename, strategy_name, legs):
        """Create multi-leg strategy test file"""
        
        general_data = [{
            'StrategyName': strategy_name.upper(),
            'Underlying': 'SPOT',
            'Index': 'NIFTY',
            'DTE': 0,
            'StartTime': 91600,
            'EndTime': 152000,
            'StrategyProfit': 10000,
            'StrategyLoss': -5000
        }]
        
        leg_data = []
        for i, leg in enumerate(legs):
            leg_data.append({
                'StrategyName': strategy_name.upper(),
                'LegID': f'LEG_{i+1}',
                'Instrument': leg['instrument'],
                'Transaction': leg['transaction'],
                'Expiry': 'current',
                'StrikeMethod': leg['strike'],
                'StrikeValue': leg['strike_value'],
                'SLType': 'percentage',
                'SLValue': 200,
                'TGTType': 'percentage',
                'TGTValue': 100,
                'Lots': leg.get('lots', 1)
            })
            
        portfolio_data = [{
            'Enabled': 'YES',
            'PortfolioName': f'MULTILEG_{strategy_name.upper()}',
            'StartDate': '01_04_2024',
            'EndDate': '05_04_2024',
            'PortfolioTarget': 20000,
            'PortfolioStoploss': -10000
        }]
        
        strategy_data = [{
            'Enabled': 'YES',
            'PortfolioName': f'MULTILEG_{strategy_name.upper()}',
            'StrategyType': 'TBS',
            'StrategyExcelFilePath': os.path.basename(filename)
        }]
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            pd.DataFrame(portfolio_data).to_excel(writer, sheet_name='PortfolioSetting', index=False)
            pd.DataFrame(strategy_data).to_excel(writer, sheet_name='StrategySetting', index=False)
            pd.DataFrame(general_data).to_excel(writer, sheet_name='GeneralParameter', index=False)
            pd.DataFrame(leg_data).to_excel(writer, sheet_name='LegParameter', index=False)
            
    def create_oi_tests(self):
        """Create comprehensive OI strategy tests"""
        print("\nCreating OI Strategy Tests...")
        
        oi_dir = self.output_dir / 'oi'
        oi_dir.mkdir(exist_ok=True)
        
        # Test all OI methods
        oi_methods = []
        
        # MAXOI tests
        for i in range(1, 11):
            oi_methods.append({
                'method': f'MAXOI_{i}',
                'test_name': f'MAXOI_rank_{i}'
            })
            
        # MAXCOI tests
        for i in range(1, 11):
            oi_methods.append({
                'method': f'MAXCOI_{i}',
                'test_name': f'MAXCOI_rank_{i}'
            })
            
        # Timeframe tests
        timeframes = [3, 6, 9, 12, 15, 18, 21, 24]
        
        general_data = []
        leg_data = []
        
        # Create tests for each method and timeframe combination
        for method in oi_methods[:10]:  # Limit for file size
            for timeframe in timeframes[:4]:  # Test key timeframes
                test_name = f"{method['test_name']}_TF{timeframe}"
                
                general_data.append({
                    'StrategyName': test_name,
                    'OiMethod': method['method'],
                    'Timeframe': timeframe,
                    'OiThreshold': 1000000,
                    'MaxOpenPositions': 5,
                    'COIBasedOn': 'TODAY_OPEN',
                    'EntryStartTime': 91600,
                    'EntryEndTime': 150000,
                    'ExitTime': 152000
                })
                
                leg_data.append({
                    'StrategyName': test_name,
                    'LegID': 'LEG_1',
                    'Transaction': 'sell',
                    'StrikeType': 'dynamic',  # OI-based dynamic selection
                    'SLType': 'percentage',
                    'SLValue': 50,
                    'TGTType': 'percentage', 
                    'TGTValue': 30,
                    'Lots': 1
                })
                
        # COI calculation method tests
        coi_tests = []
        for coi_method in ['TODAY_OPEN', 'PREV_DAY_CLOSE', 'YESTERDAY_CLOSE']:
            coi_tests.append({
                'StrategyName': f'COI_METHOD_{coi_method}',
                'OiMethod': 'MAXCOI_1',
                'Timeframe': 3,
                'COIBasedOn': coi_method
            })
            
        general_data.extend(coi_tests)
        
        # Create test file
        with pd.ExcelWriter(f"{oi_dir}/comprehensive_oi_methods.xlsx", engine='openpyxl') as writer:
            pd.DataFrame(general_data).to_excel(writer, sheet_name='GeneralParameter', index=False)
            pd.DataFrame(leg_data).to_excel(writer, sheet_name='LegParameter', index=False)
            
        print(f"Created {len(general_data)} OI strategy tests")
        
    def create_tv_tests(self):
        """Create comprehensive TV strategy tests"""
        print("\nCreating TV Strategy Tests...")
        
        tv_dir = self.output_dir / 'tv'
        tv_dir.mkdir(exist_ok=True)
        
        # Create signal file with all signal types
        signals = []
        base_time = datetime(2024, 4, 1, 9, 30)
        
        signal_types = [
            'Entry Long',
            'Exit Long', 
            'Entry Short',
            'Exit Short',
            'Manual Entry',
            'Manual Exit'
        ]
        
        # Generate signals for different scenarios
        for day in range(5):  # 5 days of signals
            current_date = base_time + timedelta(days=day)
            
            for i, signal_type in enumerate(signal_types):
                signal_time = current_date + timedelta(minutes=30*i)
                
                signals.append({
                    'Date': current_date.strftime('%d-%m-%Y'),
                    'Time': signal_time.strftime('%H:%M:%S'),
                    'SignalType': signal_type,
                    'Symbol': 'NIFTY',
                    'Strike': 20000 + (100 * i),
                    'OptionType': 'CE' if 'Long' in signal_type else 'PE',
                    'Expiry': 'CW',
                    'Lots': 1
                })
                
        # Save signals
        signals_df = pd.DataFrame(signals)
        signals_df.to_excel(f"{tv_dir}/comprehensive_tv_signals.xlsx", index=False)
        
        # Create TV configuration with various parameters
        tv_configs = []
        
        # Test different TV parameters
        for intraday_sqoff in ['YES', 'NO']:
            for tv_exit in ['YES', 'NO']:
                for rollover in ['YES', 'NO']:
                    for db_exit in ['YES', 'NO']:
                        config_name = f'TV_{intraday_sqoff}_{tv_exit}_{rollover}_{db_exit}'
                        
                        tv_configs.append({
                            'ConfigName': config_name,
                            'SignalFile': 'comprehensive_tv_signals.xlsx',
                            'IntradaySqOffApplicable': intraday_sqoff,
                            'TvExitApplicable': tv_exit,
                            'DoRollover': rollover,
                            'UseDbExitTiming': db_exit,
                            'ExitPriceSource': 'SPOT',
                            'FirstTradeEntryTime': 91600,
                            'IncreaseEntrySignalTimeBy': 0,
                            'IncreaseExitSignalTimeBy': 0
                        })
                        
        # Create config file
        config_df = pd.DataFrame(tv_configs[:10])  # Limit for practicality
        config_df.to_excel(f"{tv_dir}/tv_config_variations.xlsx", index=False)
        
        print(f"Created {len(signals)} TV signals and {len(tv_configs[:10])} config variations")
        
    def create_pos_tests(self):
        """Create comprehensive POS strategy tests"""
        print("\nCreating POS Strategy Tests...")
        
        pos_dir = self.output_dir / 'pos'
        pos_dir.mkdir(exist_ok=True)
        
        # Test all position types and subtypes
        position_configs = []
        
        position_types = ['WEEKLY', 'MONTHLY', 'CUSTOM']
        strategy_subtypes = ['CALENDAR_SPREAD', 'IRON_FLY', 'IRON_CONDOR', 'BUTTERFLY', 'CUSTOM']
        
        for pos_type in position_types:
            for subtype in strategy_subtypes:
                config_name = f'POS_{pos_type}_{subtype}'
                
                position_configs.append({
                    'StrategyName': config_name,
                    'PositionType': pos_type,
                    'StrategySubtype': subtype,
                    'EntryDTE': 30 if pos_type == 'MONTHLY' else 7,
                    'ExitDTE': 0,
                    'VixRange': 'Med',
                    'PremiumTarget': 1000,
                    'MaxLoss': -2000,
                    'UseBreakeven': 'YES',
                    'BreakevenCalculation': 'THEORETICAL',
                    'BEApproachAction': 'ADJUST',
                    'BEBreachAction': 'CLOSE',
                    'UseIVP': 'YES',
                    'IVPLookback': 252,
                    'IVPThreshold': 50
                })
                
        # Breakeven calculation tests
        be_methods = ['THEORETICAL', 'EMPIRICAL', 'MONTE_CARLO', 'HYBRID']
        be_approach_actions = ['ADJUST', 'HEDGE', 'CLOSE', 'ALERT']
        be_breach_actions = ['CLOSE', 'ADJUST', 'REVERSE', 'HOLD']
        
        for be_method in be_methods:
            for approach_action in be_approach_actions:
                for breach_action in be_breach_actions:
                    config_name = f'BE_{be_method}_{approach_action}_{breach_action}'
                    
                    position_configs.append({
                        'StrategyName': config_name,
                        'PositionType': 'WEEKLY',
                        'StrategySubtype': 'IRON_FLY',
                        'EntryDTE': 7,
                        'ExitDTE': 0,
                        'BreakevenCalculation': be_method,
                        'BEApproachAction': approach_action,
                        'BEBreachAction': breach_action,
                        'BEApproachThreshold': 0.8,
                        'DynamicBEAdjustment': 'YES',
                        'BEOptimizedStrikeSelection': 'YES'
                    })
                    
        # Create general parameters
        general_data = []
        for config in position_configs[:20]:  # Limit for file size
            general_data.append({
                'StrategyName': config['StrategyName'],
                'Index': 'NIFTY',
                'StartTime': 91600,
                'EndTime': 152000,
                **config
            })
            
        # Create leg parameters for IRON_FLY
        leg_data = []
        for config in position_configs[:20]:
            if 'IRON_FLY' in config.get('StrategySubtype', ''):
                # Iron Fly legs
                legs = [
                    {'instrument': 'call', 'transaction': 'sell', 'strike': 'ATM', 'role': 'PRIMARY'},
                    {'instrument': 'call', 'transaction': 'buy', 'strike': 'OTM2', 'role': 'HEDGE'},
                    {'instrument': 'put', 'transaction': 'sell', 'strike': 'ATM', 'role': 'PRIMARY'},
                    {'instrument': 'put', 'transaction': 'buy', 'strike': 'OTM2', 'role': 'HEDGE'}
                ]
                
                for i, leg in enumerate(legs):
                    leg_data.append({
                        'StrategyName': config['StrategyName'],
                        'LegID': f'LEG_{i+1}',
                        'Instrument': leg['instrument'],
                        'Transaction': leg['transaction'],
                        'StrikeMethod': leg['strike'],
                        'StrikeValue': 0,
                        'PositionRole': leg['role'],
                        'Lots': 1
                    })
                    
        # Create portfolio settings
        portfolio_data = [{
            'Enabled': 'YES',
            'PortfolioName': 'POS_COMPREHENSIVE',
            'StartDate': '01_04_2024',
            'EndDate': '30_04_2024'
        }]
        
        strategy_data = [{
            'Enabled': 'YES',
            'PortfolioName': 'POS_COMPREHENSIVE',
            'StrategyType': 'POS',
            'StrategyExcelFilePath': 'comprehensive_pos_tests.xlsx'
        }]
        
        with pd.ExcelWriter(f"{pos_dir}/comprehensive_pos_tests.xlsx", engine='openpyxl') as writer:
            pd.DataFrame(portfolio_data).to_excel(writer, sheet_name='PortfolioSetting', index=False)
            pd.DataFrame(strategy_data).to_excel(writer, sheet_name='StrategySetting', index=False)
            pd.DataFrame(general_data).to_excel(writer, sheet_name='GeneralParameter', index=False)
            pd.DataFrame(leg_data).to_excel(writer, sheet_name='LegParameter', index=False)
            
        print(f"Created {len(general_data)} POS strategy tests")
        
    def create_edge_case_tests(self):
        """Create edge case test scenarios"""
        print("\nCreating Edge Case Tests...")
        
        edge_dir = self.output_dir / 'edge_cases'
        edge_dir.mkdir(exist_ok=True)
        
        # Test cases for edge scenarios
        edge_cases = []
        
        # 1. Expiry day scenarios
        edge_cases.append({
            'test_name': 'EXPIRY_DAY_SWITCH',
            'description': 'Test switching to next expiry on expiry day',
            'on_expiry_trade_next': 'yes',
            'dte': 0
        })
        
        # 2. Time window edge cases
        edge_cases.append({
            'test_name': 'LATE_ENTRY_EARLY_EXIT',
            'description': 'Entry close to exit time',
            'start_time': 150000,
            'end_time': 152000
        })
        
        # 3. Premium difference scenarios
        edge_cases.append({
            'test_name': 'PREMIUM_DIFF_FORCE_ENTRY',
            'description': 'Force entry based on premium difference',
            'check_premium_diff': 'yes',
            'premium_diff_type': 'percentage',
            'premium_diff_value': 10,
            'premium_diff_force_entry': 'yes'
        })
        
        # 4. Hedge scenarios
        edge_cases.append({
            'test_name': 'DYNAMIC_HEDGE_ADJUSTMENT',
            'description': 'Dynamic hedge with premium-based selection',
            'open_hedge': 'Yes',
            'hedge_strike_method': 'PREMIUM',
            'hedge_strike_value': 50
        })
        
        # 5. Multiple square-offs
        edge_cases.append({
            'test_name': 'MULTIPLE_PARTIAL_EXITS',
            'description': 'Multiple partial exits at different times',
            'sqoff1_time': 120000,
            'sqoff1_percent': 30,
            'sqoff2_time': 140000,
            'sqoff2_percent': 40
        })
        
        # Create test files
        self._create_edge_case_file(f"{edge_dir}/edge_case_scenarios.xlsx", edge_cases)
        
        print(f"Created {len(edge_cases)} edge case tests")
        
    def _create_edge_case_file(self, filename, test_cases):
        """Create edge case test file"""
        
        general_data = []
        leg_data = []
        
        for case in test_cases:
            general_data.append({
                'StrategyName': case['test_name'],
                'Underlying': 'SPOT',
                'Index': 'NIFTY',
                'DTE': case.get('dte', 0),
                'StartTime': case.get('start_time', 91600),
                'EndTime': case.get('end_time', 152000),
                'OnExpiryDayTradeNextExpiry': case.get('on_expiry_trade_next', 'no'),
                'CheckPremiumDiffCondition': case.get('check_premium_diff', 'no'),
                'PremiumDiffType': case.get('premium_diff_type', 'point'),
                'PremiumDiffValue': case.get('premium_diff_value', 0),
                'PremiumDiffDoForceEntry': case.get('premium_diff_force_entry', 'no'),
                'SqOff1Time': case.get('sqoff1_time', 0),
                'SqOff1Percent': case.get('sqoff1_percent', 0),
                'SqOff2Time': case.get('sqoff2_time', 0),
                'SqOff2Percent': case.get('sqoff2_percent', 0)
            })
            
            leg_data.append({
                'StrategyName': case['test_name'],
                'LegID': 'LEG_1',
                'Instrument': 'call',
                'Transaction': 'sell',
                'Expiry': 'current',
                'StrikeMethod': 'ATM',
                'StrikeValue': 0,
                'OpenHedge': case.get('open_hedge', 'No'),
                'HedgeStrikeMethod': case.get('hedge_strike_method', 'OTM2'),
                'HedgeStrikeValue': case.get('hedge_strike_value', 0),
                'Lots': 1
            })
            
        portfolio_data = [{
            'Enabled': 'YES',
            'PortfolioName': 'EDGE_CASES',
            'StartDate': '01_04_2024',
            'EndDate': '30_04_2024'
        }]
        
        strategy_data = [{
            'Enabled': 'YES',
            'PortfolioName': 'EDGE_CASES',
            'StrategyType': 'TBS',
            'StrategyExcelFilePath': os.path.basename(filename)
        }]
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            pd.DataFrame(portfolio_data).to_excel(writer, sheet_name='PortfolioSetting', index=False)
            pd.DataFrame(strategy_data).to_excel(writer, sheet_name='StrategySetting', index=False)
            pd.DataFrame(general_data).to_excel(writer, sheet_name='GeneralParameter', index=False)
            pd.DataFrame(leg_data).to_excel(writer, sheet_name='LegParameter', index=False)
            
    def create_test_summary(self):
        """Create a summary of all generated tests"""
        
        summary = {
            'generation_date': datetime.now().isoformat(),
            'test_categories': {},
            'total_test_files': 0,
            'total_test_cases': 0
        }
        
        # Count files in each category
        for category in self.test_categories:
            category_path = self.output_dir / category
            if category_path.exists():
                files = list(category_path.glob('**/*.xlsx'))
                summary['test_categories'][category] = {
                    'file_count': len(files),
                    'files': [str(f.relative_to(self.output_dir)) for f in files]
                }
                summary['total_test_files'] += len(files)
                
        # Save summary
        summary_file = self.output_dir / 'test_generation_summary.json'
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)
            
        print(f"\nTest generation complete!")
        print(f"Total test files created: {summary['total_test_files']}")
        print(f"Summary saved to: {summary_file}")
        
    def run(self):
        """Run all test generators"""
        
        print("="*80)
        print("COMPREHENSIVE TEST GENERATION")
        print("="*80)
        
        # Generate all test categories
        self.create_tbs_strike_selection_tests()
        self.create_risk_management_tests()
        self.create_reentry_tests()
        self.create_expiry_tests()
        self.create_multileg_strategy_tests()
        self.create_oi_tests()
        self.create_tv_tests()
        self.create_pos_tests()
        self.create_edge_case_tests()
        
        # Create summary
        self.create_test_summary()
        
        print("\n" + "="*80)
        print("ALL TESTS GENERATED SUCCESSFULLY!")
        print("="*80)

def main():
    """Main execution"""
    generator = ComprehensiveTestGenerator()
    generator.run()

if __name__ == "__main__":
    main()