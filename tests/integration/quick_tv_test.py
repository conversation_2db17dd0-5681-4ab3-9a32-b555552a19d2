#!/usr/bin/env python3
"""
Quick test to verify TV backtesters work
"""

import os
import sys
import pandas as pd

# Add bt to path
sys.path.insert(0, "/srv/samba/shared/bt")

# Change to bt directory
os.chdir("/srv/samba/shared/bt")

print("Current directory:", os.getcwd())
print("\nChecking TV input files...")

# Check which TV file to use
tv_files = [
    "backtester_stable/BTRUN/input_sheets/input_tv.xlsx",
    "backtester_stable/BTRUN/input_sheets/input_tv_universal.xlsx",
    "backtester_stable/BTRUN/input_sheets/input_tv_fixed.xlsx"
]

for tv_file in tv_files:
    if os.path.exists(tv_file):
        print(f"\nFound: {tv_file}")
        try:
            df = pd.read_excel(tv_file, sheet_name='Setting')
            enabled = df[df['Enabled'].astype(str).str.lower() == 'yes']
            print(f"  Enabled strategies: {len(enabled)}")
            if len(enabled) > 0:
                print(f"  First strategy: {enabled.iloc[0]['Name']}")
                signal_file = enabled.iloc[0]['SignalFilePath']
                print(f"  Signal file: {signal_file}")
                
                # Check if signal file exists
                if pd.notna(signal_file):
                    # Try different paths
                    signal_paths = [
                        signal_file,
                        os.path.join("backtester_stable/BTRUN/input_sheets", signal_file),
                        os.path.join("backtester_stable/BTRUN/input_sheets", os.path.basename(signal_file))
                    ]
                    
                    for sp in signal_paths:
                        if os.path.exists(sp):
                            print(f"  ✅ Signal file found at: {sp}")
                            break
                    else:
                        print(f"  ❌ Signal file not found")
                        
        except Exception as e:
            print(f"  Error reading: {e}")

print("\nNow let's try a simple run...")

# Try to import and run
try:
    from backtester_stable.BTRUN.BT_TV_GPU_aggregated_v4 import main as v4_main
    print("✅ Successfully imported V4")
except Exception as e:
    print(f"❌ Cannot import V4: {e}")

try:
    from backtester_stable.BTRUN.BT_TV_GPU_enhanced import main as enhanced_main
    print("✅ Successfully imported Enhanced")
except Exception as e:
    print(f"❌ Cannot import Enhanced: {e}")