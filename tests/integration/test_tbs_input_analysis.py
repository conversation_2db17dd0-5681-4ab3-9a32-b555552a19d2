#!/usr/bin/env python3
"""
Analyze TBS input files to understand structure
"""

import pandas as pd
import os

def analyze_input_files():
    print("Analyzing TBS Input Files")
    print("="*60)
    
    # Check portfolio file
    portfolio_file = '/srv/samba/shared/input_portfolio.xlsx'
    print(f"\n1. Portfolio File: {portfolio_file}")
    
    # Read StrategySetting sheet
    strategy_df = pd.read_excel(portfolio_file, sheet_name='StrategySetting')
    print(f"\nStrategySetting columns: {list(strategy_df.columns)}")
    print(f"Total rows: {len(strategy_df)}")
    
    # Check first few rows
    print("\nFirst 5 rows:")
    for idx, row in strategy_df.head().iterrows():
        print(f"  Row {idx}:")
        for col in ['Enabled', 'StrategyType', 'StrategyExcelFilePath']:
            if col in strategy_df.columns:
                print(f"    {col}: {row.get(col, 'N/A')}")
    
    # Count by strategy type
    if 'StrategyType' in strategy_df.columns:
        print("\nStrategy type distribution:")
        print(strategy_df['StrategyType'].value_counts())
    
    # Check for TBS strategies
    print("\nTBS Strategies:")
    tbs_rows = strategy_df[strategy_df['StrategyType'].str.upper() == 'TBS'] if 'StrategyType' in strategy_df.columns else pd.DataFrame()
    print(f"Found {len(tbs_rows)} TBS strategies")
    
    for idx, row in tbs_rows.iterrows():
        print(f"\n  TBS Strategy {idx}:")
        print(f"    Enabled: {row.get('Enabled', 'N/A')}")
        print(f"    Excel Path: {row.get('StrategyExcelFilePath', 'N/A')}")
        if 'StrategyName' in row:
            print(f"    Strategy Name: {row.get('StrategyName', 'N/A')}")
    
    # Check TBS multi-leg file
    tbs_file = '/srv/samba/shared/input_tbs_multi_legs.xlsx'
    print(f"\n2. TBS Multi-Leg File: {tbs_file}")
    
    # Read GeneralParameter
    general_df = pd.read_excel(tbs_file, sheet_name='GeneralParameter')
    print(f"\nGeneralParameter columns: {list(general_df.columns)}")
    print(f"Total strategies: {len(general_df)}")
    
    for idx, row in general_df.iterrows():
        print(f"\n  Strategy {idx}:")
        print(f"    Name: {row.get('StrategyName', 'N/A')}")
        print(f"    Enabled: {row.get('Enabled', 'N/A')}")
        print(f"    Index: {row.get('Index', 'N/A')}")
        print(f"    NoOfLegs: {row.get('NoOfLegs', 'N/A')}")

if __name__ == "__main__":
    analyze_input_files()