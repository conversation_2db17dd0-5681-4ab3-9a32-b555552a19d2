#!/usr/bin/env python3
"""
Run column mapping tests from the correct directory level
"""

import os
import sys
import importlib.util
import pandas as pd
from datetime import datetime, date, time
from typing import Dict, Any, List
import unittest

# Add backtester to path
BACKTESTER_PATH = '/srv/samba/shared/bt/backtester_stable/BTRUN'
sys.path.insert(0, BACKTESTER_PATH)

# Change directory to BTRUN to make relative imports work
os.chdir(BACKTESTER_PATH)

try:
    # Now we can import from the BTRUN directory
    from excel_parser.portfolio_parser import parse_portfolio_excel, _transform_date
    from models.portfolio import PortfolioModel
    from models.strategy import StrategyModel
    from models.leg import LegModel
    from models.common import OptionType, TransactionType, StrikeRule, ExpiryRule

    print("Import successful!")
except ImportError as e:
    print(f"Import failed: {e}")
    sys.exit(1)

# Path to sample Excel file
INPUT_SHEETS_DIR = os.path.join(BACKTESTER_PATH, 'input_sheets')
PORTFOLIO_FILE = os.path.join(INPUT_SHEETS_DIR, 'input_portfolio_fixed.xlsx')

# Fallback to any portfolio file if the specific one doesn't exist
if not os.path.exists(PORTFOLIO_FILE):
    for file in os.listdir(INPUT_SHEETS_DIR):
        if 'portfolio' in file.lower() and file.endswith('.xlsx'):
            PORTFOLIO_FILE = os.path.join(INPUT_SHEETS_DIR, file)
            break

print(f"Using portfolio file: {PORTFOLIO_FILE}")

class TestColumnMapping(unittest.TestCase):
    """Test case for column mapping"""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures and load input files"""
        try:
            cls.portfolio_models = parse_portfolio_excel(PORTFOLIO_FILE)
            print(f"Successfully loaded {len(cls.portfolio_models)} portfolios")
        except Exception as e:
            print(f"Error loading portfolio file: {e}")
            cls.portfolio_models = None
    
    def test_portfolio_setting_fields(self):
        """Test that PortfolioSetting columns are correctly mapped to model fields."""
        self.assertIsNotNone(self.portfolio_models, "No portfolios parsed from Excel.")
        
        # Expected fields from docs/column_mapping.md
        expected_fields = [
            'start_date', 'end_date', 'is_tick_bt', 'portfolio_name', 'margin_multiplier', 'slippage_percent'
        ]
        
        for portfolio_name, portfolio in self.portfolio_models.items():
            for field in expected_fields:
                self.assertTrue(hasattr(portfolio, field), f"PortfolioModel missing field: {field}")
                
                # Print value for debugging
                value = getattr(portfolio, field)
                print(f"Portfolio {portfolio_name} {field} = {value}")
    
    def test_strategy_setting_fields(self):
        """Test that StrategySetting columns are correctly mapped to model fields."""
        self.assertIsNotNone(self.portfolio_models, "No portfolios parsed from Excel.")
        
        # Expected fields from docs/column_mapping.md
        expected_fields = [
            'evaluator', 'strategy_excel_path'
        ]
        
        for portfolio_name, portfolio in self.portfolio_models.items():
            for strategy in portfolio.strategies:
                for field in expected_fields:
                    self.assertTrue(hasattr(strategy, field), f"StrategyModel missing field: {field}")
                    
                    # Print value for debugging
                    value = getattr(strategy, field)
                    print(f"Strategy in {portfolio_name} {field} = {value}")
    
    def test_date_transformation(self):
        """Test that dates from Excel are correctly transformed."""
        # Test DD_MM_YYYY format
        dd_mm_yyyy = "01_04_2025"
        expected_date = date(2025, 4, 1)
        transformed_date = _transform_date(dd_mm_yyyy)
        self.assertEqual(transformed_date, expected_date, 
                     f"DD_MM_YYYY '{dd_mm_yyyy}' should transform to {expected_date} but got {transformed_date}")
        
        # Test already date object
        date_obj = date(2025, 4, 1)
        transformed_date_obj = _transform_date(date_obj)
        self.assertEqual(transformed_date_obj, date_obj,
                     f"Date object should be preserved: input={date_obj}, output={transformed_date_obj}")
    
    def test_extra_params_exist(self):
        """Test that extra parameters are collected in the models."""
        self.assertIsNotNone(self.portfolio_models, "No portfolios parsed from Excel.")
        
        # Common fields that should be present in extra_params
        expected_extra_params = [
            'Underlying', 'Index', 'StrikeSelectionTime', 'LastEntryTime'
        ]
        
        for portfolio_name, portfolio in self.portfolio_models.items():
            for strategy in portfolio.strategies:
                self.assertTrue(hasattr(strategy, 'extra_params'), "StrategyModel missing extra_params field")
                
                # Check for common fields in extra_params
                found_fields = set()
                if hasattr(strategy, 'extra_params'):
                    for field in expected_extra_params:
                        if field in strategy.extra_params:
                            found_fields.add(field)
                            
                # Check if fields are found in leg extra_params
                for leg in strategy.legs:
                    if hasattr(leg, 'extra_params'):
                        for field in expected_extra_params:
                            if field in leg.extra_params:
                                found_fields.add(field)
                                
                # Assert at least some of the expected fields are found
                self.assertTrue(len(found_fields) > 0, f"No expected fields found in any extra_params")
                print(f"Found fields in extra_params: {found_fields}")

if __name__ == "__main__":
    unittest.main() 