#!/usr/bin/env python3
"""
Create TBS Test Excel Files for Comprehensive Testing
====================================================

This script creates the exact test files needed for TBS comparison testing
between archive and GPU systems.

Author: Senior Test Engineer
Date: June 9, 2025
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
from pathlib import Path

class TBSTestFileGenerator:
    """Generates TBS test Excel files with various scenarios"""
    
    def __init__(self, output_dir: str = "/srv/samba/shared/test_results/tbs/inputs"):
        """Initialize the generator"""
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def create_portfolio_file(self, scenario: str = "basic") -> str:
        """Create portfolio configuration file"""
        portfolio_data = {
            "basic": {
                "Portfolio Name": ["TBS_Test_Portfolio"],
                "Capital": [1000000],
                "Start Date": ["2024-04-01"],
                "End Date": ["2024-04-05"],
                "Strategy Type": ["TBS"],
                "Max Open Positions": [10]
            },
            "multi_leg": {
                "Portfolio Name": ["TBS_Multi_Leg_Test"],
                "Capital": [2000000],
                "Start Date": ["2024-04-01"],
                "End Date": ["2024-04-05"],
                "Strategy Type": ["TBS"],
                "Max Open Positions": [20]
            }
        }
        
        df = pd.DataFrame(portfolio_data.get(scenario, portfolio_data["basic"]))
        filename = f"test_portfolio_{scenario}.xlsx"
        filepath = self.output_dir / filename
        
        with pd.ExcelWriter(filepath, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='PortfolioParameter', index=False)
            
        return str(filepath)
    
    def create_tbs_strategy_file(self, scenario: str = "basic") -> str:
        """Create TBS strategy configuration file"""
        
        # GeneralParameter sheet
        general_params = {
            "basic": pd.DataFrame({
                "Parameter": ["StartTime", "EndTime", "LastEntryTime", "ExitTime", 
                            "SquareOff", "TrailingStopLoss", "StopLossType", "TargetType"],
                "Value": ["09:20:00", "15:30:00", "14:30:00", "15:15:00",
                         "TRUE", "FALSE", "Percentage", "Percentage"]
            }),
            "multi_leg": pd.DataFrame({
                "Parameter": ["StartTime", "EndTime", "LastEntryTime", "ExitTime",
                            "SquareOff", "TrailingStopLoss", "StopLossType", "TargetType",
                            "ReEntry", "ReEntryType"],
                "Value": ["09:20:00", "15:30:00", "14:00:00", "15:15:00",
                         "TRUE", "TRUE", "Point", "Point", "TRUE", "Cost"]
            })
        }
        
        # LegParameter sheet
        leg_params = {
            "basic": pd.DataFrame({
                "LegID": [1, 2],
                "StrategyName": ["ATM_CE_Long", "ATM_PE_Long"],
                "OptionType": ["CE", "PE"],
                "StrikeMethod": ["ATM", "ATM"],
                "StrikeValue": [0, 0],
                "Position": ["BUY", "BUY"],
                "Quantity": [2, 2],
                "StopLoss": [30, 30],
                "Target": [50, 50],
                "Active": ["TRUE", "TRUE"]
            }),
            "multi_leg": pd.DataFrame({
                "LegID": [1, 2, 3, 4],
                "StrategyName": ["ATM_CE_Long", "ATM_PE_Long", "OTM_CE_Short", "OTM_PE_Short"],
                "OptionType": ["CE", "PE", "CE", "PE"],
                "StrikeMethod": ["ATM", "ATM", "OTM", "OTM"],
                "StrikeValue": [0, 0, 2, 2],
                "Position": ["BUY", "BUY", "SELL", "SELL"],
                "Quantity": [2, 2, 1, 1],
                "StopLoss": [2000, 2000, 1000, 1000],
                "Target": [3000, 3000, 1500, 1500],
                "Active": ["TRUE", "TRUE", "TRUE", "TRUE"]
            })
        }
        
        filename = f"test_tbs_strategy_{scenario}.xlsx"
        filepath = self.output_dir / filename
        
        with pd.ExcelWriter(filepath, engine='xlsxwriter') as writer:
            general_params[scenario].to_excel(writer, sheet_name='GeneralParameter', index=False)
            leg_params[scenario].to_excel(writer, sheet_name='LegParameter', index=False)
            
        return str(filepath)
    
    def create_comprehensive_test_suite(self) -> dict:
        """Create a comprehensive suite of test files"""
        test_files = {}
        
        # Test scenarios
        scenarios = {
            "basic_straddle": {
                "description": "Basic ATM straddle",
                "portfolio": "basic",
                "strategy": "basic"
            },
            "multi_leg_condor": {
                "description": "4-leg iron condor",
                "portfolio": "multi_leg", 
                "strategy": "multi_leg"
            },
            "edge_case_reentry": {
                "description": "Test re-entry logic",
                "portfolio": "multi_leg",
                "strategy": "multi_leg"
            }
        }
        
        for test_name, config in scenarios.items():
            portfolio_file = self.create_portfolio_file(config["portfolio"])
            strategy_file = self.create_tbs_strategy_file(config["strategy"])
            
            test_files[test_name] = {
                "description": config["description"],
                "portfolio_file": portfolio_file,
                "strategy_file": strategy_file
            }
            
        # Save test configuration
        config_file = self.output_dir / "test_configuration.json"
        with open(config_file, 'w') as f:
            import json
            json.dump(test_files, f, indent=2)
            
        return test_files
    
    def create_sample_market_data(self) -> str:
        """Create sample market data for testing"""
        # This would typically come from the database
        # Creating a sample CSV for testing purposes
        
        dates = pd.date_range('2024-04-01', '2024-04-05', freq='D')
        times = pd.date_range('09:15:00', '15:30:00', freq='5min').time
        
        data = []
        for date in dates:
            spot_base = 22000 + np.random.randint(-200, 200)
            for time in times:
                spot = spot_base + np.random.normal(0, 20)
                data.append({
                    'date': date.date(),
                    'time': time,
                    'spot': round(spot, 2),
                    'fut_close': round(spot + np.random.normal(10, 5), 2)
                })
        
        df = pd.DataFrame(data)
        filepath = self.output_dir / "sample_market_data.csv"
        df.to_csv(filepath, index=False)
        
        return str(filepath)


def main():
    """Generate all test files"""
    print("Generating TBS Test Files")
    print("=" * 50)
    
    generator = TBSTestFileGenerator()
    
    # Create test files
    test_files = generator.create_comprehensive_test_suite()
    
    print("\nTest files created:")
    for test_name, files in test_files.items():
        print(f"\n{test_name}:")
        print(f"  Description: {files['description']}")
        print(f"  Portfolio: {files['portfolio_file']}")
        print(f"  Strategy: {files['strategy_file']}")
    
    # Create sample market data
    market_data = generator.create_sample_market_data()
    print(f"\nMarket data: {market_data}")
    
    print("\nAll test files generated successfully!")
    print(f"Location: {generator.output_dir}")


if __name__ == "__main__":
    main()