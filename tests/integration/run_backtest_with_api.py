#!/usr/bin/env python3
"""
Run backtests using the correct API endpoint
"""
import os
import sys
import json
import requests
import pandas as pd
from datetime import datetime
import shutil
import time

# Configuration
TEST_DATE = "2024-04-01"
INPUT_DIR = "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets"
OUTPUT_DIR = "/srv/samba/shared/api_test_results"
os.makedirs(OUTPUT_DIR, exist_ok=True)

print("="*80)
print("BACKTEST API TEST - Using /api/v1/backtest/create endpoint")
print(f"Test Date: {TEST_DATE}")
print("="*80)

# Strategy configurations
STRATEGIES = {
    "TBS": {
        "portfolio": os.path.join(INPUT_DIR, "tbs/input_portfolio.xlsx"),
        "strategy": os.path.join(INPUT_DIR, "tbs/input_tbs_portfolio.xlsx")
    },
    "TV": {
        "portfolio": os.path.join(INPUT_DIR, "tv/input_portfolio_long.xlsx"),
        "strategy": os.path.join(INPUT_DIR, "tv/input_tv.xlsx")
    },
    "ORB": {
        "portfolio": os.path.join(INPUT_DIR, "orb/input_portfolio.xlsx"),
        "strategy": os.path.join(INPUT_DIR, "orb/input_orb.xlsx")
    },
    "OI": {
        "portfolio": os.path.join(INPUT_DIR, "oi/bt_setting.xlsx"),
        "strategy": os.path.join(INPUT_DIR, "oi/input_maxoi.xlsx")
    }
}

results = {}

def analyze_output(file_path, label):
    """Analyze output file structure"""
    print(f"\n  Analyzing {label}:")
    try:
        xl = pd.ExcelFile(file_path)
        sheets = xl.sheet_names
        print(f"  Sheets ({len(sheets)}): {', '.join(sheets[:5])}" + (" ..." if len(sheets) > 5 else ""))
        
        # Check for key sheets
        required_sheets = ['PortfolioParameter', 'GeneralParameter', 'LegParameter', 'Metrics']
        missing = [s for s in required_sheets if s not in sheets]
        if missing:
            print(f"  ⚠️  Missing sheets: {missing}")
        else:
            print(f"  ✓ All required sheets present")
            
        # Check metrics
        if 'Metrics' in sheets:
            metrics_df = pd.read_excel(file_path, sheet_name='Metrics')
            print(f"  Metrics rows: {len(metrics_df)}")
            
            # Extract key metrics
            key_metrics = {}
            for metric in ['Gross P&L', 'Net P&L', 'Win Rate %']:
                rows = metrics_df[metrics_df['Particulars'] == metric]
                if not rows.empty:
                    key_metrics[metric] = rows['Combined'].values[0]
            
            if key_metrics:
                print(f"  Key metrics: {key_metrics}")
                
        return sheets
    except Exception as e:
        print(f"  Error: {e}")
        return []

# Test each strategy
for strategy_name, files in STRATEGIES.items():
    print(f"\n{'='*60}")
    print(f"Testing {strategy_name} Strategy")
    print(f"{'='*60}")
    
    results[strategy_name] = {'success': False}
    
    # Check files exist
    if not os.path.exists(files['portfolio']):
        print(f"✗ Portfolio file not found: {files['portfolio']}")
        continue
    if not os.path.exists(files['strategy']):
        print(f"✗ Strategy file not found: {files['strategy']}")
        continue
    
    print("✓ Input files found")
    
    # Make API request
    url = "http://localhost:8000/api/v1/backtest/create"
    
    try:
        with open(files['portfolio'], 'rb') as pf, open(files['strategy'], 'rb') as sf:
            files_data = {
                'portfolio_file': ('portfolio.xlsx', pf, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'),
                'strategy_file': ('strategy.xlsx', sf, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            }
            
            data = {
                'strategy_type': strategy_name.lower(),
                'index': 'NIFTY',
                'gpu_config': 'auto'
            }
            
            print(f"\nSending request to {url}")
            start_time = datetime.now()
            response = requests.post(url, files=files_data, data=data)
            
            print(f"Response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"Response: {json.dumps(result, indent=2)}")
                
                backtest_id = result.get('backtest_id')
                if backtest_id:
                    print(f"Backtest ID: {backtest_id}")
                    
                    # Wait for completion
                    print("\nWaiting for backtest to complete...")
                    max_wait = 60  # seconds
                    check_interval = 2
                    elapsed = 0
                    
                    while elapsed < max_wait:
                        time.sleep(check_interval)
                        elapsed += check_interval
                        
                        # Check progress
                        progress_url = f"http://localhost:8000/api/v1/backtest/{backtest_id}/progress"
                        prog_resp = requests.get(progress_url)
                        
                        if prog_resp.status_code == 200:
                            progress = prog_resp.json()
                            status = progress.get('status', 'unknown')
                            print(f"  Status: {status} ({elapsed}s)")
                            
                            if status == 'completed':
                                # Download result
                                download_url = f"http://localhost:8000/api/v1/backtest/{backtest_id}/download"
                                dl_resp = requests.get(download_url)
                                
                                if dl_resp.status_code == 200:
                                    output_file = os.path.join(OUTPUT_DIR, f"{strategy_name}_{backtest_id}.xlsx")
                                    with open(output_file, 'wb') as f:
                                        f.write(dl_resp.content)
                                    print(f"✓ Output saved: {output_file}")
                                    
                                    # Analyze output
                                    sheets = analyze_output(output_file, f"{strategy_name} output")
                                    
                                    results[strategy_name] = {
                                        'success': True,
                                        'backtest_id': backtest_id,
                                        'output_file': output_file,
                                        'sheets': sheets,
                                        'duration': elapsed
                                    }
                                break
                            elif status == 'failed':
                                error = progress.get('error', 'Unknown error')
                                print(f"✗ Backtest failed: {error}")
                                break
                    else:
                        print(f"✗ Timeout waiting for backtest completion")
            else:
                print(f"✗ API error: {response.text}")
                
    except Exception as e:
        print(f"✗ Error: {e}")

# Summary
print(f"\n{'='*80}")
print("SUMMARY")
print(f"{'='*80}")

# Save results
summary_file = os.path.join(OUTPUT_DIR, f"api_test_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
with open(summary_file, 'w') as f:
    json.dump(results, f, indent=2, default=str)

print(f"\nResults saved to: {OUTPUT_DIR}")
print(f"Summary: {summary_file}")

# Print summary table
print("\nTest Results:")
print(f"{'Strategy':<10} {'Status':<10} {'Sheets':<10} {'Duration':<10}")
print("-"*40)
for strategy in STRATEGIES:
    if results[strategy]['success']:
        status = "✓ Success"
        sheets = len(results[strategy].get('sheets', []))
        duration = f"{results[strategy].get('duration', 0)}s"
    else:
        status = "✗ Failed"
        sheets = "N/A"
        duration = "N/A"
    print(f"{strategy:<10} {status:<10} {sheets:<10} {duration:<10}")

# Compare with archive outputs
print("\n" + "="*60)
print("COMPARING WITH ARCHIVE OUTPUTS")
print("="*60)

archive_dir = "/srv/samba/shared/comparison_results"
for strategy in STRATEGIES:
    if results[strategy]['success']:
        print(f"\n{strategy} Comparison:")
        
        # Get archive file
        archive_file = os.path.join(archive_dir, f"archive_{strategy}_2024-04-01.xlsx")
        if os.path.exists(archive_file):
            print(f"  Archive file: {os.path.basename(archive_file)}")
            archive_sheets = analyze_output(archive_file, "Archive")
            
            # Compare sheets
            new_sheets = set(results[strategy]['sheets'])
            archive_sheets_set = set(archive_sheets)
            
            print(f"\n  Sheet comparison:")
            print(f"    Common: {len(new_sheets & archive_sheets_set)}")
            print(f"    Only in new: {new_sheets - archive_sheets_set}")
            print(f"    Only in archive: {archive_sheets_set - new_sheets}")
        else:
            print(f"  ✗ Archive file not found")