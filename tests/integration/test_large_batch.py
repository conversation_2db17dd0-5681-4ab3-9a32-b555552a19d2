#!/usr/bin/env python3
import os
import sys
import csv
import subprocess
import time
from datetime import datetime

# Configuration
CSV_FILE = "/srv/samba/shared/market_data/nifty/oc_with_futures/IV_2023_jan_nifty_futures.csv"
BATCH_SIZE = 100000  # Testing with 100k batch size

def run_sql_command(sql):
    """Run SQL command via heavysql"""
    cmd = ["/opt/heavyai/bin/heavysql", "-s", "127.0.0.1", "--port", "6274", 
           "-u", "admin", "-p", "HyperInteractive", "-d", "heavyai"]
    
    process = subprocess.Popen(cmd, stdin=subprocess.PIPE, 
                              stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                              text=True)
    
    stdout, stderr = process.communicate(input=sql)
    
    if stderr and "Error" in stderr:
        print(f"SQL Error: {stderr}")
        return False
    
    return True

def ensure_table_exists():
    """Ensure the target table exists with correct schema"""
    create_table_sql = """
    DROP TABLE IF EXISTS nifty_option_chain_test;
    CREATE TABLE nifty_option_chain_test (
        trade_date       DATE,
        trade_time       TIME,
        expiry_date      DATE,
        index_name       TEXT,
        spot             DOUBLE,
        atm_strike       DOUBLE,
        strike           DOUBLE,
        dte              INT,
        expiry_bucket    TEXT,
        zone_id          SMALLINT,
        zone_name        TEXT,
        call_strike_type TEXT,
        put_strike_type  TEXT,
        ce_symbol        TEXT,
        ce_open          DOUBLE,
        ce_high          DOUBLE,
        ce_low           DOUBLE,
        ce_close         DOUBLE,
        ce_volume        BIGINT,
        ce_oi            BIGINT,
        ce_coi           BIGINT,
        ce_iv            DOUBLE,
        ce_delta         DOUBLE,
        ce_gamma         DOUBLE,
        ce_theta         DOUBLE,
        ce_vega          DOUBLE,
        ce_rho           DOUBLE,
        pe_symbol        TEXT,
        pe_open          DOUBLE,
        pe_high          DOUBLE,
        pe_low           DOUBLE,
        pe_close         DOUBLE,
        pe_volume        BIGINT,
        pe_oi            BIGINT,
        pe_coi           BIGINT,
        pe_iv            DOUBLE,
        pe_delta         DOUBLE,
        pe_gamma         DOUBLE,
        pe_theta         DOUBLE,
        pe_vega          DOUBLE,
        pe_rho           DOUBLE,
        future_open      DOUBLE,
        future_high      DOUBLE,
        future_low       DOUBLE,
        future_close     DOUBLE,
        future_volume    BIGINT,
        future_oi        BIGINT,
        future_coi       BIGINT
    );
    """
    
    if run_sql_command(create_table_sql):
        print("Test table created successfully")
        return True
    else:
        print("Failed to create test table")
        return False

def count_lines(file_path):
    """Count lines in a file"""
    with open(file_path, 'r') as f:
        return sum(1 for _ in f)

def process_batch(file_path, start_line, batch_size):
    """Process a specific batch from a file"""
    print(f"Processing batch from line {start_line} with size {batch_size}...")
    
    insert_statements = []
    lines_read = 0
    
    with open(file_path, 'r') as f:
        # Skip to the starting line
        for _ in range(start_line):
            next(f)
        
        # Read batch
        reader = csv.reader(f)
        for row in reader:
            if lines_read >= batch_size:
                break
                
            # Format values
            values = []
            for i, val in enumerate(row):
                # Handle different data types
                if i in [0, 2]:  # DATE fields (trade_date, expiry_date)
                    values.append(f"'{val}'")
                elif i == 1:  # TIME field (trade_time)
                    values.append(f"'{val}'")
                elif i in [3, 10, 11, 12, 13, 27]:  # TEXT fields
                    values.append(f"'{val}'")
                elif i == 8:  # expiry_bucket TEXT field
                    values.append(f"'{val}'")
                elif val == '':  # Handle empty values
                    values.append("NULL")
                else:
                    values.append(val)
            
            # Create INSERT statement
            insert_sql = f"INSERT INTO nifty_option_chain_test VALUES ({', '.join(values)});"
            insert_statements.append(insert_sql)
            lines_read += 1
    
    # Execute batch
    print(f"Inserting {lines_read} rows...")
    start_time = time.time()
    success = run_sql_command("\n".join(insert_statements))
    end_time = time.time()
    
    if success:
        print(f"Batch inserted successfully in {end_time - start_time:.2f} seconds")
    else:
        print("Batch insertion failed")
    
    return success, lines_read

def main():
    print(f"Testing insertion with batch size of {BATCH_SIZE}")
    
    # Create table
    if not ensure_table_exists():
        sys.exit(1)
    
    # Count total lines
    total_lines = count_lines(CSV_FILE)
    print(f"Total lines in file: {total_lines}")
    header_offset = 1  # Skip header line
    
    # Process first batch
    success, rows_inserted = process_batch(CSV_FILE, header_offset, BATCH_SIZE)
    
    if success:
        print(f"Successfully inserted {rows_inserted} rows with batch size {BATCH_SIZE}")
        print("Large batch size is feasible!")
    else:
        print("Failed with batch size 100k, try with 50k instead")
    
    # Cleanup
    print("Dropping test table...")
    run_sql_command("DROP TABLE IF EXISTS nifty_option_chain_test;")

if __name__ == "__main__":
    main() 