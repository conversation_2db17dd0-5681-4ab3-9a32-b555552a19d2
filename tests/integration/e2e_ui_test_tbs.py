#!/usr/bin/env python3
"""
Comprehensive E2E UI Test for TBS System
Tests the complete workflow: Upload → Backtest → Validate → Analyze
"""

import asyncio
import json
import logging
import os
import time
from datetime import datetime
from pathlib import Path

# Test configuration
TEST_CONFIG = {
    "base_url": "http://173.208.247.17:8000",
    "test_files": {
        "portfolio": "/srv/samba/shared/test_results/comprehensive_sl_tgt_test/ATM_TIGHT_SL_portfolio.xlsx",
        "strategy": "/srv/samba/shared/test_results/comprehensive_sl_tgt_test/ATM_TIGHT_SL_strategy.xlsx"
    },
    "expected_columns": [
        "Entry at", "Exit at", "Exit at.1", "CE/PE", "Strike", "Trade",
        "Entry Time", "Expiry", "Premium", "QTY", "PNL per Lot",
        "Total PNL", "Exit Reason", "Days", "TradingSymbol"
    ],
    "test_scenarios": [
        {
            "name": "ATM_TIGHT_SL",
            "portfolio": "ATM_TIGHT_SL_portfolio.xlsx",
            "strategy": "ATM_TIGHT_SL_strategy.xlsx",
            "expected_exit_reasons": ["Stop Loss Hit", "Exit Time Hit", "Target Hit"]
        },
        {
            "name": "OTM_TIGHT_TGT",
            "portfolio": "OTM_TIGHT_TGT_portfolio.xlsx",
            "strategy": "OTM_TIGHT_TGT_strategy.xlsx",
            "expected_exit_reasons": ["Target Hit", "Exit Time Hit"]
        },
        {
            "name": "ITM_POINTS_SL",
            "portfolio": "ITM_POINTS_SL_portfolio.xlsx",
            "strategy": "ITM_POINTS_SL_strategy.xlsx",
            "expected_exit_reasons": ["Points SL Hit", "Exit Time Hit"]
        }
    ]
}

# Test results
TEST_RESULTS = {
    "start_time": datetime.now().isoformat(),
    "tests": [],
    "summary": {
        "total": 0,
        "passed": 0,
        "failed": 0,
        "errors": 0
    }
}

def log_test_step(step, status="INFO", details=None):
    """Log test step progress"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    message = f"[{timestamp}] {status}: {step}"
    if details:
        message += f" - {details}"
    print(message)
    
    # Also save to results
    TEST_RESULTS["tests"].append({
        "timestamp": timestamp,
        "step": step,
        "status": status,
        "details": details
    })

def validate_golden_format(file_path):
    """Validate if output matches golden format"""
    try:
        import pandas as pd
        
        # Check if file exists
        if not os.path.exists(file_path):
            return False, "Output file not found"
        
        # Load Excel file
        excel_file = pd.ExcelFile(file_path)
        
        # Check required sheets
        required_sheets = [
            "PortfolioParameter", "GeneralParameter", "LegParameter",
            "Metrics", "Max Profit and Loss", "PORTFOLIO Trans",
            "PORTFOLIO Results"
        ]
        
        missing_sheets = [s for s in required_sheets if s not in excel_file.sheet_names]
        if missing_sheets:
            return False, f"Missing sheets: {missing_sheets}"
        
        # Check PORTFOLIO Trans columns
        portfolio_trans = pd.read_excel(excel_file, sheet_name="PORTFOLIO Trans")
        missing_columns = [col for col in TEST_CONFIG["expected_columns"] if col not in portfolio_trans.columns]
        
        if missing_columns:
            return False, f"Missing columns in PORTFOLIO Trans: {missing_columns}"
        
        # Check for actual trades
        if len(portfolio_trans) == 0:
            return False, "No trades found in PORTFOLIO Trans"
        
        # Check exit reasons
        exit_reasons = portfolio_trans["Exit Reason"].unique()
        log_test_step("Exit reasons found", "INFO", str(exit_reasons))
        
        return True, f"Valid golden format with {len(portfolio_trans)} trades"
        
    except Exception as e:
        return False, f"Error validating golden format: {str(e)}"

def analyze_backtest_results(output_path):
    """Analyze backtest results for issues"""
    try:
        import pandas as pd
        
        excel_file = pd.ExcelFile(output_path)
        portfolio_trans = pd.read_excel(excel_file, sheet_name="PORTFOLIO Trans")
        
        analysis = {
            "total_trades": len(portfolio_trans),
            "exit_reasons": portfolio_trans["Exit Reason"].value_counts().to_dict(),
            "total_pnl": portfolio_trans["Total PNL"].sum(),
            "avg_pnl_per_trade": portfolio_trans["Total PNL"].mean()
        }
        
        # Check for SL/TGT hits
        sl_hits = len(portfolio_trans[portfolio_trans["Exit Reason"].str.contains("Stop Loss|SL Hit", na=False)])
        tgt_hits = len(portfolio_trans[portfolio_trans["Exit Reason"].str.contains("Target|TGT Hit", na=False)])
        
        analysis["sl_hits"] = sl_hits
        analysis["tgt_hits"] = tgt_hits
        analysis["sl_tgt_ratio"] = f"{sl_hits}/{tgt_hits}" if tgt_hits > 0 else f"{sl_hits}/0"
        
        return True, analysis
        
    except Exception as e:
        return False, {"error": str(e)}

def create_test_report():
    """Create comprehensive test report"""
    TEST_RESULTS["end_time"] = datetime.now().isoformat()
    TEST_RESULTS["summary"]["total"] = len(TEST_RESULTS["tests"])
    
    # Count statuses
    for test in TEST_RESULTS["tests"]:
        if test["status"] == "PASS":
            TEST_RESULTS["summary"]["passed"] += 1
        elif test["status"] == "FAIL":
            TEST_RESULTS["summary"]["failed"] += 1
        elif test["status"] == "ERROR":
            TEST_RESULTS["summary"]["errors"] += 1
    
    # Calculate success rate
    if TEST_RESULTS["summary"]["total"] > 0:
        TEST_RESULTS["summary"]["success_rate"] = (
            TEST_RESULTS["summary"]["passed"] / TEST_RESULTS["summary"]["total"] * 100
        )
    else:
        TEST_RESULTS["summary"]["success_rate"] = 0
    
    # Save report
    report_path = f"/srv/samba/shared/test_results/e2e_ui_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_path, 'w') as f:
        json.dump(TEST_RESULTS, f, indent=2)
    
    log_test_step(f"Test report saved", "INFO", report_path)
    
    # Print summary
    print("\n" + "="*50)
    print("E2E UI TEST SUMMARY")
    print("="*50)
    print(f"Total Tests: {TEST_RESULTS['summary']['total']}")
    print(f"Passed: {TEST_RESULTS['summary']['passed']}")
    print(f"Failed: {TEST_RESULTS['summary']['failed']}")
    print(f"Errors: {TEST_RESULTS['summary']['errors']}")
    print(f"Success Rate: {TEST_RESULTS['summary']['success_rate']:.1f}%")
    print("="*50)
    
    return report_path

def main():
    """Main test execution"""
    log_test_step("Starting E2E UI Test for TBS System")
    
    # Test scenarios
    scenarios = [
        {
            "name": "File Upload Validation",
            "description": "Verify file upload functionality"
        },
        {
            "name": "Backtest Execution",
            "description": "Run backtest through UI"
        },
        {
            "name": "Golden Format Validation",
            "description": "Verify output matches golden format"
        },
        {
            "name": "SL/TGT Logic Validation",
            "description": "Verify stop loss and target logic"
        },
        {
            "name": "Multi-Strike Testing",
            "description": "Test different strike selection methods"
        }
    ]
    
    # Log test plan
    log_test_step("Test Plan", "INFO", f"{len(scenarios)} test scenarios")
    for scenario in scenarios:
        log_test_step(f"  - {scenario['name']}", "INFO", scenario['description'])
    
    # Placeholder for actual UI tests
    # These would be implemented using Playwright MCP
    log_test_step("UI Testing would be performed via Playwright MCP", "INFO")
    
    # Create test report
    report_path = create_test_report()
    
    return report_path

if __name__ == "__main__":
    main()