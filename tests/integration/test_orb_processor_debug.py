#!/usr/bin/env python3
"""
Test ORB processor with debug output
"""

import sys
from datetime import date, datetime
from heavydb import connect

# Add path
sys.path.append('/srv/samba/shared/bt/backtester_stable/BTRUN/backtester_v2')

from strategies.orb.parser import ORBParser
from strategies.orb.models import ORBSettingModel, ORBLegModel
from strategies.orb.processor import ORBProcessor

def test_orb_processor_debug():
    """Test ORB processor with debug output"""
    
    # Connect to database
    print("Connecting to HeavyDB...")
    try:
        conn = connect(
            host='localhost',
            port=6274,
            user='admin',
            password='HyperInteractive',
            dbname='heavyai'
        )
        print("Connected successfully!")
    except Exception as e:
        print(f"Failed to connect: {e}")
        return
    
    # Parse ORB input
    parser = ORBParser()
    input_file = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/orb/input_orb.xlsx'
    
    result = parser.parse_orb_excel(input_file)
    strategies = result['strategies']
    
    # Find a strategy with legs
    strategy_dict = None
    for s in strategies:
        if len(s.get('legs', [])) > 0:
            strategy_dict = s
            break
    
    if not strategy_dict:
        print("No strategy with legs found")
        return
    
    print(f"\nUsing strategy: {strategy_dict['strategy_name']}")
    
    # Convert to model
    leg_models = []
    for leg_dict in strategy_dict.get('legs', []):
        leg_model = ORBLegModel(**leg_dict)
        leg_models.append(leg_model)
    
    strategy_dict['legs'] = leg_models
    
    # Create strategy model
    strategy = ORBSettingModel(**strategy_dict)
    
    # Test processor components individually
    print("\n--- Testing Components ---")
    processor = ORBProcessor(conn)
    
    test_date = date(2024, 4, 1)
    
    # 1. Test range calculation
    print(f"\n1. Calculating opening range for {test_date}")
    orb_range = processor._calculate_opening_range(strategy, test_date)
    if orb_range:
        print(f"   Range: {orb_range['range_high']:.2f} - {orb_range['range_low']:.2f}")
    else:
        print("   No range found")
        return
    
    # 2. Test breakout detection
    print(f"\n2. Detecting breakout")
    breakout = processor._detect_breakout(strategy, test_date, orb_range)
    if breakout:
        print(f"   Breakout: {breakout['breakout_type']} at {breakout['breakout_time']}")
    else:
        print("   No breakout found")
        return
    
    # 3. Test signal generation
    print(f"\n3. Generating entry signals")
    signals = processor._generate_entry_signals(strategy, breakout, test_date)
    if signals:
        print(f"   Generated {len(signals)} signals")
        for signal in signals:
            print(f"   - Signal: {signal.signal_direction} with {len(signal.legs_to_execute)} legs")
            for leg in signal.legs_to_execute:
                print(f"     * {leg.leg_id}: {leg.instrument} {leg.transaction} @ {leg.strike_method}")
    else:
        print("   No signals generated")
        return
    
    # 4. Test query building
    print(f"\n4. Building SQL query")
    from strategies.orb.query_builder import ORBQueryBuilder
    query_builder = ORBQueryBuilder()
    
    # Prepare signal data
    signal = signals[0]
    signal_data = []
    for leg in signal.legs_to_execute:
        signal_data.append({
            'leg_id': leg.leg_id,
            'instrument': leg.instrument,
            'transaction': leg.transaction,
            'expiry': leg.expiry,
            'strike_method': leg.strike_method,
            'strike_value': leg.strike_value,
            'lots': leg.lots,
            'signal_time': datetime.combine(signal.entrydate, signal.entrytime)
        })
    
    try:
        query = query_builder.build_orb_query(
            strategy=strategy.__dict__,
            signals=signal_data,
            trade_date=test_date
        )
        print(f"   Query length: {len(query)} chars")
        print("\n--- Query Preview ---")
        print(query[:500] + "..." if len(query) > 500 else query)
    except Exception as e:
        print(f"   Error building query: {e}")
        import traceback
        traceback.print_exc()
    
    # Close connection
    conn.close()
    print("\nTest completed!")

if __name__ == "__main__":
    test_orb_processor_debug()