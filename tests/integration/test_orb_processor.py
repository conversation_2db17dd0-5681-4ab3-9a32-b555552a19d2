#!/usr/bin/env python3
"""
Test ORB processor with full execution
"""

import sys
from datetime import date
from heavydb import connect

# Add path
sys.path.append('/srv/samba/shared/bt/backtester_stable/BTRUN/backtester_v2')

from strategies.orb.parser import ORBParser
from strategies.orb.models import ORBSettingModel, ORBLegModel
from strategies.orb.processor import ORBProcessor

def test_orb_processor():
    """Test ORB processor with full execution"""
    
    # Connect to database
    print("Connecting to HeavyDB...")
    try:
        conn = connect(
            host='localhost',
            port=6274,
            user='admin',
            password='HyperInteractive',
            dbname='heavyai'
        )
        print("Connected successfully!")
    except Exception as e:
        print(f"Failed to connect: {e}")
        return
    
    # Parse ORB input
    parser = ORBParser()
    input_file = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/orb/input_orb.xlsx'
    
    result = parser.parse_orb_excel(input_file)
    strategies = result['strategies']
    
    # Find a strategy with legs
    strategy_dict = None
    for s in strategies:
        if len(s.get('legs', [])) > 0:
            strategy_dict = s
            break
    
    if not strategy_dict:
        print("No strategy with legs found")
        return
    
    print(f"\nUsing strategy: {strategy_dict['strategy_name']}")
    
    # Convert to model
    leg_models = []
    for leg_dict in strategy_dict.get('legs', []):
        leg_model = ORBLegModel(**leg_dict)
        leg_models.append(leg_model)
    
    strategy_dict['legs'] = leg_models
    
    # Create strategy model
    strategy = ORBSettingModel(**strategy_dict)
    
    # Test processor
    print("\n--- Testing ORB Processor ---")
    processor = ORBProcessor(conn)
    
    test_date = date(2024, 4, 1)
    print(f"Processing strategy for {test_date}")
    
    try:
        result = processor.process_orb_strategy(
            settings=strategy,
            trade_date=test_date
        )
        
        print(f"\nResult status: {result['status']}")
        print(f"Trade count: {result['trade_count']}")
        print(f"Total P&L: {result['total_pnl']}")
        
        if result['trades']:
            print("\nTrades executed:")
            for trade in result['trades']:
                print(f"  {trade['leg_id']}: {trade['instrument']} {trade['transaction']} @ Strike {trade['strike']}")
                print(f"    Entry: {trade['entry_time']} @ {trade['entry_price']}")
                print(f"    Exit: {trade['exit_time']} @ {trade['exit_price']}")
                print(f"    P&L: {trade['pnl']}")
        else:
            print("\nNo trades executed")
            
    except Exception as e:
        print(f"Error processing strategy: {e}")
        import traceback
        traceback.print_exc()
    
    # Close connection
    conn.close()
    print("\nTest completed!")

if __name__ == "__main__":
    test_orb_processor()