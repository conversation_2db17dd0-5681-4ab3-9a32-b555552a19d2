#!/usr/bin/env python3
"""
Properly test and compare TV backtester versions
"""

import os
import sys
import subprocess
import pandas as pd
from datetime import datetime
import time
import shutil

# Set up paths
project_root = "/srv/samba/shared"
bt_path = os.path.join(project_root, "bt")

def ensure_test_data():
    """Ensure we have proper test data"""
    print("Checking test data...")
    
    # Create a simple TV settings file for testing
    tv_test_file = "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tv_test.xlsx"
    
    # Copy from existing if not exists
    if not os.path.exists(tv_test_file):
        src = "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tv.xlsx"
        if os.path.exists(src):
            shutil.copy(src, tv_test_file)
            print(f"Created test TV file: {tv_test_file}")
    
    return tv_test_file

def run_cpu_aggregated_v4():
    """Run the CPU-based aggregated V4 version"""
    print("\n" + "="*80)
    print("Testing CPU-based TV Backtester (Aggregated V4)")
    print("="*80)
    
    output_dir = "/srv/samba/shared/Trades/tv_cpu_v4_test"
    os.makedirs(output_dir, exist_ok=True)
    
    # Change to bt directory
    os.chdir(bt_path)
    
    cmd = [
        sys.executable,
        "backtester_stable/BTRUN/BT_TV_GPU_aggregated_v4.py",
        "--start-date", "240119",
        "--end-date", "240120",
        "--output-dir", output_dir,
        "--slippage", "0.1"
    ]
    
    print("Command:", " ".join(cmd))
    print("Working directory:", os.getcwd())
    
    start_time = time.time()
    
    # Set environment
    env = os.environ.copy()
    env["PYTHONPATH"] = bt_path
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, env=env)
        elapsed = time.time() - start_time
        
        print(f"Return code: {result.returncode}")
        print(f"Time taken: {elapsed:.2f} seconds")
        
        if result.stdout:
            print("\nOutput (last 1000 chars):")
            print(result.stdout[-1000:])
        if result.stderr:
            print("\nErrors (last 1000 chars):")
            print(result.stderr[-1000:])
            
        # Check for output files
        if os.path.exists(output_dir):
            files = [f for f in os.listdir(output_dir) if f.endswith('.xlsx')]
            if files:
                print(f"\n✅ CPU V4 generated {len(files)} output files")
                return os.path.join(output_dir, files[0]), elapsed
            else:
                print("\n❌ No output files generated")
        
        return None, elapsed
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return None, 0

def run_gpu_enhanced():
    """Run the GPU enhanced version"""
    print("\n" + "="*80)
    print("Testing GPU-enhanced TV Backtester")
    print("="*80)
    
    output_dir = "/srv/samba/shared/Trades/tv_gpu_enhanced_test"
    os.makedirs(output_dir, exist_ok=True)
    
    # Change to bt directory  
    os.chdir(bt_path)
    
    cmd = [
        sys.executable,
        "backtester_stable/BTRUN/BT_TV_GPU_enhanced.py",
        "--tv-file", "input_tv.xlsx",
        "--output-dir", output_dir,
        "--debug"
    ]
    
    print("Command:", " ".join(cmd))
    print("Working directory:", os.getcwd())
    
    start_time = time.time()
    
    # Set environment
    env = os.environ.copy()
    env["PYTHONPATH"] = bt_path
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, env=env)
        elapsed = time.time() - start_time
        
        print(f"Return code: {result.returncode}")
        print(f"Time taken: {elapsed:.2f} seconds")
        
        if result.stdout:
            print("\nOutput (last 1000 chars):")
            print(result.stdout[-1000:])
        if result.stderr:
            print("\nErrors (last 1000 chars):")
            print(result.stderr[-1000:])
            
        # Check for output files
        if os.path.exists(output_dir):
            files = [f for f in os.listdir(output_dir) if f.endswith('.xlsx')]
            if files:
                print(f"\n✅ GPU Enhanced generated {len(files)} output files")
                return os.path.join(output_dir, files[0]), elapsed
            else:
                print("\n❌ No output files generated")
                
        return None, elapsed
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return None, 0

def compare_outputs(cpu_file, gpu_file, cpu_time, gpu_time):
    """Compare the outputs"""
    print("\n" + "="*80)
    print("COMPARISON RESULTS")
    print("="*80)
    
    print(f"\n⏱️  Performance:")
    print(f"CPU V4 time: {cpu_time:.2f} seconds")
    print(f"GPU Enhanced time: {gpu_time:.2f} seconds")
    if cpu_time > 0 and gpu_time > 0:
        speedup = cpu_time / gpu_time
        print(f"Speedup: {speedup:.2f}x")
    
    if not cpu_file or not gpu_file:
        print("\n❌ Cannot compare - missing output files")
        return
        
    print(f"\n📁 Files to compare:")
    print(f"CPU: {cpu_file}")
    print(f"GPU: {gpu_file}")
    
    try:
        # Try to read and compare
        sheets_to_check = ['PORTFOLIO Trans', 'Transactions', 'All Transactions', 'Summary']
        
        for sheet in sheets_to_check:
            try:
                # Try CPU file
                cpu_sheets = pd.ExcelFile(cpu_file).sheet_names
                gpu_sheets = pd.ExcelFile(gpu_file).sheet_names
                
                print(f"\nCPU sheets: {cpu_sheets}")
                print(f"GPU sheets: {gpu_sheets}")
                
                # Find a common sheet
                common_sheets = set(cpu_sheets) & set(gpu_sheets)
                if common_sheets:
                    sheet_to_compare = list(common_sheets)[0]
                    print(f"\nComparing sheet: {sheet_to_compare}")
                    
                    cpu_df = pd.read_excel(cpu_file, sheet_name=sheet_to_compare)
                    gpu_df = pd.read_excel(gpu_file, sheet_name=sheet_to_compare)
                    
                    print(f"CPU shape: {cpu_df.shape}")
                    print(f"GPU shape: {gpu_df.shape}")
                    
                    if 'pnl' in cpu_df.columns and 'pnl' in gpu_df.columns:
                        cpu_pnl = cpu_df['pnl'].sum()
                        gpu_pnl = gpu_df['pnl'].sum()
                        print(f"\nTotal P&L comparison:")
                        print(f"CPU: {cpu_pnl:.2f}")
                        print(f"GPU: {gpu_pnl:.2f}")
                        
                    break
                    
            except Exception as e:
                continue
                
    except Exception as e:
        print(f"Error comparing: {e}")

def main():
    """Main execution"""
    print("TV Backtester Version Comparison")
    print("="*80)
    print(f"Started at: {datetime.now()}")
    
    # Ensure test data
    ensure_test_data()
    
    # Run both versions
    cpu_file, cpu_time = run_cpu_aggregated_v4()
    gpu_file, gpu_time = run_gpu_enhanced()
    
    # Compare
    compare_outputs(cpu_file, gpu_file, cpu_time, gpu_time)
    
    print("\n" + "="*80)
    print("TEST COMPLETE")
    print("="*80)

if __name__ == "__main__":
    main()