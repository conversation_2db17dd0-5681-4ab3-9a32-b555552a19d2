#!/usr/bin/env python3
"""Main script for running backtests using HeavyDB."""

import argparse
import logging
import json
import os
import pandas as pd
from datetime import datetime
from typing import Dict, Any

from excel_parser.parser import ExcelParseError
from heavydb_integration.heavydb_runner import (
    HeavyDBRunnerError,
    run_backtest
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('backtest.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def parse_args():
    """Parses command line arguments."""
    parser = argparse.ArgumentParser(description="Run backtests using HeavyDB")
    
    parser.add_argument(
        "portfolio_excel",
        help="Path to portfolio Excel file"
    )
    
    parser.add_argument(
        "--start-date",
        type=int,
        help="Override start date (YYMMDD format)"
    )
    
    parser.add_argument(
        "--end-date",
        type=int,
        help="Override end date (YYMMDD format)"
    )
    
    parser.add_argument(
        "--output-dir",
        default="results",
        help="Directory for output files"
    )
    
    parser.add_argument(
        "--no-json",
        action="store_true",
        help="Skip JSON output"
    )
    
    parser.add_argument(
        "--no-excel",
        action="store_true",
        help="Skip Excel output"
    )
    
    return parser.parse_args()

def save_json_output(results: Dict[str, Any], output_dir: str) -> None:
    """Saves backtest results as JSON.
    
    Args:
        results: Backtest results dictionary
        output_dir: Output directory path
    """
    try:
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{results['portfolio_name']}_{timestamp}.json"
        filepath = os.path.join(output_dir, filename)
        
        # Save JSON
        with open(filepath, 'w') as f:
            json.dump(results, f, indent=2)
            
        logger.info(f"Saved JSON results to {filepath}")
        
    except Exception as e:
        logger.error(f"Failed to save JSON output: {e}")

def save_excel_output(results: Dict[str, Any], output_dir: str) -> None:
    """Saves backtest results as Excel.
    
    Args:
        results: Backtest results dictionary
        output_dir: Output directory path
    """
    try:
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{results['portfolio_name']}_{timestamp}.xlsx"
        filepath = os.path.join(output_dir, filename)
        
        # Create Excel writer
        with pd.ExcelWriter(filepath) as writer:
            # Write summary sheet
            summary = pd.DataFrame([{
                'Portfolio': results['portfolio_name'],
                'Start Date': results['start_date'],
                'End Date': results['end_date'],
                'Total Strategies': len(results['strategies'])
            }])
            summary.to_excel(writer, sheet_name='Summary', index=False)
            
            # Write strategy sheets
            for strategy_name, strategy_results in results['strategies'].items():
                # Trades sheet
                trades_df = pd.DataFrame(strategy_results['trades'])
                trades_df.to_excel(
                    writer,
                    sheet_name=f'{strategy_name}_Trades',
                    index=False
                )
                
                # Metrics sheet
                metrics_df = pd.DataFrame([strategy_results['metrics']])
                metrics_df.to_excel(
                    writer,
                    sheet_name=f'{strategy_name}_Metrics',
                    index=False
                )
                
        logger.info(f"Saved Excel results to {filepath}")
        
    except Exception as e:
        logger.error(f"Failed to save Excel output: {e}")

def main():
    """Main function."""
    args = parse_args()
    
    try:
        # Run backtest
        logger.info(f"Starting backtest for {args.portfolio_excel}")
        results = run_backtest(
            args.portfolio_excel,
            args.start_date,
            args.end_date
        )
        
        # Save outputs
        if not args.no_json:
            save_json_output(results, args.output_dir)
            
        if not args.no_excel:
            save_excel_output(results, args.output_dir)
            
        logger.info("Backtest completed successfully")
        
    except (ExcelParseError, HeavyDBRunnerError) as e:
        logger.error(f"Backtest failed: {e}")
        return 1
        
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return 1
        
    return 0

if __name__ == "__main__":
    exit(main()) 