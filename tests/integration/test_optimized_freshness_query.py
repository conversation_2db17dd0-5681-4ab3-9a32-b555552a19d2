#!/usr/bin/env python3
"""Test optimized freshness query that should prevent timeouts"""

import time
from heavydb import connect

try:
    # Connect to HeavyDB
    conn = connect(
        host='localhost',
        port=6274,
        user='admin',
        password='HyperInteractive',
        dbname='heavyai'
    )
    print("Connected to HeavyDB successfully")
    
    # Test 1: Original query (one per index)
    print("\n=== Testing Original Query (one per index) ===")
    indices = ['NIFTY', 'BANKNIFTY', 'MIDCAPNIFTY', 'SENSEX', 'FINNIFTY', 'BANKEX']
    
    start_time = time.time()
    for index in indices:
        cursor = conn.cursor()
        cursor.execute(f"""
            SELECT 
                COUNT(*) as row_count,
                MAX(trade_date) as latest_date,
                MIN(trade_date) as earliest_date,
                COUNT(DISTINCT trade_date) as trading_days
            FROM nifty_option_chain
            WHERE index_name = '{index}'
        """)
        result = cursor.fetchone()
        print(f"{index}: {result[0]} rows, latest: {result[1]}")
    original_time = time.time() - start_time
    print(f"Original approach took: {original_time:.2f} seconds")
    
    # Test 2: Optimized single query
    print("\n=== Testing Optimized Query (single query) ===")
    start_time = time.time()
    cursor = conn.cursor()
    
    # Single query to get all indices at once
    cursor.execute("""
        SELECT 
            index_name,
            COUNT(*) as row_count,
            MAX(trade_date) as latest_date,
            MIN(trade_date) as earliest_date,
            COUNT(DISTINCT trade_date) as trading_days
        FROM nifty_option_chain
        WHERE index_name IN ('NIFTY', 'BANKNIFTY', 'MIDCAPNIFTY', 'SENSEX', 'FINNIFTY', 'BANKEX')
        GROUP BY index_name
    """)
    
    results = cursor.fetchall()
    for row in results:
        print(f"{row[0]}: {row[1]} rows, latest: {row[2]}")
    
    optimized_time = time.time() - start_time
    print(f"Optimized approach took: {optimized_time:.2f} seconds")
    print(f"Speedup: {original_time/optimized_time:.1f}x faster")
    
    # Test 3: Even more optimized with limited aggregations
    print("\n=== Testing Super Optimized Query (minimal aggregations) ===")
    start_time = time.time()
    cursor = conn.cursor()
    
    # Just get count and max date (most important for freshness)
    cursor.execute("""
        SELECT 
            index_name,
            COUNT(*) as row_count,
            MAX(trade_date) as latest_date
        FROM nifty_option_chain
        WHERE index_name IN ('NIFTY', 'BANKNIFTY', 'MIDCAPNIFTY', 'SENSEX', 'FINNIFTY', 'BANKEX')
        GROUP BY index_name
    """)
    
    results = cursor.fetchall()
    for row in results:
        print(f"{row[0]}: {row[1]} rows, latest: {row[2]}")
    
    super_optimized_time = time.time() - start_time
    print(f"Super optimized approach took: {super_optimized_time:.2f} seconds")
    print(f"Speedup over original: {original_time/super_optimized_time:.1f}x faster")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()