#!/usr/bin/env python3
"""
Test all 4 strategy types (TBS, TV, ORB, OI) with real data
"""
import asyncio
import aiohttp
import json
import os
from pathlib import Path

BASE_URL = "http://173.208.247.17:8000"
API_BASE = f"{BASE_URL}/api/v2"

# Test data files
TEST_FILES = {
    "TBS": {
        "portfolio": "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tbs/input_portfolio.xlsx",
        "strategy": "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tbs/input_tbs_portfolio.xlsx"
    },
    "TV": {
        "portfolio": "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tv/input_portfolio_long.xlsx",
        "strategy": "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tv/input_tv.xlsx"
    },
    "ORB": {
        "portfolio": "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/orb/input_portfolio.xlsx",
        "strategy": "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/orb/input_orb.xlsx"
    },
    "OI": {
        "portfolio": "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/oi/input_maxoi.xlsx",
        "strategy": None  # OI only needs one file
    }
}

async def upload_file(session, url, file_path, field_name):
    """Upload a file to the API"""
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return None
        
    try:
        # Read file content first
        with open(file_path, 'rb') as f:
            file_content = f.read()
        
        data = aiohttp.FormData()
        data.add_field(field_name, file_content, 
                      filename=os.path.basename(file_path),
                      content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        
        async with session.post(url, data=data) as response:
            if response.status == 200:
                return await response.json()
            else:
                text = await response.text()
                print(f"❌ Upload failed ({response.status}): {text}")
                return None
    except Exception as e:
        print(f"❌ Upload error: {e}")
        return None

async def test_strategy(session, strategy_type, files):
    """Test a single strategy type"""
    print(f"\n🔍 Testing {strategy_type} Strategy...")
    
    # Upload portfolio file
    portfolio_file = files.get("portfolio")
    if portfolio_file:
        print(f"📤 Uploading portfolio: {os.path.basename(portfolio_file)}")
        result = await upload_file(session, f"{API_BASE}/upload", portfolio_file, "portfolio")
        if not result:
            return False
    
    # Upload strategy file (if needed)
    strategy_file = files.get("strategy")
    if strategy_file:
        print(f"📤 Uploading strategy: {os.path.basename(strategy_file)}")
        result = await upload_file(session, f"{API_BASE}/upload", strategy_file, "strategy")
        if not result:
            return False
    
    # Start backtest
    print(f"🚀 Starting {strategy_type} backtest...")
    backtest_data = {
        "strategy_type": strategy_type,
        "index": "NIFTY",
        "gpu_config": {
            "workers": 8,
            "max_gpu": True
        }
    }
    
    try:
        async with session.post(f"{API_BASE}/backtest/start", json=backtest_data) as response:
            if response.status == 200:
                result = await response.json()
                backtest_id = result.get("backtest_id")
                print(f"✅ Backtest started: {backtest_id}")
                
                # Wait a bit and check status
                await asyncio.sleep(5)
                
                async with session.get(f"{API_BASE}/backtest/{backtest_id}/status") as status_response:
                    if status_response.status == 200:
                        status = await status_response.json()
                        print(f"📊 Status: {status.get('status', 'Unknown')}")
                        print(f"📈 Progress: {status.get('progress', 0)}%")
                        return True
            else:
                text = await response.text()
                print(f"❌ Backtest failed ({response.status}): {text}")
                return False
    except Exception as e:
        print(f"❌ Backtest error: {e}")
        return False

async def test_websocket(session):
    """Test WebSocket connection"""
    print("\n🔌 Testing WebSocket connection...")
    try:
        # Use the token-based endpoint
        ws_url = f"ws://173.208.247.17:8000/api/v2/ws?token=demo_token_12345"
        async with session.ws_connect(ws_url) as ws:
            print("✅ WebSocket connected")
            
            # Subscribe to updates
            await ws.send_json({
                "action": "subscribe",
                "type": "all"
            })
            
            # Wait for a message
            try:
                msg = await asyncio.wait_for(ws.receive(), timeout=5.0)
                if msg.type == aiohttp.WSMsgType.TEXT:
                    data = json.loads(msg.data)
                    print(f"📨 Received: {data.get('type', 'Unknown message')}")
                    return True
            except asyncio.TimeoutError:
                print("⏱️  No WebSocket messages received (timeout)")
                return True  # Still consider it a success if connection works
                
    except Exception as e:
        print(f"❌ WebSocket error: {e}")
        return False

async def main():
    """Run all tests"""
    print("🧪 Enterprise GPU Backtester - Comprehensive Test Suite")
    print("=" * 60)
    
    results = {}
    
    async with aiohttp.ClientSession() as session:
        # Test WebSocket
        ws_result = await test_websocket(session)
        results["WebSocket"] = ws_result
        
        # Test each strategy type
        for strategy_type, files in TEST_FILES.items():
            result = await test_strategy(session, strategy_type, files)
            results[strategy_type] = result
            await asyncio.sleep(2)  # Brief pause between tests
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name:15} {status}")
        if not passed:
            all_passed = False
    
    print("=" * 60)
    if all_passed:
        print("🎉 All tests passed!")
    else:
        print("❌ Some tests failed. Please check the logs.")
    
    return all_passed

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)