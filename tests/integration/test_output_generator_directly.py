#!/usr/bin/env python3
"""
Test the output generator directly to verify strategy-specific sheets
"""
import os
import sys
import pandas as pd
import time

# Add the services directory to path
sys.path.insert(0, '/srv/samba/shared/bt/backtester_stable/BTRUN/services')

from output_generator import OutputGenerator

print("="*80)
print("TESTING OUTPUT GENERATOR DIRECTLY")
print("="*80)

# Create output generator
generator = OutputGenerator()

# Create test data
portfolio_params = {
    'start_date': '2024-04-01',
    'end_date': '2024-04-01',
    'capital': 1000000,
    'is_tick_bt': 'no',
    'lot_multiplier': 1,
    'position_size': 100,
    'max_open_positions': 10,
    'slippage': 0.5,
    'transaction_cost': 20,
    'margin_percentage': 15,
    'leverage': 1,
    'risk_per_trade': 2,
    'max_drawdown': 20,
    'trading_days': '<PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>',
    'start_time': '09:15:00',
    'end_time': '15:30:00',
    'square_off_time': '15:15:00',
    'use_atm': 'yes',
    'atm_type': 'Synthetic Future',
    'data_source': 'HeavyDB',
    'backtest_mode': 'test'
}

# Create empty dataframes for parameters
general_params = pd.DataFrame()  # Will use defaults
leg_params = pd.DataFrame()      # Will use defaults
trades = pd.DataFrame()          # Will use sample trades

# Generate output
output_path = f"/srv/samba/shared/test_outputs/direct_test_{int(time.time())}.xlsx"
os.makedirs(os.path.dirname(output_path), exist_ok=True)

print("\nGenerating output with fixed output generator...")
try:
    result = generator.generate_golden_format_output(
        portfolio_params=portfolio_params,
        general_params=general_params,
        leg_params=leg_params,
        trades=trades,
        output_path=output_path
    )
    print(f"✓ Output generated: {result}")
    
    # Analyze the output
    print("\nAnalyzing generated output...")
    xl = pd.ExcelFile(output_path)
    sheets = xl.sheet_names
    
    print(f"\nTotal sheets: {len(sheets)}")
    print("Sheet names:")
    for i, sheet in enumerate(sheets, 1):
        print(f"  {i}. {sheet}")
    
    # Check for strategy-specific sheets
    strategy_trans_sheets = [s for s in sheets if 'Trans' in s and s != 'PORTFOLIO Trans']
    strategy_results_sheets = [s for s in sheets if 'Results' in s and s != 'PORTFOLIO Results']
    
    print(f"\nStrategy-specific Trans sheets: {len(strategy_trans_sheets)}")
    if strategy_trans_sheets:
        for sheet in strategy_trans_sheets:
            print(f"  - {sheet}")
    
    print(f"\nStrategy-specific Results sheets: {len(strategy_results_sheets)}")
    if strategy_results_sheets:
        for sheet in strategy_results_sheets:
            print(f"  - {sheet}")
    
    # Check if we have all expected sheets
    expected_count = 9  # Including strategy-specific sheets
    if len(sheets) == expected_count:
        print(f"\n✅ SUCCESS: All {expected_count} sheets present!")
    else:
        print(f"\n❌ ISSUE: Expected {expected_count} sheets, found {len(sheets)}")
        
    # Check metrics sheet
    print("\nChecking Metrics sheet...")
    metrics_df = pd.read_excel(output_path, sheet_name='Metrics')
    print(f"Metrics columns: {list(metrics_df.columns)}")
    
    # Check if strategy name is in metrics columns
    if 'RS,916-1200,ATM-SELL,OTM2-BUY WITH 100%SL' in metrics_df.columns:
        print("✅ Strategy name correctly used in Metrics columns")
    else:
        print("❌ Strategy name not found in Metrics columns")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "="*80)
print("TEST COMPLETE")
print("="*80)