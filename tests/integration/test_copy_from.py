#!/usr/bin/env python3
"""
Test COPY FROM with the new single-GPU optimized table
"""
import os
import time
from heavydb import connect

def test_copy_from():
    """Test COPY FROM functionality with single GPU table"""
    
    # Connection parameters
    conn_params = {
        'host': 'localhost',
        'port': 6274,
        'user': 'admin',
        'password': 'HyperInteractive',
        'dbname': 'heavyai'
    }
    
    print(f"Connecting to HeavyDB...")
    try:
        conn = connect(**conn_params)
        cursor = conn.cursor()
        print("Successfully connected to HeavyDB")
    except Exception as e:
        print(f"Failed to connect: {e}")
        return
    
    # Get the first CSV file to test
    csv_dir = "/srv/samba/shared/market_data/nifty/oc_with_futures"
    csv_files = sorted([f for f in os.listdir(csv_dir) if f.endswith('.csv')])
    
    if not csv_files:
        print("No CSV files found!")
        return
    
    # Test with the first file
    test_file = os.path.join(csv_dir, csv_files[0])
    print(f"\nTesting COPY FROM with: {csv_files[0]}")
    print(f"Full path: {test_file}")
    
    # Check file size
    file_size_mb = os.path.getsize(test_file) / (1024 * 1024)
    print(f"File size: {file_size_mb:.2f} MB")
    
    # Get current row count
    try:
        cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
        initial_count = cursor.fetchone()[0]
        print(f"Initial row count: {initial_count:,}")
    except Exception as e:
        print(f"Error getting count: {e}")
        initial_count = 0
    
    # Test COPY FROM
    print("\nExecuting COPY FROM...")
    start_time = time.time()
    
    copy_sql = f"""
    COPY nifty_option_chain FROM '{test_file}' 
    WITH (header='true', delimiter=',');
    """
    
    try:
        cursor.execute(copy_sql)
        elapsed = time.time() - start_time
        print(f"COPY FROM completed in {elapsed:.2f} seconds!")
        
        # Get new row count
        cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
        new_count = cursor.fetchone()[0]
        rows_loaded = new_count - initial_count
        
        print(f"Rows loaded: {rows_loaded:,}")
        print(f"Loading speed: {rows_loaded/elapsed:,.0f} rows/sec")
        
    except Exception as e:
        print(f"COPY FROM failed: {e}")
        print(f"Error type: {type(e).__name__}")
        
        # Try to check if connection is still alive
        try:
            cursor.execute("SELECT 1")
            print("Connection is still alive")
        except:
            print("Connection was lost during COPY FROM")
            
        # Check if it's a path issue
        if "file or directory" in str(e).lower():
            print("\nNote: This might be a path whitelist issue")
            print("Configured allowed paths:")
            print("- /srv/samba/shared/market_data/")
            print("- /srv/samba/shared/")
            print("- /var/lib/heavyai/import/")
            print("- /tmp/")
    
    finally:
        conn.close()
        print("\nConnection closed")

if __name__ == "__main__":
    test_copy_from()