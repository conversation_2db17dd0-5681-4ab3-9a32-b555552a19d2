#!/usr/bin/env python3
"""
Column Mapping Unit Test

This script tests that all columns defined in column_mapping.md are correctly
mapped from Excel files to model objects in the HeavyDB Backtester.

It loads sample input files, parses them using the backtester parsers,
and verifies that all fields are correctly mapped and transformed.
"""

import os
import sys
import json
import pandas as pd
import logging
from datetime import datetime
import subprocess
from typing import Dict, Any, List, Optional, Union

# Add the backtester path to sys.path
backtester_path = '/srv/samba/shared/bt/backtester_stable/BTRUN'
sys.path.append(backtester_path)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'column_mapping_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger('column_mapping_test')

# Import backtester modules
try:
    # Use fully qualified imports
    from bt.backtester_stable.BTRUN.excel_parser.portfolio_parser import parse_portfolio_excel
    from bt.backtester_stable.BTRUN.models.portfolio import PortfolioModel, StrategyModel, LegModel
    logger.info("Successfully imported backtester modules")
except ImportError as e:
    logger.error(f"Failed to import backtester modules: {e}")
    sys.exit(1)

class ColumnMappingTest:
    """Test class for column mapping validation"""
    
    def __init__(self):
        self.portfolio_file = os.path.join(backtester_path, 'input_sheets/input_portfolio_fixed.xlsx')
        self.strategy_file = None  # Will be determined from portfolio file
        self.test_results = {
            'portfolio_setting': {'total': 0, 'mapped': 0, 'missing': []},
            'strategy_setting': {'total': 0, 'mapped': 0, 'missing': []},
            'general_parameter': {'total': 0, 'mapped': 0, 'missing': []},
            'leg_parameter': {'total': 0, 'mapped': 0, 'missing': []}
        }
        
        # Expected column mappings from column_mapping.md
        self.expected_mappings = {
            'portfolio_setting': {
                'StartDate': 'start_date',
                'EndDate': 'end_date',
                'IsTickBT': 'is_tick_bt',
                'PortfolioName': 'portfolio_name',
                'Multiplier': 'margin_multiplier',
                'SlippagePercent': 'slippage_percent',
                # Fields that go to extra_params or are for filtering only
                'Enabled': None,
                'PortfolioTarget': None,
                'PortfolioStoploss': None,
                'PortfolioTrailingType': None,
                'PnLCalTime': None,
                'LockPercent': None,
                'TrailPercent': None,
                'SqOff1Time': None,
                'SqOff1Percent': None,
                'SqOff2Time': None,
                'SqOff2Percent': None,
                'ProfitReaches': None,
                'LockMinProfitAt': None,
                'IncreaseInProfit': None,
                'TrailMinProfitBy': None
            },
            'strategy_setting': {
                'Enabled': None,  # Row filter only
                'PortfolioName': 'portfolio_name',
                'StrategyType': 'evaluator',
                'StrategyExcelFilePath': 'strategy_excel_path'
            },
            'general_parameter': {
                'StrategyName': 'strategy_name',
                'DTE': 'dte_filter',
                'StartTime': 'entry_start',
                'EndTime': 'entry_end',
                # Fields that go to extra_params
                'MoveSlToCost': 'extra_params',
                'Underlying': 'extra_params',
                'Index': 'extra_params',
                'Weekdays': 'extra_params',
                'StrikeSelectionTime': 'extra_params',
                'LastEntryTime': 'extra_params',
                'StrategyProfit': 'extra_params',
                'StrategyLoss': 'extra_params',
                'StrategyProfitReExecuteNo': 'extra_params',
                'StrategyLossReExecuteNo': 'extra_params',
                'StrategyTrailingType': 'extra_params',
                'PnLCalTime': 'extra_params',
                'LockPercent': 'extra_params',
                'TrailPercent': 'extra_params',
                'SqOff1Time': 'extra_params',
                'SqOff1Percent': 'extra_params',
                'SqOff2Time': 'extra_params',
                'SqOff2Percent': 'extra_params',
                'ProfitReaches': 'extra_params',
                'LockMinProfitAt': 'extra_params',
                'IncreaseInProfit': 'extra_params',
                'TrailMinProfitBy': 'extra_params',
                'TgtTrackingFrom': 'extra_params',
                'TgtRegisterPriceFrom': 'extra_params',
                'SlTrackingFrom': 'extra_params',
                'SlRegisterPriceFrom': 'extra_params',
                'PnLCalculationFrom': 'extra_params',
                'ConsiderHedgePnLForStgyPnL': 'extra_params',
                'StoplossCheckingInterval': 'extra_params',
                'TargetCheckingInterval': 'extra_params',
                'ReEntryCheckingInterval': 'extra_params',
                'OnExpiryDayTradeNextExpiry': 'extra_params'
            },
            'leg_parameter': {
                'StrategyName': 'strategy_name',
                'LegID': 'leg_id',
                'Instrument': 'option_type',
                'Transaction': 'transaction',
                'Expiry': 'expiry_rule',
                'StrikeMethod': 'strike_rule',
                'StrikeValue': 'fixed_strike',
                'Lots': 'lots',
                # Fields that go to extra_params
                'IsIdle': 'extra_params',
                'W&Type': 'extra_params',
                'W&TValue': 'extra_params',
                'TrailW&T': 'extra_params',
                'MatchPremium': 'extra_params',
                'StrikePremiumCondition': 'extra_params',
                'SLType': 'extra_params',
                'SLValue': 'extra_params',
                'TGTType': 'extra_params',
                'TGTValue': 'extra_params',
                'TrailSLType': 'extra_params',
                'SL_TrailAt': 'extra_params',
                'SL_TrailBy': 'extra_params',
                'ReEntryType': 'extra_params',
                'ReEnteriesCount': 'extra_params',
                'OnEntry_OpenTradeOn': 'extra_params',
                'OnEntry_SqOffTradeOff': 'extra_params',
                'OnEntry_SqOffAllLegs': 'extra_params',
                'OnEntry_OpenTradeDelay': 'extra_params',
                'OnEntry_SqOffDelay': 'extra_params',
                'OnExit_OpenTradeOn': 'extra_params',
                'OnExit_SqOffTradeOff': 'extra_params',
                'OnExit_SqOffAllLegs': 'extra_params',
                'OnExit_OpenAllLegs': 'extra_params',
                'OnExit_OpenTradeDelay': 'extra_params',
                'OnExit_SqOffDelay': 'extra_params',
                'OpenHedge': 'extra_params',
                'HedgeStrikeMethod': 'extra_params',
                'HedgeStrikeValue': 'extra_params',
                'HedgeStrikePremiumCondition': 'extra_params',
                'ConsiderEMAForEntry': 'extra_params',
                'EmaEntryCondition': 'extra_params',
                'ConsiderVwapForEntry': 'extra_params',
                'VwapEntryCondition': 'extra_params',
                'RsiEntryCondition': 'extra_params',
                'EMAPeriod': 'extra_params',
                'STPeriod': 'extra_params',
                'ConsiderVwapForExit': 'extra_params',
                'VwapExitCondition': 'extra_params',
                'ConsiderEMAForExit': 'extra_params',
                'EmaExitCondition': 'extra_params',
                'ConsiderSTForEntry': 'extra_params',
                'StEntryCondition': 'extra_params',
                'ConsiderSTForExit': 'extra_params',
                'StExitCondition': 'extra_params',
                'ConsiderRSIForExit': 'extra_params',
                'RsiExitCondition': 'extra_params',
                'ConsiderVolSmaForEntry': 'extra_params',
                'VolSmaEntryCondition': 'extra_params',
                'ConsiderVolSmaForExit': 'extra_params',
                'VolSmaExitCondition': 'extra_params',
                'STMultiplier': 'extra_params',
                'RsiPeriod': 'extra_params',
                'VolSMAPeriod': 'extra_params'
            }
        }
    
    def read_excel_sheets(self):
        """Read all sheets from the portfolio and strategy Excel files"""
        logger.info(f"Reading portfolio file: {self.portfolio_file}")
        
        try:
            # Read portfolio Excel file
            portfolio_sheets = pd.read_excel(self.portfolio_file, sheet_name=None)
            
            # Get strategy file path from StrategySetting sheet
            strategy_settings = portfolio_sheets.get('StrategySetting')
            if strategy_settings is not None and 'StrategyExcelFilePath' in strategy_settings.columns:
                strategy_file_path = strategy_settings.iloc[0]['StrategyExcelFilePath']
                if not os.path.isabs(strategy_file_path):
                    # If relative path, assume it's relative to backtester path
                    strategy_file_path = os.path.join(backtester_path, strategy_file_path)
                self.strategy_file = strategy_file_path
                logger.info(f"Found strategy file path: {self.strategy_file}")
            
            sheets = {
                'portfolio_setting': portfolio_sheets.get('PortfolioSetting'),
                'strategy_setting': portfolio_sheets.get('StrategySetting')
            }
            
            # Read strategy Excel file if found
            if self.strategy_file and os.path.exists(self.strategy_file):
                logger.info(f"Reading strategy file: {self.strategy_file}")
                strategy_sheets = pd.read_excel(self.strategy_file, sheet_name=None)
                sheets['general_parameter'] = strategy_sheets.get('GeneralParameter')
                sheets['leg_parameter'] = strategy_sheets.get('LegParameter')
            else:
                logger.warning("Strategy file not found or specified")
            
            return sheets
        
        except Exception as e:
            logger.error(f"Error reading Excel files: {e}")
            return {}
    
    def test_portfolio_model(self, portfolio_model: Optional[PortfolioModel], excel_sheets: Dict):
        """Test that all columns from Excel are correctly mapped to the portfolio model"""
        if portfolio_model is None:
            logger.error("Portfolio model is None, cannot test mappings")
            return
        
        logger.info("Testing portfolio model mappings...")
        
        # Test PortfolioSetting mappings
        portfolio_df = excel_sheets.get('portfolio_setting')
        if portfolio_df is not None:
            self._test_sheet_mapping('portfolio_setting', portfolio_df, portfolio_model)
        
        # Test StrategySetting mappings through strategies list
        strategy_df = excel_sheets.get('strategy_setting')
        if strategy_df is not None and portfolio_model.strategies:
            logger.info(f"Found {len(portfolio_model.strategies)} strategies in portfolio model")
            # Just check the first strategy for StrategySetting mapping
            self._test_sheet_mapping('strategy_setting', strategy_df.iloc[0], portfolio_model.strategies[0])
            
            # Get the first strategy with legs to test GeneralParameter and LegParameter
            for strategy in portfolio_model.strategies:
                if strategy.legs:
                    general_df = excel_sheets.get('general_parameter')
                    if general_df is not None:
                        # Find matching strategy row in GeneralParameter
                        matching_general = general_df[general_df['StrategyName'] == strategy.strategy_name]
                        if not matching_general.empty:
                            self._test_sheet_mapping('general_parameter', matching_general.iloc[0], strategy)
                    
                    leg_df = excel_sheets.get('leg_parameter')
                    if leg_df is not None:
                        # Test the first leg for LegParameter mapping
                        matching_legs = leg_df[leg_df['StrategyName'] == strategy.strategy_name]
                        if not matching_legs.empty:
                            for i, leg in enumerate(strategy.legs):
                                if i < len(matching_legs):
                                    self._test_sheet_mapping('leg_parameter', matching_legs.iloc[i], leg)
                    break
    
    def _test_sheet_mapping(self, sheet_name: str, excel_row, model_obj):
        """Test mapping between an Excel row and a model object"""
        logger.info(f"Testing {sheet_name} mappings...")
        
        expected_mapping = self.expected_mappings.get(sheet_name, {})
        total_columns = 0
        mapped_columns = 0
        missing_mappings = []
        
        for excel_column, model_field in expected_mapping.items():
            if excel_column in excel_row:
                total_columns += 1
                excel_value = excel_row[excel_column]
                
                # Skip None/NaN values
                if pd.isna(excel_value):
                    continue
                
                # Check direct field mapping
                if model_field and model_field != 'extra_params':
                    if hasattr(model_obj, model_field):
                        model_value = getattr(model_obj, model_field)
                        logger.info(f"{sheet_name}.{excel_column} -> {model_field}: Excel={excel_value}, Model={model_value}")
                        mapped_columns += 1
                    else:
                        missing_mappings.append(f"{excel_column} -> {model_field}")
                
                # Check extra_params mapping
                elif model_field == 'extra_params':
                    if hasattr(model_obj, 'extra_params') and isinstance(model_obj.extra_params, dict):
                        if excel_column in model_obj.extra_params:
                            model_value = model_obj.extra_params[excel_column]
                            logger.info(f"{sheet_name}.{excel_column} -> extra_params[{excel_column}]: Excel={excel_value}, Model={model_value}")
                            mapped_columns += 1
                        else:
                            # Some columns might be parsed with different names or transformations
                            logger.warning(f"{excel_column} not found in extra_params directly")
        
        # Update test results
        self.test_results[sheet_name]['total'] = total_columns
        self.test_results[sheet_name]['mapped'] = mapped_columns
        self.test_results[sheet_name]['missing'] = missing_mappings
        
        logger.info(f"{sheet_name} mapping summary: {mapped_columns}/{total_columns} columns mapped")
        if missing_mappings:
            logger.warning(f"Missing mappings in {sheet_name}: {missing_mappings}")
    
    def run_backtester(self):
        """Run the backtester with the test portfolio file"""
        logger.info("Running backtester with test portfolio file...")
        
        output_file = f'/srv/samba/shared/Trades/column_mapping_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        
        cmd = [
            "python3",
            os.path.join(backtester_path, "BTRunPortfolio_GPU.py"),
            "--portfolio-excel", self.portfolio_file,
            "--output-path", output_file
        ]
        
        logger.info(f"Command: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.info("Backtester completed successfully")
            logger.info(f"Output file: {output_file}")
            return output_file
        
        except subprocess.CalledProcessError as e:
            logger.error(f"Backtester failed with return code {e.returncode}")
            logger.error(f"STDOUT: {e.stdout}")
            logger.error(f"STDERR: {e.stderr}")
            return None
    
    def load_portfolio_model(self):
        """Load the portfolio model using the backtester's parser"""
        logger.info("Loading portfolio model using backtester parser...")
        
        try:
            portfolio_model = parse_portfolio_excel(self.portfolio_file)
            if portfolio_model:
                logger.info("Successfully parsed portfolio model")
            else:
                logger.error("Failed to parse portfolio model (returned None)")
            
            return portfolio_model
        
        except Exception as e:
            logger.error(f"Error parsing portfolio model: {e}")
            return None
    
    def run_tests(self):
        """Run all tests"""
        logger.info("Starting column mapping tests...")
        
        # Step 1: Read Excel sheets
        excel_sheets = self.read_excel_sheets()
        if not excel_sheets:
            logger.error("Failed to read Excel sheets, aborting tests")
            return False
        
        # Step 2: Load portfolio model
        portfolio_model = self.load_portfolio_model()
        if not portfolio_model:
            logger.error("Failed to load portfolio model, aborting tests")
            return False
        
        # Step 3: Test portfolio model mappings
        self.test_portfolio_model(portfolio_model, excel_sheets)
        
        # Step 4: Run backtester
        output_file = self.run_backtester()
        if not output_file:
            logger.error("Failed to run backtester, but continuing with mapping tests")
        
        # Step 5: Print final results
        self._print_results()
        
        # Check if all mappings were successful
        for sheet, results in self.test_results.items():
            if results['total'] > 0 and results['mapped'] < results['total']:
                return False
        
        return True
    
    def _print_results(self):
        """Print test results"""
        logger.info("\n=== Column Mapping Test Results ===\n")
        
        success = True
        for sheet, results in self.test_results.items():
            total = results['total']
            mapped = results['mapped']
            missing = results['missing']
            
            if total > 0:
                percentage = (mapped / total) * 100
                status = "PASSED" if mapped == total else "FAILED"
                logger.info(f"{sheet}: {mapped}/{total} columns mapped ({percentage:.1f}%) - {status}")
                
                if missing:
                    logger.info(f"  Missing mappings: {missing}")
                    success = False
        
        if success:
            logger.info("\n✅ ALL TESTS PASSED: All Excel columns are correctly mapped to model fields")
        else:
            logger.error("\n❌ SOME TESTS FAILED: Some Excel columns are not correctly mapped")

def main():
    """Main function"""
    test = ColumnMappingTest()
    if test.run_tests():
        logger.info("Column mapping tests completed successfully")
        return 0
    else:
        logger.error("Column mapping tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 