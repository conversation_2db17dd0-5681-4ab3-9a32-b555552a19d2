#!/usr/bin/env python3
"""
Comprehensive UI Test Automation Framework using Playwright
Tests all user workflows and interactions
"""

import asyncio
import os
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional
from playwright.async_api import async_playwright, <PERSON>, <PERSON><PERSON>er, BrowserContext

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('comprehensive_ui_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Configuration
BASE_URL = "http://173.208.247.17:8000"
TEST_INPUT_DIR = "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/comprehensive_tests_v2"
SCREENSHOT_DIR = "/srv/samba/shared/ui_test_screenshots"
TEST_RESULTS_DIR = "/srv/samba/shared/ui_test_results"

class UITestFramework:
    """Comprehensive UI testing framework using Playwright"""
    
    def __init__(self):
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.test_results = []
        self.start_time = datetime.now()
        
        # Create directories
        os.makedirs(SCREENSHOT_DIR, exist_ok=True)
        os.makedirs(TEST_RESULTS_DIR, exist_ok=True)
        
    async def setup(self):
        """Setup browser and page"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=True,  # Set to False for debugging
            args=['--no-sandbox', '--disable-setuid-sandbox']
        )
        self.context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            ignore_https_errors=True
        )
        self.page = await self.context.new_page()
        
        # Enable console logging
        self.page.on("console", lambda msg: logger.info(f"Browser console: {msg.text}"))
        self.page.on("pageerror", lambda err: logger.error(f"Browser error: {err}"))
        
    async def teardown(self):
        """Cleanup browser resources"""
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
            
    async def take_screenshot(self, name: str):
        """Take screenshot for documentation"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        screenshot_path = f"{SCREENSHOT_DIR}/{name}_{timestamp}.png"
        await self.page.screenshot(path=screenshot_path)
        logger.info(f"Screenshot saved: {screenshot_path}")
        
    async def test_login_flow(self) -> Dict:
        """Test login functionality"""
        logger.info("Testing login flow...")
        
        try:
            # Navigate to login page
            await self.page.goto(BASE_URL)
            await self.page.wait_for_load_state('networkidle')
            
            # Check if login form exists
            login_form = await self.page.query_selector('form#login-form')
            if not login_form:
                # If no login required, that's also valid
                return {
                    'test': 'login_flow',
                    'status': 'passed',
                    'notes': 'No authentication required'
                }
                
            # Fill login credentials
            await self.page.fill('input[name="username"]', 'admin')
            await self.page.fill('input[name="password"]', 'admin123')
            
            # Submit form
            await self.page.click('button[type="submit"]')
            
            # Wait for navigation
            await self.page.wait_for_load_state('networkidle')
            
            # Verify login success
            dashboard = await self.page.query_selector('.dashboard')
            
            if dashboard:
                await self.take_screenshot('login_success')
                return {
                    'test': 'login_flow',
                    'status': 'passed',
                    'notes': 'Login successful'
                }
            else:
                return {
                    'test': 'login_flow',
                    'status': 'failed',
                    'error': 'Dashboard not found after login'
                }
                
        except Exception as e:
            logger.error(f"Login test failed: {e}")
            return {
                'test': 'login_flow',
                'status': 'failed',
                'error': str(e)
            }
            
    async def test_tbs_upload_flow(self) -> Dict:
        """Test TBS file upload workflow"""
        logger.info("Testing TBS upload flow...")
        
        try:
            # Navigate to TBS upload page
            await self.page.goto(f"{BASE_URL}/tbs")
            await self.page.wait_for_load_state('networkidle')
            
            # Find upload zone
            upload_zone = await self.page.query_selector('.upload-zone')
            if not upload_zone:
                return {
                    'test': 'tbs_upload_flow',
                    'status': 'failed',
                    'error': 'Upload zone not found'
                }
                
            # Upload portfolio file
            test_file = f"{TEST_INPUT_DIR}/multileg_strategies/iron_condor.xlsx"
            
            # Find file input
            file_input = await self.page.query_selector('input[type="file"]')
            await file_input.set_input_files(test_file)
            
            # Wait for file processing
            await self.page.wait_for_selector('.file-status-success', timeout=10000)
            
            # Check if strategy requirements shown
            strategy_reqs = await self.page.query_selector('.strategy-requirements')
            
            if strategy_reqs:
                # Strategy file required - handle dynamic upload
                await self.take_screenshot('tbs_strategy_requirements')
                
                # For comprehensive tests, strategy references itself
                # So we can proceed
                
            # Click submit button
            submit_btn = await self.page.query_selector('button[type="submit"]')
            await submit_btn.click()
            
            # Wait for backtest to start
            await self.page.wait_for_selector('.backtest-progress', timeout=10000)
            
            await self.take_screenshot('tbs_backtest_started')
            
            return {
                'test': 'tbs_upload_flow',
                'status': 'passed',
                'notes': 'TBS upload and submission successful'
            }
            
        except Exception as e:
            logger.error(f"TBS upload test failed: {e}")
            return {
                'test': 'tbs_upload_flow',
                'status': 'failed',
                'error': str(e)
            }
            
    async def test_strategy_configuration(self) -> Dict:
        """Test strategy configuration UI"""
        logger.info("Testing strategy configuration...")
        
        try:
            # Navigate to strategy config
            await self.page.goto(f"{BASE_URL}/strategies/new")
            await self.page.wait_for_load_state('networkidle')
            
            # Test strike selection dropdown
            strike_dropdown = await self.page.query_selector('select[name="strike_method"]')
            if strike_dropdown:
                # Get all options
                options = await strike_dropdown.query_selector_all('option')
                option_values = []
                
                for option in options:
                    value = await option.get_attribute('value')
                    option_values.append(value)
                    
                # Verify all strike methods available
                expected_methods = ['ATM', 'ITM1', 'OTM1', 'FIXED', 'PREMIUM', 'ATM_WIDTH', 'DELTA']
                missing = set(expected_methods) - set(option_values)
                
                if missing:
                    return {
                        'test': 'strategy_configuration',
                        'status': 'failed',
                        'error': f'Missing strike methods: {missing}'
                    }
                    
                # Test changing strike method
                await strike_dropdown.select_option('ATM_WIDTH')
                
                # Check if additional fields appear
                strike_value_field = await self.page.query_selector('input[name="strike_value"]')
                
                if strike_value_field:
                    await strike_value_field.fill('0.5')
                    
            # Test risk type selection
            risk_types = ['percentage', 'point', 'index_point', 'index_percentage', 'absolute', 'delta']
            
            sl_type_dropdown = await self.page.query_selector('select[name="sl_type"]')
            if sl_type_dropdown:
                for risk_type in risk_types[:3]:  # Test first 3
                    await sl_type_dropdown.select_option(risk_type)
                    await self.page.wait_for_timeout(500)
                    
            await self.take_screenshot('strategy_configuration')
            
            return {
                'test': 'strategy_configuration',
                'status': 'passed',
                'notes': 'All configuration options working'
            }
            
        except Exception as e:
            logger.error(f"Strategy configuration test failed: {e}")
            return {
                'test': 'strategy_configuration',
                'status': 'failed',
                'error': str(e)
            }
            
    async def test_real_time_monitoring(self) -> Dict:
        """Test real-time backtest monitoring"""
        logger.info("Testing real-time monitoring...")
        
        try:
            # Assume a backtest is running
            # Navigate to monitoring page
            await self.page.goto(f"{BASE_URL}/monitor")
            await self.page.wait_for_load_state('networkidle')
            
            # Check for WebSocket connection
            ws_indicator = await self.page.query_selector('.ws-status-connected')
            
            if not ws_indicator:
                logger.warning("WebSocket not connected")
                
            # Check for progress updates
            progress_bar = await self.page.query_selector('.progress-bar')
            
            if progress_bar:
                # Wait for progress update
                initial_progress = await progress_bar.get_attribute('data-progress')
                await self.page.wait_for_timeout(5000)
                updated_progress = await progress_bar.get_attribute('data-progress')
                
                if initial_progress != updated_progress:
                    logger.info("Progress updates working")
                else:
                    logger.warning("No progress updates detected")
                    
            # Check for trade updates
            trade_table = await self.page.query_selector('.trades-table')
            
            if trade_table:
                trades = await trade_table.query_selector_all('tr.trade-row')
                logger.info(f"Found {len(trades)} trades")
                
            await self.take_screenshot('real_time_monitoring')
            
            return {
                'test': 'real_time_monitoring',
                'status': 'passed',
                'notes': 'Monitoring features working'
            }
            
        except Exception as e:
            logger.error(f"Real-time monitoring test failed: {e}")
            return {
                'test': 'real_time_monitoring',
                'status': 'failed',
                'error': str(e)
            }
            
    async def test_results_download(self) -> Dict:
        """Test results download functionality"""
        logger.info("Testing results download...")
        
        try:
            # Navigate to results page
            await self.page.goto(f"{BASE_URL}/results")
            await self.page.wait_for_load_state('networkidle')
            
            # Find completed backtests
            completed_tests = await self.page.query_selector_all('.backtest-completed')
            
            if not completed_tests:
                return {
                    'test': 'results_download',
                    'status': 'skipped',
                    'notes': 'No completed backtests found'
                }
                
            # Click download on first result
            first_test = completed_tests[0]
            download_btn = await first_test.query_selector('.download-btn')
            
            if download_btn:
                # Start download
                download_promise = self.page.wait_for_event('download')
                await download_btn.click()
                download = await download_promise
                
                # Save to test directory
                download_path = f"{TEST_RESULTS_DIR}/test_download.xlsx"
                await download.save_as(download_path)
                
                # Verify file exists
                if os.path.exists(download_path):
                    file_size = os.path.getsize(download_path)
                    logger.info(f"Downloaded file size: {file_size} bytes")
                    
                    return {
                        'test': 'results_download',
                        'status': 'passed',
                        'notes': f'Downloaded {file_size} bytes'
                    }
                    
            return {
                'test': 'results_download',
                'status': 'failed',
                'error': 'Download button not found or download failed'
            }
            
        except Exception as e:
            logger.error(f"Results download test failed: {e}")
            return {
                'test': 'results_download',
                'status': 'failed',
                'error': str(e)
            }
            
    async def test_error_handling(self) -> Dict:
        """Test UI error handling"""
        logger.info("Testing error handling...")
        
        try:
            # Test invalid file upload
            await self.page.goto(f"{BASE_URL}/tbs")
            await self.page.wait_for_load_state('networkidle')
            
            # Create a temporary invalid file
            invalid_file = '/tmp/invalid_test.txt'
            with open(invalid_file, 'w') as f:
                f.write('This is not a valid Excel file')
                
            # Try to upload
            file_input = await self.page.query_selector('input[type="file"]')
            await file_input.set_input_files(invalid_file)
            
            # Wait for error message
            error_msg = await self.page.wait_for_selector('.error-message', timeout=5000)
            
            if error_msg:
                error_text = await error_msg.text_content()
                logger.info(f"Error message displayed: {error_text}")
                
                await self.take_screenshot('error_handling')
                
                return {
                    'test': 'error_handling',
                    'status': 'passed',
                    'notes': 'Error handling working correctly'
                }
            else:
                return {
                    'test': 'error_handling',
                    'status': 'failed',
                    'error': 'No error message displayed for invalid file'
                }
                
        except Exception as e:
            logger.error(f"Error handling test failed: {e}")
            return {
                'test': 'error_handling',
                'status': 'failed',
                'error': str(e)
            }
            
    async def test_responsive_design(self) -> Dict:
        """Test responsive design across devices"""
        logger.info("Testing responsive design...")
        
        try:
            viewports = [
                {'name': 'mobile', 'width': 375, 'height': 667},
                {'name': 'tablet', 'width': 768, 'height': 1024},
                {'name': 'desktop', 'width': 1920, 'height': 1080}
            ]
            
            issues = []
            
            for viewport in viewports:
                await self.page.set_viewport_size(
                    width=viewport['width'],
                    height=viewport['height']
                )
                
                await self.page.goto(BASE_URL)
                await self.page.wait_for_load_state('networkidle')
                
                # Check if navigation menu is accessible
                nav_menu = await self.page.query_selector('.nav-menu')
                
                if viewport['name'] == 'mobile':
                    # Should have hamburger menu
                    hamburger = await self.page.query_selector('.hamburger-menu')
                    if not hamburger:
                        issues.append(f"No hamburger menu on {viewport['name']}")
                        
                # Check if content is properly contained
                content = await self.page.query_selector('.main-content')
                if content:
                    box = await content.bounding_box()
                    if box and box['width'] > viewport['width']:
                        issues.append(f"Content overflow on {viewport['name']}")
                        
                await self.take_screenshot(f"responsive_{viewport['name']}")
                
            if issues:
                return {
                    'test': 'responsive_design',
                    'status': 'failed',
                    'error': ', '.join(issues)
                }
            else:
                return {
                    'test': 'responsive_design',
                    'status': 'passed',
                    'notes': 'Responsive design working across all viewports'
                }
                
        except Exception as e:
            logger.error(f"Responsive design test failed: {e}")
            return {
                'test': 'responsive_design',
                'status': 'failed',
                'error': str(e)
            }
            
    async def test_performance(self) -> Dict:
        """Test UI performance metrics"""
        logger.info("Testing performance...")
        
        try:
            # Enable performance monitoring
            await self.page.goto(BASE_URL)
            
            # Get performance metrics
            metrics = await self.page.evaluate("""
                () => {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    return {
                        domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                        loadComplete: perfData.loadEventEnd - perfData.loadEventStart,
                        firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
                        firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0
                    };
                }
            """)
            
            logger.info(f"Performance metrics: {metrics}")
            
            # Check if metrics are within acceptable ranges
            issues = []
            
            if metrics['firstContentfulPaint'] > 3000:
                issues.append(f"FCP too slow: {metrics['firstContentfulPaint']}ms")
                
            if metrics['domContentLoaded'] > 5000:
                issues.append(f"DOM load too slow: {metrics['domContentLoaded']}ms")
                
            if issues:
                return {
                    'test': 'performance',
                    'status': 'warning',
                    'notes': ', '.join(issues),
                    'metrics': metrics
                }
            else:
                return {
                    'test': 'performance',
                    'status': 'passed',
                    'notes': 'Performance metrics within acceptable ranges',
                    'metrics': metrics
                }
                
        except Exception as e:
            logger.error(f"Performance test failed: {e}")
            return {
                'test': 'performance',
                'status': 'failed',
                'error': str(e)
            }
            
    async def run_all_tests(self):
        """Run all UI tests"""
        
        logger.info("="*80)
        logger.info("COMPREHENSIVE UI TESTING")
        logger.info("="*80)
        
        await self.setup()
        
        # Define test suite
        tests = [
            self.test_login_flow,
            self.test_tbs_upload_flow,
            self.test_strategy_configuration,
            self.test_real_time_monitoring,
            self.test_results_download,
            self.test_error_handling,
            self.test_responsive_design,
            self.test_performance
        ]
        
        results = []
        
        for test_func in tests:
            logger.info(f"\nRunning {test_func.__name__}...")
            try:
                result = await test_func()
                results.append(result)
                logger.info(f"Result: {result['status']}")
            except Exception as e:
                logger.error(f"Test crashed: {e}")
                results.append({
                    'test': test_func.__name__,
                    'status': 'crashed',
                    'error': str(e)
                })
                
        await self.teardown()
        
        # Generate report
        self.generate_test_report(results)
        
    def generate_test_report(self, results: List[Dict]):
        """Generate comprehensive UI test report"""
        
        report = {
            'test_run': datetime.now().isoformat(),
            'duration': str(datetime.now() - self.start_time),
            'summary': {
                'total': len(results),
                'passed': sum(1 for r in results if r['status'] == 'passed'),
                'failed': sum(1 for r in results if r['status'] == 'failed'),
                'warning': sum(1 for r in results if r['status'] == 'warning'),
                'skipped': sum(1 for r in results if r['status'] == 'skipped'),
                'crashed': sum(1 for r in results if r['status'] == 'crashed')
            },
            'results': results,
            'screenshots': os.listdir(SCREENSHOT_DIR)
        }
        
        # Save JSON report
        report_file = f"{TEST_RESULTS_DIR}/ui_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
            
        # Generate HTML report
        self.generate_html_report(report, report_file.replace('.json', '.html'))
        
        # Print summary
        logger.info("\n" + "="*80)
        logger.info("UI TEST SUMMARY")
        logger.info("="*80)
        logger.info(f"Total Tests: {report['summary']['total']}")
        logger.info(f"Passed: {report['summary']['passed']}")
        logger.info(f"Failed: {report['summary']['failed']}")
        logger.info(f"Warnings: {report['summary']['warning']}")
        logger.info(f"Success Rate: {report['summary']['passed'] / max(1, report['summary']['total']) * 100:.2f}%")
        logger.info(f"\nDetailed report: {report_file}")
        logger.info(f"Screenshots: {SCREENSHOT_DIR}")
        
    def generate_html_report(self, report: Dict, output_file: str):
        """Generate HTML report with screenshots"""
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>UI Test Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                h1, h2, h3 {{ color: #333; }}
                .test-result {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
                .passed {{ background-color: #d4edda; border-color: #c3e6cb; }}
                .failed {{ background-color: #f8d7da; border-color: #f5c6cb; }}
                .warning {{ background-color: #fff3cd; border-color: #ffeeba; }}
                .skipped {{ background-color: #e2e3e5; border-color: #d6d8db; }}
                .crashed {{ background-color: #f8d7da; border-color: #f5c6cb; }}
                .metric {{ display: inline-block; margin: 10px; padding: 10px; background: #f0f0f0; border-radius: 3px; }}
                .screenshot-link {{ color: #007bff; text-decoration: none; }}
                .screenshot-link:hover {{ text-decoration: underline; }}
            </style>
        </head>
        <body>
            <h1>UI Test Report</h1>
            <p>Generated: {report['test_run']}</p>
            <p>Duration: {report['duration']}</p>
            
            <h2>Summary</h2>
            <div class="metric">Total: {report['summary']['total']}</div>
            <div class="metric" style="background: #d4edda;">Passed: {report['summary']['passed']}</div>
            <div class="metric" style="background: #f8d7da;">Failed: {report['summary']['failed']}</div>
            <div class="metric" style="background: #fff3cd;">Warnings: {report['summary']['warning']}</div>
            <div class="metric">Success Rate: {report['summary']['passed'] / max(1, report['summary']['total']) * 100:.2f}%</div>
            
            <h2>Test Results</h2>
        """
        
        for result in report['results']:
            status_class = result['status']
            html_content += f"""
            <div class="test-result {status_class}">
                <h3>{result['test'].replace('_', ' ').title()}</h3>
                <p><strong>Status:</strong> {result['status'].upper()}</p>
            """
            
            if 'notes' in result:
                html_content += f"<p><strong>Notes:</strong> {result['notes']}</p>"
                
            if 'error' in result:
                html_content += f"<p><strong>Error:</strong> {result['error']}</p>"
                
            if 'metrics' in result:
                html_content += "<p><strong>Metrics:</strong></p><ul>"
                for key, value in result['metrics'].items():
                    html_content += f"<li>{key}: {value}</li>"
                html_content += "</ul>"
                
            html_content += "</div>"
            
        # Add screenshots section
        if report['screenshots']:
            html_content += """
            <h2>Screenshots</h2>
            <ul>
            """
            for screenshot in sorted(report['screenshots']):
                html_content += f'<li><a href="../../../ui_test_screenshots/{screenshot}" class="screenshot-link" target="_blank">{screenshot}</a></li>'
                
            html_content += "</ul>"
            
        html_content += """
        </body>
        </html>
        """
        
        with open(output_file, 'w') as f:
            f.write(html_content)
            
        logger.info(f"HTML report saved to: {output_file}")

async def main():
    """Main execution"""
    
    framework = UITestFramework()
    await framework.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())