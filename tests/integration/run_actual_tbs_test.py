#!/usr/bin/env python3
"""
Run Actual TBS Test with GPU Backtester
=======================================

This script runs the actual TBS backtester to verify if fixes have been applied.

Author: Senior Test Engineer
Date: June 9, 2025
"""

import subprocess
import json
import pandas as pd
from pathlib import Path
import sys
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ActualTBSTest:
    """Run actual TBS tests with GPU backtester"""
    
    def __init__(self):
        # Use fixed wrapper for E2E testing
        self.gpu_backtester = Path("/srv/samba/shared/BTRunPortfolio_GPU_Fixed.py")
        if not self.gpu_backtester.exists():
            # Fallback to original
            self.gpu_backtester = Path("/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py")
        self.test_dir = Path("/srv/samba/shared/test_results/tbs")
        self.output_dir = self.test_dir / "gpu_outputs"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def run_gpu_backtest(self, portfolio_file: str, strategy_file: str, output_name: str) -> dict:
        """Run GPU backtester with test files"""
        logger.info(f"Running GPU backtest: {output_name}")
        
        # Check if backtester exists
        if not self.gpu_backtester.exists():
            logger.error(f"GPU backtester not found: {self.gpu_backtester}")
            return {"error": "Backtester not found"}
            
        # Prepare command with correct arguments
        cmd = [
            sys.executable,
            str(self.gpu_backtester),
            "--portfolio-excel", portfolio_file,
            "--output-path", str(self.output_dir / f"{output_name}_output.xlsx"),
            "--start-date", "20240401",  # YYYYMMDD format
            "--end-date", "20240405",
            "--cpu-only"  # Start with CPU mode for testing
        ]
        
        logger.info(f"Command: {' '.join(cmd)}")
        
        try:
            # Run the backtester
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            if result.returncode != 0:
                logger.error(f"Backtester failed: {result.stderr}")
                return {
                    "error": "Backtester failed",
                    "stderr": result.stderr,
                    "stdout": result.stdout
                }
                
            # Parse output
            output_file = self.output_dir / f"{output_name}_output.xlsx"
            if output_file.exists():
                logger.info(f"Output generated: {output_file}")
                # Read and analyze output
                try:
                    df = pd.read_excel(output_file, sheet_name='Trans')
                    return {
                        "success": True,
                        "output_file": str(output_file),
                        "total_trades": len(df),
                        "all_closed": all(df['Status'] == 'CLOSED') if 'Status' in df.columns else False,
                        "total_pnl": df['NetPL'].sum() if 'NetPL' in df.columns else 0
                    }
                except Exception as e:
                    logger.error(f"Error reading output: {e}")
                    return {"error": f"Error reading output: {e}"}
            else:
                return {"error": "Output file not generated"}
                
        except subprocess.TimeoutExpired:
            logger.error("Backtester timeout")
            return {"error": "Timeout"}
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return {"error": str(e)}
            
    def run_test_suite(self):
        """Run complete test suite"""
        test_cases = [
            {
                "name": "basic_straddle",
                "portfolio": "/srv/samba/shared/test_results/tbs/inputs/test_portfolio_basic.xlsx",
                "strategy": "/srv/samba/shared/test_results/tbs/inputs/test_tbs_strategy_basic.xlsx"
            },
            {
                "name": "multi_leg_condor",
                "portfolio": "/srv/samba/shared/test_results/tbs/inputs/test_portfolio_multi_leg.xlsx",
                "strategy": "/srv/samba/shared/test_results/tbs/inputs/test_tbs_strategy_multi_leg.xlsx"
            }
        ]
        
        results = {}
        
        for test in test_cases:
            logger.info(f"\n{'='*60}")
            logger.info(f"Running test: {test['name']}")
            logger.info('='*60)
            
            result = self.run_gpu_backtest(
                test['portfolio'],
                test['strategy'],
                test['name']
            )
            
            results[test['name']] = result
            
            # Log summary
            if result.get('success'):
                logger.info(f"✅ Test completed successfully")
                logger.info(f"   Total trades: {result.get('total_trades', 0)}")
                logger.info(f"   All closed: {result.get('all_closed', False)}")
                logger.info(f"   Total PnL: {result.get('total_pnl', 0)}")
            else:
                logger.error(f"❌ Test failed: {result.get('error', 'Unknown error')}")
                
        return results
        
    def generate_report(self, results: dict):
        """Generate test report"""
        report_path = self.test_dir / "actual_tbs_test_report.json"
        
        with open(report_path, 'w') as f:
            json.dump(results, f, indent=2)
            
        logger.info(f"\nReport saved: {report_path}")
        
        # Print summary
        print("\n" + "="*80)
        print("ACTUAL TBS TEST RESULTS")
        print("="*80)
        
        all_passed = True
        for test_name, result in results.items():
            if result.get('success'):
                status = "✅ PASSED" if result.get('all_closed') else "⚠️ TRADES NOT CLOSED"
                print(f"\n{test_name}: {status}")
                print(f"  - Trades: {result.get('total_trades', 0)}")
                print(f"  - PnL: {result.get('total_pnl', 0)}")
                if not result.get('all_closed'):
                    all_passed = False
            else:
                print(f"\n{test_name}: ❌ FAILED")
                print(f"  - Error: {result.get('error', 'Unknown')}")
                all_passed = False
                
        print("\n" + "="*80)
        if all_passed:
            print("✅ ALL TESTS PASSED - GPU SYSTEM READY FOR PHASE 3.2")
        else:
            print("❌ TESTS FAILED - GPU SYSTEM NEEDS FIXES")
        print("="*80)


def main():
    """Run actual TBS tests"""
    print("Starting Actual TBS Test with GPU Backtester")
    print("=" * 80)
    
    tester = ActualTBSTest()
    
    # Check if test files exist
    test_files = Path("/srv/samba/shared/test_results/tbs/inputs")
    if not test_files.exists():
        print("❌ Test files not found. Please run create_tbs_test_files.py first")
        return
        
    # Run tests
    results = tester.run_test_suite()
    
    # Generate report
    tester.generate_report(results)


if __name__ == "__main__":
    main()