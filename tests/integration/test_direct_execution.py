#!/usr/bin/env python3
"""
Direct execution test to validate comprehensive test files
"""

import os
import subprocess
import sys
from datetime import datetime

def test_direct_execution():
    """Test running backtesters directly from their directories"""
    
    print("="*80)
    print("DIRECT EXECUTION TEST")
    print("="*80)
    
    # Test 1: Run TBS from archive system
    print("\n1. Testing TBS from archive system:")
    archive_dir = "/srv/samba/shared/bt/archive/backtester_stable/BTRUN"
    
    # Copy test file to archive dir
    test_file = "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/comprehensive_tests/tbs/comprehensive_tbs_portfolio.xlsx"
    
    if os.path.exists(test_file):
        subprocess.run([
            'cp', test_file, 
            os.path.join(archive_dir, 'test_portfolio.xlsx')
        ])
        
        cmd = f"cd {archive_dir} && python3 BTRunPortfolio.py --input test_portfolio.xlsx --start 01-04-2024 --end 02-04-2024"
        print(f"Command: {cmd}")
        
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Archive TBS test passed")
        else:
            print("❌ Archive TBS test failed")
            print(f"Error: {result.stderr[:500]}")
    else:
        print("❌ Test file not found")
    
    # Test 2: Run new system with correct imports
    print("\n2. Testing new system with modified imports:")
    
    # Create a simple wrapper to test
    wrapper_content = '''#!/usr/bin/env python3
import sys
import os

# Add paths for imports
sys.path.insert(0, '/srv/samba/shared')
sys.path.insert(0, '/srv/samba/shared/bt')
sys.path.insert(0, '/srv/samba/shared/bt/backtester_stable')

# Now try to run the backtester
try:
    from bt.backtester_stable.BTRUN import BTRunPortfolio_GPU
    print("Import successful!")
except Exception as e:
    print(f"Import failed: {e}")
    # Try direct execution
    os.chdir('/srv/samba/shared/bt/backtester_stable/BTRUN')
    exec(open('BTRunPortfolio_GPU.py').read())
'''
    
    with open('/tmp/test_wrapper.py', 'w') as f:
        f.write(wrapper_content)
    
    result = subprocess.run([
        'python3', '/tmp/test_wrapper.py',
        '--help'
    ], capture_output=True, text=True)
    
    print(f"Wrapper result: {result.stdout[:500]}")
    
    # Test 3: Direct file execution
    print("\n3. Testing direct file execution:")
    os.chdir('/srv/samba/shared/bt/backtester_stable/BTRUN')
    
    # Check what files exist
    gpu_files = subprocess.run(['ls', '-la', '*GPU.py'], shell=True, capture_output=True, text=True)
    print(f"GPU files found:\n{gpu_files.stdout}")
    
    # Try to get help from BT_OI_GPU
    print("\n4. Testing BT_OI_GPU help:")
    cmd = "python3 -c \"exec(open('BT_OI_GPU.py').read().replace('from backtester_stable.BTRUN', 'from'))\" --help"
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    print(f"Result: {result.stdout[:200] if result.stdout else result.stderr[:200]}")

if __name__ == "__main__":
    test_direct_execution()