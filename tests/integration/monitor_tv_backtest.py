#!/usr/bin/env python3
"""Monitor TV backtest progress"""

import os
import time
import subprocess
from datetime import datetime

output_dir = "bt/backtester_stable/BTRUN/output/tv_complete_clean"

print("=" * 80)
print("TV BACKTEST MONITOR")
print("=" * 80)
print(f"Monitoring: {output_dir}")
print("Press Ctrl+C to stop monitoring")
print("=" * 80)

try:
    while True:
        # Check if process is running
        result = subprocess.run(["pgrep", "-f", "BT_TV_GPU"], capture_output=True)
        if result.returncode == 0:
            pid = result.stdout.decode().strip()
            
            # Get process info
            ps_result = subprocess.run(["ps", "-p", pid, "-o", "%cpu,%mem,etime"], capture_output=True, text=True)
            if ps_result.returncode == 0:
                lines = ps_result.stdout.strip().split('\n')
                if len(lines) > 1:
                    cpu, mem, elapsed = lines[1].split()
                    print(f"\r[{datetime.now().strftime('%H:%M:%S')}] PID: {pid} | CPU: {cpu}% | MEM: {mem}% | Elapsed: {elapsed}", end="", flush=True)
            
            # Count output files
            if os.path.exists(output_dir):
                files = [f for f in os.listdir(output_dir) if f.endswith('.xlsx')]
                if files:
                    long_count = sum(1 for f in files if '_LONG_' in f)
                    short_count = sum(1 for f in files if '_SHORT_' in f)
                    manual_count = sum(1 for f in files if '_MANUAL_' in f)
                    print(f" | Files: {len(files)} (L:{long_count} S:{short_count} M:{manual_count})", end="", flush=True)
        else:
            print(f"\n[{datetime.now().strftime('%H:%M:%S')}] Process completed!")
            
            # Final count
            if os.path.exists(output_dir):
                files = [f for f in os.listdir(output_dir) if f.endswith('.xlsx')]
                print(f"Total output files: {len(files)}")
                
                long_count = sum(1 for f in files if '_LONG_' in f)
                short_count = sum(1 for f in files if '_SHORT_' in f)
                manual_count = sum(1 for f in files if '_MANUAL_' in f)
                
                print(f"  LONG trades: {long_count}")
                print(f"  SHORT trades: {short_count}")
                print(f"  MANUAL trades: {manual_count}")
            break
            
        time.sleep(5)  # Check every 5 seconds
        
except KeyboardInterrupt:
    print("\n\nMonitoring stopped by user.")
    
print("=" * 80) 