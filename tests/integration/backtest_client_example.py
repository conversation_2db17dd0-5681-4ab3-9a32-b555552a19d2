"""
Example client for accessing both legacy and HeavyDB backtester APIs
"""

import asyncio
import aiohttp
import time
from typing import Dict, Optional

class BacktesterClient:
    """Unified client for both legacy and GPU backtesters"""
    
    def __init__(self, legacy_url: str = None, gpu_url: str = None, api_key: str = None):
        self.legacy_url = legacy_url or "http://106.51.63.60:5000"
        self.gpu_url = gpu_url or "http://your-gpu-server:8000"
        self.api_key = api_key
        self.headers = {"Authorization": f"Bearer {api_key}"} if api_key else {}
    
    async def submit_legacy_backtest(self, portfolio_excel: str, start_date: str, end_date: str) -> Dict:
        """Submit backtest to legacy system"""
        async with aiohttp.ClientSession() as session:
            payload = {
                "portfolio_excel": portfolio_excel,
                "start_date": start_date,
                "end_date": end_date
            }
            
            async with session.post(
                f"{self.legacy_url}/api/v1/backtest",
                json=payload,
                headers=self.headers
            ) as response:
                return await response.json()
    
    async def submit_gpu_backtest(self, portfolio_excel: str, start_date: str, end_date: str, 
                                  use_gpu: bool = True, strategy_type: str = "TBS") -> Dict:
        """Submit backtest to GPU-accelerated system"""
        async with aiohttp.ClientSession() as session:
            payload = {
                "portfolio_excel": portfolio_excel,
                "start_date": start_date,
                "end_date": end_date,
                "use_gpu": use_gpu,
                "strategy_type": strategy_type
            }
            
            async with session.post(
                f"{self.gpu_url}/api/v2/backtest",
                json=payload,
                headers=self.headers
            ) as response:
                return await response.json()
    
    async def get_job_status(self, job_id: str, system: str = "gpu") -> Dict:
        """Get status of a backtest job"""
        url = self.gpu_url if system == "gpu" else self.legacy_url
        api_version = "v2" if system == "gpu" else "v1"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{url}/api/{api_version}/backtest/{job_id}",
                headers=self.headers
            ) as response:
                return await response.json()
    
    async def wait_for_completion(self, job_id: str, system: str = "gpu", 
                                  timeout: int = 3600, poll_interval: int = 5) -> Dict:
        """Wait for a job to complete with polling"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            status = await self.get_job_status(job_id, system)
            
            if status["status"] in ["completed", "failed"]:
                return status
            
            print(f"Job {job_id} status: {status['status']}, progress: {status.get('progress', 0)}%")
            await asyncio.sleep(poll_interval)
        
        raise TimeoutError(f"Job {job_id} did not complete within {timeout} seconds")
    
    async def download_results(self, job_id: str, output_path: str, system: str = "gpu"):
        """Download results from completed job"""
        url = self.gpu_url if system == "gpu" else self.legacy_url
        api_version = "v2" if system == "gpu" else "v1"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{url}/api/{api_version}/backtest/{job_id}/download",
                headers=self.headers
            ) as response:
                if response.status == 200:
                    with open(output_path, 'wb') as f:
                        f.write(await response.read())
                    print(f"Results saved to {output_path}")
                else:
                    raise Exception(f"Failed to download results: {await response.text()}")

async def run_comparison_backtest():
    """Run the same backtest on both systems and compare"""
    client = BacktesterClient()
    
    # Test parameters
    portfolio_excel = "/path/to/input_portfolio.xlsx"
    start_date = "20250401"
    end_date = "20250402"
    
    print("Running backtests on both systems...")
    
    # Submit to both systems
    legacy_result = await client.submit_legacy_backtest(portfolio_excel, start_date, end_date)
    gpu_result = await client.submit_gpu_backtest(portfolio_excel, start_date, end_date)
    
    print(f"Legacy job ID: {legacy_result['job_id']}")
    print(f"GPU job ID: {gpu_result['job_id']}")
    
    # Wait for both to complete
    print("\nWaiting for legacy backtest...")
    legacy_final = await client.wait_for_completion(legacy_result['job_id'], system="legacy")
    
    print("\nWaiting for GPU backtest...")
    gpu_final = await client.wait_for_completion(gpu_result['job_id'], system="gpu")
    
    # Download results
    await client.download_results(legacy_result['job_id'], "legacy_results.xlsx", system="legacy")
    await client.download_results(gpu_result['job_id'], "gpu_results.xlsx", system="gpu")
    
    print("\nBacktest complete! Results saved to legacy_results.xlsx and gpu_results.xlsx")
    
    # Compare execution times
    if legacy_final.get('started_at') and legacy_final.get('completed_at'):
        legacy_duration = (legacy_final['completed_at'] - legacy_final['started_at']).total_seconds()
        print(f"Legacy execution time: {legacy_duration:.2f} seconds")
    
    if gpu_final.get('started_at') and gpu_final.get('completed_at'):
        gpu_duration = (gpu_final['completed_at'] - gpu_final['started_at']).total_seconds()
        print(f"GPU execution time: {gpu_duration:.2f} seconds")

async def run_batch_backtest():
    """Submit multiple backtests in batch"""
    client = BacktesterClient()
    
    # Multiple test scenarios
    scenarios = [
        {"portfolio": "portfolio1.xlsx", "start": "20250401", "end": "20250405"},
        {"portfolio": "portfolio2.xlsx", "start": "20250406", "end": "20250410"},
        {"portfolio": "portfolio3.xlsx", "start": "20250411", "end": "20250415"},
    ]
    
    # Submit all jobs
    jobs = []
    for scenario in scenarios:
        result = await client.submit_gpu_backtest(
            scenario["portfolio"],
            scenario["start"],
            scenario["end"]
        )
        jobs.append(result["job_id"])
        print(f"Submitted job {result['job_id']} for {scenario['portfolio']}")
    
    # Wait for all to complete
    print("\nWaiting for all jobs to complete...")
    results = await asyncio.gather(*[
        client.wait_for_completion(job_id) for job_id in jobs
    ])
    
    print(f"\nAll {len(results)} jobs completed!")

if __name__ == "__main__":
    # Run comparison test
    asyncio.run(run_comparison_backtest())
    
    # Or run batch test
    # asyncio.run(run_batch_backtest()) 