#!/usr/bin/env python3
"""
Create comprehensive POC tests for all TBS column functionality
"""

import pandas as pd
import os
from datetime import datetime, timedelta
import json

def create_sl_tp_test_cases():
    """Create test cases for different SL/TP variations"""
    
    print("="*80)
    print("CREATING SL/TP TEST CASES")
    print("="*80)
    
    base_dir = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/poc_tests'
    os.makedirs(base_dir, exist_ok=True)
    
    # Test Case 1: Percentage-based SL/TP
    test1_general = pd.DataFrame([{
        'StrategyName': 'SL_TP_PERCENT_TEST',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '0',
        'StrikeSelectionTime': '92000',
        'StartTime': '91600',
        'EndTime': '153000',
        'Weekdays': '1,2,3,4,5'
    }])
    
    test1_legs = pd.DataFrame([
        {
            'LegID': 1,
            'Instrument': 'OPTIONS',
            'Transaction': 'SELL',
            'StrikeMethod': 'ATM',
            'StrikeValue': 0,
            'Expiry': 'CW',
            'SLType': 'PERCENT',
            'SLValue': 25,  # 25% SL
            'TGTType': 'PERCENT',
            'TGTValue': 50,  # 50% target
            'Lots': 2
        },
        {
            'LegID': 2,
            'Instrument': 'OPTIONS',
            'Transaction': 'BUY',
            'StrikeMethod': 'OTM',
            'StrikeValue': 2,
            'Expiry': 'CW',
            'SLType': 'NONE',
            'SLValue': 0,
            'TGTType': 'NONE',
            'TGTValue': 0,
            'Lots': 2
        }
    ])
    
    # Test Case 2: Points-based SL/TP
    test2_general = pd.DataFrame([{
        'StrategyName': 'SL_TP_POINTS_TEST',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '0',
        'StrikeSelectionTime': '92000',
        'StartTime': '91600',
        'EndTime': '153000',
        'Weekdays': '1,2,3,4,5'
    }])
    
    test2_legs = pd.DataFrame([
        {
            'LegID': 1,
            'Instrument': 'OPTIONS',
            'Transaction': 'SELL',
            'StrikeMethod': 'ATM',
            'StrikeValue': 0,
            'Expiry': 'CW',
            'SLType': 'POINTS',
            'SLValue': 30,  # 30 points SL
            'TGTType': 'POINTS',
            'TGTValue': 50,  # 50 points target
            'Lots': 2
        }
    ])
    
    # Test Case 3: Trailing SL
    test3_general = pd.DataFrame([{
        'StrategyName': 'TRAILING_SL_TEST',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '0',
        'StrikeSelectionTime': '92000',
        'StartTime': '91600',
        'EndTime': '153000',
        'Weekdays': '1,2,3,4,5'
    }])
    
    test3_legs = pd.DataFrame([
        {
            'LegID': 1,
            'Instrument': 'OPTIONS',
            'Transaction': 'SELL',
            'StrikeMethod': 'ATM',
            'StrikeValue': 0,
            'Expiry': 'CW',
            'SLType': 'PERCENT',
            'SLValue': 50,
            'TGTType': 'NONE',
            'TGTValue': 0,
            'TrailSLType': 'PERCENT',
            'SL_TrailAt': 20,  # Start trailing at 20% profit
            'SL_TrailBy': 10,  # Trail by 10%
            'Lots': 2
        }
    ])
    
    # Save test cases
    test_cases = [
        ('sl_tp_percent_test.xlsx', test1_general, test1_legs),
        ('sl_tp_points_test.xlsx', test2_general, test2_legs),
        ('trailing_sl_test.xlsx', test3_general, test3_legs)
    ]
    
    for filename, general_df, leg_df in test_cases:
        filepath = os.path.join(base_dir, filename)
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            general_df.to_excel(writer, sheet_name='GeneralParameter', index=False)
            leg_df.to_excel(writer, sheet_name='LegParameter', index=False)
        print(f"✅ Created: {filepath}")
    
    return base_dir

def create_reentry_test_cases():
    """Create test cases for re-entry and re-execute logic"""
    
    print("\n" + "="*80)
    print("CREATING RE-ENTRY TEST CASES")
    print("="*80)
    
    base_dir = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/poc_tests'
    
    # Test Case 1: Re-entry on SL hit
    test1_general = pd.DataFrame([{
        'StrategyName': 'REENTRY_ON_SL_TEST',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '0',
        'StrikeSelectionTime': '92000',
        'StartTime': '91600',
        'EndTime': '153000',
        'LastEntryTime': '143000',  # Last entry at 2:30 PM
        'Weekdays': '1,2,3,4,5'
    }])
    
    test1_legs = pd.DataFrame([
        {
            'LegID': 1,
            'Instrument': 'OPTIONS',
            'Transaction': 'SELL',
            'StrikeMethod': 'ATM',
            'StrikeValue': 0,
            'Expiry': 'CW',
            'SLType': 'PERCENT',
            'SLValue': 30,
            'TGTType': 'NONE',
            'TGTValue': 0,
            'ReEntryType': 'ASPERSTRIKE',  # Re-enter at same strike
            'ReEnteriesCount': 2,  # Allow 2 re-entries
            'Lots': 2
        }
    ])
    
    # Test Case 2: Re-execute strategy
    test2_general = pd.DataFrame([{
        'StrategyName': 'REEXECUTE_STRATEGY_TEST',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '0',
        'StrikeSelectionTime': '92000',
        'StartTime': '91600',
        'EndTime': '153000',
        'StrategyProfit': 5000,  # Re-execute on 5000 profit
        'StrategyLoss': -3000,   # Re-execute on 3000 loss
        'StrategyProfitReExecuteNo': 2,  # Re-execute 2 times on profit
        'StrategyLossReExecuteNo': 1,    # Re-execute 1 time on loss
        'Weekdays': '1,2,3,4,5'
    }])
    
    test2_legs = pd.DataFrame([
        {
            'LegID': 1,
            'Instrument': 'OPTIONS',
            'Transaction': 'SELL',
            'StrikeMethod': 'ATM',
            'StrikeValue': 0,
            'Expiry': 'CW',
            'SLType': 'PERCENT',
            'SLValue': 50,
            'TGTType': 'PERCENT',
            'TGTValue': 100,
            'Lots': 1
        }
    ])
    
    # Test Case 3: Complex re-entry with conditions
    test3_general = pd.DataFrame([{
        'StrategyName': 'COMPLEX_REENTRY_TEST',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '0',
        'StrikeSelectionTime': '92000',
        'StartTime': '91600',
        'EndTime': '153000',
        'Weekdays': '1,2,3,4,5'
    }])
    
    test3_legs = pd.DataFrame([
        {
            'LegID': 1,
            'Instrument': 'OPTIONS',
            'Transaction': 'SELL',
            'StrikeMethod': 'ATM',
            'StrikeValue': 0,
            'Expiry': 'CW',
            'SLType': 'PERCENT',
            'SLValue': 25,
            'TGTType': 'PERCENT',
            'TGTValue': 50,
            'ReEntryType': 'STRIKE',  # Re-enter at new strike
            'ReEnteriesCount': 3,
            'OnExit_OpenTradeOn': 'YES',  # Open new trade on exit
            'OnExit_OpenTradeDelay': 60,  # Wait 60 seconds
            'Lots': 2
        },
        {
            'LegID': 2,
            'Instrument': 'OPTIONS',
            'Transaction': 'BUY',
            'StrikeMethod': 'OTM',
            'StrikeValue': 3,
            'Expiry': 'CW',
            'OnEntry_SqOffTradeOff': 'YES',  # Square off if main leg enters
            'Lots': 2
        }
    ])
    
    # Save test cases
    test_cases = [
        ('reentry_on_sl_test.xlsx', test1_general, test1_legs),
        ('reexecute_strategy_test.xlsx', test2_general, test2_legs),
        ('complex_reentry_test.xlsx', test3_general, test3_legs)
    ]
    
    for filename, general_df, leg_df in test_cases:
        filepath = os.path.join(base_dir, filename)
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            general_df.to_excel(writer, sheet_name='GeneralParameter', index=False)
            leg_df.to_excel(writer, sheet_name='LegParameter', index=False)
        print(f"✅ Created: {filepath}")

def create_advanced_column_tests():
    """Create test cases for advanced column features"""
    
    print("\n" + "="*80)
    print("CREATING ADVANCED COLUMN TESTS")
    print("="*80)
    
    base_dir = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/poc_tests'
    
    # Test Case 1: Different strike selection methods
    test1_general = pd.DataFrame([{
        'StrategyName': 'STRIKE_METHODS_TEST',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '0',
        'StrikeSelectionTime': '92000',
        'StartTime': '91600',
        'EndTime': '153000',
        'Weekdays': '4'  # Only Thursdays
    }])
    
    test1_legs = pd.DataFrame([
        # ATM with offset
        {
            'LegID': 1,
            'Instrument': 'OPTIONS',
            'Transaction': 'SELL',
            'StrikeMethod': 'ATM',
            'StrikeValue': -2,  # ITM by 2 strikes
            'Expiry': 'CW',
            'Lots': 1
        },
        # Premium-based selection
        {
            'LegID': 2,
            'Instrument': 'OPTIONS', 
            'Transaction': 'SELL',
            'StrikeMethod': 'PREMIUM',
            'MatchPremium': 50,  # Find strike with 50 premium
            'StrikePremiumCondition': 'GT',  # Greater than
            'Expiry': 'CW',
            'Lots': 1
        },
        # Fixed strike
        {
            'LegID': 3,
            'Instrument': 'OPTIONS',
            'Transaction': 'BUY',
            'StrikeMethod': 'FIXED',
            'StrikeValue': 22000,  # Fixed strike
            'Expiry': 'CW',
            'Lots': 1
        }
    ])
    
    # Test Case 2: Partial exits (SqOff)
    test2_general = pd.DataFrame([{
        'StrategyName': 'PARTIAL_EXIT_TEST',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '0',
        'StrikeSelectionTime': '92000',
        'StartTime': '91600',
        'EndTime': '153000',
        'SqOff1Time': '110000',  # First square off at 11 AM
        'SqOff1Percent': 50,     # Exit 50%
        'SqOff2Time': '140000',  # Second square off at 2 PM
        'SqOff2Percent': 100,    # Exit remaining
        'Weekdays': '1,2,3,4,5'
    }])
    
    test2_legs = pd.DataFrame([
        {
            'LegID': 1,
            'Instrument': 'OPTIONS',
            'Transaction': 'SELL',
            'StrikeMethod': 'ATM',
            'StrikeValue': 0,
            'Expiry': 'CW',
            'SLType': 'PERCENT',
            'SLValue': 100,
            'Lots': 4  # 4 lots for partial exits
        }
    ])
    
    # Test Case 3: Strategy trailing
    test3_general = pd.DataFrame([{
        'StrategyName': 'STRATEGY_TRAILING_TEST',
        'Underlying': 'NIFTY',
        'Index': 'NIFTY',
        'DTE': '0',
        'StrikeSelectionTime': '92000',
        'StartTime': '91600',
        'EndTime': '153000',
        'StrategyTrailingType': 'LOCK_AND_TRAIL',
        'ProfitReaches': 3000,     # When profit reaches 3000
        'LockMinProfitAt': 2000,   # Lock minimum 2000
        'IncreaseInProfit': 1000,  # For every 1000 increase
        'TrailMinProfitBy': 500,   # Trail by 500
        'Weekdays': '1,2,3,4,5'
    }])
    
    test3_legs = pd.DataFrame([
        {
            'LegID': 1,
            'Instrument': 'OPTIONS',
            'Transaction': 'SELL',
            'StrikeMethod': 'ATM',
            'StrikeValue': 0,
            'Expiry': 'CW',
            'Lots': 2
        },
        {
            'LegID': 2,
            'Instrument': 'OPTIONS',
            'Transaction': 'BUY',
            'StrikeMethod': 'OTM',
            'StrikeValue': 3,
            'Expiry': 'CW',
            'Lots': 2
        }
    ])
    
    # Save test cases
    test_cases = [
        ('strike_methods_test.xlsx', test1_general, test1_legs),
        ('partial_exit_test.xlsx', test2_general, test2_legs),
        ('strategy_trailing_test.xlsx', test3_general, test3_legs)
    ]
    
    for filename, general_df, leg_df in test_cases:
        filepath = os.path.join(base_dir, filename)
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            general_df.to_excel(writer, sheet_name='GeneralParameter', index=False)
            leg_df.to_excel(writer, sheet_name='LegParameter', index=False)
        print(f"✅ Created: {filepath}")

def create_master_portfolio():
    """Create master portfolio to run all tests"""
    
    print("\n" + "="*80)
    print("CREATING MASTER PORTFOLIO")
    print("="*80)
    
    portfolio_file = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/poc_tests/poc_master_portfolio.xlsx'
    
    # Portfolio settings
    portfolio_df = pd.DataFrame([{
        'PortfolioName': 'POC_ALL_COLUMNS',
        'StartDate': '2024-04-01',
        'EndDate': '2024-04-30',
        'InitialCapital': 1000000,
        'Enabled': 'YES'
    }])
    
    # Strategy settings - include all test strategies
    strategies = [
        'sl_tp_percent_test.xlsx',
        'sl_tp_points_test.xlsx',
        'trailing_sl_test.xlsx',
        'reentry_on_sl_test.xlsx',
        'reexecute_strategy_test.xlsx',
        'complex_reentry_test.xlsx',
        'strike_methods_test.xlsx',
        'partial_exit_test.xlsx',
        'strategy_trailing_test.xlsx'
    ]
    
    strategy_df = pd.DataFrame([{
        'StrategyName': os.path.splitext(s)[0].upper(),
        'StrategyExcelFilePath': s,
        'Enabled': 'YES',
        'LegsInStrategy': 2 if 'complex' in s or 'trailing' in s else 1,
        'CapitalAllocation': 100000
    } for s in strategies])
    
    # Save portfolio
    with pd.ExcelWriter(portfolio_file, engine='openpyxl') as writer:
        portfolio_df.to_excel(writer, sheet_name='PortfolioSetting', index=False)
        strategy_df.to_excel(writer, sheet_name='StrategySetting', index=False)
    
    print(f"✅ Created master portfolio: {portfolio_file}")
    
    return portfolio_file

def create_run_script():
    """Create script to run the POC"""
    
    script_content = '''#!/usr/bin/env python3
"""
Run comprehensive POC test for all TBS columns
"""

import os
import subprocess
from datetime import datetime
import pandas as pd

def run_poc_backtest():
    """Run the POC backtest with all optimizations"""
    
    print("="*80)
    print("TBS COLUMNS POC - COMPREHENSIVE TEST")
    print("="*80)
    print(f"Start time: {datetime.now()}")
    
    portfolio_file = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/poc_tests/poc_master_portfolio.xlsx'
    output_file = f'/srv/samba/shared/poc_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
    
    # Build command with GPU optimization
    cmd = [
        'python3',
        '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py',
        '--portfolio-excel', portfolio_file,
        '--output-path', output_file,
        '--workers', 'auto',
        '--use-gpu-optimization',
        '--gpu-workers', '4',
        '--batch-days', '7',
        '--merge-output',
        '--debug'
    ]
    
    print("\\nCommand:")
    print(' '.join(cmd))
    
    print("\\n" + "-"*80)
    print("Running POC backtest...")
    print("-"*80 + "\\n")
    
    # Set GPU environment
    env = os.environ.copy()
    env['USE_GPU'] = '1'
    
    # Run backtest
    process = subprocess.Popen(
        cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        universal_newlines=True,
        bufsize=1,
        env=env
    )
    
    # Print output in real-time
    for line in process.stdout:
        print(line, end='')
    
    return_code = process.wait()
    
    if return_code == 0:
        print("\\n✅ POC backtest completed successfully!")
        print(f"Output file: {output_file}")
        
        # Analyze results
        analyze_poc_results(output_file)
    else:
        print(f"\\n❌ POC backtest failed with return code: {return_code}")

def analyze_poc_results(output_file):
    """Analyze POC results to verify all columns worked"""
    
    print("\\n" + "="*80)
    print("POC RESULTS ANALYSIS")
    print("="*80)
    
    try:
        xls = pd.ExcelFile(output_file)
        
        # Check each strategy
        for sheet in xls.sheet_names:
            if 'Trans' in sheet:
                df = pd.read_excel(output_file, sheet_name=sheet)
                
                # Extract strategy name
                if '_TEST' in sheet:
                    strategy = sheet.split('_TEST')[0]
                    print(f"\\n{strategy} TEST:")
                    print("-"*60)
                    
                    # Check exits
                    if 'Reason' in df.columns:
                        exit_reasons = df['Reason'].value_counts()
                        print("Exit reasons:")
                        for reason, count in exit_reasons.items():
                            print(f"  - {reason}: {count}")
                    
                    # Check re-entries
                    if 'Re_Entry_No' in df.columns:
                        max_reentry = df['Re_Entry_No'].max()
                        if max_reentry > 0:
                            print(f"✅ Re-entries detected: Max {max_reentry}")
                    
                    # Check P&L
                    if 'Netpnlafterexpenses' in df.columns:
                        total_pnl = df['Netpnlafterexpenses'].sum()
                        print(f"Total P&L: ₹{total_pnl:,.2f}")
        
        print("\\n✅ POC test completed - check results for column functionality")
        
    except Exception as e:
        print(f"Error analyzing results: {e}")

if __name__ == "__main__":
    run_poc_backtest()
'''
    
    script_file = '/srv/samba/shared/run_poc_all_columns.py'
    with open(script_file, 'w') as f:
        f.write(script_content)
    
    os.chmod(script_file, 0o755)
    print(f"\n✅ Created POC run script: {script_file}")

def main():
    """Main execution"""
    
    print("CREATING COMPREHENSIVE POC FOR ALL TBS COLUMNS")
    print("="*80)
    
    # Create all test cases
    base_dir = create_sl_tp_test_cases()
    create_reentry_test_cases()
    create_advanced_column_tests()
    
    # Create master portfolio
    portfolio_file = create_master_portfolio()
    
    # Create run script
    create_run_script()
    
    print("\n" + "="*80)
    print("POC CREATION COMPLETE")
    print("="*80)
    
    print("\n✅ Created test cases for:")
    print("  1. SL/TP variations (percent, points, trailing)")
    print("  2. Re-entry logic (on SL, strategy re-execute)")
    print("  3. Advanced features (strike methods, partial exits, strategy trailing)")
    
    print("\n📁 Test files location:")
    print(f"  {base_dir}")
    
    print("\n🚀 To run the POC:")
    print("  python3 /srv/samba/shared/run_poc_all_columns.py")
    
    print("\n📊 The POC will test:")
    print("  - All strike selection methods (ATM, ITM, OTM, FIXED, PREMIUM)")
    print("  - SL/TP with different types (PERCENT, POINTS)")
    print("  - Trailing stop loss")
    print("  - Re-entry on SL hit")
    print("  - Strategy re-execution on profit/loss")
    print("  - Partial exits (SqOff)")
    print("  - Strategy-level trailing")
    print("  - Complex entry/exit conditions")
    print("  - GPU optimization with concurrent workers")

if __name__ == "__main__":
    main()