#!/usr/bin/env python3
"""
Minimal test to check if TV backtesters produce any output
"""

import os
import sys
import pandas as pd

# Set up paths
sys.path.insert(0, "/srv/samba/shared/bt")
os.chdir("/srv/samba/shared/bt")

# Import modules directly
from backtester_stable.BTRUN.core import config

print("Config INPUT_FILE_FOLDER:", config.INPUT_FILE_FOLDER)

# Check TV files
tv_files = {
    'universal': os.path.join(config.INPUT_FILE_FOLDER, "input_tv_universal.xlsx"),
    'regular': os.path.join(config.INPUT_FILE_FOLDER, "input_tv.xlsx"),
    'fixed': os.path.join(config.INPUT_FILE_FOLDER, "input_tv_fixed.xlsx")
}

for name, path in tv_files.items():
    print(f"\n{name}: {path}")
    print(f"  Exists: {os.path.exists(path)}")
    
    if os.path.exists(path):
        try:
            df = pd.read_excel(path, sheet_name='Setting')
            enabled = df[df['Enabled'].astype(str).str.lower() == 'yes']
            print(f"  Enabled: {len(enabled)}")
            if len(enabled) > 0:
                row = enabled.iloc[0]
                print(f"  Name: {row['Name']}")
                print(f"  StartDate: {row.get('StartDate', 'N/A')}")
                print(f"  EndDate: {row.get('EndDate', 'N/A')}")
        except Exception as e:
            print(f"  Error: {e}")

# Now let's try to run V4 directly with proper imports
print("\n" + "="*60)
print("Testing direct V4 import and execution")
print("="*60)

try:
    # Import V4
    from backtester_stable.BTRUN.BT_TV_GPU_aggregated_v4 import (
        load_tv_settings, 
        process_signals_simple,
        run_aggregated_backtest
    )
    
    # Load settings
    print("\nLoading TV settings...")
    settings = load_tv_settings()
    print(f"Found {len(settings)} enabled TV settings")
    
    if settings:
        tv_setting = settings[0]
        print(f"\nFirst setting: {tv_setting.Name}")
        print(f"Signal file: {tv_setting.SignalFilePath}")
        
        # Check if signal file exists
        if os.path.exists(tv_setting.SignalFilePath):
            print("✅ Signal file exists")
            
            # Try to read signals
            try:
                signals_df = pd.read_excel(tv_setting.SignalFilePath)
                print(f"Signal shape: {signals_df.shape}")
                print(f"Signal columns: {list(signals_df.columns)}")
                
                # Process signals
                processed = process_signals_simple(signals_df, tv_setting)
                for direction, sigs in processed.items():
                    print(f"{direction}: {len(sigs)} signals")
                    
            except Exception as e:
                print(f"Error reading signals: {e}")
        else:
            print("❌ Signal file not found")
            
except Exception as e:
    print(f"Error with V4: {e}")
    import traceback
    traceback.print_exc()

# Test Enhanced version
print("\n" + "="*60)
print("Testing Enhanced version")
print("="*60)

try:
    from backtester_stable.BTRUN.BT_TV_GPU_enhanced import EnhancedTvGpuBacktester
    
    # Create mock args
    class Args:
        tv_file = "input_tv.xlsx"
        output_dir = "/srv/samba/shared/Trades/test_enhanced"
        strict = False
        debug = True
    
    args = Args()
    backtester = EnhancedTvGpuBacktester(args)
    
    # Try to load settings
    settings = backtester._load_tv_settings()
    print(f"Enhanced found {len(settings)} TV settings")
    
except Exception as e:
    print(f"Error with Enhanced: {e}")
    import traceback
    traceback.print_exc()